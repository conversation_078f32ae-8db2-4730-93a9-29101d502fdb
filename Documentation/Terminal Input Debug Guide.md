# Terminal Input Debug Guide

## **Current Status** 🔍

The terminal is **visually active** with a **green status** (shell running), but **user input is not reaching the terminal**. This indicates a break in the input flow chain.

## **Input Flow Chain Analysis**

### **Expected Flow** ✅
```
User Keystroke → xterm.onData() → terminalAPI.writeToTerminal() → IPC → PTY.write() → Shell
```

### **Debugging Points** 🔍

#### **1. Frontend Input Capture** 
**File**: `file-explorer/components/terminal/TerminalPanel.tsx`
**Line**: ~340

```typescript
const inputDisposable = terminal.onData((data: string) => {
  console.log(`⌨️ [TerminalPanel] User input captured:`, JSON.stringify(data))
  // Enhanced debugging added
  console.log(`🔍 [TerminalPanel] Debug info:`, {
    backendId,
    terminalAPIExists: !!terminalAPI,
    writeToTerminalExists: !!terminalAPI?.writeToTerminal
  })
  
  if (backendId && terminalAPI?.writeToTerminal) {
    terminalAPI.writeToTerminal(backendId, data)
    console.log(`✅ [TerminalPanel] Input sent successfully`)
  }
})
```

#### **2. IPC Bridge** 
**File**: `file-explorer/electron/preload.js`
**Line**: ~53

```javascript
writeToTerminal: (sessionId, input) => ipcRenderer.send('terminal:input', sessionId, input)
```

#### **3. Backend Input Handler**
**File**: `file-explorer/electron/main.ts`
**Line**: ~1330

```typescript
ipcMain.on('terminal:input', (event, sessionId: string, input: string) => {
  safeConsole.log(`⌨️ [Terminal:${sessionId}] Input: ${JSON.stringify(input)}`);
  session.ptyProcess.write(input);
})
```

## **Testing & Debugging Steps**

### **Step 1: Test Manual Input** 🧪
Open Electron DevTools and run:
```javascript
// Test if API is available
console.log('Terminal API:', window.electronAPI?.terminalAPI)

// Test manual input (added to TerminalPanel)
window.testTerminalInput('ls\n')
```

**Expected Results**:
- Should see backend logs: `⌨️ [Terminal:session-id] Input: "ls\n"`
- Should see command output in terminal

### **Step 2: Test Keystroke Capture** ⌨️
1. Click in terminal
2. Type any character (e.g., 'a')
3. Check console for:
   - `⌨️ [TerminalPanel] User input captured: "a"`
   - `🔍 [TerminalPanel] Debug info: {...}`

### **Step 3: Check Session State** 🔗
Look for these logs:
- `✅ [TerminalPanel] PTY session established: terminal-xxxxx`
- `🎯 [TerminalPanel] Terminal focused after initialization`

### **Step 4: Verify Backend Reception** 📡
In Electron main process logs, look for:
- `⌨️ [Terminal:session-id] Input: "character"`
- `✅ [Terminal:session-id] Input written successfully`

## **Common Issues & Fixes**

### **Issue 1: No Input Capture** ❌
**Symptoms**: No `⌨️ [TerminalPanel] User input captured` logs
**Cause**: Terminal not focused or onData not registered
**Fix**: 
```typescript
// Ensure terminal is focused
terminal.focus()
// Check if onData is properly registered
console.log('onData registered:', !!inputDisposable)
```

### **Issue 2: API Not Available** ❌
**Symptoms**: `terminalAPIExists: false` in debug logs
**Cause**: Preload script not loaded or Electron API not exposed
**Fix**: 
- Restart Electron app
- Check preload script path in main.ts
- Verify `window.electronAPI` exists

### **Issue 3: No Backend Session** ❌
**Symptoms**: `hasBackendId: false` in debug logs
**Cause**: PTY session failed to start or was terminated
**Fix**:
- Check for PTY startup errors
- Verify `node-pty-prebuilt-multiarch` is installed
- Look for session exit logs

### **Issue 4: IPC Communication Broken** ❌
**Symptoms**: Frontend logs show input sent, but no backend logs
**Cause**: IPC channel mismatch or preload script issues
**Fix**:
- Verify IPC channel names match (`terminal:input`)
- Check preload script compilation
- Restart Electron app

## **Quick Diagnostic Commands**

### **In Electron DevTools Console**:
```javascript
// 1. Check API availability
console.log('Electron API:', window.electronAPI)
console.log('Terminal API:', window.electronAPI?.terminalAPI)

// 2. Test manual input
window.testTerminalInput('echo "Hello World"\n')

// 3. Check terminal instance
console.log('Terminal focused:', document.activeElement)

// 4. Force focus
document.querySelector('[data-terminal]')?.focus()
```

### **Expected Console Output** ✅
```
⌨️ [TerminalPanel] User input captured: "a"
🔍 [TerminalPanel] Debug info: {
  backendId: "terminal-1234567890-abcdef",
  terminalAPIExists: true,
  terminalAPIType: "object",
  writeToTerminalExists: true,
  writeToTerminalType: "function"
}
📤 [TerminalPanel] Sending input to backend session: terminal-1234567890-abcdef
✅ [TerminalPanel] Input sent successfully
```

## **Fix Implementation Status**

### **✅ Completed**
- Enhanced input debugging in TerminalPanel
- Added manual test function (`window.testTerminalInput`)
- Improved error handling and logging
- Fixed node-pty dependency issues
- Enhanced PTY session stability

### **🔍 Next Steps**
1. **Start Electron app**: `npm run electron:dev`
2. **Open terminal** in the app
3. **Open DevTools** (F12)
4. **Test manual input**: `window.testTerminalInput()`
5. **Test keyboard input**: Type in terminal and check logs
6. **Report findings**: Which step in the chain is failing

## **Expected Resolution**

With the enhanced debugging, we should be able to pinpoint exactly where the input flow breaks:

- **Frontend**: Input capture and API calls
- **IPC**: Communication between renderer and main
- **Backend**: PTY session and shell interaction

The comprehensive logging will show us the exact failure point, allowing for a targeted fix.
