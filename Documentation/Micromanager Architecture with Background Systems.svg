<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 1000 900" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1000" height="900" fill="#f8f9fa" />

  <!-- Title -->
  <text x="500" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#333">Micromanager Architecture with Background Systems</text>

  <!-- Background Systems (Left Section) -->
  <rect x="30" y="80" width="330" height="700" fill="#e6f7ff" stroke="#a8d1f8" stroke-width="2" rx="10" />
  <text x="195" y="110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#0055aa">Background Systems</text>

  <!-- Vector Database -->
  <rect x="50" y="130" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="160" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Vector Database</text>
  <text x="195" y="185" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Code embeddings for semantic search</text>

  <!-- Knowledge Graph -->
  <rect x="50" y="220" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="250" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Knowledge Graph</text>
  <text x="195" y="275" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Component relationships and dependencies</text>

  <!-- Project Dictionary -->
  <rect x="50" y="310" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="340" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Project Dictionary</text>
  <text x="195" y="365" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Project-specific terminology and patterns</text>

  <!-- Configuration Store -->
  <rect x="50" y="400" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="430" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Configuration Store</text>
  <text x="195" y="455" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">.naming-conventions, .code-architecture, etc.</text>

  <!-- Context History -->
  <rect x="50" y="490" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="520" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Context History</text>
  <text x="195" y="545" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Project evolution and decision records</text>

  <!-- Rule Repository -->
  <rect x="50" y="580" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="610" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Rule Repository</text>
  <text x="195" y="635" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Agent rules with checksums and references</text>

  <!-- Learning Database -->
  <rect x="50" y="670" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="700" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Learning Database</text>
  <text x="195" y="725" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Project and global learning patterns</text>

  <!-- Middleware (Center Section) -->
  <rect x="380" y="80" width="240" height="790" fill="#f0e6ff" stroke="#d0b8f0" stroke-width="2" rx="10" />
  <text x="500" y="110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#5500aa">Middleware</text>

  <!-- Context Provider -->
  <rect x="400" y="130" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="160" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Context Provider</text>
  <text x="500" y="185" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Supplies relevant context to agents</text>

  <!-- Task Classifier -->
  <rect x="400" y="220" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="250" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Task Classifier</text>
  <text x="500" y="275" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Determines task complexity and type</text>

  <!-- Resource Optimizer -->
  <rect x="400" y="310" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="340" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Resource Optimizer</text>
  <text x="500" y="365" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Selects optimal model for each task</text>

  <!-- Result Validator -->
  <rect x="400" y="400" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="430" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Result Validator</text>
  <text x="500" y="455" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Verifies output quality and integration</text>

  <!-- Execution Manager -->
  <rect x="400" y="490" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="520" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Execution Manager</text>
  <text x="500" y="545" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Handles file system and operations</text>

  <!-- Context Prefetcher -->
  <rect x="400" y="580" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="610" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Context Prefetcher</text>
  <text x="500" y="635" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Preloads relevant context for tasks</text>

  <!-- Agent State Monitor -->
  <rect x="400" y="670" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="700" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Agent State Monitor</text>
  <text x="500" y="725" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Tracks agent health and performance</text>

  <!-- Failure Recovery System -->
  <rect x="400" y="760" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="790" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Failure Recovery System</text>
  <text x="500" y="815" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Handles errors and ensures resilience</text>

  <!-- Agent Modes (Right Section) -->
  <rect x="640" y="80" width="330" height="790" fill="#fff0e6" stroke="#f8d1a8" stroke-width="2" rx="10" />
  <text x="805" y="110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#aa5500">Agent Modes</text>

  <!-- Micromanager -->
  <rect x="660" y="130" width="290" height="60" fill="#d3b0ff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="805" y="165" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Micromanager</text>

  <!-- Researcher & Architect -->
  <rect x="660" y="200" width="140" height="60" fill="#c4e0ff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="730" y="235" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Researcher</text>

  <rect x="810" y="200" width="140" height="60" fill="#c4e0ff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="880" y="235" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Architect</text>

  <!-- Implementation Modes -->
  <rect x="660" y="270" width="290" height="40" fill="#e6fff0" stroke="#a8f8d1" stroke-width="2" rx="5" />
  <text x="805" y="295" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Implementation Modes</text>

  <!-- Implementation Mode Types -->
  <rect x="660" y="320" width="70" height="160" fill="#ffffff" stroke="#4ea679" stroke-width="2" rx="5" />
  <text x="695" y="355" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#333">Intern</text>
  <text x="695" y="375" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">Boilerplate</text>

  <rect x="740" y="320" width="70" height="160" fill="#ffffff" stroke="#4ea679" stroke-width="2" rx="5" />
  <text x="775" y="355" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#333">Junior</text>
  <text x="775" y="375" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">Basic Logic</text>

  <rect x="820" y="320" width="70" height="160" fill="#ffffff" stroke="#4ea679" stroke-width="2" rx="5" />
  <text x="855" y="355" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#333">Mid Level</text>
  <text x="855" y="375" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">Moderate</text>

  <rect x="900" y="320" width="50" height="160" fill="#ffffff" stroke="#4ea679" stroke-width="2" rx="5" />
  <text x="925" y="355" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#333">Senior</text>
  <text x="925" y="375" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">Complex</text>

  <!-- Specialized Modes -->
  <rect x="660" y="490" width="140" height="60" fill="#ffffff" stroke="#df924e" stroke-width="2" rx="5" />
  <text x="730" y="520" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Designer</text>
  <text x="730" y="540" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">UI/UX Elements</text>

  <rect x="810" y="490" width="140" height="60" fill="#ffffff" stroke="#df924e" stroke-width="2" rx="5" />
  <text x="880" y="520" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Tester</text>
  <text x="880" y="540" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">QA & Validation</text>

  <!-- Cost Monitoring System -->
  <rect x="660" y="580" width="290" height="80" fill="#ffe6e6" stroke="#f8a8a8" stroke-width="2" rx="5" />
  <text x="805" y="610" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Cost Monitoring System</text>
  <text x="805" y="635" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Tracks and optimizes resource usage costs</text>

  <!-- Agent Health Indicators -->
  <rect x="660" y="670" width="290" height="80" fill="#e6ffe6" stroke="#a8f8a8" stroke-width="2" rx="5" />
  <text x="805" y="700" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Agent Health Dashboard</text>
  <text x="805" y="725" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Color-coded health status indicators</text>

  <!-- Continuous Learning System -->
  <rect x="660" y="760" width="290" height="80" fill="#e6f0ff" stroke="#a8c4f8" stroke-width="2" rx="5" />
  <text x="805" y="790" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Continuous Learning System</text>
  <text x="805" y="815" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Project and system-wide improvement</text>

  <!-- Connection Lines -->
  <!-- Background to Middleware -->
  <line x1="330" y1="170" x2="400" y2="170" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="260" x2="400" y2="260" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="350" x2="400" y2="350" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="440" x2="400" y2="440" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="530" x2="400" y2="530" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="620" x2="400" y2="620" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="710" x2="400" y2="710" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="710" x2="400" y2="800" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="710" x2="660" y2="800" stroke="#4e92df" stroke-width="2" />

  <!-- Middleware to Agents -->
  <line x1="600" y1="170" x2="660" y2="170" stroke="#9966cc" stroke-width="2" />
  <path d="M600,260 Q630,230 660,230" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,350 Q630,380 660,380" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,440 Q630,520 660,520" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,530 Q630,520 660,520" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,620 Q630,620 660,620" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,710 Q630,710 660,710" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,800 Q630,800 660,800" fill="none" stroke="#9966cc" stroke-width="2" />

  <!-- Micromanager to Other Modes -->
  <line x1="805" y1="190" x2="805" y2="200" stroke="#9966cc" stroke-width="2" />
  <path d="M805,260 Q805,270 805,270" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M805,310 Q805,320 805,320" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M805,480 Q805,490 805,490" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M805,550 Q805,580 805,580" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M805,660 Q805,670 805,670" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M805,750 Q805,760 805,760" fill="none" stroke="#9966cc" stroke-width="2" />

  <!-- Information Flow Arrows -->
  <polygon points="398,170 390,166 390,174" fill="#4e92df" />
  <polygon points="398,260 390,256 390,264" fill="#4e92df" />
  <polygon points="398,350 390,346 390,354" fill="#4e92df" />
  <polygon points="398,440 390,436 390,444" fill="#4e92df" />
  <polygon points="398<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 1000 800" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1000" height="800" fill="#f8f9fa" />

  <!-- Title -->
  <text x="500" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#333">Micromanager Architecture with Background Systems</text>

  <!-- Background Systems (Left Section) -->
  <rect x="30" y="80" width="330" height="600" fill="#e6f7ff" stroke="#a8d1f8" stroke-width="2" rx="10" />
  <text x="195" y="110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#0055aa">Background Systems</text>

  <!-- Vector Database -->
  <rect x="50" y="130" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="160" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Vector Database</text>
  <text x="195" y="185" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Code embeddings for semantic search</text>

  <!-- Knowledge Graph -->
  <rect x="50" y="220" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="250" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Knowledge Graph</text>
  <text x="195" y="275" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Component relationships and dependencies</text>

  <!-- Project Dictionary -->
  <rect x="50" y="310" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="340" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Project Dictionary</text>
  <text x="195" y="365" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Project-specific terminology and patterns</text>

  <!-- Configuration Store -->
  <rect x="50" y="400" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="430" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Configuration Store</text>
  <text x="195" y="455" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">.naming-conventions, .code-architecture, etc.</text>

  <!-- Context History -->
  <rect x="50" y="490" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="520" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Context History</text>
  <text x="195" y="545" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Project evolution and decision records</text>

  <!-- Rule Repository -->
  <rect x="50" y="580" width="290" height="80" fill="#ffffff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="195" y="610" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Rule Repository</text>
  <text x="195" y="635" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Agent rules with checksums and references</text>

  <!-- Middleware (Center Section) -->
  <rect x="380" y="80" width="240" height="690" fill="#f0e6ff" stroke="#d0b8f0" stroke-width="2" rx="10" />
  <text x="500" y="110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#5500aa">Middleware</text>

  <!-- Context Provider -->
  <rect x="400" y="130" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="160" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Context Provider</text>
  <text x="500" y="185" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Supplies relevant context to agents</text>

  <!-- Task Classifier -->
  <rect x="400" y="220" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="250" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Task Classifier</text>
  <text x="500" y="275" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Determines task complexity and type</text>

  <!-- Resource Optimizer -->
  <rect x="400" y="310" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="340" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Resource Optimizer</text>
  <text x="500" y="365" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Selects optimal model for each task</text>

  <!-- Result Validator -->
  <rect x="400" y="400" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="430" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Result Validator</text>
  <text x="500" y="455" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Verifies output quality and integration</text>

  <!-- Execution Manager -->
  <rect x="400" y="490" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="520" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Execution Manager</text>
  <text x="500" y="545" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Handles file system and operations</text>

  <!-- Context Prefetcher -->
  <rect x="400" y="580" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="610" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Context Prefetcher</text>
  <text x="500" y="635" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Preloads relevant context for tasks</text>

  <!-- Agent State Monitor -->
  <rect x="400" y="670" width="200" height="80" fill="#ffffff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="500" y="700" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Agent State Monitor</text>
  <text x="500" y="725" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Tracks agent health and performance</text>

  <!-- Agent Modes (Right Section) -->
  <rect x="640" y="80" width="330" height="690" fill="#fff0e6" stroke="#f8d1a8" stroke-width="2" rx="10" />
  <text x="805" y="110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#aa5500">Agent Modes</text>

  <!-- Micromanager -->
  <rect x="660" y="130" width="290" height="60" fill="#d3b0ff" stroke="#9966cc" stroke-width="2" rx="5" />
  <text x="805" y="165" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Micromanager</text>

  <!-- Researcher & Architect -->
  <rect x="660" y="200" width="140" height="60" fill="#c4e0ff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="730" y="235" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Researcher</text>

  <rect x="810" y="200" width="140" height="60" fill="#c4e0ff" stroke="#4e92df" stroke-width="2" rx="5" />
  <text x="880" y="235" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Architect</text>

  <!-- Implementation Modes -->
  <rect x="660" y="270" width="290" height="40" fill="#e6fff0" stroke="#a8f8d1" stroke-width="2" rx="5" />
  <text x="805" y="295" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Implementation Modes</text>

  <!-- Implementation Mode Types -->
  <rect x="660" y="320" width="70" height="160" fill="#ffffff" stroke="#4ea679" stroke-width="2" rx="5" />
  <text x="695" y="355" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#333">Intern</text>
  <text x="695" y="375" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">Boilerplate</text>

  <rect x="740" y="320" width="70" height="160" fill="#ffffff" stroke="#4ea679" stroke-width="2" rx="5" />
  <text x="775" y="355" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#333">Junior</text>
  <text x="775" y="375" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">Basic Logic</text>

  <rect x="820" y="320" width="70" height="160" fill="#ffffff" stroke="#4ea679" stroke-width="2" rx="5" />
  <text x="855" y="355" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#333">Mid Level</text>
  <text x="855" y="375" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">Moderate</text>

  <rect x="900" y="320" width="50" height="160" fill="#ffffff" stroke="#4ea679" stroke-width="2" rx="5" />
  <text x="925" y="355" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#333">Senior</text>
  <text x="925" y="375" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">Complex</text>

  <!-- Specialized Modes -->
  <rect x="660" y="490" width="140" height="60" fill="#ffffff" stroke="#df924e" stroke-width="2" rx="5" />
  <text x="730" y="520" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Designer</text>
  <text x="730" y="540" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">UI/UX Elements</text>

  <rect x="810" y="490" width="140" height="60" fill="#ffffff" stroke="#df924e" stroke-width="2" rx="5" />
  <text x="880" y="520" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">Tester</text>
  <text x="880" y="540" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#555">QA & Validation</text>

  <!-- Cost Monitoring System -->
  <rect x="660" y="580" width="290" height="80" fill="#ffe6e6" stroke="#f8a8a8" stroke-width="2" rx="5" />
  <text x="805" y="610" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Cost Monitoring System</text>
  <text x="805" y="635" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Tracks and optimizes resource usage costs</text>

  <!-- Agent Health Indicators -->
  <rect x="660" y="670" width="290" height="80" fill="#e6ffe6" stroke="#a8f8a8" stroke-width="2" rx="5" />
  <text x="805" y="700" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Agent Health Dashboard</text>
  <text x="805" y="725" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#555">Color-coded health status indicators</text>

  <!-- Connection Lines -->
  <!-- Background to Middleware -->
  <line x1="330" y1="170" x2="400" y2="170" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="260" x2="400" y2="260" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="350" x2="400" y2="350" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="440" x2="400" y2="440" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="530" x2="400" y2="530" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="620" x2="400" y2="620" stroke="#4e92df" stroke-width="2" />
  <line x1="330" y1="710" x2="400" y2="710" stroke="#4e92df" stroke-width="2" />

  <!-- Middleware to Agents -->
  <line x1="600" y1="170" x2="660" y2="170" stroke="#9966cc" stroke-width="2" />
  <path d="M600,260 Q630,230 660,230" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,350 Q630,380 660,380" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,440 Q630,520 660,520" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,530 Q630,520 660,520" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,620 Q630,620 660,620" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M600,710 Q630,710 660,710" fill="none" stroke="#9966cc" stroke-width="2" />

  <!-- Micromanager to Other Modes -->
  <line x1="805" y1="190" x2="805" y2="200" stroke="#9966cc" stroke-width="2" />
  <path d="M805,260 Q805,270 805,270" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M805,310 Q805,320 805,320" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M805,480 Q805,490 805,490" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M805,550 Q805,580 805,580" fill="none" stroke="#9966cc" stroke-width="2" />
  <path d="M805,660 Q805,670 805,670" fill="none" stroke="#9966cc" stroke-width="2" />

  <!-- Information Flow Arrows -->
  <polygon points="398,170 390,166 390,174" fill="#4e92df" />
  <polygon points="398,260 390,256 390,264" fill="#4e92df" />
  <polygon points="398,350 390,346 390,354" fill="#4e92df" />
  <polygon points="398,440 390,436 390,444" fill="#4e92df" />
  <polygon points="398,530 390,526 390,534" fill="#4e92df" />
  <polygon points="398,620 390,616 390,624" fill="#4e92df" />
  <polygon points="398,710 390,706 390,714" fill="#4e92df" />

  <polygon points="658,170 650,166 650,174" fill="#9966cc" />
  <polygon points="658,230 652,228 654,236" fill="#9966cc" />
  <polygon points="658,380 652,376 654,384" fill="#9966cc" />
  <polygon points="658,520 652,516 654,524" fill="#9966cc" />
  <polygon points="658,620 652,616 654,624" fill="#9966cc" />
  <polygon points="658,710 652,706 654,714" fill="#9966cc" />

  <!-- Data Flow Labels -->
  <text x="365" y="155" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#0055aa" transform="rotate(-10, 365, 155)">Query Results</text>
  <text x="365" y="245" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#0055aa" transform="rotate(-10, 365, 245)">Relationships</text>
  <text x="365" y="335" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#0055aa" transform="rotate(-10, 365, 335)">Terminology</text>
  <text x="365" y="425" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#0055aa" transform="rotate(-10, 365, 425)">Rules</text>
  <text x="365" y="515" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#0055aa" transform="rotate(-10, 365, 515)">History</text>
  <text x="365" y="605" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#0055aa" transform="rotate(-10, 365, 605)">Rule Sets</text>
  <text x="365" y="695" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#0055aa" transform="rotate(-10, 365, 695)">Health Data</text>

  <text x="630" y="155" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#5500aa" transform="rotate(10, 630, 155)">Context</text>
  <text x="630" y="245" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#5500aa" transform="rotate(25, 630, 245)">Task Routes</text>
  <text x="630" y="365" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#5500aa" transform="rotate(-25, 630, 365)">Resources</text>
  <text x="630" y="505" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#5500aa" transform="rotate(-40, 630, 505)">Commands</text>
  <text x="630" y="605" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#5500aa" transform="rotate(0, 630, 605)">Prefetched Context</text>
  <text x="630" y="695" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#5500aa" transform="rotate(0, 630, 695)">Health Status</text>

  <!-- Health Status Indicators -->
  <circle cx="675" y="710" r="8" fill="#a8f8a8" />
  <text x="675" y="730" font-family="Arial, sans-serif" font-size="8" text-anchor="middle" fill="#333">GREEN</text>

  <circle cx="705" y="710" r="8" fill="#f8f8a8" />
  <text x="705" y="730" font-family="Arial, sans-serif" font-size="8" text-anchor="middle" fill="#333">YELLOW</text>

  <circle cx="735" y="710" r="8" fill="#f8a8a8" />
  <text x="735" y="730" font-family="Arial, sans-serif" font-size="8" text-anchor="middle" fill="#333">RED</text>

  <!-- Legend -->
  <rect x="350" y="780" width="300" height="40" fill="#ffffff" stroke="#999999" stroke-width="1" rx="5" />
  <circle cx="380" cy="800" r="8" fill="#e6f7ff" stroke="#a8d1f8" stroke-width="1" />
  <text x="405" y="805" font-family="Arial, sans-serif" font-size="12" fill="#333">Knowledge Store</text>

  <circle cx="480" cy="800" r="8" fill="#f0e6ff" stroke="#d0b8f0" stroke-width="1" />
  <text x="505" y="805" font-family="Arial, sans-serif" font-size="12" fill="#333">Processing</text>

  <circle cx="570" cy="800" r="8" fill="#fff0e6" stroke="#f8d1a8" stroke-width="1" />
  <text x="595" y="805" font-family="Arial, sans-serif" font-size="12" fill="#333">Agent Modes</text>
</svg>
