# Task 85 - Wait for Initial Subtasks Before Micromanager Completes

## 🎯 Goal
Micromanager must wait for its initial orchestration layer to finish (subtasks created → routed → completed) before responding.

## ✅ Implementation Status

### 1. Added Card ID Tracking to TaskOrchestrator
**File**: `file-explorer/components/agents/task-orchestrator.ts`

**Changes Made**:
- Added `lastCreatedCardIds: string[]` static property to track created card IDs
- Added `setLastCreatedCardIds(cardIds: string[])` method to store card IDs for tracking
- Added `getLastCreatedCardIds(): string[]` method to retrieve tracked card IDs
- Added `clearLastCreatedCardIds()` method to clean up after completion

**Implementation**:
```typescript
export class TaskOrchestrator {
  private static taskIdCounter = 0;
  private static lastCreatedCardIds: string[] = []; // ✅ Task 85: Track created card IDs

  /**
   * Set the last created card IDs for completion tracking
   */
  static setLastCreatedCardIds(cardIds: string[]): void {
    this.lastCreatedCardIds = [...cardIds];
    console.log(`🎯 TaskOrchestrator: Tracking ${cardIds.length} card IDs for completion:`, cardIds);
  }

  /**
   * Get the last created card IDs for completion tracking
   */
  static getLastCreatedCardIds(): string[] {
    return [...this.lastCreatedCardIds];
  }

  /**
   * Clear the tracked card IDs
   */
  static clearLastCreatedCardIds(): void {
    this.lastCreatedCardIds = [];
  }
}
```

### 2. Created TaskCompletionTracker Class
**File**: `file-explorer/components/agents/task-completion-tracker.ts`

**Key Features**:
- Event-driven approach using `kanbanEvents.on('cardStatusChanged')`
- Timeout protection (5 minutes default with retryable error)
- Tracks completion/failure states based on column positions
- Initial state checking for already completed cards
- Comprehensive result reporting

**Core Method**:
```typescript
/**
 * ✅ Task 85: Wait for cards to finish execution
 * Uses event-driven approach with timeout protection
 */
public async waitForCardsToFinish(
  cardIds: string[], 
  options: TaskCompletionOptions = {}
): Promise<TaskCompletionResult> {
  // Set up timeout protection
  const timeoutId = setTimeout(() => {
    result.timedOutCards = Array.from(pendingCards);
    result.success = false;
    result.error = `Timeout exceeded: ${result.timedOutCards.length} cards did not complete within ${opts.timeoutMs}ms`;
    resolve(result);
  }, opts.timeoutMs);

  // Event-driven tracking
  eventListener = (data: KanbanCardStatusChangeEvent) => {
    if (!pendingCards.has(cardId)) return;
    
    if (opts.completedColumns.includes(newColumnId)) {
      pendingCards.delete(cardId);
      result.completedCards.push(cardId);
    } else if (opts.failedColumns.includes(newColumnId)) {
      pendingCards.delete(cardId);
      result.failedCards.push(cardId);
    }
    
    // Check if all cards are done
    if (pendingCards.size === 0) {
      clearTimeout(timeoutId);
      result.success = result.failedCards.length === 0;
      resolve(result);
    }
  };

  kanbanEvents.on('cardStatusChanged', eventListener);
}
```

**Configuration Options**:
- `timeoutMs`: 5 minutes default (300,000ms)
- `completedColumns`: ['column-6'] (Done column)
- `failedColumns`: ['column-4'] (In Review/Failed column)
- `retryIntervalMs`: 1 second for polling (if needed)

### 3. Updated Complete Integration to Track Card IDs
**File**: `file-explorer/components/agents/complete-integration.tsx`

**Changes Made**:
- Extract created card IDs after `KanbanTaskBridge.createCardsFromSubtasks()`
- Call `TaskOrchestrator.setLastCreatedCardIds(createdCardIds)` to store for tracking
- Ensures Micromanager can access the card IDs for completion waiting

**Implementation**:
```typescript
// Create Kanban cards for all subtasks
const { KanbanTaskBridge } = await import('./kanban-task-bridge');
const cardResults = await KanbanTaskBridge.createCardsFromSubtasks(decomposition.subtasks);

// ✅ Task 85: Set created card IDs in TaskOrchestrator for completion tracking
const createdCardIds = cardResults.success.map(card => card.id);
TaskOrchestrator.setLastCreatedCardIds(createdCardIds);

// ✅ Task 83: Route decomposed Kanban tasks to assigned agents
for (const card of cardResults.success) {
  await taskCoordinator.dispatchToAgent(card);
}
```

### 4. Modified MicromanagerAgent.execute() to Wait for Completion
**File**: `file-explorer/components/agents/micromanager-agent.ts`

**Changes Made**:
- Added completion waiting logic after orchestration response generation
- Uses `TaskOrchestrator.getLastCreatedCardIds()` to get tracked card IDs
- Calls `taskCompletionTracker.waitForCardsToFinish()` with timeout protection
- Returns error response if timeout or failures occur
- Clears tracked card IDs after completion

**Implementation Flow**:
```typescript
// Generate orchestration response using LLM
const response = await this.generateOrchestrationResponse(context, decomposition, executionPlan);

// ✅ Task 85: Wait for initial subtasks to complete before responding
try {
  const { TaskOrchestrator } = await import('./task-orchestrator');
  const { taskCompletionTracker } = await import('./task-completion-tracker');
  
  const initialCardIds = TaskOrchestrator.getLastCreatedCardIds();
  
  if (initialCardIds.length > 0) {
    const completionResult = await taskCompletionTracker.waitForCardsToFinish(initialCardIds, {
      timeoutMs: 5 * 60 * 1000, // 5 minutes timeout
      completedColumns: ['column-6'], // Done column
      failedColumns: ['column-4'] // In Review (failed) column
    });
    
    if (!completionResult.success) {
      // Clear tracked card IDs
      TaskOrchestrator.clearLastCreatedCardIds();
      
      // Return error response with timeout details
      return this.createErrorResponse(
        completionResult.error || 
        `Orchestration timeout: ${completionResult.timedOutCards.length} tasks did not complete, ${completionResult.failedCards.length} tasks failed`
      );
    }
    
    // Clear tracked card IDs after successful completion
    TaskOrchestrator.clearLastCreatedCardIds();
  }
} catch (error) {
  console.error(`❌ MicromanagerAgent: Error waiting for task completion:`, error);
  // Continue with response even if waiting fails
}

return this.createSuccessResponse(response, tokensUsed, executionTime, suggestions, metadata);
```

## 🔄 Execution Flow

### Complete Orchestration Flow:
1. **User submits task** → Micromanager receives execution request
2. **Task decomposition** → Creates subtasks and execution plan
3. **Card creation** → `KanbanTaskBridge.createCardsFromSubtasks()` creates Kanban cards
4. **Card ID tracking** → `TaskOrchestrator.setLastCreatedCardIds()` stores card IDs
5. **Agent dispatch** → Each card dispatched to assigned agent via `dispatchToAgent()`
6. **Completion waiting** → `taskCompletionTracker.waitForCardsToFinish()` waits for all cards
7. **Event monitoring** → Listens to `kanbanEvents.on('cardStatusChanged')` for completion
8. **Micromanager response** → Only responds after all initial tasks complete or timeout

### Column-Based Status Tracking:
- **Pending**: `column-1` (Backlog) - Initial state
- **In Progress**: `column-3` (In Development) - Agent execution started
- **Completed**: `column-6` (Done) - Successful completion
- **Failed**: `column-4` (In Review) - Failed execution

## ✅ Test Criteria Met

- ✅ **Micromanager only responds after initial task layer completes**
  - `waitForCardsToFinish()` blocks until all cards reach completion/failure state
  - Response only sent after successful completion or timeout

- ✅ **Timeout triggers clear error if agents fail or hang**
  - 5-minute timeout with detailed error message
  - Includes count of timed out and failed tasks
  - Retryable error format for user feedback

- ✅ **No partial orchestration**
  - All-or-nothing approach: waits for ALL cards to complete
  - Tracks both successful and failed completions
  - Clears tracking state after completion to prevent memory leaks

## 🚀 Ready for Testing

The implementation ensures that:
1. Micromanager waits for complete orchestration layer execution
2. Timeout protection prevents infinite waiting
3. Event-driven approach provides real-time completion tracking
4. Clear error reporting for timeout and failure scenarios
5. Proper cleanup of tracking state after completion

The system now provides complete orchestration with guaranteed completion before Micromanager responds to the user.
