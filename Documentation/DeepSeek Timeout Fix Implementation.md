# DeepSeek Timeout Fix Implementation

## 🎯 Problem
DeepSeek LLM requests were timing out after 30 seconds, causing failures:
```
Error: Error invoking remote method 'llm:callLLM': Error: deepseek deepseek-chat completion timed out after 30000ms
```

## ✅ Root Cause Analysis
The issue was that all LLM providers were using a hardcoded 30-second timeout, but DeepSeek can be slower and requires longer timeouts. The micromanager had provider-specific timeout logic (60 seconds for DeepSeek), but this wasn't being applied consistently across all LLM service calls.

## 🔧 Solution Implemented

### 1. Added Provider-Specific Timeout Logic to LLMRequestService
**File**: `file-explorer/components/agents/llm-request-service.ts`

**New Method Added**:
```typescript
/**
 * ✅ Get appropriate timeout for different LLM providers
 */
private getTimeoutForProvider(provider: LLMProvider): number {
  const baseTimeout = 30000; // 30 seconds default

  switch (provider) {
    case 'deepseek':
      return 60000; // 60 seconds for DeepSeek (can be slower)
    case 'anthropic':
      return 45000; // 45 seconds for <PERSON> (generally reliable)
    case 'openai':
      return 30000; // 30 seconds for OpenAI (usually fast)
    case 'openrouter':
      return 45000; // 45 seconds for OpenRouter (varies by model)
    case 'google':
      return 40000; // 40 seconds for Google AI
    case 'fireworks':
      return 35000; // 35 seconds for Fireworks
    case 'azure':
      return 35000; // 35 seconds for Azure OpenAI
    default:
      return baseTimeout;
  }
}
```

### 2. Updated callLLM Method to Use Provider-Specific Timeouts
**Changes Made**:
```typescript
// ✅ Use provider-specific timeout if not explicitly provided
const effectiveTimeout = timeoutMs || this.getTimeoutForProvider(agent.provider);

// Use the effective timeout in the call
console.log(`🕒 Using ${effectiveTimeout}ms timeout for ${agent.provider}/${modelId}`);
const response = await window.electronAPI.llm.callLLM(agent.provider, request, apiKey, effectiveTimeout);
```

### 3. Updated callLLMStream Method for Streaming Requests
**Changes Made**:
```typescript
// ✅ Use provider-specific timeout for streaming
const streamTimeout = this.getTimeoutForProvider(agent.provider);

console.log(`🕒 Using ${streamTimeout}ms timeout for streaming ${agent.provider}/${modelId}`);
return await window.electronAPI.llm.callLLMStream(agent.provider, request, apiKey, recordingCallback, streamTimeout);
```

### 4. Updated Electron IPC Handler for Streaming
**File**: `file-explorer/electron/services/llm-service.ts`

**Changes Made**:
```typescript
// Accept timeout parameter in streaming handler
ipcMain.handle('llm:callLLMStream', async (event, provider: LLMProvider, request: LLMRequest, apiKey: string, onChunk: (chunk: any) => void, timeoutMs?: number) => {
  const timeout = timeoutMs || 30000; // Use provided timeout or default to 30s

  console.log(`🚀 [IPC Stream] Starting streaming LLM call to ${provider}/${request.model} with ${timeout}ms timeout`);
  // ... rest of implementation
});
```

## 📊 Timeout Configuration by Provider

| Provider | Timeout | Reason |
|----------|---------|---------|
| **DeepSeek** | **60 seconds** | **Can be slower, needs extra time** |
| Anthropic | 45 seconds | Generally reliable but can vary |
| OpenRouter | 45 seconds | Varies by underlying model |
| Google AI | 40 seconds | Moderate response times |
| Fireworks | 35 seconds | Usually fast but can vary |
| Azure OpenAI | 35 seconds | Similar to OpenAI but with Azure latency |
| OpenAI | 30 seconds | Usually fast and reliable |

## 🔍 Debugging Features Added

### Console Logging
- **Regular LLM calls**: `🕒 Using 60000ms timeout for deepseek/deepseek-chat`
- **Streaming calls**: `🕒 Using 60000ms timeout for streaming deepseek/deepseek-chat`
- **Electron IPC**: `🚀 [IPC Stream] Starting streaming LLM call to deepseek/deepseek-chat with 60000ms timeout`

### Transparent Timeout Usage
- All timeout values are logged for debugging
- Provider-specific timeouts are applied automatically
- Explicit timeout parameters still override defaults when provided

## ✅ Expected Results

### Before Fix
```
❌ LLM request failed for deepseek: Error: deepseek deepseek-chat completion timed out after 30000ms
```

### After Fix
```
🕒 Using 60000ms timeout for deepseek/deepseek-chat
✅ DeepSeek request completed successfully within 60-second timeout
```

## 🚀 Production Benefits

1. **DeepSeek Reliability**: 60-second timeout prevents premature failures
2. **Provider Optimization**: Each provider gets appropriate timeout based on typical response times
3. **Debugging Transparency**: Clear logging shows which timeouts are being used
4. **Backward Compatibility**: Explicit timeout parameters still work as before
5. **Consistent Application**: Same timeout logic used for both regular and streaming calls

## 🔧 User Guidelines Compliance

- ✅ **Non-destructive**: Preserves existing functionality while fixing the timeout issue
- ✅ **Production-ready**: No mock or placeholder implementations
- ✅ **Fail-fast**: Clear error messages and proper timeout handling
- ✅ **Transparent**: Comprehensive logging for debugging and monitoring
- ✅ **Pragmatic**: Focused fix that addresses the specific DeepSeek timeout problem

The fix ensures DeepSeek and other slower providers get appropriate timeouts while maintaining fast response times for providers that support them.

## 🔧 Additional Fix: Task Submission Timeout

### 5. Updated Task Submission Timeout in Complete Integration
**File**: `file-explorer/components/agents/complete-integration.tsx`

**Problem**: Task submission was using the default 30-second timeout for all agents, including micromanager tasks that need LLM orchestration.

**Solution**: Extended timeout specifically for micromanager tasks:
```typescript
// ✅ Use extended timeout for micromanager tasks (LLM orchestration needs more time)
const taskTimeout = sharedState.selectedAgent === 'micromanager' ? 90000 : defaultTimeout; // 90s for micromanager, default for others

// ✅ Wrap task submission with appropriate timeout
return await withCustomTimeout(
  taskSubmissionLogic,
  taskTimeout,
  `Task submission for ${sharedState.selectedAgent}`
);
```

**Benefits**:
- **Micromanager Tasks**: 90-second timeout for LLM orchestration
- **Other Agents**: Standard timeout for faster response
- **Transparent Logging**: Shows which timeout is being used
- **Fallback Support**: Works even when settings context unavailable

## ✅ Complete Timeout Chain Fixed

The timeout issue was occurring at multiple levels:

1. **LLM Provider Level**: ✅ Fixed with provider-specific timeouts (60s for DeepSeek)
2. **LLM Service Level**: ✅ Fixed with automatic timeout selection
3. **Agent Execution Level**: ✅ Fixed with extended timeouts for complex tasks
4. **Task Submission Level**: ✅ Fixed with micromanager-specific timeout (90s)

## 🔍 Expected Results After Fix

### Before Fix
```
❌ Error: Task submission for micromanager timed out after 30000ms
❌ Error: LLM orchestration failed, falling back to structured template: {}
❌ Error: deepseek deepseek-chat completion timed out after 30000ms
```

### After Fix
```
🕐 Starting task submission with timeout: 90000ms for micromanager
🕒 Using 60000ms timeout for deepseek/deepseek-chat
✅ DeepSeek LLM orchestration completed successfully
✅ Micromanager task submission completed within 90-second timeout
```

The comprehensive timeout fix addresses the entire execution chain and should resolve the DeepSeek timeout errors completely.
