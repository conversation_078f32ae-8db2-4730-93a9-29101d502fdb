graph TD
    %% Main Application Components
    APP[Electron Application]
    MAIN[Main Process]
    RENDERER[Renderer Process]

    %% Core Components
    EDITOR[Editor Component]
    AGENT[Agent System]
    MIDDLEWARE[Middleware]
    BACKGROUND[Background Systems]

    %% Main Process Components
    APP_MGR[Application Manager]
    WIN_MGR[Window Manager]
    IPC_MGR[IPC Manager]
    UPDATE[Update System]
    MENU[Menu System]

    %% Editor Components
    <PERSON>ONAC<PERSON>[Monaco Editor]
    FILE_SYS[File System]
    TERM[Terminal]
    GIT[Git Integration]
    EXPLORER[Project Explorer]

    %% Agent System Components
    AGENT_BASE[Agent Base Class]
    AGENT_MGR[Agent Manager]
    PROMPT_SEC[Prompt Security]
    AGENT_COMM[Agent Communication]

    %% Agent Types
    MICRO[Micromanager]
    IMPL[Implementation Agents]
    SPEC[Specialized Agents]
    ERR_RES[Error Resolution]

    %% Implementation Agents
    INTERN[Intern]
    JUNIOR[Junior]
    MID[Mid Level]
    SENIOR[Senior]

    %% Specialized Agents
    RESEARCHER[Researcher]
    ARCHITECT[Architect]
    DESIGNER[Designer]
    TESTER[Tester]

    %% Middleware Components
    TASK_CLASS[Task Classifier]
    RES_OPT[Resource Optimizer]
    CTX_PROV[Context Provider]
    EXEC_MGR[Execution Manager]
    AGENT_MON[Agent State Monitor]

    %% Background Systems
    DB_MGR[Database Manager]
    VECTOR_DB[Vector Storage]
    KNOW_GRAPH[Knowledge Graph]
    PROJ_DICT[Project Dictionary]
    RULE_REPO[Rule Repository]
    CTX_HIST[Context History]
    LEARN_SYS[Learning System]

    %% LLM Integration
    LLM[LLM Integration]
    PROV_ABS[Provider Abstraction]
    CRED_MGR[Credentials Manager]
    MODEL_MGR[Model Manager]

    %% UI Components
    UI[UI Components]
    CMD_PAL[Command Palette]
    STATUS[Status Bar]
    NOTIF[Notifications]
    DIALOGS[Dialogs]

    %% Hierarchy
    APP --> MAIN
    APP --> RENDERER

    MAIN --> APP_MGR
    MAIN --> WIN_MGR
    MAIN --> IPC_MGR
    MAIN --> UPDATE
    MAIN --> MENU

    RENDERER --> EDITOR
    RENDERER --> AGENT
    RENDERER --> MIDDLEWARE
    RENDERER --> BACKGROUND
    RENDERER --> UI

    EDITOR --> MONACO
    EDITOR --> FILE_SYS
    EDITOR --> TERM
    EDITOR --> GIT
    EDITOR --> EXPLORER

    AGENT --> AGENT_BASE
    AGENT --> AGENT_MGR
    AGENT --> PROMPT_SEC
    AGENT --> AGENT_COMM

    AGENT_MGR --> MICRO
    AGENT_MGR --> IMPL
    AGENT_MGR --> SPEC
    AGENT_MGR --> ERR_RES

    IMPL --> INTERN
    IMPL --> JUNIOR
    IMPL --> MID
    IMPL --> SENIOR

    SPEC --> RESEARCHER
    SPEC --> ARCHITECT
    SPEC --> DESIGNER
    SPEC --> TESTER

    MIDDLEWARE --> TASK_CLASS
    MIDDLEWARE --> RES_OPT
    MIDDLEWARE --> CTX_PROV
    MIDDLEWARE --> EXEC_MGR
    MIDDLEWARE --> AGENT_MON

    BACKGROUND --> DB_MGR
    BACKGROUND --> VECTOR_DB
    BACKGROUND --> KNOW_GRAPH
    BACKGROUND --> PROJ_DICT
    BACKGROUND --> RULE_REPO
    BACKGROUND --> CTX_HIST
    BACKGROUND --> LEARN_SYS

    LLM --> PROV_ABS
    LLM --> CRED_MGR
    LLM --> MODEL_MGR

    RENDERER --> LLM

    UI --> CMD_PAL
    UI --> STATUS
    UI --> NOTIF
    UI --> DIALOGS

    %% Special Relationships
    AGENT_BASE -.-> LLM
    CTX_PROV -.-> VECTOR_DB
    CTX_PROV -.-> KNOW_GRAPH
    EXEC_MGR -.-> FILE_SYS
    EXEC_MGR -.-> TERM
    MICRO -.-> TASK_CLASS
    ERR_RES -.-> LEARN_SYS
