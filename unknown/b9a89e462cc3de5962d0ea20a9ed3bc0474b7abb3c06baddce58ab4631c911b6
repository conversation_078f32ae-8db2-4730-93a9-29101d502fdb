// types/agent-history.ts

export interface AgentAction {
  id: string
  type: 'file_write' | 'file_read' | 'file_delete' | 'command_run' | 'api_call' | 'validation' | 'delegation'
  description: string
  target?: string // file path, command, API endpoint
  result: 'success' | 'error' | 'warning'
  details?: Record<string, any>
  timestamp: number
}

export interface AgentHistoryEntry {
  id: string
  agentId: string
  agentType: string
  agentName: string
  taskId: string
  taskDescription: string
  timestamp: number
  startTime: number
  endTime?: number
  duration?: number
  status: 'success' | 'error' | 'cancelled' | 'in_progress'
  resultSummary: string
  tokensUsed?: number
  cost?: number
  messages: AgentHistoryMessage[]
  actionsTaken: AgentAction[]
  metadata?: Record<string, any>
  parentTaskId?: string // For subtasks
  childTaskIds?: string[] // For decomposed tasks
}

export interface AgentHistoryMessage {
  id: string
  role: 'user' | 'agent' | 'system'
  content: string
  timestamp: number
  agentId?: string
  agentType?: string
  metadata?: Record<string, any>
}

export interface AgentHistoryFilter {
  agentTypes?: string[]
  status?: ('success' | 'error' | 'cancelled' | 'in_progress')[]
  dateRange?: {
    start: number
    end: number
  }
  searchQuery?: string
  taskTypes?: string[]
  minDuration?: number
  maxDuration?: number
  minTokens?: number
  maxTokens?: number
}

export interface AgentHistoryStats {
  totalTasks: number
  successfulTasks: number
  failedTasks: number
  cancelledTasks: number
  totalTokensUsed: number
  totalCost: number
  averageDuration: number
  mostActiveAgent: string
  tasksByAgent: Record<string, number>
  tasksByType: Record<string, number>
  tasksByStatus: Record<string, number>
  recentActivity: AgentHistoryEntry[]
}

export interface AgentHistorySession {
  id: string
  startTime: number
  endTime?: number
  entries: AgentHistoryEntry[]
  totalTasks: number
  successRate: number
  totalTokensUsed: number
  totalCost: number
}

export interface AgentHistoryExport {
  exportId: string
  timestamp: number
  format: 'json' | 'csv' | 'markdown'
  filter?: AgentHistoryFilter
  entries: AgentHistoryEntry[]
  stats: AgentHistoryStats
}

// Task type categorization
export const TASK_TYPES = {
  'code_generation': 'Code Generation',
  'code_modification': 'Code Modification',
  'code_review': 'Code Review',
  'debugging': 'Debugging',
  'testing': 'Testing',
  'documentation': 'Documentation',
  'research': 'Research',
  'architecture': 'Architecture',
  'design': 'UI/UX Design',
  'orchestration': 'Task Orchestration',
  'validation': 'Validation',
  'optimization': 'Optimization',
  'refactoring': 'Refactoring',
  'deployment': 'Deployment',
  'general': 'General Task'
} as const

// Agent type display names
export const AGENT_TYPE_NAMES = {
  'micromanager': 'Micromanager',
  'intern': 'Intern Developer',
  'junior': 'Junior Developer',
  'midlevel': 'Mid-level Developer',
  'senior': 'Senior Developer',
  'architect': 'System Architect',
  'designer': 'UI/UX Designer',
  'tester': 'QA Tester',
  'researcher': 'Research Specialist'
} as const

// Status display configuration
export const STATUS_CONFIG = {
  success: {
    label: 'Success',
    icon: '✅',
    color: 'text-green-600',
    bgColor: 'bg-green-50 dark:bg-green-950',
    borderColor: 'border-green-200 dark:border-green-800'
  },
  error: {
    label: 'Error',
    icon: '❌',
    color: 'text-red-600',
    bgColor: 'bg-red-50 dark:bg-red-950',
    borderColor: 'border-red-200 dark:border-red-800'
  },
  cancelled: {
    label: 'Cancelled',
    icon: '⚠️',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 dark:bg-yellow-950',
    borderColor: 'border-yellow-200 dark:border-yellow-800'
  },
  in_progress: {
    label: 'In Progress',
    icon: '🔄',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-950',
    borderColor: 'border-blue-200 dark:border-blue-800'
  }
} as const

// Action type configuration
export const ACTION_TYPE_CONFIG = {
  file_write: {
    label: 'File Write',
    icon: '📝',
    color: 'text-blue-600'
  },
  file_read: {
    label: 'File Read',
    icon: '👁️',
    color: 'text-gray-600'
  },
  file_delete: {
    label: 'File Delete',
    icon: '🗑️',
    color: 'text-red-600'
  },
  command_run: {
    label: 'Command',
    icon: '💻',
    color: 'text-purple-600'
  },
  api_call: {
    label: 'API Call',
    icon: '🌐',
    color: 'text-green-600'
  },
  validation: {
    label: 'Validation',
    icon: '🔍',
    color: 'text-orange-600'
  },
  delegation: {
    label: 'Delegation',
    icon: '🔄',
    color: 'text-indigo-600'
  }
} as const
