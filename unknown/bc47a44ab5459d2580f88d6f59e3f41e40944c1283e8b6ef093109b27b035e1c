// hooks/useAgentPresence.ts

import { useState, useEffect, useCallback } from "react"
import { getAgentEventsService } from "@/services/agent-events"
import type {
  LiveAgentState,
  AgentActivityEvent,
  TaskProgressUpdate,
  AgentPresenceUpdate
} from "@/types/agent-events"

export interface UseAgentPresenceReturn {
  // Current state
  agents: LiveAgentState[]
  activeTaskCount: number
  recentActivity: AgentActivityEvent[]
  
  // Individual agent queries
  getAgentStatus: (agentId: string) => LiveAgentState | null
  getAgentActivity: (agentId: string) => AgentActivityEvent[]
  
  // Active agents
  getActiveAgents: () => LiveAgentState[]
  getIdleAgents: () => LiveAgentState[]
  
  // Current task info
  getCurrentTasks: () => Array<{
    agentId: string
    agentName: string
    taskId: string
    taskDescription: string
    progress: number
    startedAt: number
  }>
  
  // Utility
  isAgentWorking: (agentId: string) => boolean
  getWorkingSummary: () => string
}

export function useAgentPresence(): UseAgentPresenceReturn {
  const [agents, setAgents] = useState<LiveAgentState[]>([])
  const [activeTaskCount, setActiveTaskCount] = useState(0)
  const [recentActivity, setRecentActivity] = useState<AgentActivityEvent[]>([])
  const [eventsService] = useState(() => getAgentEventsService())

  // Initialize and subscribe to updates
  useEffect(() => {
    // Load initial state
    const initialAgents = eventsService.getAllAgentPresence()
    const initialTaskCount = eventsService.getActiveTaskCount()
    const initialActivity = eventsService.getRecentActivity(20)
    
    setAgents(initialAgents)
    setActiveTaskCount(initialTaskCount)
    setRecentActivity(initialActivity)

    // Subscribe to presence updates
    const unsubscribePresence = eventsService.onPresenceUpdate((update: AgentPresenceUpdate) => {
      setAgents(prev => {
        const updated = [...prev]
        const index = updated.findIndex(agent => agent.agentId === update.agentId)
        
        if (index >= 0) {
          updated[index] = {
            ...updated[index],
            status: update.status,
            lastActivity: update.timestamp,
            currentTask: update.taskId && update.taskDescription ? {
              id: update.taskId,
              description: update.taskDescription,
              progress: update.progress || 0,
              startedAt: update.timestamp,
              estimatedCompletion: update.metadata?.estimatedCompletion
            } : (update.status === 'idle' || update.status === 'done') ? undefined : updated[index].currentTask
          }
        }
        
        return updated
      })
      
      // Update active task count
      setActiveTaskCount(eventsService.getActiveTaskCount())
    })

    // Subscribe to task progress updates
    const unsubscribeProgress = eventsService.onTaskProgress((update: TaskProgressUpdate) => {
      setAgents(prev => {
        const updated = [...prev]
        const index = updated.findIndex(agent => agent.agentId === update.agentId)
        
        if (index >= 0 && updated[index].currentTask?.id === update.taskId) {
          updated[index] = {
            ...updated[index],
            currentTask: {
              ...updated[index].currentTask!,
              progress: update.progress,
              estimatedCompletion: update.estimatedCompletion
            }
          }
        }
        
        return updated
      })
    })

    // Subscribe to activity events
    const unsubscribeActivity = eventsService.onAgentActivity((event: AgentActivityEvent) => {
      setRecentActivity(prev => {
        const updated = [event, ...prev.slice(0, 19)] // Keep last 20 events
        return updated
      })
    })

    return () => {
      unsubscribePresence()
      unsubscribeProgress()
      unsubscribeActivity()
    }
  }, [eventsService])

  // Agent query functions
  const getAgentStatus = useCallback((agentId: string): LiveAgentState | null => {
    return agents.find(agent => agent.agentId === agentId) || null
  }, [agents])

  const getAgentActivity = useCallback((agentId: string): AgentActivityEvent[] => {
    return eventsService.getAgentActivity(agentId)
  }, [eventsService])

  const getActiveAgents = useCallback((): LiveAgentState[] => {
    return agents.filter(agent => 
      agent.status === 'working' || agent.status === 'thinking'
    )
  }, [agents])

  const getIdleAgents = useCallback((): LiveAgentState[] => {
    return agents.filter(agent => agent.status === 'idle')
  }, [agents])

  const getCurrentTasks = useCallback(() => {
    return agents
      .filter(agent => agent.currentTask)
      .map(agent => ({
        agentId: agent.agentId,
        agentName: agent.agentName,
        taskId: agent.currentTask!.id,
        taskDescription: agent.currentTask!.description,
        progress: agent.currentTask!.progress,
        startedAt: agent.currentTask!.startedAt
      }))
  }, [agents])

  const isAgentWorking = useCallback((agentId: string): boolean => {
    const agent = getAgentStatus(agentId)
    return agent ? (agent.status === 'working' || agent.status === 'thinking') : false
  }, [getAgentStatus])

  const getWorkingSummary = useCallback((): string => {
    const activeAgents = getActiveAgents()
    
    if (activeAgents.length === 0) {
      return "All agents are idle"
    }
    
    if (activeAgents.length === 1) {
      const agent = activeAgents[0]
      const statusText = agent.status === 'thinking' ? 'thinking' : 'working'
      return `${agent.agentName} is ${statusText}`
    }
    
    return `${activeAgents.length} agents are active`
  }, [getActiveAgents])

  return {
    // Current state
    agents,
    activeTaskCount,
    recentActivity,
    
    // Individual agent queries
    getAgentStatus,
    getAgentActivity,
    
    // Active agents
    getActiveAgents,
    getIdleAgents,
    
    // Current task info
    getCurrentTasks,
    
    // Utility
    isAgentWorking,
    getWorkingSummary
  }
}
