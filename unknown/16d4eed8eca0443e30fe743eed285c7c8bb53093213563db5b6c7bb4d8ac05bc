// services/agent-events.ts

import type {
  AgentPresenceUpdate,
  TaskProgressUpdate,
  AgentActivityEvent,
  LiveAgentState,
  AgentPresenceState,
  AgentEventEmitter,
  PresenceUpdateListener,
  TaskProgressListener,
  AgentEventListener
} from "@/types/agent-events"

export class AgentEventsService implements AgentEventEmitter {
  private presenceListeners: Set<PresenceUpdateListener> = new Set()
  private taskProgressListeners: Set<TaskProgressListener> = new Set()
  private activityListeners: Set<AgentEventListener> = new Set()
  
  private presenceState: AgentPresenceState = {
    agents: new Map(),
    activeTaskCount: 0,
    lastUpdate: Date.now()
  }

  private eventHistory: AgentActivityEvent[] = []
  private maxHistorySize = 100

  constructor() {
    this.initializeDefaultAgents()
  }

  private initializeDefaultAgents(): void {
    const defaultAgents = [
      { id: 'micromanager', type: 'micromanager', name: 'Micromanager' },
      { id: 'intern', type: 'intern', name: 'Intern Developer' },
      { id: 'junior', type: 'junior', name: 'Junior Developer' },
      { id: 'midlevel', type: 'midlevel', name: 'Mid-level Developer' },
      { id: 'senior', type: 'senior', name: 'Senior Developer' },
      { id: 'architect', type: 'architect', name: 'System Architect' },
      { id: 'designer', type: 'designer', name: 'UI/UX Designer' },
      { id: 'tester', type: 'tester', name: 'QA Tester' },
      { id: 'researcher', type: 'researcher', name: 'Research Specialist' }
    ]

    defaultAgents.forEach(agent => {
      this.presenceState.agents.set(agent.id, {
        agentId: agent.id,
        agentType: agent.type,
        agentName: agent.name,
        status: 'idle',
        lastActivity: Date.now(),
        isOnline: true
      })
    })
  }

  // Event emission methods
  emitPresenceUpdate = (update: AgentPresenceUpdate): void => {
    // Update internal state
    const agent = this.presenceState.agents.get(update.agentId)
    if (agent) {
      agent.status = update.status
      agent.lastActivity = update.timestamp

      if (update.taskId && update.taskDescription) {
        agent.currentTask = {
          id: update.taskId,
          description: update.taskDescription,
          progress: update.progress || 0,
          startedAt: update.timestamp,
          estimatedCompletion: update.metadata?.estimatedCompletion
        }
      } else if (update.status === 'idle' || update.status === 'done') {
        agent.currentTask = undefined
      }

      this.presenceState.lastUpdate = Date.now()
    }

    // Notify listeners
    this.presenceListeners.forEach(listener => {
      try {
        listener(update)
      } catch (error) {
        console.error('Error in presence update listener:', error)
      }
    })

    // Emit corresponding activity event
    this.emitAgentActivity({
      id: `activity-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: this.getActivityTypeFromStatus(update.status),
      agentId: update.agentId,
      agentType: update.agentType,
      taskId: update.taskId,
      message: this.getHumanReadableMessage(update),
      timestamp: update.timestamp,
      data: update.metadata
    })
  }

  emitTaskProgress = (update: TaskProgressUpdate): void => {
    // Update agent's current task progress
    const agent = this.presenceState.agents.get(update.agentId)
    if (agent && agent.currentTask && agent.currentTask.id === update.taskId) {
      agent.currentTask.progress = update.progress
      if (update.estimatedCompletion) {
        agent.currentTask.estimatedCompletion = update.estimatedCompletion
      }
    }

    // Update active task count
    this.updateActiveTaskCount()

    // Notify listeners
    this.taskProgressListeners.forEach(listener => {
      try {
        listener(update)
      } catch (error) {
        console.error('Error in task progress listener:', error)
      }
    })
  }

  emitAgentActivity = (event: AgentActivityEvent): void => {
    // Add to history
    this.eventHistory.push(event)
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize)
    }

    // Notify listeners
    this.activityListeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error('Error in activity listener:', error)
      }
    })
  }

  // Event subscription methods
  onPresenceUpdate = (listener: PresenceUpdateListener): (() => void) => {
    this.presenceListeners.add(listener)
    return () => this.presenceListeners.delete(listener)
  }

  onTaskProgress = (listener: TaskProgressListener): (() => void) => {
    this.taskProgressListeners.add(listener)
    return () => this.taskProgressListeners.delete(listener)
  }

  onAgentActivity = (listener: AgentEventListener): (() => void) => {
    this.activityListeners.add(listener)
    return () => this.activityListeners.delete(listener)
  }

  // State query methods
  getAgentPresence = (agentId: string): LiveAgentState | null => {
    return this.presenceState.agents.get(agentId) || null
  }

  getAllAgentPresence = (): LiveAgentState[] => {
    return Array.from(this.presenceState.agents.values())
  }

  getActiveTaskCount = (): number => {
    return this.presenceState.activeTaskCount
  }

  // Utility methods
  private getActivityTypeFromStatus(status: AgentPresenceUpdate['status']): AgentActivityEvent['type'] {
    switch (status) {
      case 'thinking':
        return 'agent_thinking'
      case 'working':
        return 'task_started'
      case 'done':
        return 'task_completed'
      case 'error':
        return 'task_failed'
      case 'idle':
      default:
        return 'agent_idle'
    }
  }

  private getHumanReadableMessage(update: AgentPresenceUpdate): string {
    const agentName = update.agentType.charAt(0).toUpperCase() + update.agentType.slice(1)
    
    switch (update.status) {
      case 'thinking':
        return `${agentName} is analyzing the request...`
      case 'working':
        return `${agentName} is working on: ${update.taskDescription || 'a task'}`
      case 'done':
        return `${agentName} completed the task successfully`
      case 'error':
        return `${agentName} encountered an error`
      case 'idle':
      default:
        return `${agentName} is available for tasks`
    }
  }

  private updateActiveTaskCount(): void {
    this.presenceState.activeTaskCount = Array.from(this.presenceState.agents.values())
      .filter(agent => agent.status === 'working' || agent.status === 'thinking')
      .length
  }

  // Public utility methods
  getRecentActivity(limit = 10): AgentActivityEvent[] {
    return this.eventHistory.slice(-limit).reverse()
  }

  getAgentActivity(agentId: string, limit = 10): AgentActivityEvent[] {
    return this.eventHistory
      .filter(event => event.agentId === agentId)
      .slice(-limit)
      .reverse()
  }

  clearHistory(): void {
    this.eventHistory = []
  }

  // Integration methods for agent manager
  notifyTaskStarted(agentId: string, agentType: string, taskId: string, taskDescription: string): void {
    this.emitPresenceUpdate({
      agentId,
      agentType,
      agentName: this.getAgentName(agentType),
      status: 'working',
      taskId,
      taskDescription,
      progress: 0,
      timestamp: Date.now()
    })
  }

  notifyTaskProgress(agentId: string, taskId: string, progress: number, message?: string): void {
    this.emitTaskProgress({
      taskId,
      agentId,
      status: 'in_progress',
      progress,
      message: message || `Task ${Math.round(progress)}% complete`,
      timestamp: Date.now()
    })
  }

  notifyTaskCompleted(agentId: string, agentType: string, taskId: string): void {
    this.emitPresenceUpdate({
      agentId,
      agentType,
      agentName: this.getAgentName(agentType),
      status: 'done',
      taskId,
      timestamp: Date.now()
    })

    // Set back to idle after a brief delay
    setTimeout(() => {
      this.emitPresenceUpdate({
        agentId,
        agentType,
        agentName: this.getAgentName(agentType),
        status: 'idle',
        timestamp: Date.now()
      })
    }, 2000)
  }

  notifyTaskFailed(agentId: string, agentType: string, taskId: string, error: string): void {
    this.emitPresenceUpdate({
      agentId,
      agentType,
      agentName: this.getAgentName(agentType),
      status: 'error',
      taskId,
      timestamp: Date.now(),
      metadata: { error }
    })

    // Set back to idle after a delay
    setTimeout(() => {
      this.emitPresenceUpdate({
        agentId,
        agentType,
        agentName: this.getAgentName(agentType),
        status: 'idle',
        timestamp: Date.now()
      })
    }, 3000)
  }

  private getAgentName(agentType: string): string {
    const names: Record<string, string> = {
      micromanager: "Micromanager",
      intern: "Intern Developer",
      junior: "Junior Developer",
      midlevel: "Mid-level Developer", 
      senior: "Senior Developer",
      architect: "System Architect",
      designer: "UI/UX Designer",
      tester: "QA Tester",
      researcher: "Research Specialist"
    }
    return names[agentType] || agentType
  }
}

// Global instance
let globalAgentEventsService: AgentEventsService | null = null

export function getAgentEventsService(): AgentEventsService {
  if (!globalAgentEventsService) {
    globalAgentEventsService = new AgentEventsService()
  }
  return globalAgentEventsService
}
