// hooks/useAgentHistory.ts

import { useState, useEffect, useCallback } from "react"
import { getAgentHistoryStore } from "@/services/agent-history-store"
import type {
  AgentH<PERSON>oryEntry,
  AgentHistoryFilter,
  AgentHistoryStats
} from "@/types/agent-history"

export interface UseAgentHistoryReturn {
  // Data
  entries: AgentHistoryEntry[]
  stats: AgentHistoryStats | null
  isLoading: boolean
  error: string | null
  
  // Filtering
  filter: AgentHistoryFilter
  setFilter: (filter: AgentHistoryFilter) => void
  clearFilter: () => void
  
  // Actions
  refreshHistory: () => Promise<void>
  clearHistory: () => Promise<void>
  
  // Utilities
  getEntryById: (id: string) => AgentHistoryEntry | null
  getEntriesByAgent: (agentType: string) => AgentHistoryEntry[]
  getEntriesByStatus: (status: AgentHistoryEntry['status']) => AgentHistoryEntry[]
  getRecentEntries: (limit?: number) => AgentHistoryEntry[]
  
  // Export
  exportHistory: (format: 'json' | 'csv' | 'markdown') => Promise<string>
}

const defaultFilter: AgentHistoryFilter = {}

export function useAgentHistory(): UseAgentHistoryReturn {
  const [entries, setEntries] = useState<AgentHistoryEntry[]>([])
  const [stats, setStats] = useState<AgentHistoryStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<AgentHistoryFilter>(defaultFilter)
  const [historyStore] = useState(() => getAgentHistoryStore())

  // Load history data
  const loadHistory = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const [historyEntries, historyStats] = await Promise.all([
        historyStore.getHistory(filter),
        historyStore.getHistoryStats()
      ])
      
      setEntries(historyEntries)
      setStats(historyStats)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load history'
      setError(errorMessage)
      console.error('Failed to load agent history:', err)
    } finally {
      setIsLoading(false)
    }
  }, [historyStore, filter])

  // Load history on mount and when filter changes
  useEffect(() => {
    loadHistory()
  }, [loadHistory])

  // Refresh history
  const refreshHistory = useCallback(async () => {
    await loadHistory()
  }, [loadHistory])

  // Clear history
  const clearHistory = useCallback(async () => {
    try {
      setIsLoading(true)
      await historyStore.clearHistory()
      setEntries([])
      setStats(null)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear history'
      setError(errorMessage)
      console.error('Failed to clear agent history:', err)
    } finally {
      setIsLoading(false)
    }
  }, [historyStore])

  // Clear filter
  const clearFilter = useCallback(() => {
    setFilter(defaultFilter)
  }, [])

  // Utility functions
  const getEntryById = useCallback((id: string): AgentHistoryEntry | null => {
    return entries.find(entry => entry.id === id) || null
  }, [entries])

  const getEntriesByAgent = useCallback((agentType: string): AgentHistoryEntry[] => {
    return entries.filter(entry => entry.agentType === agentType)
  }, [entries])

  const getEntriesByStatus = useCallback((status: AgentHistoryEntry['status']): AgentHistoryEntry[] => {
    return entries.filter(entry => entry.status === status)
  }, [entries])

  const getRecentEntries = useCallback((limit = 10): AgentHistoryEntry[] => {
    return entries.slice(0, limit)
  }, [entries])

  // Export functionality
  const exportHistory = useCallback(async (format: 'json' | 'csv' | 'markdown'): Promise<string> => {
    try {
      switch (format) {
        case 'json':
          return JSON.stringify({
            exportedAt: new Date().toISOString(),
            filter,
            stats,
            entries
          }, null, 2)
          
        case 'csv':
          return exportToCsv(entries)
          
        case 'markdown':
          return exportToMarkdown(entries, stats)
          
        default:
          throw new Error(`Unsupported export format: ${format}`)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Export failed'
      throw new Error(errorMessage)
    }
  }, [entries, stats, filter])

  return {
    // Data
    entries,
    stats,
    isLoading,
    error,
    
    // Filtering
    filter,
    setFilter,
    clearFilter,
    
    // Actions
    refreshHistory,
    clearHistory,
    
    // Utilities
    getEntryById,
    getEntriesByAgent,
    getEntriesByStatus,
    getRecentEntries,
    
    // Export
    exportHistory
  }
}

// Export utility functions
function exportToCsv(entries: AgentHistoryEntry[]): string {
  const headers = [
    'ID',
    'Agent Type',
    'Agent Name',
    'Task Description',
    'Status',
    'Start Time',
    'End Time',
    'Duration (ms)',
    'Tokens Used',
    'Cost',
    'Result Summary',
    'Actions Count',
    'Messages Count'
  ]

  const rows = entries.map(entry => [
    entry.id,
    entry.agentType,
    entry.agentName,
    `"${entry.taskDescription.replace(/"/g, '""')}"`,
    entry.status,
    new Date(entry.startTime).toISOString(),
    entry.endTime ? new Date(entry.endTime).toISOString() : '',
    entry.duration || '',
    entry.tokensUsed || '',
    entry.cost || '',
    `"${entry.resultSummary.replace(/"/g, '""')}"`,
    entry.actionsTaken.length,
    entry.messages.length
  ])

  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
}

function exportToMarkdown(entries: AgentHistoryEntry[], stats: AgentHistoryStats | null): string {
  const lines: string[] = []
  
  lines.push('# Agent History Report')
  lines.push('')
  lines.push(`Generated: ${new Date().toISOString()}`)
  lines.push('')
  
  if (stats) {
    lines.push('## Summary Statistics')
    lines.push('')
    lines.push(`- **Total Tasks**: ${stats.totalTasks}`)
    lines.push(`- **Successful Tasks**: ${stats.successfulTasks}`)
    lines.push(`- **Failed Tasks**: ${stats.failedTasks}`)
    lines.push(`- **Success Rate**: ${((stats.successfulTasks / stats.totalTasks) * 100).toFixed(1)}%`)
    lines.push(`- **Total Tokens Used**: ${stats.totalTokensUsed.toLocaleString()}`)
    lines.push(`- **Total Cost**: $${stats.totalCost.toFixed(4)}`)
    lines.push(`- **Average Duration**: ${(stats.averageDuration / 1000).toFixed(1)}s`)
    lines.push(`- **Most Active Agent**: ${stats.mostActiveAgent}`)
    lines.push('')
  }
  
  lines.push('## Task History')
  lines.push('')
  
  entries.forEach(entry => {
    const statusIcon = entry.status === 'success' ? '✅' : 
                      entry.status === 'error' ? '❌' : 
                      entry.status === 'cancelled' ? '⚠️' : '🔄'
    
    lines.push(`### ${statusIcon} ${entry.agentName} - ${entry.taskDescription}`)
    lines.push('')
    lines.push(`- **Status**: ${entry.status}`)
    lines.push(`- **Started**: ${new Date(entry.startTime).toLocaleString()}`)
    if (entry.endTime) {
      lines.push(`- **Completed**: ${new Date(entry.endTime).toLocaleString()}`)
    }
    if (entry.duration) {
      lines.push(`- **Duration**: ${(entry.duration / 1000).toFixed(1)}s`)
    }
    if (entry.tokensUsed) {
      lines.push(`- **Tokens Used**: ${entry.tokensUsed.toLocaleString()}`)
    }
    if (entry.cost) {
      lines.push(`- **Cost**: $${entry.cost.toFixed(4)}`)
    }
    lines.push(`- **Result**: ${entry.resultSummary}`)
    
    if (entry.actionsTaken.length > 0) {
      lines.push('')
      lines.push('**Actions Taken:**')
      entry.actionsTaken.forEach(action => {
        lines.push(`- ${action.description} (${action.result})`)
      })
    }
    
    lines.push('')
  })
  
  return lines.join('\n')
}
