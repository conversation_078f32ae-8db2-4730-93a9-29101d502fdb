// types/agent-events.ts

export interface AgentPresenceUpdate {
  agentId: string
  agentType: string
  agentName: string
  status: 'idle' | 'thinking' | 'working' | 'done' | 'error'
  taskId?: string
  taskDescription?: string
  progress?: number
  timestamp: number
  metadata?: Record<string, any>
}

export interface TaskProgressUpdate {
  taskId: string
  agentId: string
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'failed' | 'escalated'
  progress: number // 0-100
  message: string
  timestamp: number
  estimatedCompletion?: number
  metadata?: Record<string, any>
}

export interface AgentActivityEvent {
  id: string
  type: 'task_started' | 'task_progress' | 'task_completed' | 'task_failed' | 'agent_thinking' | 'agent_idle'
  agentId: string
  agentType: string
  taskId?: string
  message: string
  timestamp: number
  data?: Record<string, any>
}

export interface LiveAgentState {
  agentId: string
  agentType: string
  agentName: string
  status: AgentPresenceUpdate['status']
  currentTask?: {
    id: string
    description: string
    progress: number
    startedAt: number
    estimatedCompletion?: number
  }
  lastActivity: number
  isOnline: boolean
}

export interface AgentPresenceState {
  agents: Map<string, LiveAgentState>
  activeTaskCount: number
  lastUpdate: number
}

export type AgentEventListener = (event: AgentActivityEvent) => void
export type PresenceUpdateListener = (update: AgentPresenceUpdate) => void
export type TaskProgressListener = (update: TaskProgressUpdate) => void

export interface AgentEventEmitter {
  // Event emission
  emitPresenceUpdate: (update: AgentPresenceUpdate) => void
  emitTaskProgress: (update: TaskProgressUpdate) => void
  emitAgentActivity: (event: AgentActivityEvent) => void

  // Event subscription
  onPresenceUpdate: (listener: PresenceUpdateListener) => () => void
  onTaskProgress: (listener: TaskProgressListener) => () => void
  onAgentActivity: (listener: AgentEventListener) => () => void

  // State queries
  getAgentPresence: (agentId: string) => LiveAgentState | null
  getAllAgentPresence: () => LiveAgentState[]
  getActiveTaskCount: () => number
}

// Human-readable status messages
export const AGENT_STATUS_MESSAGES = {
  idle: "Available for tasks",
  thinking: "Analyzing request...",
  working: "Executing task...",
  done: "Task completed",
  error: "Encountered an error"
} as const

// Agent type display names
export const AGENT_TYPE_NAMES = {
  micromanager: "Micromanager",
  intern: "Intern Developer",
  junior: "Junior Developer", 
  midlevel: "Mid-level Developer",
  senior: "Senior Developer",
  architect: "System Architect",
  designer: "UI/UX Designer",
  tester: "QA Tester",
  researcher: "Research Specialist"
} as const

// Task type descriptions
export const TASK_TYPE_DESCRIPTIONS = {
  code_generation: "Writing new code",
  code_modification: "Modifying existing code",
  research: "Researching solutions",
  architecture: "Designing system architecture",
  design: "Creating UI/UX designs",
  testing: "Running tests and validation",
  orchestration: "Coordinating team tasks",
  review: "Reviewing code and designs"
} as const
