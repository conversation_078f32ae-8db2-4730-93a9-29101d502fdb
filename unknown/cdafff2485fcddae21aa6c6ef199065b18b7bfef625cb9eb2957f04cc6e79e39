// lib/budget-error.ts

import { LLMProvider } from '../components/agents/llm-provider-registry';

/**
 * ✅ Budget Exceeded Error
 * Custom error class for budget enforcement
 */
export class BudgetExceededError extends Error {
  public readonly code = 'BUDGET_EXCEEDED';
  public readonly retryable = false;
  
  constructor(
    public readonly provider: LLMProvider,
    public readonly model: string,
    public readonly currentCost: number,
    public readonly estimatedCost: number,
    public readonly budgetLimit: number,
    public readonly wouldCost: number
  ) {
    super(
      `💸 Budget exceeded: Current monthly cost $${currentCost.toFixed(2)} + estimated $${estimatedCost.toFixed(4)} = $${wouldCost.toFixed(2)} would exceed budget limit of $${budgetLimit.toFixed(2)}`
    );
    this.name = 'BudgetExceededError';
  }

  /**
   * ✅ Get user-friendly error message
   */
  public getUserMessage(): string {
    return `Budget limit exceeded. This ${this.provider}/${this.model} request would cost $${this.estimatedCost.toFixed(4)} and bring your monthly total to $${this.wouldCost.toFixed(2)}, exceeding your budget limit of $${this.budgetLimit.toFixed(2)}.`;
  }

  /**
   * ✅ Get detailed error information
   */
  public getDetails(): {
    provider: LLMProvider;
    model: string;
    currentCost: number;
    estimatedCost: number;
    budgetLimit: number;
    wouldCost: number;
    remainingBudget: number;
    overageAmount: number;
  } {
    return {
      provider: this.provider,
      model: this.model,
      currentCost: this.currentCost,
      estimatedCost: this.estimatedCost,
      budgetLimit: this.budgetLimit,
      wouldCost: this.wouldCost,
      remainingBudget: Math.max(0, this.budgetLimit - this.currentCost),
      overageAmount: this.wouldCost - this.budgetLimit
    };
  }
}

/**
 * ✅ Check if error is a budget exceeded error
 */
export function isBudgetExceededError(error: any): error is BudgetExceededError {
  return error instanceof BudgetExceededError || error?.code === 'BUDGET_EXCEEDED';
}
