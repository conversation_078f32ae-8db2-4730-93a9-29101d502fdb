// components/background/monaco-integration.ts

export interface MonacoEditorInstance {
  id: string;
  editor: any; // Monaco editor instance
  model: any; // Monaco model instance
  filePath: string;
  language: string;
  content: string;
  lastModified: number;
  agentId?: string; // Agent currently working on this editor
  isLocked: boolean; // Prevent concurrent modifications
  metadata?: Record<string, any>;
}

export interface EditorOperation {
  id: string;
  type: 'insert' | 'replace' | 'delete' | 'format' | 'refactor' | 'complete';
  editorId: string;
  position: { line: number; column: number };
  endPosition?: { line: number; column: number };
  content?: string;
  agentId: string;
  timestamp: number;
  applied: boolean;
  rollbackData?: any;
}

export interface EditorEvent {
  id: string;
  type: 'content_changed' | 'cursor_moved' | 'selection_changed' | 'focus_changed' | 'error_detected' | 'completion_requested';
  editorId: string;
  data: any;
  timestamp: number;
  agentId?: string;
}

export interface MonacoIntegrationStats {
  totalEditors: number;
  activeEditors: number;
  totalOperations: number;
  operationsByType: Record<string, number>;
  averageOperationTime: number;
  errorCount: number;
  lastOperationTime?: number;
}

export type EditorEventHandler = (event: EditorEvent) => void;

export class MonacoIntegration {
  private editors: Map<string, MonacoEditorInstance> = new Map();
  private operations: Map<string, EditorOperation> = new Map();
  private operationHistory: EditorOperation[] = [];
  private eventHandlers: EditorEventHandler[] = [];
  private maxHistorySize = 1000;
  private stats: MonacoIntegrationStats = {
    totalEditors: 0,
    activeEditors: 0,
    totalOperations: 0,
    operationsByType: {},
    averageOperationTime: 0,
    errorCount: 0
  };

  /**
   * Register a Monaco editor instance
   */
  registerEditor(
    editor: any,
    filePath: string,
    language: string,
    content: string = ''
  ): string {
    const editorId = this.generateEditorId();
    const model = editor.getModel();

    const editorInstance: MonacoEditorInstance = {
      id: editorId,
      editor,
      model,
      filePath,
      language,
      content,
      lastModified: Date.now(),
      isLocked: false,
      metadata: {
        lineCount: model?.getLineCount() || 0,
        characterCount: content.length
      }
    };

    this.editors.set(editorId, editorInstance);
    this.setupEditorListeners(editorInstance);
    this.updateStats();

    console.log(`Monaco editor registered: ${filePath} (${editorId})`);
    return editorId;
  }

  /**
   * Unregister a Monaco editor instance
   */
  unregisterEditor(editorId: string): boolean {
    const editorInstance = this.editors.get(editorId);
    if (!editorInstance) {
      return false;
    }

    // Clean up listeners
    this.cleanupEditorListeners(editorInstance);

    // Remove from registry
    this.editors.delete(editorId);
    this.updateStats();

    console.log(`Monaco editor unregistered: ${editorInstance.filePath} (${editorId})`);
    return true;
  }

  /**
   * Execute an operation on an editor
   */
  async executeOperation(operation: Omit<EditorOperation, 'id' | 'timestamp' | 'applied'>): Promise<boolean> {
    const operationId = this.generateOperationId();
    const fullOperation: EditorOperation = {
      ...operation,
      id: operationId,
      timestamp: Date.now(),
      applied: false
    };

    const editorInstance = this.editors.get(operation.editorId);
    if (!editorInstance) {
      console.error(`Editor not found: ${operation.editorId}`);
      return false;
    }

    if (editorInstance.isLocked) {
      console.warn(`Editor is locked: ${operation.editorId}`);
      return false;
    }

    try {
      // Lock editor during operation
      editorInstance.isLocked = true;

      const startTime = Date.now();
      const success = await this.performOperation(editorInstance, fullOperation);
      const executionTime = Date.now() - startTime;

      if (success) {
        fullOperation.applied = true;
        this.operations.set(operationId, fullOperation);
        this.addToHistory(fullOperation);
        this.updateOperationStats(fullOperation, executionTime, true);

        // Update editor metadata
        editorInstance.content = editorInstance.model.getValue();
        editorInstance.lastModified = Date.now();
        editorInstance.metadata = {
          ...editorInstance.metadata,
          lineCount: editorInstance.model.getLineCount(),
          characterCount: editorInstance.content.length
        };

        // Emit event
        this.emitEvent({
          id: this.generateEventId(),
          type: 'content_changed',
          editorId: operation.editorId,
          data: {
            operation: fullOperation,
            newContent: editorInstance.content
          },
          timestamp: Date.now(),
          agentId: operation.agentId
        });

        console.log(`Operation executed successfully: ${operation.type} on ${editorInstance.filePath}`);
        return true;
      } else {
        this.updateOperationStats(fullOperation, executionTime, false);
        console.error(`Operation failed: ${operation.type} on ${editorInstance.filePath}`);
        return false;
      }

    } catch (error) {
      console.error(`Error executing operation:`, error);
      this.stats.errorCount++;
      return false;
    } finally {
      // Unlock editor
      editorInstance.isLocked = false;
    }
  }

  /**
   * Insert text at a specific position
   */
  async insertText(editorId: string, line: number, column: number, text: string, agentId: string): Promise<boolean> {
    return this.executeOperation({
      type: 'insert',
      editorId,
      position: { line, column },
      content: text,
      agentId
    });
  }

  /**
   * Replace text in a range
   */
  async replaceText(
    editorId: string,
    startLine: number,
    startColumn: number,
    endLine: number,
    endColumn: number,
    text: string,
    agentId: string
  ): Promise<boolean> {
    return this.executeOperation({
      type: 'replace',
      editorId,
      position: { line: startLine, column: startColumn },
      endPosition: { line: endLine, column: endColumn },
      content: text,
      agentId
    });
  }

  /**
   * Delete text in a range
   */
  async deleteText(
    editorId: string,
    startLine: number,
    startColumn: number,
    endLine: number,
    endColumn: number,
    agentId: string
  ): Promise<boolean> {
    return this.executeOperation({
      type: 'delete',
      editorId,
      position: { line: startLine, column: startColumn },
      endPosition: { line: endLine, column: endColumn },
      agentId
    });
  }

  /**
   * Format the entire document
   */
  async formatDocument(editorId: string, agentId: string): Promise<boolean> {
    return this.executeOperation({
      type: 'format',
      editorId,
      position: { line: 1, column: 1 },
      agentId
    });
  }

  /**
   * Get editor content
   */
  getEditorContent(editorId: string): string | null {
    const editorInstance = this.editors.get(editorId);
    return editorInstance ? editorInstance.content : null;
  }

  /**
   * Get editor instance
   */
  getEditor(editorId: string): MonacoEditorInstance | undefined {
    return this.editors.get(editorId);
  }

  /**
   * Get all registered editors
   */
  getAllEditors(): MonacoEditorInstance[] {
    return Array.from(this.editors.values());
  }

  /**
   * Lock/unlock editor for exclusive access
   */
  setEditorLock(editorId: string, locked: boolean, agentId?: string): boolean {
    const editorInstance = this.editors.get(editorId);
    if (!editorInstance) {
      return false;
    }

    editorInstance.isLocked = locked;
    editorInstance.agentId = locked ? agentId : undefined;
    return true;
  }

  /**
   * Add event handler
   */
  onEditorEvent(handler: EditorEventHandler): void {
    this.eventHandlers.push(handler);
  }

  /**
   * Remove event handler
   */
  offEditorEvent(handler: EditorEventHandler): void {
    const index = this.eventHandlers.indexOf(handler);
    if (index > -1) {
      this.eventHandlers.splice(index, 1);
    }
  }

  /**
   * Get integration statistics
   */
  getStats(): MonacoIntegrationStats {
    return { ...this.stats };
  }

  /**
   * Get operation history
   */
  getOperationHistory(limit?: number): EditorOperation[] {
    const history = [...this.operationHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Clear operation history
   */
  clearHistory(): void {
    this.operationHistory = [];
    this.operations.clear();
  }

  /**
   * Shutdown Monaco integration
   */
  shutdown(): void {
    // Clean up all editors
    for (const editorInstance of this.editors.values()) {
      this.cleanupEditorListeners(editorInstance);
    }

    this.editors.clear();
    this.operations.clear();
    this.operationHistory = [];
    this.eventHandlers = [];

    console.log('Monaco integration shutdown');
  }

  // Private methods will be added in the next chunk
  private generateEditorId(): string {
    return `editor-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateOperationId(): string {
    return `op-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupEditorListeners(editorInstance: MonacoEditorInstance): void {
    const { editor, id } = editorInstance;

    // Content change listener
    const contentChangeDisposable = editor.onDidChangeModelContent((e: any) => {
      this.emitEvent({
        id: this.generateEventId(),
        type: 'content_changed',
        editorId: id,
        data: {
          changes: e.changes,
          versionId: e.versionId,
          isFlush: e.isFlush
        },
        timestamp: Date.now()
      });
    });

    // Cursor position change listener
    const cursorChangeDisposable = editor.onDidChangeCursorPosition((e: any) => {
      this.emitEvent({
        id: this.generateEventId(),
        type: 'cursor_moved',
        editorId: id,
        data: {
          position: e.position,
          reason: e.reason,
          source: e.source
        },
        timestamp: Date.now()
      });
    });

    // Selection change listener
    const selectionChangeDisposable = editor.onDidChangeCursorSelection((e: any) => {
      this.emitEvent({
        id: this.generateEventId(),
        type: 'selection_changed',
        editorId: id,
        data: {
          selection: e.selection,
          secondarySelections: e.secondarySelections,
          reason: e.reason,
          source: e.source
        },
        timestamp: Date.now()
      });
    });

    // Focus change listener
    const focusChangeDisposable = editor.onDidFocusEditorText(() => {
      this.emitEvent({
        id: this.generateEventId(),
        type: 'focus_changed',
        editorId: id,
        data: { focused: true },
        timestamp: Date.now()
      });
    });

    const blurChangeDisposable = editor.onDidBlurEditorText(() => {
      this.emitEvent({
        id: this.generateEventId(),
        type: 'focus_changed',
        editorId: id,
        data: { focused: false },
        timestamp: Date.now()
      });
    });

    // Store disposables for cleanup
    editorInstance.metadata = {
      ...editorInstance.metadata,
      disposables: [
        contentChangeDisposable,
        cursorChangeDisposable,
        selectionChangeDisposable,
        focusChangeDisposable,
        blurChangeDisposable
      ]
    };
  }

  private cleanupEditorListeners(editorInstance: MonacoEditorInstance): void {
    const disposables = editorInstance.metadata?.disposables;
    if (disposables && Array.isArray(disposables)) {
      disposables.forEach((disposable: any) => {
        try {
          disposable.dispose();
        } catch (error) {
          console.warn('Error disposing editor listener:', error);
        }
      });
    }
  }

  private async performOperation(editorInstance: MonacoEditorInstance, operation: EditorOperation): Promise<boolean> {
    const { editor, model } = editorInstance;

    try {
      switch (operation.type) {
        case 'insert':
          if (operation.content) {
            const range = new (window as any).monaco.Range(
              operation.position.line,
              operation.position.column,
              operation.position.line,
              operation.position.column
            );
            editor.executeEdits(operation.agentId, [{
              range,
              text: operation.content,
              forceMoveMarkers: true
            }]);
          }
          break;

        case 'replace':
          if (operation.content && operation.endPosition) {
            const range = new (window as any).monaco.Range(
              operation.position.line,
              operation.position.column,
              operation.endPosition.line,
              operation.endPosition.column
            );
            editor.executeEdits(operation.agentId, [{
              range,
              text: operation.content,
              forceMoveMarkers: true
            }]);
          }
          break;

        case 'delete':
          if (operation.endPosition) {
            const range = new (window as any).monaco.Range(
              operation.position.line,
              operation.position.column,
              operation.endPosition.line,
              operation.endPosition.column
            );
            editor.executeEdits(operation.agentId, [{
              range,
              text: '',
              forceMoveMarkers: true
            }]);
          }
          break;

        case 'format':
          await editor.getAction('editor.action.formatDocument')?.run();
          break;

        case 'refactor':
          // Custom refactoring logic would go here
          console.log('Refactor operation not yet implemented');
          break;

        case 'complete':
          // Trigger completion at current position
          await editor.getAction('editor.action.triggerSuggest')?.run();
          break;

        default:
          console.warn(`Unknown operation type: ${operation.type}`);
          return false;
      }

      return true;
    } catch (error) {
      console.error(`Error performing operation ${operation.type}:`, error);
      return false;
    }
  }

  private emitEvent(event: EditorEvent): void {
    this.eventHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error in event handler:', error);
      }
    });
  }

  private updateStats(): void {
    this.stats.totalEditors = this.editors.size;
    this.stats.activeEditors = Array.from(this.editors.values())
      .filter(editor => !editor.isLocked).length;
  }

  private updateOperationStats(operation: EditorOperation, executionTime: number, success: boolean): void {
    this.stats.totalOperations++;
    this.stats.operationsByType[operation.type] = (this.stats.operationsByType[operation.type] || 0) + 1;

    // Update average operation time
    const totalTime = this.stats.averageOperationTime * (this.stats.totalOperations - 1) + executionTime;
    this.stats.averageOperationTime = totalTime / this.stats.totalOperations;

    this.stats.lastOperationTime = Date.now();

    if (!success) {
      this.stats.errorCount++;
    }
  }

  private addToHistory(operation: EditorOperation): void {
    this.operationHistory.push(operation);

    // Maintain history size limit
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory = this.operationHistory.slice(-this.maxHistorySize);
    }
  }
}

// Singleton instance
let monacoIntegration: MonacoIntegration | null = null;

export function getMonacoIntegration(): MonacoIntegration {
  if (!monacoIntegration) {
    monacoIntegration = new MonacoIntegration();
  }
  return monacoIntegration;
}

export function shutdownMonacoIntegration(): void {
  if (monacoIntegration) {
    monacoIntegration.shutdown();
    monacoIntegration = null;
  }
}

// ✅ Export singleton manager class for compatibility with agent execution service
export class MonacoIntegrationManager {
  private static instance: MonacoIntegrationManager;
  private monacoIntegration: MonacoIntegration;

  private constructor() {
    this.monacoIntegration = getMonacoIntegration();
  }

  public static getInstance(): MonacoIntegrationManager {
    if (!MonacoIntegrationManager.instance) {
      MonacoIntegrationManager.instance = new MonacoIntegrationManager();
    }
    return MonacoIntegrationManager.instance;
  }

  // Delegate all methods to the underlying MonacoIntegration instance
  registerEditor(editor: any, filePath: string, language: string, content: string = ''): string {
    return this.monacoIntegration.registerEditor(editor, filePath, language, content);
  }

  executeOperation(operation: Omit<EditorOperation, 'id' | 'timestamp' | 'applied'>): Promise<boolean> {
    return this.monacoIntegration.executeOperation(operation);
  }

  insertCode(editorId: string, code: string, position: { line: number; column: number }, agentId: string): Promise<boolean> {
    return this.monacoIntegration.insertText(editorId, position.line, position.column, code, agentId);
  }

  replaceCode(editorId: string, code: string, startPosition: { line: number; column: number }, endPosition: { line: number; column: number }, agentId: string): Promise<boolean> {
    return this.monacoIntegration.replaceText(editorId, startPosition.line, startPosition.column, endPosition.line, endPosition.column, code, agentId);
  }

  deleteCode(editorId: string, startPosition: { line: number; column: number }, endPosition: { line: number; column: number }, agentId: string): Promise<boolean> {
    return this.monacoIntegration.deleteText(editorId, startPosition.line, startPosition.column, endPosition.line, endPosition.column, agentId);
  }

  formatDocument(editorId: string, agentId: string): Promise<boolean> {
    return this.monacoIntegration.formatDocument(editorId, agentId);
  }

  getEditorContent(editorId: string): string | null {
    return this.monacoIntegration.getEditorContent(editorId);
  }

  getEditor(editorId: string): MonacoEditorInstance | undefined {
    return this.monacoIntegration.getEditor(editorId);
  }

  getAllEditors(): MonacoEditorInstance[] {
    return this.monacoIntegration.getAllEditors();
  }

  setEditorLock(editorId: string, locked: boolean, agentId?: string): boolean {
    return this.monacoIntegration.setEditorLock(editorId, locked, agentId);
  }

  onEditorEvent(handler: EditorEventHandler): void {
    return this.monacoIntegration.onEditorEvent(handler);
  }

  offEditorEvent(handler: EditorEventHandler): void {
    return this.monacoIntegration.offEditorEvent(handler);
  }

  getStats(): MonacoIntegrationStats {
    return this.monacoIntegration.getStats();
  }

  getOperationHistory(limit?: number): EditorOperation[] {
    return this.monacoIntegration.getOperationHistory(limit);
  }

  clearHistory(): void {
    return this.monacoIntegration.clearHistory();
  }

  shutdown(): void {
    return this.monacoIntegration.shutdown();
  }
}
