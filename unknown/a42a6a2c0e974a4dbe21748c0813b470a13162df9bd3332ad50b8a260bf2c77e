"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronDown, ChevronRight, Terminal, FileText, AlertCircle, Info, Clock } from 'lucide-react'
import { cn } from '@/lib/utils'
import { taskOutputLoggingService, TaskOutputEntry, TaskSessionLog } from '../services/task-output-logging-service'
import { kanbanEvents } from './lib/kanban-events'

interface TaskLogViewerProps {
  taskId: string
  cardId?: string
  className?: string
  maxHeight?: string
}

export default function TaskLogViewer({ taskId, cardId, className, maxHeight = "400px" }: TaskLogViewerProps) {
  const [sessionLog, setSessionLog] = useState<TaskSessionLog | undefined>()
  const [isExpanded, setIsExpanded] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // ✅ Task 84: Load task session log
  useEffect(() => {
    const loadTaskLog = () => {
      const log = taskOutputLoggingService.getTaskLog(taskId)
      setSessionLog(log)
      setIsLoading(false)
    }

    loadTaskLog()

    // ✅ Task 84: Listen for real-time log updates
    const handleLogUpdate = (data: any) => {
      if (data.taskId === taskId) {
        loadTaskLog() // Reload the full log
      }
    }

    const handleLogCompleted = (data: any) => {
      if (data.taskId === taskId) {
        loadTaskLog() // Reload the full log
      }
    }

    kanbanEvents.on('taskLogUpdated', handleLogUpdate)
    kanbanEvents.on('taskLogCompleted', handleLogCompleted)

    return () => {
      kanbanEvents.off('taskLogUpdated', handleLogUpdate)
      kanbanEvents.off('taskLogCompleted', handleLogCompleted)
    }
  }, [taskId])

  // ✅ Task 84: Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  // ✅ Task 84: Format duration
  const formatDuration = (startTime: number, endTime?: number) => {
    const duration = (endTime || Date.now()) - startTime
    if (duration < 1000) return `${duration}ms`
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`
    return `${(duration / 60000).toFixed(1)}m`
  }

  // ✅ Task 84: Get icon for entry type
  const getEntryIcon = (type: string) => {
    switch (type) {
      case 'command': return <Terminal className="h-3 w-3" />
      case 'output': return <FileText className="h-3 w-3" />
      case 'error': return <AlertCircle className="h-3 w-3" />
      case 'system': return <Info className="h-3 w-3" />
      default: return <FileText className="h-3 w-3" />
    }
  }

  // ✅ Task 84: Get color for entry type
  const getEntryColor = (type: string) => {
    switch (type) {
      case 'command': return 'text-blue-500'
      case 'output': return 'text-green-500'
      case 'error': return 'text-red-500'
      case 'system': return 'text-yellow-500'
      default: return 'text-muted-foreground'
    }
  }

  if (isLoading) {
    return (
      <div className={cn('p-4 border rounded-lg', className)}>
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 animate-spin" />
          <span className="text-sm text-muted-foreground">Loading task logs...</span>
        </div>
      </div>
    )
  }

  if (!sessionLog) {
    return (
      <div className={cn('p-4 border rounded-lg', className)}>
        <div className="text-sm text-muted-foreground">
          No session logs available for this task
        </div>
      </div>
    )
  }

  return (
    <div className={cn('border rounded-lg', className)}>
      {/* ✅ Task 84: Collapsible header */}
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-4 h-auto"
          >
            <div className="flex items-center space-x-2">
              <Terminal className="h-4 w-4" />
              <span className="font-medium">Task Session Log</span>
              <Badge variant={sessionLog.status === 'completed' ? 'default' : 
                             sessionLog.status === 'failed' ? 'destructive' : 'secondary'}>
                {sessionLog.status}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {sessionLog.totalLines} lines
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-muted-foreground">
                {formatDuration(sessionLog.startTime, sessionLog.endTime)}
              </span>
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </div>
          </Button>
        </CollapsibleTrigger>

        {/* ✅ Task 84: Collapsible content with terminal log */}
        <CollapsibleContent>
          <div className="border-t">
            <ScrollArea className="p-4" style={{ maxHeight }}>
              <div className="space-y-2">
                {sessionLog.entries.map((entry) => (
                  <div
                    key={entry.id}
                    className="flex items-start space-x-2 text-sm font-mono"
                  >
                    <span className="text-xs text-muted-foreground min-w-[60px]">
                      {formatTimestamp(entry.timestamp)}
                    </span>
                    <div className={cn('flex items-center space-x-1', getEntryColor(entry.type))}>
                      {getEntryIcon(entry.type)}
                      <span className="text-xs font-medium uppercase">
                        {entry.type}
                      </span>
                    </div>
                    <div className="flex-1 break-words">
                      <span className="text-foreground">{entry.content}</span>
                      {entry.metadata?.formattedForTerminal && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Terminal: {entry.metadata.formattedForTerminal}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {sessionLog.entries.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    No log entries yet
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* ✅ Task 84: Session summary */}
            <div className="border-t p-4 bg-muted/50">
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <span className="text-muted-foreground">Agent:</span>
                  <span className="ml-2 font-medium">{sessionLog.agentId}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Started:</span>
                  <span className="ml-2">{new Date(sessionLog.startTime).toLocaleString()}</span>
                </div>
                {sessionLog.endTime && (
                  <div>
                    <span className="text-muted-foreground">Completed:</span>
                    <span className="ml-2">{new Date(sessionLog.endTime).toLocaleString()}</span>
                  </div>
                )}
                <div>
                  <span className="text-muted-foreground">Duration:</span>
                  <span className="ml-2">{formatDuration(sessionLog.startTime, sessionLog.endTime)}</span>
                </div>
              </div>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
