// components/agents/universal-model-selector.tsx
import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PricingDisplay } from './pricing-display';

export interface UniversalModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input: number;
    output: number;
  };
  tags?: string[];
  provider?: string;
  releaseDate?: string;
}

interface UniversalModelSelectorProps {
  value: string;
  onChange: (value: string) => void;
  availableModels: string[];
  getModelMetadata: (modelId: string) => UniversalModelMetadata | null;
  hasModelMetadata: (modelId: string) => boolean;
  getModelSeries?: (modelId: string) => string;
  isLoadingModels: boolean;
  onRefreshModels?: () => void;
  apiKey?: string;
  showRefresh?: boolean;
  showCustomInput?: boolean;
  disabled?: boolean;
  placeholder?: string;
  providerName: string;
}

export const UniversalModelSelector: React.FC<UniversalModelSelectorProps> = ({
  value,
  onChange,
  availableModels,
  getModelMetadata,
  hasModelMetadata,
  getModelSeries,
  isLoadingModels,
  onRefreshModels,
  apiKey,
  showRefresh = false,
  showCustomInput = true,
  disabled = false,
  placeholder,
  providerName
}) => {
  const [showCustom, setShowCustom] = useState(false);
  const [customModel, setCustomModel] = useState('');

  const selectedModelMetadata = getModelMetadata(value);

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === 'custom') {
      setShowCustom(true);
    } else {
      setShowCustom(false);
      onChange(selectedValue);
    }
  };

  const handleCustomSubmit = () => {
    if (customModel.trim()) {
      onChange(customModel.trim());
      setShowCustom(false);
      setCustomModel('');
    }
  };

  const handleCustomCancel = () => {
    setShowCustom(false);
    setCustomModel('');
  };

  const getModelBadgeVariant = (modelId: string) => {
    if (hasModelMetadata(modelId)) {
      return 'default';
    }
    return 'outline';
  };

  const isElectronAPIAvailable = () => {
    return typeof window !== 'undefined' && window.electronAPI?.llm?.fetchModels !== undefined;
  };

  // Group models by series if function provided
  const groupedModels = getModelSeries ? (() => {
    const groups: Record<string, string[]> = {};
    availableModels.forEach(model => {
      const series = getModelSeries(model);
      if (!groups[series]) groups[series] = [];
      groups[series].push(model);
    });
    return groups;
  })() : { 'All Models': availableModels };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Label>Model</Label>
        {showRefresh && onRefreshModels && (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefreshModels}
              disabled={isLoadingModels || !apiKey || !isElectronAPIAvailable()}
              className="h-6 px-2 text-xs"
            >
              {isLoadingModels ? (
                <div className="animate-spin h-3 w-3 border border-gray-300 border-t-blue-600 rounded-full" />
              ) : (
                'Refresh'
              )}
            </Button>
            {!isElectronAPIAvailable() && (
              <span className="text-xs text-orange-600 dark:text-orange-400">
                Electron required
              </span>
            )}
          </div>
        )}
      </div>

      {/* Custom Model Input */}
      {showCustom ? (
        <div className="space-y-2">
          <Input
            value={customModel}
            onChange={(e) => setCustomModel(e.target.value)}
            placeholder={`Enter custom ${providerName} model ID`}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleCustomSubmit();
              } else if (e.key === 'Escape') {
                handleCustomCancel();
              }
            }}
            autoFocus
          />
          <div className="flex gap-2">
            <Button size="sm" onClick={handleCustomSubmit} disabled={!customModel.trim()}>
              Use Model
            </Button>
            <Button size="sm" variant="outline" onClick={handleCustomCancel}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        /* Model Selection Dropdown */
        <Select
          value={value}
          onValueChange={handleSelectChange}
          disabled={disabled || isLoadingModels}
        >
          <SelectTrigger>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(groupedModels).map(([seriesName, models]) => (
              <div key={seriesName}>
                {Object.keys(groupedModels).length > 1 && (
                  <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b">
                    {seriesName}
                  </div>
                )}
                {models.map((modelId) => (
                  <SelectItem key={modelId} value={modelId}>
                    <div className="flex items-center gap-2">
                      <span>{getModelMetadata(modelId)?.label || modelId}</span>
                      {hasModelMetadata(modelId) && (
                        <Badge variant="default" className="text-xs">Verified</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </div>
            ))}

            {/* Custom Model Option */}
            {showCustomInput && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                  Custom Model
                </div>
                <SelectItem value="custom">
                  <div className="flex items-center gap-2">
                    <span>Custom Model ID...</span>
                    <Badge variant="outline" className="text-xs">Manual</Badge>
                  </div>
                </SelectItem>
              </>
            )}
          </SelectContent>
        </Select>
      )}

      {/* Model Metadata Display */}
      {selectedModelMetadata && !showCustom && (
        <div className="p-3 bg-muted rounded-lg space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant={getModelBadgeVariant(selectedModelMetadata.id)}>
              {getModelSeries ? getModelSeries(selectedModelMetadata.id) : providerName}
            </Badge>
            <span className="text-sm font-medium">{selectedModelMetadata.label}</span>
            {selectedModelMetadata.provider && (
              <Badge variant="outline" className="text-xs">
                {selectedModelMetadata.provider}
              </Badge>
            )}
          </div>

          {selectedModelMetadata.description && (
            <p className="text-sm text-muted-foreground">
              {selectedModelMetadata.description}
            </p>
          )}

          <div className="space-y-2">
            {selectedModelMetadata.contextSize && (
              <div className="text-xs">
                <span className="font-medium">Context:</span> {selectedModelMetadata.contextSize.toLocaleString()} tokens
              </div>
            )}
            <PricingDisplay pricing={selectedModelMetadata.pricing} />
          </div>

          {selectedModelMetadata.tags && selectedModelMetadata.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {selectedModelMetadata.tags.map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Custom Model Info */}
      {!selectedModelMetadata && value && !showCustom && (
        <div className="p-3 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            <Badge variant="outline">Custom</Badge>
            <span className="text-sm font-medium">{value}</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Custom {providerName} model ID
          </p>
        </div>
      )}
    </div>
  );
};

export default UniversalModelSelector;
