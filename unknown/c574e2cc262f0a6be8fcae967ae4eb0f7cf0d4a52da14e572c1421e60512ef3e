// services/agent-history-store.ts

import { getConfigStoreBrowser } from "@/components/background/config-store-browser"
import { getAgentEventsService } from "./agent-events"
import type {
  Agent<PERSON><PERSON>oryEntry,
  AgentHistoryFilter,
  AgentHistoryStats,
  AgentHistorySession,
  AgentAction,
  AgentHistoryMessage
} from "@/types/agent-history"
import type { AgentActivityEvent } from "@/types/agent-events"

export class AgentHistoryStore {
  private configStore = getConfigStoreBrowser()
  private eventsService = getAgentEventsService()
  private initialized = false
  private historyEntries: Map<string, AgentHistoryEntry> = new Map()
  private activeTasks: Map<string, AgentHistoryEntry> = new Map()
  private maxHistorySize = 1000

  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      await this.configStore.initialize()
      await this.loadHistoryFromStorage()
      this.subscribeToAgentEvents()
      this.initialized = true
      console.log('AgentHistoryStore initialized')
    } catch (error) {
      console.error('Failed to initialize AgentHistoryStore:', error)
      this.initialized = true // Continue with fallback
    }
  }

  private async loadHistoryFromStorage(): Promise<void> {
    try {
      const storedHistory = await this.configStore.getGlobalSetting('agentHistory', 'entries', null)
      
      if (storedHistory && Array.isArray(storedHistory)) {
        storedHistory.forEach((entry: AgentHistoryEntry) => {
          this.historyEntries.set(entry.id, {
            ...entry,
            timestamp: new Date(entry.timestamp).getTime(),
            startTime: new Date(entry.startTime).getTime(),
            endTime: entry.endTime ? new Date(entry.endTime).getTime() : undefined
          })
        })
        console.log(`Loaded ${storedHistory.length} history entries from storage`)
      }
    } catch (error) {
      console.error('Failed to load history from storage:', error)
    }
  }

  private async saveHistoryToStorage(): Promise<void> {
    try {
      const entries = Array.from(this.historyEntries.values())
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, this.maxHistorySize)

      await this.configStore.setGlobalSetting('agentHistory', 'entries', entries)
    } catch (error) {
      console.error('Failed to save history to storage:', error)
    }
  }

  private subscribeToAgentEvents(): void {
    // Subscribe to agent activity events to build history
    this.eventsService.onAgentActivity((event: AgentActivityEvent) => {
      this.handleAgentActivity(event)
    })
  }

  private handleAgentActivity(event: AgentActivityEvent): void {
    switch (event.type) {
      case 'task_started':
        this.handleTaskStarted(event)
        break
      case 'task_completed':
        this.handleTaskCompleted(event)
        break
      case 'task_failed':
        this.handleTaskFailed(event)
        break
      case 'task_progress':
        this.handleTaskProgress(event)
        break
    }
  }

  private handleTaskStarted(event: AgentActivityEvent): void {
    const entry: AgentHistoryEntry = {
      id: event.taskId || `task-${event.timestamp}`,
      agentId: event.agentId,
      agentType: event.agentType,
      agentName: this.getAgentName(event.agentType),
      taskId: event.taskId || `task-${event.timestamp}`,
      taskDescription: event.message.replace(/^.*is working on: /, ''),
      timestamp: event.timestamp,
      startTime: event.timestamp,
      status: 'in_progress',
      resultSummary: 'Task in progress...',
      messages: [],
      actionsTaken: [],
      metadata: event.data
    }

    this.activeTasks.set(entry.id, entry)
    this.historyEntries.set(entry.id, entry)
  }

  private handleTaskCompleted(event: AgentActivityEvent): void {
    const taskId = event.taskId || `task-${event.timestamp}`
    const entry = this.activeTasks.get(taskId) || this.historyEntries.get(taskId)

    if (entry) {
      entry.status = 'success'
      entry.endTime = event.timestamp
      entry.duration = entry.endTime - entry.startTime
      entry.resultSummary = 'Task completed successfully'
      
      this.activeTasks.delete(taskId)
      this.historyEntries.set(taskId, entry)
      this.saveHistoryToStorage()
    }
  }

  private handleTaskFailed(event: AgentActivityEvent): void {
    const taskId = event.taskId || `task-${event.timestamp}`
    const entry = this.activeTasks.get(taskId) || this.historyEntries.get(taskId)

    if (entry) {
      entry.status = 'error'
      entry.endTime = event.timestamp
      entry.duration = entry.endTime - entry.startTime
      entry.resultSummary = `Task failed: ${event.message}`
      
      this.activeTasks.delete(taskId)
      this.historyEntries.set(taskId, entry)
      this.saveHistoryToStorage()
    }
  }

  private handleTaskProgress(event: AgentActivityEvent): void {
    const taskId = event.taskId || `task-${event.timestamp}`
    const entry = this.activeTasks.get(taskId)

    if (entry) {
      // Add progress message
      const message: AgentHistoryMessage = {
        id: `msg-${event.timestamp}`,
        role: 'system',
        content: event.message,
        timestamp: event.timestamp,
        agentId: event.agentId,
        agentType: event.agentType
      }
      entry.messages.push(message)
    }
  }

  // Public methods
  async getHistory(filter?: AgentHistoryFilter): Promise<AgentHistoryEntry[]> {
    await this.initialize()

    let entries = Array.from(this.historyEntries.values())

    if (filter) {
      entries = this.applyFilter(entries, filter)
    }

    return entries.sort((a, b) => b.timestamp - a.timestamp)
  }

  async getHistoryStats(): Promise<AgentHistoryStats> {
    await this.initialize()

    const entries = Array.from(this.historyEntries.values())
    const totalTasks = entries.length
    const successfulTasks = entries.filter(e => e.status === 'success').length
    const failedTasks = entries.filter(e => e.status === 'error').length
    const cancelledTasks = entries.filter(e => e.status === 'cancelled').length

    const totalTokensUsed = entries.reduce((sum, e) => sum + (e.tokensUsed || 0), 0)
    const totalCost = entries.reduce((sum, e) => sum + (e.cost || 0), 0)
    
    const completedTasks = entries.filter(e => e.duration)
    const averageDuration = completedTasks.length > 0
      ? completedTasks.reduce((sum, e) => sum + (e.duration || 0), 0) / completedTasks.length
      : 0

    // Agent statistics
    const tasksByAgent: Record<string, number> = {}
    const tasksByType: Record<string, number> = {}
    const tasksByStatus: Record<string, number> = {}

    entries.forEach(entry => {
      tasksByAgent[entry.agentType] = (tasksByAgent[entry.agentType] || 0) + 1
      
      const taskType = this.extractTaskType(entry.taskDescription)
      tasksByType[taskType] = (tasksByType[taskType] || 0) + 1
      
      tasksByStatus[entry.status] = (tasksByStatus[entry.status] || 0) + 1
    })

    const mostActiveAgent = Object.entries(tasksByAgent)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none'

    const recentActivity = entries
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10)

    return {
      totalTasks,
      successfulTasks,
      failedTasks,
      cancelledTasks,
      totalTokensUsed,
      totalCost,
      averageDuration,
      mostActiveAgent,
      tasksByAgent,
      tasksByType,
      tasksByStatus,
      recentActivity
    }
  }

  async addAction(taskId: string, action: Omit<AgentAction, 'id' | 'timestamp'>): Promise<void> {
    const entry = this.historyEntries.get(taskId)
    if (entry) {
      const fullAction: AgentAction = {
        ...action,
        id: `action-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now()
      }
      entry.actionsTaken.push(fullAction)
      await this.saveHistoryToStorage()
    }
  }

  async addMessage(taskId: string, message: Omit<AgentHistoryMessage, 'id' | 'timestamp'>): Promise<void> {
    const entry = this.historyEntries.get(taskId)
    if (entry) {
      const fullMessage: AgentHistoryMessage = {
        ...message,
        id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now()
      }
      entry.messages.push(fullMessage)
      await this.saveHistoryToStorage()
    }
  }

  async clearHistory(): Promise<void> {
    this.historyEntries.clear()
    this.activeTasks.clear()
    await this.configStore.setGlobalSetting('agentHistory', 'entries', [])
  }

  // Private utility methods
  private applyFilter(entries: AgentHistoryEntry[], filter: AgentHistoryFilter): AgentHistoryEntry[] {
    return entries.filter(entry => {
      if (filter.agentTypes && !filter.agentTypes.includes(entry.agentType)) {
        return false
      }
      
      if (filter.status && !filter.status.includes(entry.status)) {
        return false
      }
      
      if (filter.dateRange) {
        if (entry.timestamp < filter.dateRange.start || entry.timestamp > filter.dateRange.end) {
          return false
        }
      }
      
      if (filter.searchQuery) {
        const query = filter.searchQuery.toLowerCase()
        if (!entry.taskDescription.toLowerCase().includes(query) &&
            !entry.resultSummary.toLowerCase().includes(query)) {
          return false
        }
      }
      
      if (filter.minDuration && (!entry.duration || entry.duration < filter.minDuration)) {
        return false
      }
      
      if (filter.maxDuration && entry.duration && entry.duration > filter.maxDuration) {
        return false
      }
      
      if (filter.minTokens && (!entry.tokensUsed || entry.tokensUsed < filter.minTokens)) {
        return false
      }
      
      if (filter.maxTokens && entry.tokensUsed && entry.tokensUsed > filter.maxTokens) {
        return false
      }
      
      return true
    })
  }

  private extractTaskType(taskDescription: string): string {
    const desc = taskDescription.toLowerCase()
    if (desc.includes('implement') || desc.includes('create') || desc.includes('generate')) return 'code_generation'
    if (desc.includes('modify') || desc.includes('update') || desc.includes('change')) return 'code_modification'
    if (desc.includes('review') || desc.includes('check')) return 'code_review'
    if (desc.includes('debug') || desc.includes('fix') || desc.includes('error')) return 'debugging'
    if (desc.includes('test')) return 'testing'
    if (desc.includes('document')) return 'documentation'
    if (desc.includes('research') || desc.includes('analyze')) return 'research'
    if (desc.includes('design') || desc.includes('ui') || desc.includes('ux')) return 'design'
    if (desc.includes('architect')) return 'architecture'
    if (desc.includes('refactor')) return 'refactoring'
    if (desc.includes('optimize')) return 'optimization'
    if (desc.includes('deploy')) return 'deployment'
    if (desc.includes('coordinate') || desc.includes('manage')) return 'orchestration'
    if (desc.includes('validate')) return 'validation'
    return 'general'
  }

  private getAgentName(agentType: string): string {
    const names: Record<string, string> = {
      micromanager: "Micromanager",
      intern: "Intern Developer",
      junior: "Junior Developer",
      midlevel: "Mid-level Developer",
      senior: "Senior Developer",
      architect: "System Architect",
      designer: "UI/UX Designer",
      tester: "QA Tester",
      researcher: "Research Specialist"
    }
    return names[agentType] || agentType
  }
}

// Global instance
let globalAgentHistoryStore: AgentHistoryStore | null = null

export function getAgentHistoryStore(): AgentHistoryStore {
  if (!globalAgentHistoryStore) {
    globalAgentHistoryStore = new AgentHistoryStore()
  }
  return globalAgentHistoryStore
}
