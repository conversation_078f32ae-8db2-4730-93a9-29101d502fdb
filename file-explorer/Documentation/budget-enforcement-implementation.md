# 💸 Budget Enforcement Implementation

## Overview

The budget enforcement system provides real-time cost monitoring and budget limits for LLM API usage. When users configure a `budgetLimit` in Cost Settings, the system automatically prevents costly operations that would exceed the monthly budget.

## Architecture

### Core Components

1. **CostTracker** (`lib/cost-tracker.ts`)
   - Tracks all LLM API costs by provider, model, and time
   - Stores cost history persistently
   - Calculates monthly totals and budget utilization

2. **BudgetEnforcer** (`lib/budget-enforcer.ts`)
   - Enforces budget limits before LLM calls
   - Provides budget checking and cost recording
   - Integrates with cost settings

3. **BudgetExceededError** (`lib/budget-error.ts`)
   - Custom error class for budget violations
   - Provides detailed error information
   - User-friendly error messages

4. **LLMRequestService** (Enhanced)
   - Intercepts all LLM calls for budget checking
   - Records actual costs after successful requests
   - Throws budget errors when limits exceeded

## Implementation Details

### Budget Enforcement Flow

```
1. User initiates LLM request
2. LLMRequestService checks if budget enforcement enabled
3. If enabled:
   a. Estimate request cost (input + output tokens)
   b. Get current monthly cost from CostTracker
   c. Check if (current + estimated) > budget limit
   d. If exceeded: throw BudgetExceededError
   e. If within budget: proceed with request
4. After successful request:
   a. Record actual cost with CostTracker
   b. Update monthly totals
```

### Cost Estimation

- **Input tokens**: Estimated using 4 characters per token approximation
- **Output tokens**: Uses maxTokens from request
- **Cost calculation**: Uses provider-specific pricing from LLMProviderRegistry

### Data Persistence

- **Primary**: ConfigStore for cross-session persistence
- **Fallback**: localStorage for browser-only environments
- **Format**: JSON array of CostEntry objects

## Features

### ✅ Real-time Budget Enforcement

- Blocks LLM requests that would exceed budget
- Shows detailed cost breakdown in error messages
- Preserves user experience with clear feedback

### ✅ Comprehensive Cost Tracking

- Per-provider cost breakdown
- Per-model usage statistics
- Monthly cost summaries
- Historical cost data

### ✅ Budget Status UI

- Real-time budget utilization display
- Progress bars and alerts
- Provider breakdown charts
- Monthly usage statistics

### ✅ Error Handling

- Custom budget exceeded errors
- User-friendly error messages
- Actionable error dialogs
- Settings integration

## Configuration

### Cost Settings

```typescript
interface CostSettings {
  budgetLimit: number;        // Monthly budget in USD
  alertThreshold: number;     // Alert percentage (e.g., 80%)
  trackUsage: boolean;        // Enable/disable tracking
  showCostEstimates: boolean; // Show cost estimates in UI
  preferCheaperModels: boolean; // Future: auto-select cheaper models
}
```

### Budget Status

```typescript
interface BudgetStatus {
  currentMonthlyCost: number;
  budgetLimit: number;
  remainingBudget: number;
  utilizationPercentage: number;
  isOverBudget: boolean;
  isNearThreshold: boolean;
}
```

## Integration Points

### Settings Integration

- **LLMIntegrationService**: Passes cost settings to LLMRequestService
- **Settings Context**: Provides cost settings to UI components
- **Real-time Updates**: Settings changes immediately update budget enforcement

### Agent System Integration

- **Agent Manager**: Uses LLMRequestService for all agent LLM calls
- **Task Tracking**: Links costs to specific agents and tasks
- **Error Propagation**: Budget errors surface in agent execution

### UI Integration

- **Cost Settings Tab**: Shows budget configuration and status
- **Budget Status Component**: Real-time budget monitoring
- **Error Handlers**: Budget-specific error displays

## Usage Examples

### Basic Budget Setup

1. Navigate to Settings → Cost
2. Set "Monthly Budget Limit" (e.g., $100)
3. Set "Alert Threshold" (e.g., 80%)
4. Enable "Track Usage"
5. Budget enforcement is now active

### Budget Exceeded Scenario

```
💸 Budget exceeded: gpt-4/gpt-4 request ($0.0240) blocked. 
Current monthly cost: $98.50 + estimated $0.0240 = $98.52 
would exceed budget limit of $100.00
```

### Budget Status Display

- **Green**: Within budget (< alert threshold)
- **Yellow**: Near threshold (≥ alert threshold)
- **Red**: Over budget (≥ budget limit)

## Error Messages

### Console Logs

- `💰 Budget check passed: $0.0120 estimated cost, 45.2% budget used`
- `⚠️ Budget alert: 85.3% of budget used (80% threshold)`
- `💸 Budget exceeded: Would exceed budget limit: $101.20 > $100.00`

### UI Messages

- **Alert**: "You have used 85.3% of your monthly budget"
- **Error**: "Budget limit exceeded. This request would cost $0.0240..."
- **Status**: "$45.20 / $100.00 (45.2% used)"

## Technical Notes

### Performance

- Budget checks add ~1-2ms overhead to LLM requests
- Cost tracking is asynchronous and non-blocking
- UI updates every 30 seconds for real-time status

### Accuracy

- Cost estimates are approximate (actual costs recorded post-request)
- Provider pricing may change (update LLMProviderRegistry as needed)
- Token counting is estimated (providers may count differently)

### Reliability

- Budget enforcement fails safe (allows requests if checking fails)
- Cost recording failures don't break LLM requests
- Persistent storage with localStorage fallback

## Future Enhancements

1. **Smart Model Selection**: Auto-select cheaper models when near budget
2. **Cost Alerts**: Email/notification when approaching limits
3. **Usage Analytics**: Detailed cost analysis and trends
4. **Team Budgets**: Shared budgets across multiple users
5. **Cost Optimization**: Automatic prompt optimization for cost reduction

## Validation

### Test Scenarios

1. **Budget Enforcement**: Set low budget, verify requests blocked
2. **Cost Tracking**: Make requests, verify costs recorded accurately
3. **UI Updates**: Change settings, verify real-time UI updates
4. **Error Handling**: Trigger budget errors, verify user-friendly messages
5. **Persistence**: Restart app, verify cost history preserved

### Success Criteria

- ✅ All LLM requests intercepted for budget checking
- ✅ Budget exceeded errors clearly surfaced in UI
- ✅ Real-time budget status updates
- ✅ Accurate cost tracking and persistence
- ✅ No performance degradation in LLM requests
