<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dialog Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Test styles for dialog positioning */
        .dialog-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(4px);
            z-index: 50;
        }
        
        .dialog-content-old {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 50;
            background: white;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 24px;
            max-width: 500px;
            width: 90vw;
            max-height: 90vh;
        }
        
        .dialog-content-new {
            position: fixed;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
        }
        
        .dialog-inner {
            background: white;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 24px;
            max-width: 500px;
            width: 90vw;
            max-height: 90vh;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">Dialog Positioning Test</h1>
        
        <div class="space-y-4">
            <button onclick="showOldDialog()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Show Old Dialog (Transform Method)
            </button>
            
            <button onclick="showNewDialog()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                Show New Dialog (Flexbox Method)
            </button>
        </div>
        
        <!-- Old Dialog -->
        <div id="old-dialog" class="hidden">
            <div class="dialog-overlay" onclick="hideOldDialog()"></div>
            <div class="dialog-content-old">
                <h2 class="text-xl font-semibold mb-4">Old Dialog (Transform)</h2>
                <p class="mb-4">This dialog uses the old positioning method with transform: translate(-50%, -50%)</p>
                <div class="flex justify-end">
                    <button onclick="hideOldDialog()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Close
                    </button>
                </div>
            </div>
        </div>
        
        <!-- New Dialog -->
        <div id="new-dialog" class="hidden">
            <div class="dialog-overlay" onclick="hideNewDialog()"></div>
            <div class="dialog-content-new">
                <div class="dialog-inner">
                    <h2 class="text-xl font-semibold mb-4">New Dialog (Flexbox)</h2>
                    <p class="mb-4">This dialog uses the new positioning method with flexbox centering</p>
                    <div class="flex justify-end">
                        <button onclick="hideNewDialog()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showOldDialog() {
            document.getElementById('old-dialog').classList.remove('hidden');
        }
        
        function hideOldDialog() {
            document.getElementById('old-dialog').classList.add('hidden');
        }
        
        function showNewDialog() {
            document.getElementById('new-dialog').classList.remove('hidden');
        }
        
        function hideNewDialog() {
            document.getElementById('new-dialog').classList.add('hidden');
        }
    </script>
</body>
</html>
