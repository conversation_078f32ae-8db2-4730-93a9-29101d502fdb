// services/analytics-service.ts

import { getAgentHistoryStore } from "./agent-history-store"
import { costTracker, type CostEntry } from "../lib/cost-tracker"
import { getAgentEventsService } from "./agent-events"
import type {
  AgentAnalyticsMetrics,
  AnalyticsFilter,
  ChartDataPoint,
  TimeSeriesDataPoint,
  AnalyticsInsight,
  AnalyticsRecommendation
} from "@/types/analytics"
import type { StressTestResult } from "../systems/stress/StressTestRunner"
import type { AgentHistoryEntry } from "@/types/agent-history"

import { TIME_PERIODS } from "@/types/analytics"

export class AnalyticsService {
  private historyStore = getAgentHistoryStore()
  private eventsService = getAgentEventsService()
  private initialized = false
  private stressTestResults: StressTestResult[] = []

  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      await this.historyStore.initialize()
      this.initialized = true
      console.log('AnalyticsService initialized')
    } catch (error) {
      console.error('Failed to initialize AnalyticsService:', error)
      this.initialized = true // Continue with fallback
    }
  }

  async getAnalyticsMetrics(filter?: AnalyticsFilter): Promise<AgentAnalyticsMetrics> {
    await this.initialize()

    try {
      // Get data from all sources
      const [historyEntries, costEntries, currentPresence] = await Promise.all([
        this.historyStore.getHistory(),
        this.getCostEntries(filter),
        this.getCurrentPresenceData()
      ])

      // Apply filters
      const filteredEntries = filter ? this.applyHistoryFilter(historyEntries, filter) : historyEntries
      const filteredCosts = filter ? this.applyCostFilter(costEntries, filter) : costEntries

      // Calculate core metrics
      const totalTasks = filteredEntries.length
      const completedTasks = filteredEntries.filter(e => e.status === 'success').length
      const failedTasks = filteredEntries.filter(e => e.status === 'error').length

      const completedEntries = filteredEntries.filter(e => e.duration)
      const averageCompletionTime = completedEntries.length > 0
        ? completedEntries.reduce((sum, e) => sum + (e.duration || 0), 0) / completedEntries.length
        : 0

      const totalTokensUsed = filteredEntries.reduce((sum, e) => sum + (e.tokensUsed || 0), 0)
      const totalCost = filteredCosts.reduce((sum, e) => sum + e.cost, 0)

      // Time-based metrics
      const now = new Date()
      const todayPeriod = TIME_PERIODS.today()
      const weekPeriod = TIME_PERIODS.thisWeek()
      const monthPeriod = TIME_PERIODS.thisMonth()

      const tasksToday = this.getTasksInPeriod(filteredEntries, todayPeriod.start, todayPeriod.end)
      const tasksThisWeek = this.getTasksInPeriod(filteredEntries, weekPeriod.start, weekPeriod.end)
      const tasksThisMonth = this.getTasksInPeriod(filteredEntries, monthPeriod.start, monthPeriod.end)

      const costToday = this.getCostInPeriod(filteredCosts, todayPeriod.start, todayPeriod.end)
      const costThisWeek = this.getCostInPeriod(filteredCosts, weekPeriod.start, weekPeriod.end)
      const costThisMonth = this.getCostInPeriod(filteredCosts, monthPeriod.start, monthPeriod.end)

      // Agent performance metrics
      const agentMetrics = this.calculateAgentMetrics(filteredEntries)
      const modelMetrics = this.calculateModelMetrics(filteredCosts)

      // Trend data
      const dailyTaskTrend = this.calculateDailyTrend(filteredEntries, filteredCosts)
      const weeklyTokenTrend = this.calculateWeeklyTokenTrend(filteredEntries)
      const monthlyUsageTrend = this.calculateMonthlyTrend(filteredEntries, filteredCosts)

      // Performance insights
      const peakUsageHours = this.calculatePeakUsageHours(filteredEntries)
      const taskTypeDistribution = this.calculateTaskTypeDistribution(filteredEntries)
      const errorRateByAgent = this.calculateErrorRateByAgent(filteredEntries)

      return {
        // Core metrics
        totalTasks,
        completedTasks,
        failedTasks,
        averageCompletionTime,
        totalTokensUsed,
        totalCost,

        // Time-based metrics
        tasksToday,
        tasksThisWeek,
        tasksThisMonth,
        costToday,
        costThisWeek,
        costThisMonth,

        // Agent performance
        mostActiveAgent: agentMetrics.mostActive,
        mostEfficientAgent: agentMetrics.mostEfficient,
        agentSuccessRates: agentMetrics.successRates,
        agentAverageTime: agentMetrics.averageTimes,
        agentTokenUsage: agentMetrics.tokenUsage,
        agentCostBreakdown: agentMetrics.costBreakdown,

        // Model usage
        modelUsageBreakdown: modelMetrics,

        // Trends
        dailyTaskTrend,
        weeklyTokenTrend,
        monthlyUsageTrend,

        // Performance insights
        peakUsageHours,
        taskTypeDistribution,
        errorRateByAgent,

        // Real-time metrics
        currentActiveAgents: currentPresence.activeAgents,
        tasksInProgress: currentPresence.tasksInProgress,
        queuedTasks: currentPresence.queuedTasks,
        systemLoad: currentPresence.systemLoad,

        // Quality metrics (simplified for now)
        averageTaskComplexity: this.calculateAverageComplexity(filteredEntries),
        codeQualityScore: this.calculateCodeQualityScore(filteredEntries),
        userSatisfactionScore: this.calculateSatisfactionScore(filteredEntries),

        lastUpdated: Date.now()
      }
    } catch (error) {
      console.error('Failed to calculate analytics metrics:', error)
      return this.getEmptyMetrics()
    }
  }

  async generateInsights(metrics: AgentAnalyticsMetrics): Promise<AnalyticsInsight[]> {
    const insights: AnalyticsInsight[] = []

    // Performance insights
    if (metrics.averageCompletionTime > 30000) { // > 30 seconds
      insights.push({
        id: 'slow-performance',
        type: 'performance',
        title: 'Slow Task Completion',
        description: `Average task completion time is ${(metrics.averageCompletionTime / 1000).toFixed(1)}s, which is above optimal range.`,
        severity: 'warning',
        impact: 'medium',
        actionable: true
      })
    }

    // Cost insights
    if (metrics.costThisMonth > metrics.costToday * 20) { // Projected monthly cost
      insights.push({
        id: 'high-cost-trend',
        type: 'cost',
        title: 'High Cost Trend',
        description: `Current spending trend suggests monthly cost will exceed $${(metrics.costToday * 30).toFixed(2)}.`,
        severity: 'warning',
        impact: 'high',
        actionable: true
      })
    }

    // Usage insights
    const failureRate = metrics.totalTasks > 0 ? (metrics.failedTasks / metrics.totalTasks) * 100 : 0
    if (failureRate > 10) {
      insights.push({
        id: 'high-failure-rate',
        type: 'quality',
        title: 'High Task Failure Rate',
        description: `${failureRate.toFixed(1)}% of tasks are failing, indicating potential system issues.`,
        severity: 'critical',
        impact: 'high',
        actionable: true
      })
    }

    // Positive insights
    if (metrics.tasksToday > metrics.tasksThisWeek / 7 * 1.5) {
      insights.push({
        id: 'high-productivity',
        type: 'performance',
        title: 'High Productivity Day',
        description: `Today's task completion is 50% above average daily rate.`,
        severity: 'positive',
        impact: 'medium',
        actionable: false
      })
    }

    return insights
  }

  async generateRecommendations(metrics: AgentAnalyticsMetrics): Promise<AnalyticsRecommendation[]> {
    const recommendations: AnalyticsRecommendation[] = []

    // Performance optimization
    if (metrics.averageCompletionTime > 30000) {
      recommendations.push({
        id: 'optimize-performance',
        title: 'Optimize Agent Performance',
        description: 'Consider optimizing agent prompts and reducing model complexity for faster responses.',
        category: 'performance',
        priority: 'medium',
        estimatedImpact: '30% faster task completion',
        implementation: {
          difficulty: 'medium',
          timeRequired: '2-3 hours',
          steps: [
            'Analyze slow-performing agents',
            'Optimize prompts for efficiency',
            'Consider using faster models for simple tasks',
            'Implement caching for common operations'
          ]
        },
        metrics: {
          performanceGain: 30
        }
      })
    }

    // Cost optimization
    if (metrics.totalCost > 10) {
      recommendations.push({
        id: 'reduce-costs',
        title: 'Implement Cost Optimization',
        description: 'Switch to more cost-effective models for routine tasks to reduce overall spending.',
        category: 'cost_reduction',
        priority: 'high',
        estimatedImpact: `Save $${(metrics.totalCost * 0.3).toFixed(2)} monthly`,
        implementation: {
          difficulty: 'easy',
          timeRequired: '1 hour',
          steps: [
            'Identify high-cost, low-complexity tasks',
            'Configure cheaper models for routine operations',
            'Set up cost monitoring alerts',
            'Review and adjust model selection rules'
          ]
        },
        metrics: {
          potentialSavings: metrics.totalCost * 0.3
        }
      })
    }

    return recommendations
  }

  // Private helper methods
  private async getCostEntries(filter?: AnalyticsFilter): Promise<CostEntry[]> {
    try {
      if (filter?.dateRange) {
        return costTracker.getCostHistory(filter.dateRange.start, filter.dateRange.end)
      }
      // Use exportCostData to get all entries since getAllCostEntries doesn't exist
      const exportData = costTracker.exportCostData()
      return exportData.entries
    } catch (error) {
      console.error('Failed to get cost entries:', error)
      return []
    }
  }

  private async getCurrentPresenceData() {
    const agents = this.eventsService.getAllAgentPresence()
    const activeAgents = agents.filter(a => a.status === 'working' || a.status === 'thinking').length
    const tasksInProgress = agents.filter(a => a.currentTask).length

    return {
      activeAgents,
      tasksInProgress,
      queuedTasks: 0, // Would need queue service
      systemLoad: Math.min(100, (activeAgents / agents.length) * 100)
    }
  }

  private applyHistoryFilter(entries: AgentHistoryEntry[], filter: AnalyticsFilter): AgentHistoryEntry[] {
    return entries.filter(entry => {
      if (filter.dateRange) {
        const entryDate = new Date(entry.timestamp)
        if (entryDate < filter.dateRange.start || entryDate > filter.dateRange.end) {
          return false
        }
      }

      if (filter.agentTypes && !filter.agentTypes.includes(entry.agentType)) {
        return false
      }

      if (filter.status && !filter.status.includes(entry.status)) {
        return false
      }

      return true
    })
  }

  private applyCostFilter(entries: CostEntry[], filter: AnalyticsFilter): CostEntry[] {
    return entries.filter(entry => {
      if (filter.dateRange) {
        const entryDate = new Date(entry.timestamp)
        if (entryDate < filter.dateRange.start || entryDate > filter.dateRange.end) {
          return false
        }
      }

      if (filter.providers && !filter.providers.includes(entry.provider)) {
        return false
      }

      if (filter.models && !filter.models.includes(entry.model)) {
        return false
      }

      return true
    })
  }

  private getTasksInPeriod(entries: AgentHistoryEntry[], start: Date, end: Date): number {
    return entries.filter(e => {
      const date = new Date(e.timestamp)
      return date >= start && date <= end
    }).length
  }

  private getCostInPeriod(entries: CostEntry[], start: Date, end: Date): number {
    return entries
      .filter(e => {
        const date = new Date(e.timestamp)
        return date >= start && date <= end
      })
      .reduce((sum, e) => sum + e.cost, 0)
  }

  private calculateAgentMetrics(entries: AgentHistoryEntry[]) {
    const agentStats: Record<string, {
      total: number
      successful: number
      totalTime: number
      totalTokens: number
      totalCost: number
    }> = {}

    entries.forEach(entry => {
      if (!agentStats[entry.agentType]) {
        agentStats[entry.agentType] = {
          total: 0,
          successful: 0,
          totalTime: 0,
          totalTokens: 0,
          totalCost: 0
        }
      }

      const stats = agentStats[entry.agentType]
      stats.total++
      if (entry.status === 'success') stats.successful++
      if (entry.duration) stats.totalTime += entry.duration
      if (entry.tokensUsed) stats.totalTokens += entry.tokensUsed
      if (entry.cost) stats.totalCost += entry.cost
    })

    const successRates: Record<string, number> = {}
    const averageTimes: Record<string, number> = {}
    const tokenUsage: Record<string, number> = {}
    const costBreakdown: Record<string, number> = {}

    let mostActive = ''
    let mostActiveCount = 0
    let mostEfficient = ''
    let bestEfficiency = 0

    Object.entries(agentStats).forEach(([agent, stats]) => {
      successRates[agent] = stats.total > 0 ? (stats.successful / stats.total) * 100 : 0
      averageTimes[agent] = stats.successful > 0 ? stats.totalTime / stats.successful : 0
      tokenUsage[agent] = stats.totalTokens
      costBreakdown[agent] = stats.totalCost

      if (stats.total > mostActiveCount) {
        mostActiveCount = stats.total
        mostActive = agent
      }

      const efficiency = stats.total > 0 ? (stats.successful / stats.total) * (1000 / (averageTimes[agent] || 1000)) : 0
      if (efficiency > bestEfficiency) {
        bestEfficiency = efficiency
        mostEfficient = agent
      }
    })

    return {
      mostActive,
      mostEfficient,
      successRates,
      averageTimes,
      tokenUsage,
      costBreakdown
    }
  }

  private calculateModelMetrics(entries: CostEntry[]) {
    const modelStats: Record<string, {
      calls: number
      tokens: number
      cost: number
      successRate: number
    }> = {}

    entries.forEach(entry => {
      const modelKey = `${entry.provider}/${entry.model}`
      if (!modelStats[modelKey]) {
        modelStats[modelKey] = {
          calls: 0,
          tokens: 0,
          cost: 0,
          successRate: 100 // Simplified - would need error tracking
        }
      }

      const stats = modelStats[modelKey]
      stats.calls++
      stats.tokens += entry.inputTokens + entry.outputTokens
      stats.cost += entry.cost
    })

    return modelStats
  }

  private calculateDailyTrend(historyEntries: AgentHistoryEntry[], costEntries: CostEntry[]): TimeSeriesDataPoint[] {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (29 - i))
      return date
    })

    return last30Days.map(date => {
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate())
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000)

      const tasks = this.getTasksInPeriod(historyEntries, dayStart, dayEnd)
      const cost = this.getCostInPeriod(costEntries, dayStart, dayEnd)

      return {
        timestamp: date.getTime(),
        date: date.toISOString().split('T')[0],
        value: tasks,
        label: `${tasks} tasks, $${cost.toFixed(2)}`
      }
    })
  }

  private calculateWeeklyTokenTrend(entries: AgentHistoryEntry[]): TimeSeriesDataPoint[] {
    // Simplified implementation
    const last8Weeks = Array.from({ length: 8 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (7 - i) * 7)
      return date
    })

    return last8Weeks.map(date => {
      const weekStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay())
      const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000)

      const weekEntries = entries.filter(e => {
        const entryDate = new Date(e.timestamp)
        return entryDate >= weekStart && entryDate < weekEnd
      })

      const tokens = weekEntries.reduce((sum, e) => sum + (e.tokensUsed || 0), 0)

      return {
        timestamp: weekStart.getTime(),
        date: `Week of ${weekStart.toISOString().split('T')[0]}`,
        value: tokens
      }
    })
  }

  private calculateMonthlyTrend(historyEntries: AgentHistoryEntry[], costEntries: CostEntry[]): TimeSeriesDataPoint[] {
    // Simplified implementation for last 6 months
    const last6Months = Array.from({ length: 6 }, (_, i) => {
      const date = new Date()
      date.setMonth(date.getMonth() - (5 - i))
      return date
    })

    return last6Months.map(date => {
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1)
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 1)

      const tasks = this.getTasksInPeriod(historyEntries, monthStart, monthEnd)
      const cost = this.getCostInPeriod(costEntries, monthStart, monthEnd)
      const tokens = historyEntries
        .filter(e => {
          const entryDate = new Date(e.timestamp)
          return entryDate >= monthStart && entryDate < monthEnd
        })
        .reduce((sum, e) => sum + (e.tokensUsed || 0), 0)

      return {
        timestamp: monthStart.getTime(),
        date: monthStart.toLocaleDateString('en-US', { year: 'numeric', month: 'short' }),
        value: tasks,
        label: `${tasks} tasks, ${tokens.toLocaleString()} tokens, $${cost.toFixed(2)}`
      }
    })
  }

  private calculatePeakUsageHours(entries: AgentHistoryEntry[]) {
    const hourCounts = Array(24).fill(0)

    entries.forEach(entry => {
      const hour = new Date(entry.timestamp).getHours()
      hourCounts[hour]++
    })

    return hourCounts.map((tasks, hour) => ({ hour, tasks }))
  }

  private calculateTaskTypeDistribution(entries: AgentHistoryEntry[]): Record<string, number> {
    const distribution: Record<string, number> = {}

    entries.forEach(entry => {
      const taskType = this.extractTaskType(entry.taskDescription)
      distribution[taskType] = (distribution[taskType] || 0) + 1
    })

    return distribution
  }

  private calculateErrorRateByAgent(entries: AgentHistoryEntry[]): Record<string, number> {
    const agentStats: Record<string, { total: number; errors: number }> = {}

    entries.forEach(entry => {
      if (!agentStats[entry.agentType]) {
        agentStats[entry.agentType] = { total: 0, errors: 0 }
      }
      agentStats[entry.agentType].total++
      if (entry.status === 'error') {
        agentStats[entry.agentType].errors++
      }
    })

    const errorRates: Record<string, number> = {}
    Object.entries(agentStats).forEach(([agent, stats]) => {
      errorRates[agent] = stats.total > 0 ? (stats.errors / stats.total) * 100 : 0
    })

    return errorRates
  }

  private extractTaskType(description: string): string {
    const desc = description.toLowerCase()
    if (desc.includes('implement') || desc.includes('create') || desc.includes('generate')) return 'Implementation'
    if (desc.includes('design') || desc.includes('ui') || desc.includes('ux')) return 'Design'
    if (desc.includes('test') || desc.includes('qa')) return 'Testing'
    if (desc.includes('research') || desc.includes('analyze')) return 'Research'
    if (desc.includes('debug') || desc.includes('fix')) return 'Debugging'
    if (desc.includes('refactor') || desc.includes('optimize')) return 'Optimization'
    if (desc.includes('document')) return 'Documentation'
    return 'General'
  }

  private calculateAverageComplexity(entries: AgentHistoryEntry[]): number {
    // Simplified complexity calculation based on task duration and description length
    if (entries.length === 0) return 0

    const complexityScores = entries.map(entry => {
      const durationScore = (entry.duration || 0) / 10000 // Normalize to 0-10 scale
      const descriptionScore = entry.taskDescription.length / 20 // Normalize description length
      return Math.min(10, durationScore + descriptionScore)
    })

    return complexityScores.reduce((sum, score) => sum + score, 0) / complexityScores.length
  }

  private calculateCodeQualityScore(entries: AgentHistoryEntry[]): number {
    // Simplified quality score based on success rate and task complexity
    const successRate = entries.length > 0 ?
      (entries.filter(e => e.status === 'success').length / entries.length) * 100 : 0

    // Higher success rate = higher quality score
    return Math.min(100, successRate * 1.1) // Slight boost for high success rates
  }

  private calculateSatisfactionScore(entries: AgentHistoryEntry[]): number {
    // Simplified satisfaction score based on completion time and success rate
    const successRate = entries.length > 0 ?
      (entries.filter(e => e.status === 'success').length / entries.length) * 100 : 0

    const avgTime = entries.filter(e => e.duration).length > 0 ?
      entries.filter(e => e.duration).reduce((sum, e) => sum + (e.duration || 0), 0) /
      entries.filter(e => e.duration).length : 0

    // Lower average time and higher success rate = higher satisfaction
    const timeScore = Math.max(0, 100 - (avgTime / 1000)) // Penalize longer times
    return (successRate * 0.7 + timeScore * 0.3)
  }

  private getEmptyMetrics(): AgentAnalyticsMetrics {
    return {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageCompletionTime: 0,
      totalTokensUsed: 0,
      totalCost: 0,
      tasksToday: 0,
      tasksThisWeek: 0,
      tasksThisMonth: 0,
      costToday: 0,
      costThisWeek: 0,
      costThisMonth: 0,
      mostActiveAgent: 'none',
      mostEfficientAgent: 'none',
      agentSuccessRates: {},
      agentAverageTime: {},
      agentTokenUsage: {},
      agentCostBreakdown: {},
      modelUsageBreakdown: {},
      dailyTaskTrend: [],
      weeklyTokenTrend: [],
      monthlyUsageTrend: [],
      peakUsageHours: [],
      taskTypeDistribution: {},
      errorRateByAgent: {},
      currentActiveAgents: 0,
      tasksInProgress: 0,
      queuedTasks: 0,
      systemLoad: 0,
      averageTaskComplexity: 0,
      codeQualityScore: 0,
      userSatisfactionScore: 0,
      lastUpdated: Date.now()
    }
  }

  /**
   * Report stress test results to the metrics service
   */
  public reportStressTestResults(result: StressTestResult): void {
    console.log(`📊 AnalyticsService: Recording stress test result ${result.testId}`);

    // Store the stress test result
    this.stressTestResults.push(result);

    // Keep only the last 50 stress test results to prevent memory bloat
    if (this.stressTestResults.length > 50) {
      this.stressTestResults = this.stressTestResults.slice(-50);
    }

    // Log summary to console
    console.log(`📊 Stress Test ${result.testId} Summary:`, {
      duration: `${(result.duration / 1000).toFixed(1)}s`,
      totalTasks: result.metrics.totalTasks,
      successRate: `${((result.metrics.successfulTasks / result.metrics.totalTasks) * 100).toFixed(1)}%`,
      throughput: `${result.metrics.throughput.toFixed(2)} tasks/sec`,
      avgResponseTime: `${result.metrics.averageResponseTime.toFixed(0)}ms`,
      systemHealth: `${result.systemMetrics.systemHealthScore.toFixed(1)}/100`,
      concurrencyRespected: result.systemMetrics.concurrencyLimitRespected,
      errors: result.errors.length,
      warnings: result.warnings.length
    });
  }

  /**
   * Get stress test results for metrics display
   */
  public getStressTestResults(limit?: number): StressTestResult[] {
    const results = limit ? this.stressTestResults.slice(-limit) : this.stressTestResults;
    return results.sort((a, b) => b.startTime - a.startTime); // Most recent first
  }

  /**
   * Get stress test analytics summary
   */
  public getStressTestAnalytics(): {
    totalTests: number;
    averageSuccessRate: number;
    averageThroughput: number;
    averageResponseTime: number;
    averageSystemHealth: number;
    recentTrend: 'improving' | 'declining' | 'stable';
    lastTestResult?: StressTestResult;
  } {
    if (this.stressTestResults.length === 0) {
      return {
        totalTests: 0,
        averageSuccessRate: 0,
        averageThroughput: 0,
        averageResponseTime: 0,
        averageSystemHealth: 0,
        recentTrend: 'stable'
      };
    }

    const results = this.stressTestResults;
    const totalTests = results.length;

    // Calculate averages
    const averageSuccessRate = results.reduce((sum, r) =>
      sum + ((r.metrics.successfulTasks / r.metrics.totalTasks) * 100), 0) / totalTests;

    const averageThroughput = results.reduce((sum, r) => sum + r.metrics.throughput, 0) / totalTests;
    const averageResponseTime = results.reduce((sum, r) => sum + r.metrics.averageResponseTime, 0) / totalTests;
    const averageSystemHealth = results.reduce((sum, r) => sum + r.systemMetrics.systemHealthScore, 0) / totalTests;

    // Determine trend (compare last 3 tests with previous 3)
    let recentTrend: 'improving' | 'declining' | 'stable' = 'stable';
    if (results.length >= 6) {
      const recent = results.slice(-3);
      const previous = results.slice(-6, -3);

      const recentAvgHealth = recent.reduce((sum, r) => sum + r.systemMetrics.systemHealthScore, 0) / 3;
      const previousAvgHealth = previous.reduce((sum, r) => sum + r.systemMetrics.systemHealthScore, 0) / 3;

      if (recentAvgHealth > previousAvgHealth + 5) {
        recentTrend = 'improving';
      } else if (recentAvgHealth < previousAvgHealth - 5) {
        recentTrend = 'declining';
      }
    }

    return {
      totalTests,
      averageSuccessRate,
      averageThroughput,
      averageResponseTime,
      averageSystemHealth,
      recentTrend,
      lastTestResult: results[results.length - 1]
    };
  }

  /**
   * Clear stress test results (for cleanup)
   */
  public clearStressTestResults(): void {
    console.log(`📊 AnalyticsService: Clearing ${this.stressTestResults.length} stress test results`);
    this.stressTestResults = [];
  }
}

// Global instance
let globalAnalyticsService: AnalyticsService | null = null

export function getAnalyticsService(): AnalyticsService {
  if (!globalAnalyticsService) {
    globalAnalyticsService = new AnalyticsService()
  }
  return globalAnalyticsService
}
