// services/chat-history.ts

import { getConfigStoreBrowser } from "@/components/background/config-store-browser"
import type { 
  AgentChatHistory, 
  AgentChatMessage, 
  SystemFeedbackMessage, 
  ChatHistoryStats,
  ChatPersistenceConfig 
} from "@/types/chat"

export class ChatHistoryService {
  private configStore = getConfigStoreBrowser()
  private initialized = false
  private currentWorkspaceId: string | null = null
  private config: ChatPersistenceConfig = {
    maxMessages: 1000,
    maxHistoryDays: 30,
    autoCleanup: true,
    enableSystemMessages: true,
    enableThreading: true,
    compressionEnabled: false
  }

  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      await this.configStore.initialize()
      this.currentWorkspaceId = await this.detectCurrentWorkspace()
      this.initialized = true
      console.log('ChatHistoryService initialized for workspace:', this.currentWorkspaceId)
    } catch (error) {
      console.error('Failed to initialize ChatHistoryService:', error)
      this.initialized = true // Continue with fallback
    }
  }

  /**
   * Detect current workspace based on various sources
   */
  private async detectCurrentWorkspace(): Promise<string> {
    try {
      // Try to get from current working directory
      if (typeof window !== 'undefined' && window.electronAPI?.getCurrentWorkingDirectory) {
        const cwd = await window.electronAPI.getCurrentWorkingDirectory()
        if (cwd) {
          return this.pathToWorkspaceId(cwd)
        }
      }

      // Try to get from localStorage
      const storedWorkspace = localStorage.getItem('synapse-current-workspace')
      if (storedWorkspace) {
        return storedWorkspace
      }

      // Try to get from project config
      const projects = await this.configStore.getAllProjects()
      if (projects.length > 0) {
        // Use the most recently updated project
        const recent = projects.sort((a, b) => 
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        )[0]
        return this.pathToWorkspaceId(recent.path)
      }

      // Fallback to default workspace
      return 'default-workspace'
    } catch (error) {
      console.warn('Failed to detect workspace, using default:', error)
      return 'default-workspace'
    }
  }

  /**
   * Convert file path to workspace ID
   */
  private pathToWorkspaceId(path: string): string {
    // Create a stable ID from the path
    const normalized = path.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()
    return `workspace-${normalized}`
  }

  /**
   * Get current workspace ID
   */
  getCurrentWorkspaceId(): string {
    return this.currentWorkspaceId || 'default-workspace'
  }

  /**
   * Load chat history for current workspace
   */
  async loadChatHistory(): Promise<AgentChatMessage[]> {
    await this.initialize()

    try {
      const workspaceId = this.getCurrentWorkspaceId()
      const historyKey = `agentChat.${workspaceId}.history`
      
      const history = await this.configStore.getGlobalSetting('agentChat', workspaceId, null)
      
      if (history && history.messages) {
        // Convert stored dates back to Date objects
        const messages = history.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))

        console.log(`Loaded ${messages.length} messages for workspace ${workspaceId}`)
        return messages
      }

      return []
    } catch (error) {
      console.error('Failed to load chat history:', error)
      return []
    }
  }

  /**
   * Save chat history for current workspace
   */
  async saveChatHistory(messages: AgentChatMessage[]): Promise<void> {
    await this.initialize()

    try {
      const workspaceId = this.getCurrentWorkspaceId()
      const now = new Date()

      // Calculate stats
      const totalTokensUsed = messages.reduce((sum, msg) => sum + (msg.tokensUsed || 0), 0)
      const totalCost = messages.reduce((sum, msg) => sum + (msg.cost || 0), 0)

      const history: AgentChatHistory = {
        workspaceId,
        workspacePath: await this.getWorkspacePath(),
        messages: messages.map(msg => ({
          ...msg,
          timestamp: msg.timestamp // Will be serialized as ISO string
        })),
        createdAt: now.toISOString(),
        updatedAt: now.toISOString(),
        messageCount: messages.length,
        totalTokensUsed,
        totalCost
      }

      await this.configStore.setGlobalSetting('agentChat', workspaceId, history)
      console.log(`Saved ${messages.length} messages for workspace ${workspaceId}`)
    } catch (error) {
      console.error('Failed to save chat history:', error)
    }
  }

  /**
   * Add system feedback message
   */
  async addSystemMessage(message: SystemFeedbackMessage): Promise<void> {
    if (!this.config.enableSystemMessages) return

    try {
      const currentMessages = await this.loadChatHistory()
      
      const systemMessage: AgentChatMessage = {
        id: message.id,
        content: message.content,
        role: "system",
        timestamp: message.timestamp,
        status: "completed",
        source: message.source,
        linkedToMessageId: message.linkedToMessageId,
        metadata: {
          severity: message.severity,
          ...message.metadata
        }
      }

      const updatedMessages = [...currentMessages, systemMessage]
      await this.saveChatHistory(updatedMessages)
    } catch (error) {
      console.error('Failed to add system message:', error)
    }
  }

  /**
   * Clear chat history for current workspace
   */
  async clearChatHistory(): Promise<void> {
    await this.initialize()

    try {
      const workspaceId = this.getCurrentWorkspaceId()
      await this.configStore.setGlobalSetting('agentChat', workspaceId, null)
      console.log(`Cleared chat history for workspace ${workspaceId}`)
    } catch (error) {
      console.error('Failed to clear chat history:', error)
    }
  }

  /**
   * Get workspace path
   */
  private async getWorkspacePath(): Promise<string> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI?.getCurrentWorkingDirectory) {
        return await window.electronAPI.getCurrentWorkingDirectory() || process.cwd()
      }
      return process.cwd()
    } catch (error) {
      return 'unknown'
    }
  }

  /**
   * Get chat history statistics
   */
  async getChatStats(): Promise<ChatHistoryStats> {
    const messages = await this.loadChatHistory()
    
    const agentMessages = messages.filter(m => m.role === 'agent')
    const agentCounts = agentMessages.reduce((acc, msg) => {
      const agent = msg.agentType || 'unknown'
      acc[agent] = (acc[agent] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const mostActiveAgent = Object.entries(agentCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none'

    const totalTokensUsed = messages.reduce((sum, msg) => sum + (msg.tokensUsed || 0), 0)
    const totalCost = messages.reduce((sum, msg) => sum + (msg.cost || 0), 0)

    // Calculate average response time (simplified)
    const responseTimes = messages
      .filter(m => m.role === 'agent' && m.metadata?.executionTime)
      .map(m => m.metadata!.executionTime as number)
    
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0

    return {
      totalMessages: messages.length,
      totalTokensUsed,
      totalCost,
      averageResponseTime,
      mostActiveAgent,
      sessionCount: 1, // Simplified for now
      lastActivity: messages.length > 0 ? messages[messages.length - 1].timestamp : new Date()
    }
  }
}

// Global instance
let globalChatHistoryService: ChatHistoryService | null = null

export function getChatHistoryService(): ChatHistoryService {
  if (!globalChatHistoryService) {
    globalChatHistoryService = new ChatHistoryService()
  }
  return globalChatHistoryService
}
