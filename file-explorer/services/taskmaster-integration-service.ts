// services/taskmaster-integration-service.ts
// Service for integrating with Taskmaster AI task management system

export interface TaskmasterTask {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  assignedAgent?: string;
  estimatedHours?: number;
  dependencies?: string[];
  tags?: string[];
  createdAt: number;
  updatedAt: number;
}

export interface TaskmasterProject {
  id: string;
  name: string;
  path: string;
  description?: string;
  tasks: TaskmasterTask[];
  createdAt: number;
  updatedAt: number;
}

export interface TaskmasterInitResult {
  success: boolean;
  projectId?: string;
  error?: string;
  tasksCreated?: number;
}

class TaskmasterIntegrationService {
  private projects: Map<string, TaskmasterProject> = new Map();
  private taskmasterPath = '.taskmaster';

  /**
   * Initialize Taskmaster for a project
   */
  async initializeForProject(projectPath: string, projectName: string): Promise<TaskmasterInitResult> {
    try {
      console.log(`🔧 Initializing Taskmaster for project: ${projectName}`);

      // Create .taskmaster directory
      const taskmasterDir = `${projectPath}/${this.taskmasterPath}`;
      
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        // Create taskmaster directory structure
        const dirResult = await (window as any).electronAPI.createFile(`${taskmasterDir}/tasks.json`, '[]');
        if (!dirResult.success) {
          return {
            success: false,
            error: `Failed to create taskmaster directory: ${dirResult.error}`
          };
        }

        // Create project configuration
        const projectConfig = {
          id: `project-${Date.now()}`,
          name: projectName,
          path: projectPath,
          description: `AI-managed project: ${projectName}`,
          tasks: [],
          createdAt: Date.now(),
          updatedAt: Date.now()
        };

        const configResult = await (window as any).electronAPI.createFile(
          `${taskmasterDir}/project.json`,
          JSON.stringify(projectConfig, null, 2)
        );

        if (!configResult.success) {
          return {
            success: false,
            error: `Failed to create project configuration: ${configResult.error}`
          };
        }

        // Store project in memory
        this.projects.set(projectPath, projectConfig);

        console.log(`✅ Taskmaster initialized for ${projectName}`);
        return {
          success: true,
          projectId: projectConfig.id,
          tasksCreated: 0
        };
      } else {
        // Web environment - store in localStorage
        const projectConfig = {
          id: `project-${Date.now()}`,
          name: projectName,
          path: projectPath,
          description: `AI-managed project: ${projectName}`,
          tasks: [],
          createdAt: Date.now(),
          updatedAt: Date.now()
        };

        localStorage.setItem(`taskmaster-${projectPath}`, JSON.stringify(projectConfig));
        this.projects.set(projectPath, projectConfig);

        return {
          success: true,
          projectId: projectConfig.id,
          tasksCreated: 0
        };
      }
    } catch (error) {
      console.error('Failed to initialize Taskmaster:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Load project from Taskmaster
   */
  async loadProject(projectPath: string): Promise<TaskmasterProject | null> {
    try {
      if (this.projects.has(projectPath)) {
        return this.projects.get(projectPath) || null;
      }

      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        const configPath = `${projectPath}/${this.taskmasterPath}/project.json`;
        const result = await (window as any).electronAPI.readFile(configPath);
        
        if (result.success) {
          const project = JSON.parse(result.content);
          this.projects.set(projectPath, project);
          return project;
        }
      } else {
        // Web environment
        const stored = localStorage.getItem(`taskmaster-${projectPath}`);
        if (stored) {
          const project = JSON.parse(stored);
          this.projects.set(projectPath, project);
          return project;
        }
      }

      return null;
    } catch (error) {
      console.warn('Failed to load Taskmaster project:', error);
      return null;
    }
  }

  /**
   * Add task to project
   */
  async addTask(projectPath: string, task: Omit<TaskmasterTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<TaskmasterTask | null> {
    try {
      const project = await this.loadProject(projectPath);
      if (!project) {
        console.warn('Project not found for adding task');
        return null;
      }

      const newTask: TaskmasterTask = {
        ...task,
        id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      project.tasks.push(newTask);
      project.updatedAt = Date.now();

      await this.saveProject(project);
      return newTask;
    } catch (error) {
      console.error('Failed to add task:', error);
      return null;
    }
  }

  /**
   * Update task status
   */
  async updateTaskStatus(projectPath: string, taskId: string, status: TaskmasterTask['status']): Promise<boolean> {
    try {
      const project = await this.loadProject(projectPath);
      if (!project) return false;

      const task = project.tasks.find(t => t.id === taskId);
      if (!task) return false;

      task.status = status;
      task.updatedAt = Date.now();
      project.updatedAt = Date.now();

      await this.saveProject(project);
      return true;
    } catch (error) {
      console.error('Failed to update task status:', error);
      return false;
    }
  }

  /**
   * Get project tasks
   */
  async getProjectTasks(projectPath: string): Promise<TaskmasterTask[]> {
    const project = await this.loadProject(projectPath);
    return project?.tasks || [];
  }

  /**
   * Save project to storage
   */
  private async saveProject(project: TaskmasterProject): Promise<void> {
    try {
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        const configPath = `${project.path}/${this.taskmasterPath}/project.json`;
        await (window as any).electronAPI.saveFile(configPath, JSON.stringify(project, null, 2));
        
        const tasksPath = `${project.path}/${this.taskmasterPath}/tasks.json`;
        await (window as any).electronAPI.saveFile(tasksPath, JSON.stringify(project.tasks, null, 2));
      } else {
        // Web environment
        localStorage.setItem(`taskmaster-${project.path}`, JSON.stringify(project));
      }

      this.projects.set(project.path, project);
    } catch (error) {
      console.error('Failed to save project:', error);
    }
  }

  /**
   * Get project statistics
   */
  async getProjectStats(projectPath: string): Promise<{
    totalTasks: number;
    completedTasks: number;
    pendingTasks: number;
    inProgressTasks: number;
    failedTasks: number;
  }> {
    const tasks = await this.getProjectTasks(projectPath);
    
    return {
      totalTasks: tasks.length,
      completedTasks: tasks.filter(t => t.status === 'completed').length,
      pendingTasks: tasks.filter(t => t.status === 'pending').length,
      inProgressTasks: tasks.filter(t => t.status === 'in_progress').length,
      failedTasks: tasks.filter(t => t.status === 'failed').length
    };
  }

  /**
   * Initialize the service
   */
  initialize(): void {
    console.log('✅ Taskmaster integration service initialized');
  }

  /**
   * Cleanup the service
   */
  cleanup(): void {
    this.projects.clear();
    console.log('✅ Taskmaster integration service cleaned up');
  }
}

// Export singleton instance
export const taskmasterIntegrationService = new TaskmasterIntegrationService();
