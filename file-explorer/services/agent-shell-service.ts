// services/agent-shell-service.ts
import { AgentExecutionService } from '../components/agents/agent-execution-service';
import { terminalEventBus, getAgentDisplayName } from '../components/terminal/terminal-event-bus';

export interface ShellExecutionResult {
  success: boolean;
  output: string;
  error?: string;
  executionTime?: number;
  agentId: string;
  command: string;
}

export interface ShellExecutionOptions {
  agentId: string;
  taskId?: string;
  cardId?: string;
  timeout?: number;
}

export class AgentShellService {
  private static instance: AgentShellService;
  private executionService: AgentExecutionService;

  private constructor() {
    this.executionService = AgentExecutionService.getInstance();
  }

  public static getInstance(): AgentShellService {
    if (!AgentShellService.instance) {
      AgentShellService.instance = new AgentShellService();
    }
    return AgentShellService.instance;
  }

  /**
   * ✅ Task 93: Execute shell command for agent with Kanban integration
   */
  public async runCommand(
    command: string, 
    options: ShellExecutionOptions
  ): Promise<ShellExecutionResult> {
    const { agentId, taskId, cardId } = options;
    const startTime = Date.now();

    try {
      console.log(`AgentShellService: Agent ${agentId} executing command: ${command}`);

      // ✅ Emit command start to terminal UI
      const agentDisplayName = getAgentDisplayName(agentId);
      terminalEventBus.emitSystemMessage(
        `[${agentDisplayName}] Starting Kanban task command: ${command}`,
        'info'
      );

      // Execute the command through the agent execution service
      const result = await this.executionService.executeShellCommand(agentId, command, taskId);

      const executionTime = Date.now() - startTime;

      if (result.success) {
        console.log(`AgentShellService: Command completed successfully for agent ${agentId}`);
        
        // ✅ Emit success to terminal UI
        terminalEventBus.emitSystemMessage(
          `[${agentDisplayName}] Kanban task command completed successfully`,
          'success'
        );

        return {
          success: true,
          output: result.output,
          executionTime,
          agentId,
          command
        };
      } else {
        console.error(`AgentShellService: Command failed for agent ${agentId}: ${result.error}`);
        
        // ✅ Emit error to terminal UI
        terminalEventBus.emitSystemMessage(
          `[${agentDisplayName}] Kanban task command failed: ${result.error}`,
          'error'
        );

        return {
          success: false,
          output: result.output,
          error: result.error,
          executionTime,
          agentId,
          command
        };
      }
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message : String(error);
      
      console.error(`AgentShellService: Exception during command execution for agent ${agentId}:`, error);
      
      // ✅ Emit exception to terminal UI
      const agentDisplayName = getAgentDisplayName(agentId);
      terminalEventBus.emitSystemMessage(
        `[${agentDisplayName}] Kanban task command exception: ${errorMsg}`,
        'error'
      );

      return {
        success: false,
        output: '',
        error: errorMsg,
        executionTime,
        agentId,
        command
      };
    }
  }

  /**
   * ✅ Task 93: Validate shell command before execution
   */
  public validateCommand(command: string): { valid: boolean; reason?: string } {
    if (!command || typeof command !== 'string') {
      return { valid: false, reason: 'Command must be a non-empty string' };
    }

    const trimmedCommand = command.trim();
    if (!trimmedCommand) {
      return { valid: false, reason: 'Command cannot be empty or whitespace only' };
    }

    // ✅ Basic security validation (additional to main process validation)
    const dangerousPatterns = [
      /rm\s+-rf\s*\//, // rm -rf /
      /sudo\s+rm/, // sudo rm
      /shutdown/, // shutdown
      /reboot/, // reboot
      /mkfs/, // format filesystem
      /fdisk/, // disk partitioning
      /dd\s+if=/, // disk duplication
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(trimmedCommand.toLowerCase())) {
        return { valid: false, reason: 'Command contains potentially dangerous operations' };
      }
    }

    return { valid: true };
  }

  /**
   * ✅ Task 93: Get execution statistics
   */
  public getExecutionStats(): {
    totalCommands: number;
    successfulCommands: number;
    failedCommands: number;
    averageExecutionTime: number;
  } {
    // This would be implemented with actual tracking in a production system
    return {
      totalCommands: 0,
      successfulCommands: 0,
      failedCommands: 0,
      averageExecutionTime: 0
    };
  }

  /**
   * ✅ Task 93: Check if agent shell execution is available
   */
  public isAvailable(): boolean {
    return typeof window !== 'undefined' && 
           window.electronAPI?.terminal?.agentCommand !== undefined;
  }

  /**
   * ✅ Task 93: Get supported shell commands for UI hints
   */
  public getSupportedCommands(): string[] {
    return [
      'ls -la',
      'pwd',
      'echo "message"',
      'cat filename.txt',
      'whoami',
      'date',
      'npm --version',
      'node --version',
      'git status',
      'git log --oneline -5'
    ];
  }

  /**
   * ✅ Task 93: Get dangerous commands for UI warnings
   */
  public getDangerousCommands(): string[] {
    return [
      'rm -rf',
      'sudo rm',
      'shutdown',
      'reboot',
      'mkfs',
      'fdisk',
      'dd if=',
      'format'
    ];
  }
}

// Export singleton instance
export const agentShellService = AgentShellService.getInstance();

// Export types for external use
export type { ShellExecutionResult, ShellExecutionOptions };
