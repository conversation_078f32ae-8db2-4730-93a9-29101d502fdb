// systems/stress/StressTestRunner.ts

import { CompleteAgentManager } from '../../components/agents/agent-manager-complete';
import { SettingsManager, SystemSettings } from '../../components/settings/settings-manager';
import { getGlobalConcurrencyManager } from '../../lib/utils/concurrency-manager';
import { withTimeout } from '../../lib/utils/timeout';

export interface StressTestConfig {
  agents: string[];                    // agent IDs to test
  taskType: "simple" | "complex";      // simulated prompt type
  duration: number;                    // in seconds
  concurrency: number;                 // number of parallel tasks
  maxTasks?: number;                   // optional task limit
}

export interface StressTestResult {
  testId: string;
  config: StressTestConfig;
  startTime: number;
  endTime: number;
  duration: number;
  metrics: {
    totalTasks: number;
    successfulTasks: number;
    failedTasks: number;
    timeoutTasks: number;
    throughput: number;                // tasks per second
    averageResponseTime: number;       // milliseconds
    maxResponseTime: number;
    minResponseTime: number;
    failureRate: number;               // percentage
    tokenUsage: {
      total: number;
      average: number;
      max: number;
    };
    memoryFootprint?: {
      initial: number;
      peak: number;
      final: number;
    };
  };
  taskResults: TaskExecutionResult[];
  systemMetrics: {
    concurrencyLimitRespected: boolean;
    maxConcurrentTasksUsed: number;
    queuePeakLength: number;
    systemHealthScore: number;
  };
  errors: StressTestError[];
  warnings: string[];
}

export interface TaskExecutionResult {
  taskId: string;
  agentId: string;
  startTime: number;
  endTime: number;
  duration: number;
  status: 'success' | 'failure' | 'timeout';
  tokensUsed?: number;
  errorMessage?: string;
  responseSize?: number;
}

export interface StressTestError {
  type: 'execution' | 'timeout' | 'system' | 'validation';
  message: string;
  taskId?: string;
  agentId?: string;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * StressTestRunner - Comprehensive stress testing for the Agent System
 *
 * Features:
 * - Real task execution through CompleteAgentManager
 * - Respects system concurrency limits
 * - Multiple task variation types
 * - Comprehensive metrics collection
 * - Integration with metrics service
 * - System safety guarantees
 */
export class StressTestRunner {
  private agentManager: CompleteAgentManager;
  private settingsManager: SettingsManager;
  private isRunning: boolean = false;
  private currentTestId: string | null = null;
  private taskCounter: number = 0;
  private concurrencyManager = getGlobalConcurrencyManager(5); // Will be updated from settings

  constructor(agentManager: CompleteAgentManager, settingsManager: SettingsManager) {
    this.agentManager = agentManager;
    this.settingsManager = settingsManager;

    // Update concurrency manager with current settings
    const systemSettings = this.settingsManager.getSystemSettings();
    this.concurrencyManager.setLimit(systemSettings.maxConcurrentTasks);
  }

  /**
   * Run a comprehensive stress test
   */
  public async runTest(config: StressTestConfig): Promise<StressTestResult> {
    if (this.isRunning) {
      throw new Error('Stress test is already running. Only one test can run at a time.');
    }

    // Validate configuration
    this.validateConfig(config);

    // Ensure test mode is enabled
    const systemSettings = this.settingsManager.getSystemSettings();
    if (!systemSettings.testModeEnabled) {
      throw new Error('Stress testing requires testModeEnabled to be true in system settings.');
    }

    this.isRunning = true;
    const testId = this.generateTestId();
    this.currentTestId = testId;

    console.log(`🧪 StressTestRunner: Starting test ${testId}`, config);

    const startTime = Date.now();
    const result: StressTestResult = {
      testId,
      config,
      startTime,
      endTime: 0,
      duration: 0,
      metrics: {
        totalTasks: 0,
        successfulTasks: 0,
        failedTasks: 0,
        timeoutTasks: 0,
        throughput: 0,
        averageResponseTime: 0,
        maxResponseTime: 0,
        minResponseTime: Infinity,
        failureRate: 0,
        tokenUsage: { total: 0, average: 0, max: 0 },
      },
      taskResults: [],
      systemMetrics: {
        concurrencyLimitRespected: true,
        maxConcurrentTasksUsed: 0,
        queuePeakLength: 0,
        systemHealthScore: 100,
      },
      errors: [],
      warnings: []
    };

    try {
      // Capture initial memory if available
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        result.metrics.memoryFootprint = {
          initial: (performance as any).memory.usedJSHeapSize,
          peak: (performance as any).memory.usedJSHeapSize,
          final: 0
        };
      }

      // Generate test tasks
      const tasks = this.generateTestTasks(config);
      result.metrics.totalTasks = tasks.length;

      console.log(`🧪 Generated ${tasks.length} test tasks for ${config.agents.length} agents`);

      // Execute stress test
      await this.executeStressTest(tasks, config, result);

      // Calculate final metrics
      this.calculateFinalMetrics(result);

      result.endTime = Date.now();
      result.duration = result.endTime - result.startTime;

      console.log(`🧪 StressTestRunner: Test ${testId} completed`, {
        duration: result.duration,
        totalTasks: result.metrics.totalTasks,
        successRate: ((result.metrics.successfulTasks / result.metrics.totalTasks) * 100).toFixed(2) + '%',
        throughput: result.metrics.throughput.toFixed(2) + ' tasks/sec'
      });

      return result;

    } catch (error) {
      result.errors.push({
        type: 'system',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
        severity: 'critical'
      });

      result.endTime = Date.now();
      result.duration = result.endTime - result.startTime;

      throw error;
    } finally {
      this.isRunning = false;
      this.currentTestId = null;
    }
  }

  /**
   * Validate stress test configuration
   */
  private validateConfig(config: StressTestConfig): void {
    if (!config.agents || config.agents.length === 0) {
      throw new Error('At least one agent must be specified for stress testing');
    }

    if (config.concurrency <= 0) {
      throw new Error('Concurrency must be greater than 0');
    }

    if (config.duration <= 0) {
      throw new Error('Duration must be greater than 0');
    }

    const systemSettings = this.settingsManager.getSystemSettings();
    if (config.concurrency > systemSettings.maxConcurrentTasks) {
      throw new Error(`Concurrency (${config.concurrency}) cannot exceed system limit (${systemSettings.maxConcurrentTasks})`);
    }

    // Validate agent IDs exist
    for (const agentId of config.agents) {
      const agent = this.agentManager.getAgent(agentId);
      if (!agent) {
        throw new Error(`Agent '${agentId}' not found in agent manager`);
      }
    }
  }

  /**
   * Generate test tasks based on configuration
   */
  private generateTestTasks(config: StressTestConfig): Array<{
    id: string;
    agentId: string;
    prompt: string;
    type: string;
    expectedTokens: number;
    priority: 'low' | 'medium' | 'high';
  }> {
    const tasks = [];
    const maxTasks = config.maxTasks || Math.ceil(config.duration / 1000 * config.concurrency);

    for (let i = 0; i < maxTasks; i++) {
      const agentId = config.agents[i % config.agents.length];
      const taskVariation = this.getTaskVariation(config.taskType, i);

      tasks.push({
        id: `stress-task-${this.generateTaskId()}`,
        agentId,
        prompt: taskVariation.prompt,
        type: taskVariation.type,
        expectedTokens: taskVariation.expectedTokens,
        priority: taskVariation.priority
      });
    }

    return tasks;
  }

  /**
   * Get task variation based on type and index
   */
  private getTaskVariation(taskType: "simple" | "complex", index: number): {
    prompt: string;
    type: string;
    expectedTokens: number;
    priority: 'low' | 'medium' | 'high';
  } {
    const variations = taskType === "simple" ? this.getSimpleTaskVariations() : this.getComplexTaskVariations();
    const variation = variations[index % variations.length];

    // Add some randomization for edge cases
    if (index % 10 === 0) {
      // Every 10th task: max token test
      return {
        ...variation,
        prompt: variation.prompt + " Please provide a very detailed and comprehensive response with examples and explanations.",
        expectedTokens: 4000,
        type: 'max-tokens'
      };
    } else if (index % 15 === 0) {
      // Every 15th task: malformed prompt test
      return {
        prompt: "Invalid prompt with special chars: @#$%^&*(){}[]|\\:;\"'<>?,./`~",
        type: 'malformed',
        expectedTokens: 100,
        priority: 'low'
      };
    } else if (index % 20 === 0) {
      // Every 20th task: long response generation
      return {
        prompt: "Generate a comprehensive technical documentation for a complex software system including architecture, API references, deployment guides, and troubleshooting sections.",
        type: 'long-response',
        expectedTokens: 3500,
        priority: 'high'
      };
    }

    return variation;
  }

  /**
   * Get simple task variations
   */
  private getSimpleTaskVariations(): Array<{
    prompt: string;
    type: string;
    expectedTokens: number;
    priority: 'low' | 'medium' | 'high';
  }> {
    return [
      {
        prompt: "What is the current time?",
        type: 'simple-query',
        expectedTokens: 50,
        priority: 'low'
      },
      {
        prompt: "Explain what TypeScript is in one sentence.",
        type: 'simple-explanation',
        expectedTokens: 100,
        priority: 'medium'
      },
      {
        prompt: "List 5 programming languages.",
        type: 'simple-list',
        expectedTokens: 75,
        priority: 'low'
      },
      {
        prompt: "Calculate 15 + 27.",
        type: 'simple-math',
        expectedTokens: 25,
        priority: 'low'
      },
      {
        prompt: "What does API stand for?",
        type: 'simple-acronym',
        expectedTokens: 50,
        priority: 'low'
      }
    ];
  }

  /**
   * Get complex task variations
   */
  private getComplexTaskVariations(): Array<{
    prompt: string;
    type: string;
    expectedTokens: number;
    priority: 'low' | 'medium' | 'high';
  }> {
    return [
      {
        prompt: "Design a scalable microservices architecture for an e-commerce platform with user management, inventory, payments, and notifications.",
        type: 'architecture-design',
        expectedTokens: 1500,
        priority: 'high'
      },
      {
        prompt: "Write a React component with TypeScript that implements a data table with sorting, filtering, and pagination.",
        type: 'code-generation',
        expectedTokens: 1200,
        priority: 'high'
      },
      {
        prompt: "Analyze the performance implications of different database indexing strategies for a high-traffic application.",
        type: 'technical-analysis',
        expectedTokens: 1000,
        priority: 'medium'
      },
      {
        prompt: "Create a comprehensive testing strategy for a distributed system including unit, integration, and end-to-end tests.",
        type: 'strategy-planning',
        expectedTokens: 1300,
        priority: 'high'
      },
      {
        prompt: "Debug this error: 'Cannot read property of undefined' in a React application and provide multiple solution approaches.",
        type: 'debugging-help',
        expectedTokens: 800,
        priority: 'medium'
      }
    ];
  }

  /**
   * Execute the stress test with real task submission
   */
  private async executeStressTest(
    tasks: Array<{
      id: string;
      agentId: string;
      prompt: string;
      type: string;
      expectedTokens: number;
      priority: 'low' | 'medium' | 'high';
    }>,
    config: StressTestConfig,
    result: StressTestResult
  ): Promise<void> {
    const startTime = Date.now();
    const endTime = startTime + (config.duration * 1000);
    let taskIndex = 0;
    let activeTasks = 0;
    let maxConcurrentTasks = 0;

    console.log(`🧪 Starting stress test execution with ${config.concurrency} concurrent tasks`);

    // Create a promise pool for concurrent execution
    const executeTask = async (task: typeof tasks[0]): Promise<void> => {
      activeTasks++;
      maxConcurrentTasks = Math.max(maxConcurrentTasks, activeTasks);

      const taskStartTime = Date.now();
      const taskResult: TaskExecutionResult = {
        taskId: task.id,
        agentId: task.agentId,
        startTime: taskStartTime,
        endTime: 0,
        duration: 0,
        status: 'failure'
      };

      try {
        // Use real agent manager to submit task
        const systemSettings = this.settingsManager.getSystemSettings();
        const timeoutMs = systemSettings.defaultTimeout || 30000;

        console.log(`🧪 Submitting task ${task.id} to agent ${task.agentId}`);

        // Submit task through real agent manager with timeout
        const taskPromise = this.agentManager.submitTask({
          id: task.id,
          prompt: task.prompt,
          agentId: task.agentId,
          priority: task.priority,
          metadata: {
            stressTest: true,
            testId: this.currentTestId,
            expectedTokens: task.expectedTokens,
            taskType: task.type
          }
        });

        const response = await withTimeout(taskPromise, timeoutMs);

        taskResult.endTime = Date.now();
        taskResult.duration = taskResult.endTime - taskResult.startTime;
        taskResult.status = 'success';

        // Extract token usage if available
        if (response && typeof response === 'object' && 'tokensUsed' in response) {
          taskResult.tokensUsed = response.tokensUsed as number;
        }

        // Extract response size
        if (response && typeof response === 'string') {
          taskResult.responseSize = response.length;
        } else if (response && typeof response === 'object' && 'content' in response) {
          taskResult.responseSize = String(response.content).length;
        }

        result.metrics.successfulTasks++;

      } catch (error) {
        taskResult.endTime = Date.now();
        taskResult.duration = taskResult.endTime - taskResult.startTime;

        if (error instanceof Error && error.message.includes('timeout')) {
          taskResult.status = 'timeout';
          result.metrics.timeoutTasks++;
        } else {
          taskResult.status = 'failure';
          result.metrics.failedTasks++;
        }

        taskResult.errorMessage = error instanceof Error ? error.message : 'Unknown error';

        result.errors.push({
          type: 'execution',
          message: taskResult.errorMessage,
          taskId: task.id,
          agentId: task.agentId,
          timestamp: Date.now(),
          severity: taskResult.status === 'timeout' ? 'medium' : 'high'
        });

        console.warn(`🧪 Task ${task.id} failed:`, taskResult.errorMessage);
      } finally {
        activeTasks--;
        result.taskResults.push(taskResult);

        // Update memory footprint if available
        if (result.metrics.memoryFootprint && typeof performance !== 'undefined' && (performance as any).memory) {
          const currentMemory = (performance as any).memory.usedJSHeapSize;
          result.metrics.memoryFootprint.peak = Math.max(result.metrics.memoryFootprint.peak, currentMemory);
        }
      }
    };

    // Execute tasks with controlled concurrency
    const taskPromises: Promise<void>[] = [];

    while (Date.now() < endTime && taskIndex < tasks.length) {
      // Maintain concurrency level
      while (taskPromises.length < config.concurrency && taskIndex < tasks.length && Date.now() < endTime) {
        const task = tasks[taskIndex++];
        const taskPromise = executeTask(task);
        taskPromises.push(taskPromise);

        // Remove completed promises
        taskPromise.finally(() => {
          const index = taskPromises.indexOf(taskPromise);
          if (index > -1) {
            taskPromises.splice(index, 1);
          }
        });
      }

      // Wait a bit before checking again
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Wait for all remaining tasks to complete
    await Promise.all(taskPromises);

    // Update system metrics
    result.systemMetrics.maxConcurrentTasksUsed = maxConcurrentTasks;
    result.systemMetrics.concurrencyLimitRespected = maxConcurrentTasks <= config.concurrency;
    result.systemMetrics.queuePeakLength = Math.max(0, taskIndex - result.taskResults.length);

    console.log(`🧪 Stress test execution completed. Processed ${result.taskResults.length} tasks`);
  }

  /**
   * Calculate final metrics from task results
   */
  private calculateFinalMetrics(result: StressTestResult): void {
    const taskResults = result.taskResults;

    if (taskResults.length === 0) {
      result.warnings.push('No tasks were executed during the stress test');
      return;
    }

    // Calculate response times
    const responseTimes = taskResults.map(task => task.duration);
    result.metrics.averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    result.metrics.maxResponseTime = Math.max(...responseTimes);
    result.metrics.minResponseTime = Math.min(...responseTimes);

    // Calculate throughput (tasks per second)
    result.metrics.throughput = (result.metrics.totalTasks / result.duration) * 1000;

    // Calculate failure rate
    result.metrics.failureRate = (result.metrics.failedTasks / result.metrics.totalTasks) * 100;

    // Calculate token usage
    const tokenUsages = taskResults
      .filter(task => task.tokensUsed !== undefined)
      .map(task => task.tokensUsed!);

    if (tokenUsages.length > 0) {
      result.metrics.tokenUsage.total = tokenUsages.reduce((sum, tokens) => sum + tokens, 0);
      result.metrics.tokenUsage.average = result.metrics.tokenUsage.total / tokenUsages.length;
      result.metrics.tokenUsage.max = Math.max(...tokenUsages);
    }

    // Calculate system health score
    const successRate = (result.metrics.successfulTasks / result.metrics.totalTasks) * 100;
    const timeoutPenalty = (result.metrics.timeoutTasks / result.metrics.totalTasks) * 20;
    const concurrencyPenalty = result.systemMetrics.concurrencyLimitRespected ? 0 : 30;

    result.systemMetrics.systemHealthScore = Math.max(0, 100 - (100 - successRate) - timeoutPenalty - concurrencyPenalty);

    // Update final memory footprint
    if (result.metrics.memoryFootprint && typeof performance !== 'undefined' && (performance as any).memory) {
      result.metrics.memoryFootprint.final = (performance as any).memory.usedJSHeapSize;
    }

    console.log(`🧪 Final metrics calculated:`, {
      successRate: successRate.toFixed(2) + '%',
      averageResponseTime: result.metrics.averageResponseTime.toFixed(2) + 'ms',
      throughput: result.metrics.throughput.toFixed(2) + ' tasks/sec',
      systemHealthScore: result.systemMetrics.systemHealthScore.toFixed(1)
    });
  }

  /**
   * Generate unique test ID
   */
  private generateTestId(): string {
    return `stress-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique task ID
   */
  private generateTaskId(): string {
    return `${Date.now()}-${++this.taskCounter}`;
  }

  /**
   * Get current test status
   */
  public getStatus(): {
    isRunning: boolean;
    currentTestId: string | null;
  } {
    return {
      isRunning: this.isRunning,
      currentTestId: this.currentTestId
    };
  }

  /**
   * Stop current test (if running)
   */
  public async stopTest(): Promise<void> {
    if (!this.isRunning) {
      throw new Error('No stress test is currently running');
    }

    console.log(`🧪 Stopping stress test ${this.currentTestId}`);

    // The test will naturally stop when the duration expires or tasks complete
    // This method provides a way to check status and potentially implement early termination
    this.isRunning = false;
    this.currentTestId = null;
  }
}