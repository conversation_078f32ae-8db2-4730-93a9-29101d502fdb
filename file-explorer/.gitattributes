# Git attributes for better diffing and handling

# Ensure JSON files are treated as text and get proper line-by-line diffs
*.json text eol=lf diff=json

# Model registry snapshots should be tracked line-by-line for better diffing
snapshots/*.json text eol=lf diff=json

# TypeScript and JavaScript files
*.ts text eol=lf
*.tsx text eol=lf
*.js text eol=lf
*.jsx text eol=lf

# Markdown files
*.md text eol=lf

# Configuration files
*.yml text eol=lf
*.yaml text eol=lf
*.toml text eol=lf
*.ini text eol=lf

# Ensure shell scripts have proper line endings
*.sh text eol=lf

# Binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text eol=lf
