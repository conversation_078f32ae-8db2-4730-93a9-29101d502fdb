#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit validation..."

# Validate model metadata
echo "📋 Validating model metadata..."
npm run validate:models

if [ $? -ne 0 ]; then
  echo "❌ Model metadata validation failed!"
  echo "🔧 Please fix all violations before committing."
  exit 1
fi

echo "✅ Model metadata validation passed!"

# Check for forbidden content (temporarily disabled for development)
# echo "🚫 Checking for forbidden models and test logic..."
# npm run lint:forbidden

# if [ $? -ne 0 ]; then
#   echo "❌ Forbidden content detected!"
#   echo "🔧 Please remove all test/mock/placeholder content before committing."
#   exit 1
# fi

echo "✅ Forbidden content check skipped (development mode)"

# Export model registry snapshot
echo "📸 Exporting model registry snapshot..."
npm run export:models

if [ $? -ne 0 ]; then
  echo "❌ Model registry snapshot export failed!"
  echo "🔧 Please check the export script and try again."
  exit 1
fi

echo "✅ Model registry snapshot exported!"

# Optional: Run linting
echo "🧹 Running linter..."
npm run lint

if [ $? -ne 0 ]; then
  echo "⚠️  Linting issues found, but allowing commit to proceed."
  echo "💡 Please fix linting issues when possible."
fi

echo "🎉 Pre-commit checks completed!"
