const fs = require('fs');
const path = require('path');

// Check if the out directory exists
const outDir = path.join(__dirname, 'out');
console.log('Checking if out directory exists:', outDir);
if (fs.existsSync(outDir)) {
  console.log('✅ Out directory exists');
  
  // Check if index.html exists
  const indexPath = path.join(outDir, 'index.html');
  console.log('Checking if index.html exists:', indexPath);
  if (fs.existsSync(indexPath)) {
    console.log('✅ index.html exists');
    
    // Check the content of index.html
    const indexContent = fs.readFileSync(indexPath, 'utf-8');
    console.log('index.html size:', indexContent.length, 'bytes');
    
    // Check if it contains expected content
    if (indexContent.includes('<html') && indexContent.includes('<body') && indexContent.includes('<script')) {
      console.log('✅ index.html contains expected content');
    } else {
      console.log('❌ index.html does not contain expected content');
    }
  } else {
    console.log('❌ index.html does not exist');
  }
  
  // List files in the out directory
  console.log('\nFiles in out directory:');
  const files = fs.readdirSync(outDir);
  files.forEach(file => {
    const filePath = path.join(outDir, file);
    const stats = fs.statSync(filePath);
    if (stats.isDirectory()) {
      console.log(` - ${file}/ (directory)`);
      
      // List files in subdirectory
      try {
        const subFiles = fs.readdirSync(filePath);
        subFiles.slice(0, 5).forEach(subFile => {
          console.log(`   - ${subFile}`);
        });
        if (subFiles.length > 5) {
          console.log(`   - ... and ${subFiles.length - 5} more files`);
        }
      } catch (err) {
        console.log(`   Error reading directory: ${err.message}`);
      }
    } else {
      console.log(` - ${file} (${stats.size} bytes)`);
    }
  });
  
  // Check for _next directory
  const nextDir = path.join(outDir, '_next');
  if (fs.existsSync(nextDir)) {
    console.log('\n✅ _next directory exists');
    
    // Check for static directory
    const staticDir = path.join(nextDir, 'static');
    if (fs.existsSync(staticDir)) {
      console.log('✅ _next/static directory exists');
      
      // List subdirectories in static
      try {
        const staticSubDirs = fs.readdirSync(staticDir);
        console.log('Subdirectories in _next/static:');
        staticSubDirs.forEach(dir => {
          console.log(` - ${dir}`);
        });
      } catch (err) {
        console.log(`Error reading _next/static directory: ${err.message}`);
      }
    } else {
      console.log('❌ _next/static directory does not exist');
    }
  } else {
    console.log('\n❌ _next directory does not exist');
  }
} else {
  console.log('❌ Out directory does not exist');
}

// Check if the .next directory exists
const dotNextDir = path.join(__dirname, '.next');
console.log('\nChecking if .next directory exists:', dotNextDir);
if (fs.existsSync(dotNextDir)) {
  console.log('✅ .next directory exists');
  
  // Check for server directory
  const serverDir = path.join(dotNextDir, 'server');
  if (fs.existsSync(serverDir)) {
    console.log('✅ .next/server directory exists');
    
    // Check for app-paths-manifest.json
    const manifestPath = path.join(serverDir, 'app-paths-manifest.json');
    if (fs.existsSync(manifestPath)) {
      console.log('✅ app-paths-manifest.json exists');
      
      // Read the manifest
      try {
        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));
        console.log('app-paths-manifest.json content:', JSON.stringify(manifest, null, 2));
      } catch (err) {
        console.log(`Error reading app-paths-manifest.json: ${err.message}`);
      }
    } else {
      console.log('❌ app-paths-manifest.json does not exist');
    }
  } else {
    console.log('❌ .next/server directory does not exist');
  }
} else {
  console.log('❌ .next directory does not exist');
}

console.log('\nBuild check complete.');
