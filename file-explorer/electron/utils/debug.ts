// electron/utils/debug.ts

/**
 * ✅ Electron-side Debug Mode Utility System
 * Provides controlled diagnostic output for Electron main process
 */

// Global debug state
let globalDebugMode = false;

/**
 * ✅ Set global debug mode
 */
export function setGlobalDebugMode(enabled: boolean): void {
  globalDebugMode = enabled;
}

/**
 * ✅ Get current debug mode state
 */
export function getDebugMode(): boolean {
  return globalDebugMode;
}

/**
 * ✅ Main debug logging function
 */
export function debugLog(...args: any[]): void {
  if (globalDebugMode) {
    console.debug('[AgentSystem DEBUG]', ...args);
  }
}

/**
 * ✅ Debug logging with category
 */
export function debugLogCategory(category: string, ...args: any[]): void {
  if (globalDebugMode) {
    console.debug(`[AgentSystem DEBUG:${category}]`, ...args);
  }
}

/**
 * ✅ Debug logging for LLM operations
 */
export function debugLLM(...args: any[]): void {
  debugLogCategory('LLM', ...args);
}

/**
 * ✅ Debug logging for Agent operations
 */
export function debugAgent(...args: any[]): void {
  debugLogCategory('AGENT', ...args);
}

/**
 * ✅ Debug logging for Concurrency operations
 */
export function debugConcurrency(...args: any[]): void {
  debugLogCategory('CONCURRENCY', ...args);
}

/**
 * ✅ Debug logging for File operations
 */
export function debugFile(...args: any[]): void {
  debugLogCategory('FILE', ...args);
}

/**
 * ✅ Debug logging for IPC operations
 */
export function debugIPC(...args: any[]): void {
  debugLogCategory('IPC', ...args);
}

/**
 * ✅ Debug logging for Settings operations
 */
export function debugSettings(...args: any[]): void {
  debugLogCategory('SETTINGS', ...args);
}

/**
 * ✅ Debug logging with timing information
 */
export function debugTiming(label: string, startTime: number, ...args: any[]): void {
  if (globalDebugMode) {
    const duration = Date.now() - startTime;
    console.debug(`[AgentSystem DEBUG:TIMING] ${label} took ${duration}ms`, ...args);
  }
}

/**
 * ✅ Debug logging for errors
 */
export function debugError(error: any, context?: string, ...args: any[]): void {
  if (globalDebugMode) {
    const contextStr = context ? `:${context}` : '';
    console.debug(`[AgentSystem DEBUG:ERROR${contextStr}]`, error, ...args);
  }
}

/**
 * ✅ Debug logging for warnings
 */
export function debugWarn(...args: any[]): void {
  if (globalDebugMode) {
    console.debug('[AgentSystem DEBUG:WARN]', ...args);
  }
}

/**
 * ✅ Debug logging for performance metrics
 */
export function debugPerformance(metric: string, value: number, unit = 'ms', ...args: any[]): void {
  if (globalDebugMode) {
    console.debug(`[AgentSystem DEBUG:PERF] ${metric}: ${value}${unit}`, ...args);
  }
}

/**
 * ✅ Debug logging for network requests
 */
export function debugNetwork(method: string, url: string, status?: number, ...args: any[]): void {
  if (globalDebugMode) {
    const statusStr = status ? ` (${status})` : '';
    console.debug(`[AgentSystem DEBUG:NETWORK] ${method} ${url}${statusStr}`, ...args);
  }
}

/**
 * ✅ Debug logging for state changes
 */
export function debugState(component: string, oldState: any, newState: any, ...args: any[]): void {
  if (globalDebugMode) {
    console.debug(`[AgentSystem DEBUG:STATE:${component}]`, {
      from: oldState,
      to: newState,
      ...args
    });
  }
}
