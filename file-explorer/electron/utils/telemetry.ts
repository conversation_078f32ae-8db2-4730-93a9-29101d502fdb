// electron/utils/telemetry.ts

/**
 * ✅ Electron-side Privacy-Aware Telemetry System
 * Collects anonymized usage data for Electron main process
 * GDPR compliant - no PII, user input, or file content collected
 */

// Global telemetry state
let globalTelemetryEnabled = false;
let telemetryQueue: TelemetryEvent[] = [];
let isProcessingQueue = false;

export interface TelemetryEvent {
  eventName: string;
  data: Record<string, any>;
  timestamp: number;
  sessionId: string;
}

export interface TelemetryConfig {
  endpoint: string;
  batchSize: number;
  flushInterval: number;
  maxQueueSize: number;
}

const defaultConfig: TelemetryConfig = {
  endpoint: 'http://localhost:4444/api/telemetry',
  batchSize: 10,
  flushInterval: 30000, // 30 seconds
  maxQueueSize: 100
};

// Generate session ID (not tied to user identity)
const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

/**
 * ✅ Set global telemetry mode
 */
export function setGlobalTelemetryEnabled(enabled: boolean): void {
  globalTelemetryEnabled = enabled;
  if (!enabled) {
    // Clear queue when disabled
    telemetryQueue = [];
  }
}

/**
 * ✅ Get current telemetry enabled state
 */
export function getTelemetryEnabled(): boolean {
  return globalTelemetryEnabled;
}

/**
 * ✅ Sanitize data to remove PII and sensitive information
 */
function sanitizeData(data: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    // Skip sensitive keys
    if (isSensitiveKey(key)) {
      continue;
    }
    
    // Sanitize values
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      sanitized[key] = value;
    } else if (Array.isArray(value)) {
      sanitized[key] = value.length; // Only store array length
    } else if (value && typeof value === 'object') {
      sanitized[key] = sanitizeData(value); // Recursive sanitization
    }
  }
  
  return sanitized;
}

/**
 * ✅ Check if a key contains sensitive information
 */
function isSensitiveKey(key: string): boolean {
  const sensitiveKeys = [
    'password', 'token', 'key', 'secret', 'auth', 'credential',
    'email', 'name', 'username', 'user', 'content', 'input', 
    'output', 'response', 'prompt', 'message', 'text', 'code',
    'file', 'path', 'url', 'ip', 'address', 'location'
  ];
  
  const lowerKey = key.toLowerCase();
  return sensitiveKeys.some(sensitive => lowerKey.includes(sensitive));
}

/**
 * ✅ Sanitize string values
 */
function sanitizeString(value: string): string {
  // Only keep length and basic characteristics
  return `string_length_${value.length}`;
}

/**
 * ✅ Main telemetry tracking function
 */
export function trackEvent(eventName: string, data: Record<string, any> = {}): void {
  if (!globalTelemetryEnabled) {
    return;
  }

  try {
    // Sanitize data to remove PII
    const sanitizedData = sanitizeData(data);
    
    const event: TelemetryEvent = {
      eventName,
      data: sanitizedData,
      timestamp: Date.now(),
      sessionId
    };

    // Add to queue
    telemetryQueue.push(event);
    
    // Limit queue size
    if (telemetryQueue.length > defaultConfig.maxQueueSize) {
      telemetryQueue = telemetryQueue.slice(-defaultConfig.maxQueueSize);
    }
    
    // Process queue if batch size reached
    if (telemetryQueue.length >= defaultConfig.batchSize) {
      processQueue();
    }
  } catch (error) {
    // Telemetry must fail silently
    console.debug('Telemetry error (silent):', error);
  }
}

/**
 * ✅ Process telemetry queue
 */
async function processQueue(): Promise<void> {
  if (!globalTelemetryEnabled || isProcessingQueue || telemetryQueue.length === 0) {
    return;
  }

  isProcessingQueue = true;
  const eventsToSend = telemetryQueue.splice(0, defaultConfig.batchSize);

  try {
    // Use Node.js fetch (available in Node.js 18+)
    if (typeof fetch !== 'undefined') {
      await fetch(defaultConfig.endpoint, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'X-Telemetry-Session': sessionId
        },
        body: JSON.stringify({ events: eventsToSend })
      });
    }
  } catch (error) {
    // Telemetry must fail silently - don't re-queue events
    console.debug('Telemetry send failed (silent):', error);
  } finally {
    isProcessingQueue = false;
  }
}

/**
 * ✅ Flush all queued events
 */
export async function flushTelemetry(): Promise<void> {
  if (!globalTelemetryEnabled) {
    return;
  }
  
  while (telemetryQueue.length > 0) {
    await processQueue();
  }
}

/**
 * ✅ Track LLM operation events
 */
export function trackLLMEvent(provider: string, operation: string, data: Record<string, any> = {}): void {
  trackEvent('llm_operation', {
    provider,
    operation,
    ...data
  });
}

/**
 * ✅ Track performance metrics
 */
export function trackPerformance(metric: string, value: number, unit = 'ms', context?: string): void {
  trackEvent('performance_metric', {
    metric,
    value,
    unit,
    context
  });
}

/**
 * ✅ Track error events (without sensitive details)
 */
export function trackError(errorType: string, context?: string, data: Record<string, any> = {}): void {
  trackEvent('error_occurred', {
    errorType,
    context,
    ...data
  });
}

// Auto-flush queue periodically
setInterval(() => {
  if (globalTelemetryEnabled && telemetryQueue.length > 0) {
    processQueue();
  }
}, defaultConfig.flushInterval);
