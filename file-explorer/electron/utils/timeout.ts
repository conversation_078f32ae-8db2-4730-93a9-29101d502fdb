// electron/utils/timeout.ts

/**
 * ✅ Timeout utility for Electron main process
 * Simplified version for server-side use
 */

export interface TimeoutOptions {
  timeoutMs: number;
  label?: string;
  abortController?: AbortController;
}

/**
 * ✅ Wrap any promise with timeout functionality
 * @param promise - The promise to wrap with timeout
 * @param timeoutMs - Timeout in milliseconds
 * @param label - Label for error messages (default: 'Operation')
 * @returns Promise that resolves with result or rejects with timeout error
 */
export async function withTimeout<T>(
  promise: Promise<T>, 
  timeoutMs: number, 
  label = 'Operation'
): Promise<T> {
  let timeoutId: NodeJS.Timeout | undefined;

  const timeout = new Promise<never>((_, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error(`${label} timed out after ${timeoutMs}ms`));
    }, timeoutMs);
  });

  try {
    const result = await Promise.race([promise, timeout]);
    if (timeoutId) clearTimeout(timeoutId);
    return result;
  } catch (error) {
    if (timeoutId) clearTimeout(timeoutId);
    throw error;
  }
}

/**
 * ✅ Advanced timeout wrapper with abort controller support
 * @param promise - The promise to wrap
 * @param options - Timeout options including abort controller
 * @returns Promise that resolves with result or rejects with timeout error
 */
export async function withTimeoutAndAbort<T>(
  promise: Promise<T>,
  options: TimeoutOptions
): Promise<T> {
  const { timeoutMs, label = 'Operation', abortController } = options;
  let timeoutId: NodeJS.Timeout | undefined;

  const timeout = new Promise<never>((_, reject) => {
    timeoutId = setTimeout(() => {
      // Abort the operation if abort controller is provided
      if (abortController) {
        abortController.abort();
      }
      reject(new Error(`${label} timed out after ${timeoutMs}ms`));
    }, timeoutMs);
  });

  try {
    const result = await Promise.race([promise, timeout]);
    if (timeoutId) clearTimeout(timeoutId);
    return result;
  } catch (error) {
    if (timeoutId) clearTimeout(timeoutId);
    throw error;
  }
}

/**
 * ✅ Create a timeout-aware fetch wrapper
 * @param url - URL to fetch
 * @param options - Fetch options
 * @param timeoutMs - Timeout in milliseconds
 * @param label - Label for error messages
 * @returns Promise that resolves with Response or rejects with timeout error
 */
export async function fetchWithTimeout(
  url: string,
  options: RequestInit = {},
  timeoutMs: number,
  label = 'Fetch Request'
): Promise<Response> {
  const abortController = new AbortController();
  
  const fetchPromise = fetch(url, {
    ...options,
    signal: abortController.signal
  });

  return withTimeoutAndAbort(fetchPromise, {
    timeoutMs,
    label: `${label} (${url})`,
    abortController
  });
}

/**
 * ✅ Timeout error type for better error handling
 */
export class TimeoutError extends Error {
  public readonly isTimeout = true;
  public readonly timeoutMs: number;
  public readonly operation: string;

  constructor(operation: string, timeoutMs: number) {
    super(`${operation} timed out after ${timeoutMs}ms`);
    this.name = 'TimeoutError';
    this.operation = operation;
    this.timeoutMs = timeoutMs;
  }
}

/**
 * ✅ Enhanced timeout wrapper that throws TimeoutError
 * @param promise - The promise to wrap
 * @param timeoutMs - Timeout in milliseconds
 * @param label - Label for error messages
 * @returns Promise that resolves with result or rejects with TimeoutError
 */
export async function withTimeoutError<T>(
  promise: Promise<T>,
  timeoutMs: number,
  label = 'Operation'
): Promise<T> {
  let timeoutId: NodeJS.Timeout | undefined;

  const timeout = new Promise<never>((_, reject) => {
    timeoutId = setTimeout(() => {
      reject(new TimeoutError(label, timeoutMs));
    }, timeoutMs);
  });

  try {
    const result = await Promise.race([promise, timeout]);
    if (timeoutId) clearTimeout(timeoutId);
    return result;
  } catch (error) {
    if (timeoutId) clearTimeout(timeoutId);
    throw error;
  }
}

/**
 * ✅ Check if an error is a timeout error
 * @param error - Error to check
 * @returns True if error is a timeout error
 */
export function isTimeoutError(error: any): error is TimeoutError {
  return error instanceof TimeoutError || 
         (error instanceof Error && error.message.includes('timed out after'));
}

/**
 * ✅ Format timeout error for user display
 * @param error - Timeout error
 * @returns User-friendly error message
 */
export function formatTimeoutError(error: TimeoutError | Error): string {
  if (isTimeoutError(error)) {
    return `Operation timed out. Please try again or increase the timeout setting.`;
  }
  return error.message;
}
