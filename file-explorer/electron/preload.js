const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// ✅ Task 100 Step 3: Set up terminal log event forwarding
ipcRenderer.on('terminal:log', (event, logData) => {
  // Dispatch custom event to window for React components to listen
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('terminal-log', { detail: logData }));
  }
});

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  selectFolder: () => ipcRenderer.invoke('select-folder'),
  readDirectory: (dirPath) => ipcRenderer.invoke('read-directory', dirPath),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  saveFile: (filePath, content) => ipcRenderer.invoke('save-file', filePath, content),
  createFile: (filePath, content) => ipcRenderer.invoke('create-file', filePath, content),
  deleteFile: (filePath) => ipcRenderer.invoke('delete-file', filePath),
  executeCommand: (command, workingDirectory) => ipcRenderer.invoke('execute-command', command, workingDirectory),
  executeCommandWithEnv: (command, workingDirectory, envVars) => ipcRenderer.invoke('execute-command-with-env', command, workingDirectory, envVars),

  openKanbanWindow: (boardId) => ipcRenderer.send('open-kanban-window', boardId),
  openAgentSystemWindow: () => ipcRenderer.send('open-agent-system-window'),
  openEditorWindow: (filePath) => ipcRenderer.send('open-editor-window', filePath),
  openExplorerWindow: () => ipcRenderer.send('open-explorer-window'),
  openChatWindow: () => ipcRenderer.send('open-chat-window'),
  openTimelineWindow: () => ipcRenderer.send('open-timeline-window'),
  openTerminalWindow: () => ipcRenderer.send('open-terminal-window'),

  // ✅ Auto-Save API
  saveAgentStates: (agentStates) => ipcRenderer.invoke('save-agent-states', agentStates),
  saveBoardState: (boardState) => ipcRenderer.invoke('save-board-state', boardState),

  // ✅ Task 78: Taskmaster Task Sync API
  taskmasterAPI: {
    updateTask: (taskId, updatedFields) => ipcRenderer.invoke('taskmaster:update-task', taskId, updatedFields),
    backupTasks: (projectPath) => ipcRenderer.invoke('taskmaster:backup-tasks', projectPath),
    validateTasksFile: (filePath) => ipcRenderer.invoke('taskmaster:validate-tasks', filePath),
    syncTaskStatus: (taskId, status, metadata) => ipcRenderer.invoke('taskmaster:sync-status', taskId, status, metadata)
  },

  // New: LLM API for AI provider integration
  llm: {
    validateApiKey: (provider, apiKey) => ipcRenderer.invoke('llm:validateApiKey', provider, apiKey),
    callLLM: (provider, request, apiKey) => ipcRenderer.invoke('llm:callLLM', provider, request, apiKey),
    fetchModels: (provider, apiKey) => ipcRenderer.invoke('llm:fetchModels', provider, apiKey),
  },

  // ✅ MCP Protocol API
  mcp: {
    initializeConnection: (serverId, config) => ipcRenderer.invoke('mcp:initializeConnection', serverId, config),
    sendTask: (serverId, request) => ipcRenderer.invoke('mcp:sendTask', serverId, request),
    syncAgentState: (serverId, agentId, state) => ipcRenderer.invoke('mcp:syncAgentState', serverId, agentId, state),
    testConnection: (serverId) => ipcRenderer.invoke('mcp:testConnection', serverId),
    disconnectServer: (serverId) => ipcRenderer.invoke('mcp:disconnectServer', serverId),
    getConnectedServers: () => ipcRenderer.invoke('mcp:getConnectedServers'),
  },

  // ✅ Terminal PTY API
  terminal: {
    create: (terminalSettings) => ipcRenderer.invoke('terminal:create', terminalSettings),
    write: (id, data) => ipcRenderer.send('terminal:input', { id, data }),
    resize: (id, cols, rows) => ipcRenderer.invoke('terminal:resize', { id, cols, rows }),
    listen: (id, callback) => {
      ipcRenderer.send('terminal:listen', id);
      const dataListener = (event, data) => callback(data);
      const exitListener = (event, { exitCode, signal }) => callback(null, { exitCode, signal });

      ipcRenderer.on(`terminal:data:${id}`, dataListener);
      ipcRenderer.on(`terminal:exit:${id}`, exitListener);

      // Return cleanup function
      return () => {
        ipcRenderer.removeListener(`terminal:data:${id}`, dataListener);
        ipcRenderer.removeListener(`terminal:exit:${id}`, exitListener);
      };
    },
    dispose: (id) => ipcRenderer.send('terminal:dispose', id),
    // ✅ Task 99: Enhanced agent command with session support
    agentCommand: ({ command, agentId, sessionId, timeout, workingDirectory, environment }) =>
      ipcRenderer.invoke('terminal:agent-command', { command, agentId, sessionId, timeout, workingDirectory, environment }),

    // ✅ Task 100 Step 3: Terminal log event handling
    onLog: (callback) => {
      const handleLog = (event, logData) => {
        callback(logData);
      };
      ipcRenderer.on('terminal:log', handleLog);

      // Return cleanup function
      return () => {
        ipcRenderer.removeListener('terminal:log', handleLog);
      };
    },

    // ✅ Task 101 Step 3: TerminalSessionManager API
    createSession: (sessionId, agentId, options) =>
      ipcRenderer.invoke('terminal:create-session', sessionId, agentId, options),

    writeToSession: (sessionId, data) =>
      ipcRenderer.invoke('terminal:write-session', sessionId, data),

    destroySession: (sessionId) =>
      ipcRenderer.invoke('terminal:destroy-session', sessionId),

    listSessions: (agentId) =>
      ipcRenderer.invoke('terminal:list-sessions', agentId),

    getSessionInfo: (sessionId) =>
      ipcRenderer.invoke('terminal:get-session-info', sessionId),

    resizeSession: (sessionId, cols, rows) =>
      ipcRenderer.invoke('terminal:resize-session', sessionId, cols, rows),

    getSessionStats: () =>
      ipcRenderer.invoke('terminal:get-session-stats'),

    cleanupAgentSessions: (agentId) =>
      ipcRenderer.invoke('terminal:cleanup-agent-sessions', agentId),

    // Session event listeners
    onSessionData: (sessionId, callback) => {
      const handleData = (event, data) => callback(data);
      ipcRenderer.on(`terminal:session-data:${sessionId}`, handleData);

      return () => {
        ipcRenderer.removeListener(`terminal:session-data:${sessionId}`, handleData);
      };
    },

    onSessionExit: (sessionId, callback) => {
      const handleExit = (event, exitInfo) => callback(exitInfo);
      ipcRenderer.on(`terminal:session-exit:${sessionId}`, handleExit);

      return () => {
        ipcRenderer.removeListener(`terminal:session-exit:${sessionId}`, handleExit);
      };
    },

    // ✅ Task 104 Step 2: User Terminal Session API
    createUserSession: (shell) => ipcRenderer.invoke('terminal:create-user-session', shell),
    disposeUserSession: (sessionId) => ipcRenderer.invoke('terminal:dispose-user-session', sessionId),
    writeUserSession: (sessionId, data) => ipcRenderer.invoke('terminal:write-user-session', sessionId, data),
    resizeUserSession: (sessionId, cols, rows) => ipcRenderer.invoke('terminal:resize-user-session', sessionId, cols, rows),
    listUserSessions: () => ipcRenderer.invoke('terminal:list-user-sessions'),

    // User session event listeners
    onUserSessionData: (sessionId, callback) => {
      const handleData = (event, data) => callback(data);
      ipcRenderer.on(`terminal:user-session-data:${sessionId}`, handleData);

      return () => {
        ipcRenderer.removeListener(`terminal:user-session-data:${sessionId}`, handleData);
      };
    },

    onUserSessionExit: (sessionId, callback) => {
      const handleExit = (event, exitInfo) => callback(exitInfo);
      ipcRenderer.on(`terminal:user-session-exit:${sessionId}`, handleExit);

      return () => {
        ipcRenderer.removeListener(`terminal:user-session-exit:${sessionId}`, handleExit);
      };
    },
  },



  // New: Generic IPC methods for bridges like BoardIPCBridge
  ipc: {
    send: (channel, ...args) => ipcRenderer.send(channel, ...args),
    invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
    on: (channel, listener) => {
      // ipcRenderer.on returns a cleanup function itself if needed by removing the listener.
      // However, the typical pattern is to provide a way to remove listeners.
      // For simplicity, we wrap the listener.
      const wrappedListener = (event, ...args) => listener(...args);
      ipcRenderer.on(channel, wrappedListener);
      // Return a function to remove this specific listener
      return () => ipcRenderer.removeListener(channel, wrappedListener);
    },
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  }
});

// Log when the API is exposed with enhanced debugging
console.log('🚀 [Preload] Electron API exposed to renderer process (including generic IPC)');


// Verify the API is actually available on window after DOM loads
window.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    if (typeof window !== 'undefined' && window.electronAPI) {
      console.log('✅ [Preload] Verification: electronAPI is available on window');

    } else {
      console.error('❌ [Preload] Verification failed: electronAPI not found on window');
    }
  }, 100);
});

// All of the Node.js APIs are available in the preload process.
// It has the same sandbox as a Chrome extension.
window.addEventListener('DOMContentLoaded', () => {
  const replaceText = (selector, text) => {
    const element = document.getElementById(selector)
    if (element) element.innerText = text
  }

  for (const type of ['chrome', 'node', 'electron']) {
    replaceText(`${type}-version`, process.versions[type])
  }
})