// electron/board-constants.ts

/**
 * IPC Commands sent from Renderer to Main for board operations.
 */
export const BOARD_COMMANDS = {
  GET_STATE: 'board:get-state',
  CREATE_BOARD: 'board:create-board', // New
  UPDATE_BOARD_METADATA: 'board:update-board-metadata', // New for name/desc
  DELETE_BOARD: 'board:delete-board', // New

  ADD_COLUMN: 'board:add-column', // New
  UPDATE_COLUMN: 'board:update-column',
  DELETE_COLUMN: 'board:delete-column', // New
  MOVE_COLUMN: 'board:move-column', // New

  ADD_SWIMLANE: 'board:add-swimlane', // New
  UPDATE_SWIMLANE: 'board:update-swimlane', // New
  DELETE_SWIMLANE: 'board:delete-swimlane', // New
  TOGGLE_SWIMLANE_EXPANSION: 'board:toggle-swimlane-expansion', // New

  CREATE_CARD: 'board:create-card',
  UPDATE_CARD: 'board:update-card',
  DELETE_CARD: 'board:delete-card',
  MOVE_CARD: 'board:move-card',

  UPDATE_CARD_TYPES: 'board:update-card-types', // New for legend
  UPDATE_AGENTS_ON_BOARD: 'board:update-agents-on-board', // New for agent list on board
};

/**
 * IPC Events sent from Main to Renderer to notify of state changes.
 */
export const BOARD_EVENTS = {
  STATE_INIT: 'board:state-init', // For initial full state load
  STATE_UPDATE: 'board:state-update', // For incremental or full state updates
  BOARD_LIST_UPDATED: 'board:list-updated', // When list of boards changes

  // Optional: More granular events if needed, though STATE_UPDATE can cover most
  CARD_CREATED: 'board:card-created',
  CARD_UPDATED: 'board:card-updated',
  CARD_DELETED: 'board:card-deleted',
  CARD_MOVED: 'board:card-moved',
  COLUMN_UPDATED: 'board:column-updated',
  SWIMLANE_UPDATED: 'board:swimlane-updated', // New
  AGENT_STATUS: 'board:agent-status', // Existing, might be for specific agent interactions
  AGENT_LOG: 'board:agent-log', // Existing
};