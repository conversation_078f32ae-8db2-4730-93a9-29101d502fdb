// utils/system-message-utils.ts

import type { SystemFeedbackMessage, SystemMessageSource } from "@/types/chat"
import { getChatHistoryService } from "@/services/chat-history"

/**
 * Create and emit system feedback messages
 */
export class SystemMessageUtils {
  private static chatHistory = getChatHistoryService()

  /**
   * Create a validation warning message
   */
  static async createValidationWarning(
    content: string,
    linkedToMessageId?: string,
    details?: Record<string, any>
  ): Promise<void> {
    const message: SystemFeedbackMessage = {
      id: `system-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content: `⚠️ Validation Warning: ${content}`,
      source: "validator",
      linkedToMessageId,
      severity: "warning",
      timestamp: new Date(),
      metadata: {
        operation: "validation",
        details
      }
    }

    await this.chatHistory.addSystemMessage(message)
  }

  /**
   * Create an execution result message
   */
  static async createExecutionResult(
    content: string,
    success: boolean,
    linkedToMessageId?: string,
    filePath?: string,
    details?: Record<string, any>
  ): Promise<void> {
    const icon = success ? "✅" : "❌"
    const message: SystemFeedbackMessage = {
      id: `system-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content: `${icon} ${content}`,
      source: "executor",
      linkedToMessageId,
      severity: success ? "success" : "error",
      timestamp: new Date(),
      metadata: {
        operation: "execution",
        filePath,
        success,
        details
      }
    }

    await this.chatHistory.addSystemMessage(message)
  }

  /**
   * Create a task status update message
   */
  static async createTaskStatusUpdate(
    taskId: string,
    status: string,
    agentId: string,
    linkedToMessageId?: string,
    details?: Record<string, any>
  ): Promise<void> {
    const statusIcons = {
      pending: "⏳",
      running: "🔄",
      completed: "✅",
      failed: "❌",
      paused: "⏸️"
    }

    const icon = statusIcons[status as keyof typeof statusIcons] || "📋"
    const message: SystemFeedbackMessage = {
      id: `system-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content: `${icon} Task ${status}: ${agentId} is ${status === 'running' ? 'working on' : status} task`,
      source: "micromanager",
      linkedToMessageId,
      severity: status === 'failed' ? "error" : "info",
      timestamp: new Date(),
      metadata: {
        taskId,
        agentId,
        status,
        operation: "task_status",
        details
      }
    }

    await this.chatHistory.addSystemMessage(message)
  }

  /**
   * Create a file system operation message
   */
  static async createFileSystemMessage(
    operation: string,
    filePath: string,
    success: boolean,
    linkedToMessageId?: string,
    details?: Record<string, any>
  ): Promise<void> {
    const operationIcons = {
      create: "📄",
      update: "✏️",
      delete: "🗑️",
      read: "👁️",
      move: "📁"
    }

    const icon = operationIcons[operation as keyof typeof operationIcons] || "📋"
    const statusIcon = success ? "✅" : "❌"
    
    const message: SystemFeedbackMessage = {
      id: `system-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content: `${icon} ${statusIcon} File ${operation}: ${filePath}`,
      source: "file_system",
      linkedToMessageId,
      severity: success ? "success" : "error",
      timestamp: new Date(),
      metadata: {
        operation,
        filePath,
        success,
        details
      }
    }

    await this.chatHistory.addSystemMessage(message)
  }

  /**
   * Create a terminal command result message
   */
  static async createTerminalMessage(
    command: string,
    output: string,
    exitCode: number,
    linkedToMessageId?: string
  ): Promise<void> {
    const success = exitCode === 0
    const icon = success ? "✅" : "❌"
    
    const message: SystemFeedbackMessage = {
      id: `system-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content: `${icon} Terminal: ${command}${output ? `\n${output.slice(0, 200)}${output.length > 200 ? '...' : ''}` : ''}`,
      source: "terminal",
      linkedToMessageId,
      severity: success ? "success" : "error",
      timestamp: new Date(),
      metadata: {
        command,
        output,
        exitCode,
        operation: "terminal_command"
      }
    }

    await this.chatHistory.addSystemMessage(message)
  }

  /**
   * Create a general system note
   */
  static async createSystemNote(
    content: string,
    source: SystemMessageSource = "system",
    linkedToMessageId?: string,
    severity: "info" | "warning" | "error" | "success" = "info"
  ): Promise<void> {
    const severityIcons = {
      info: "💬",
      warning: "⚠️",
      error: "❌",
      success: "✅"
    }

    const icon = severityIcons[severity]
    const message: SystemFeedbackMessage = {
      id: `system-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content: `${icon} ${content}`,
      source,
      linkedToMessageId,
      severity,
      timestamp: new Date(),
      metadata: {
        operation: "system_note"
      }
    }

    await this.chatHistory.addSystemMessage(message)
  }

  /**
   * Get system message styling classes
   */
  static getSystemMessageStyling(severity: "info" | "warning" | "error" | "success"): string {
    const baseClasses = "text-xs italic border-l-2 pl-2 my-1"
    
    switch (severity) {
      case "error":
        return `${baseClasses} bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 border-red-500`
      case "warning":
        return `${baseClasses} bg-yellow-50 dark:bg-yellow-950 text-yellow-800 dark:text-yellow-200 border-yellow-500`
      case "success":
        return `${baseClasses} bg-green-50 dark:bg-green-950 text-green-800 dark:text-green-200 border-green-500`
      case "info":
      default:
        return `${baseClasses} bg-blue-50 dark:bg-blue-950 text-blue-800 dark:text-blue-200 border-blue-500`
    }
  }

  /**
   * Get system message icon
   */
  static getSystemMessageIcon(source: SystemMessageSource): string {
    const sourceIcons = {
      micromanager: "🧠",
      validator: "🔍",
      executor: "⚙️",
      system: "💻",
      file_system: "📁",
      terminal: "💻"
    }

    return sourceIcons[source] || "💻"
  }
}

// Export convenience functions
export const {
  createValidationWarning,
  createExecutionResult,
  createTaskStatusUpdate,
  createFileSystemMessage,
  createTerminalMessage,
  createSystemNote,
  getSystemMessageStyling,
  getSystemMessageIcon
} = SystemMessageUtils
