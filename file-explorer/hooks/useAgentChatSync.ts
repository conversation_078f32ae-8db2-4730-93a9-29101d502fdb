// hooks/useAgentChatSync.ts
// ✅ Synchronized Agent Cha<PERSON> Hook for Real-time Cross-Window Sync

import { useState, useCallback, useEffect } from "react"
import { CompleteAgentManager } from "@/components/agents/agent-manager-complete"
import { useSharedAgentState } from "@/components/agents/shared-agent-state"
import { AgentContext } from "@/components/agents/agent-base"
import { createTaskStatusUpdate } from "@/utils/system-message-utils"
import { LLMRequestService, type StreamChunk } from "@/components/agents/llm-request-service"
import { llmIntegration } from "@/components/agents/llm-integration-service"
import { SemanticSearchService } from "@/components/background/semantic-search"
import { globalChatState, GlobalChatState } from "@/services/global-chat-state"
import type { AgentChatMessage } from "@/types/chat"

export function useAgentChatSync() {
  // ✅ Use global chat state instead of local state
  const [chatState, setChatState] = useState<GlobalChatState>(() => globalChatState.getState())
  const [agentManager] = useState(() => new CompleteAgentManager())
  const [llmService] = useState(() => LLMRequestService.getInstance())
  const [semanticSearch] = useState(() => new SemanticSearchService())
  const [isLLMInitialized, setIsLLMInitialized] = useState(false)

  const sharedState = useSharedAgentState()

  // ✅ Subscribe to global chat state changes
  useEffect(() => {
    const unsubscribe = globalChatState.subscribe(setChatState)

    // Initialize global chat state and force sync
    const initializeAndSync = async () => {
      try {
        await globalChatState.initialize()
        // Force sync with other windows after initialization
        await globalChatState.syncWithOtherWindows()
        console.log('✅ Global chat state initialized and synced')
      } catch (error) {
        console.error('❌ Failed to initialize global chat state:', error)
      }
    }

    initializeAndSync()

    return unsubscribe
  }, [])

  // ✅ Initialize LLM service (matching original implementation)
  useEffect(() => {
    const initializeLLM = async () => {
      try {
        if (!llmIntegration.isInitialized()) {
          console.log('useAgentChatSync: Initializing LLM integration service...')
          await llmIntegration.initialize()
          console.log('useAgentChatSync: LLM integration service initialized successfully')
        }
        setIsLLMInitialized(true)
      } catch (error) {
        console.error('useAgentChatSync: Failed to initialize LLM integration service:', error)
        setIsLLMInitialized(true) // Set to true anyway to allow UI to function
      }
    }

    initializeLLM()
  }, [])

  // ✅ Listen for agent messages for multi-agent responses
  useEffect(() => {
    const handleAgentMessage = (message: any) => {
      console.log('🔔 Agent message received in sync chat:', message)

      if (message.type === 'completion' || message.type === 'info') {
        const agentResponse: AgentChatMessage = {
          id: `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          content: message.message,
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: message.agentId,
          agentId: message.agentId,
          taskId: message.taskId,
          metadata: message.metadata
        }

        // Add to global state (will sync across windows)
        globalChatState.addMessage(agentResponse)
      }
    }

    // Subscribe to shared agent state messages
    const unsubscribe = sharedState.messages ? () => {} : () => {}

    return unsubscribe
  }, [sharedState])

  // ✅ Task 65: Semantic Query Detection
  const detectSemanticQuery = useCallback((input: string): boolean => {
    const semanticPatterns = [
      /^(where\s+is|find|search\s+for|locate|show\s+me)/i,
      /^(what\s+is|how\s+does|explain)/i,
      /^(list\s+all|show\s+all|find\s+all)/i,
      /(functions?\s+that|methods?\s+that|classes?\s+that)/i,
      /(components?\s+that|files?\s+that|modules?\s+that)/i,
      /^(grep|search|find)\s+/i
    ]

    return semanticPatterns.some(pattern => pattern.test(input.trim()))
  }, [])

  // ✅ Task 65: Handle Semantic Query
  const handleSemanticQuery = useCallback(async (query: string): Promise<AgentChatMessage> => {
    try {
      console.log('🔍 Processing semantic query:', query)

      const searchResults = await semanticSearch.searchCode({
        query,
        maxResults: 5,
        minSimilarity: 0.3
      })

      if (searchResults.length === 0) {
        return {
          id: `semantic-${Date.now()}`,
          content: "🔍 **Semantic Search Results**\n\nNo matching code found in the current project for your query. Try rephrasing or using different keywords.",
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: "semantic_search",
          metadata: { isSemanticResult: true, query, resultCount: 0 }
        }
      }

      let formattedResults = `🔍 **Semantic Search Results** (${searchResults.length} matches)\n\n`

      searchResults.forEach((result, index) => {
        formattedResults += `**${index + 1}. ${result.filePath}**\n`
        formattedResults += `*Similarity: ${(result.similarity * 100).toFixed(1)}%*\n`
        if (result.metadata.startLine) {
          formattedResults += `*Lines: ${result.metadata.startLine}-${result.metadata.endLine}*\n`
        }
        formattedResults += `\`\`\`${result.language}\n${result.content.substring(0, 300)}${result.content.length > 300 ? '...' : ''}\n\`\`\`\n\n`
      })

      return {
        id: `semantic-${Date.now()}`,
        content: formattedResults,
        role: "agent",
        timestamp: new Date(),
        status: "completed",
        agentType: "semantic_search",
        metadata: {
          isSemanticResult: true,
          query,
          resultCount: searchResults.length,
          results: searchResults.map(r => ({ filePath: r.filePath, similarity: r.similarity }))
        }
      }
    } catch (error) {
      console.error('Semantic search failed:', error)
      return {
        id: `semantic-error-${Date.now()}`,
        content: `🔍 **Semantic Search Error**\n\nFailed to search the codebase: ${error instanceof Error ? error.message : 'Unknown error'}`,
        role: "agent",
        timestamp: new Date(),
        status: "error",
        agentType: "semantic_search",
        metadata: { isSemanticResult: true, query, error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }, [semanticSearch])

  // ✅ Send message with global state sync
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || chatState.isProcessing) return

    // ✅ Check if LLM is initialized before proceeding
    if (!isLLMInitialized) {
      console.warn('⚠️ LLM service not initialized yet, please wait...')
      const errorMessage: AgentChatMessage = {
        id: `error-${Date.now()}`,
        content: `⚠️ **System Initializing**: Please wait for the LLM service to initialize before sending messages.`,
        role: "agent",
        timestamp: new Date(),
        status: "error",
        agentType: "system"
      }
      await globalChatState.addMessage(errorMessage)
      return
    }

    // ✅ Check if API keys are configured
    if (!llmService.hasAnyApiKey()) {
      console.warn('⚠️ No API keys configured for any LLM provider')
      llmService.logApiKeyStatus()
      const errorMessage: AgentChatMessage = {
        id: `error-${Date.now()}`,
        content: `⚠️ **No API Keys Configured**: Please configure at least one LLM provider API key in Settings before using the Agent Chat. Supported providers: OpenAI, Anthropic, OpenRouter.`,
        role: "agent",
        timestamp: new Date(),
        status: "error",
        agentType: "system"
      }
      await globalChatState.addMessage(errorMessage)
      return
    }

    const userMessage: AgentChatMessage = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      role: "user",
      timestamp: new Date(),
      status: "sent"
    }

    // Add user message to global state (syncs across windows)
    await globalChatState.addMessage(userMessage)

    // Set processing state (syncs across windows)
    globalChatState.setProcessingState(true)

    try {
      // ✅ Task 65: Check for semantic queries first
      if (detectSemanticQuery(content.trim())) {
        console.log('🔍 Detected semantic query, routing to semantic search')

        const semanticResult = await handleSemanticQuery(content.trim())
        await globalChatState.addMessage(semanticResult)

        globalChatState.setProcessingState(false)
        return
      }

      // Add streaming message placeholder FIRST to get the ID
      const streamingMessage: AgentChatMessage = {
        id: `streaming-${Date.now()}`,
        content: "",
        role: "agent",
        timestamp: new Date(),
        status: "processing",
        agentType: "micromanager",
        agentId: "micromanager",
        taskId: '', // Will be updated after task assignment
        isStreaming: true,
        stream: true
      }

      // ✅ REPLICATE COMMAND CENTER BEHAVIOR: Use same task submission pipeline
      console.log('🔄 Agent Chat: Replicating Command Center task submission pipeline')

      // Import TaskOrchestrator dynamically to avoid circular dependencies
      const { TaskOrchestrator } = await import('@/components/agents/task-orchestrator')

      // Decompose the task into subtasks (same as Command Center)
      const decomposition = TaskOrchestrator.decompose(content.trim())
      console.log(`🧠 Agent Chat: Decomposed task into ${decomposition.subtasks.length} subtasks:`, decomposition)

      // Create parent task in shared state (same as Command Center)
      await sharedState.assignTask({
        agentId: 'micromanager',
        description: `[AGENT CHAT] ${content.trim()}`,
        status: 'pending',
        priority: 'medium'
      })

      // ✅ Create agent context with the streaming message ID as chatMessageId
      const context: AgentContext = {
        task: content.trim(),
        metadata: {
          source: 'agent_chat',
          requestedAt: Date.now(),
          chatMessageId: streamingMessage.id,  // ✅ Use streaming message ID
          decomposition,
          isOrchestrationTask: true,
          originalTaskId: decomposition.parentTaskId
        }
      }

      console.log(`🔍 Agent Chat: Created context with chatMessageId: "${context.metadata?.chatMessageId}"`)

      // Submit task to Micromanager for orchestration (same as Command Center)
      const taskId = await agentManager.assignTask('micromanager', context, 'medium')

      // ✅ Create Kanban cards for all subtasks (same as Command Center)
      const { KanbanTaskBridge } = await import('@/components/agents/kanban-task-bridge')
      const cardResults = await KanbanTaskBridge.createCardsFromSubtasks(decomposition.subtasks)
      console.log(`🎯 Agent Chat: Created ${cardResults.success.length} Kanban cards, ${cardResults.failed.length} failed`)

      // Update streaming message with actual taskId
      streamingMessage.taskId = taskId

      await globalChatState.addMessage(streamingMessage)
      globalChatState.setProcessingState(true, streamingMessage.id)

      // Start real streaming response
      await streamAgentResponse(streamingMessage.id, content.trim())

      // Emit system event for metrics
      emitAgentChatEvent('response_received', {
        agent: 'micromanager',
        taskId
      })

    } catch (error) {
      console.error('❌ Error in sendMessage:', error)

      let errorContent = `❌ **Error**: ${error instanceof Error ? error.message : 'Unknown error occurred'}`

      // ✅ Provide specific guidance for common errors
      if (error instanceof Error) {
        if (error.message.includes('No API key configured')) {
          errorContent = `⚠️ **API Key Missing**: Please configure an API key for your selected LLM provider in Settings. Go to Settings → API Keys to add your OpenAI, Anthropic, or OpenRouter API key.`
        } else if (error.message.includes('Agent response timeout')) {
          errorContent = `⏱️ **Response Timeout**: The agent took too long to respond. This might be due to high server load or network issues. Please try again in a moment.`
        } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
          errorContent = `🔑 **Invalid API Key**: Your API key appears to be invalid or expired. Please check your API key in Settings and ensure it has the correct permissions.`
        } else if (error.message.includes('429') || error.message.includes('rate limit')) {
          errorContent = `🚦 **Rate Limited**: You've exceeded the API rate limit. Please wait a moment before sending another message.`
        }
      }

      const errorMessage: AgentChatMessage = {
        id: `error-${Date.now()}`,
        content: errorContent,
        role: "agent",
        timestamp: new Date(),
        status: "error",
        agentType: "system"
      }

      await globalChatState.addMessage(errorMessage)
      globalChatState.setProcessingState(false)
    }
  }, [chatState.isProcessing, isLLMInitialized, agentManager, sharedState, detectSemanticQuery, handleSemanticQuery])

  // ✅ Task 74: Real-time streaming response (no more polling)
  const streamAgentResponse = useCallback(async (messageId: string, userMessage: string) => {
    try {
      console.log(`🚀 [Frontend Stream] Starting real-time streaming for messageId: "${messageId}"`)

      // ✅ Set up real-time streaming listener for this specific messageId
      const streamingPromise = new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          console.error(`❌ [Frontend Stream] Timeout waiting for streaming chunks for messageId: "${messageId}"`)
          reject(new Error('Streaming timeout - no chunks received'))
        }, 30000) // 30 second timeout

        // ✅ Listen for streaming chunks from agent
        const handleStreamingChunk = (chunkData: any) => {
          console.log(`📡 [Frontend Stream] Received chunk for messageId: "${messageId}":`, chunkData)

          if (chunkData.chatMessageId === messageId) {
            console.log(`✅ [Frontend Stream] Processing chunk: "${chunkData.delta}", complete: ${chunkData.isComplete}`)

            // Update message in global state with streaming chunk
            globalChatState.updateMessage(messageId, {
              content: chunkData.content,
              status: chunkData.isComplete ? "completed" : "processing",
              isStreaming: !chunkData.isComplete,
              tokensUsed: chunkData.tokensUsed?.total,
              metadata: {
                tokensUsed: chunkData.tokensUsed,
                finishReason: chunkData.finishReason,
                provider: 'streaming',
                model: 'streaming'
              }
            })

            // Complete streaming when done
            if (chunkData.isComplete) {
              clearTimeout(timeout)
              globalChatState.setProcessingState(false)
              console.log(`✅ [Frontend Stream] Streaming completed for messageId: "${messageId}"`)
              resolve()
            }
          }
        }

        // ✅ Listen for streaming chunks via Electron IPC
        if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
          window.electronAPI.ipc.on('agent-streaming-chunk', handleStreamingChunk)

          // Cleanup listener when done
          const cleanup = () => {
            window.electronAPI.ipc.removeListener('agent-streaming-chunk', handleStreamingChunk)
          }

          // Set up cleanup on resolve/reject
          streamingPromise.finally(cleanup)
        } else {
          console.warn(`⚠️ [Frontend Stream] Electron API not available for messageId: "${messageId}"`)
          reject(new Error('Electron API not available for streaming'))
        }
      })

      // ✅ Wait for streaming to complete
      await streamingPromise

    } catch (error) {
      console.error(`❌ [Frontend Stream] Streaming failed for messageId: "${messageId}":`, error)

      globalChatState.updateMessage(messageId, {
        content: `❌ **Error**: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        status: "error",
        isStreaming: false,
        stream: false
      })

      globalChatState.setProcessingState(false)
    }
  }, [])

  // ✅ Simulation removed - now using real agent responses only

  // ✅ Wait for real agent response with enhanced debugging
  const waitForAgentResponse = useCallback(async (messageId: string): Promise<any> => {
    const maxWaitTime = 30000 // 30 seconds
    const pollInterval = 500 // 500ms
    const startTime = Date.now()

    console.log(`🔍 waitForAgentResponse: Looking for chatMessageId = "${messageId}"`)

    return new Promise((resolve, reject) => {
      const poll = () => {
        // Check if we have a response from the agent system
        const messages = sharedState.messages

        // ✅ Enhanced debugging - log all completion messages
        const completionMessages = messages.filter(msg => msg.type === 'completion')
        console.log(`🔍 [✅ Frontend Sees Completion] Found ${completionMessages.length} completion messages:`,
          completionMessages.map(msg => ({
            agentId: msg.agentId,
            taskId: msg.taskId,
            chatMessageId: msg.metadata?.chatMessageId,
            hasMetadata: !!msg.metadata,
            message: msg.message?.substring(0, 50) + '...'
          }))
        )

        // ✅ Task 74: Add detailed polling debug logs
        console.log(`🧪 Looking for: "${messageId}", Available:`, completionMessages.map(m => m.metadata?.chatMessageId).filter(Boolean))
        console.log(`🔍 [✅ Frontend Sees Completion] Looking for chatMessageId: "${messageId}" in streamingMessages[]`)

        const agentResponse = messages.find(msg =>
          msg.type === 'completion' &&
          msg.metadata?.chatMessageId === messageId
        )

        if (agentResponse) {
          console.log(`✅ [✅ Clear the Timeout Error] Found matching completion message for chatMessageId "${messageId}":`, {
            agentId: agentResponse.agentId,
            taskId: agentResponse.taskId,
            contentLength: agentResponse.message?.length,
            metadata: agentResponse.metadata
          })

          // ✅ Return real agent response with LLM metadata
          resolve({
            content: agentResponse.message,
            tokensUsed: agentResponse.metadata?.tokensUsed || 0,
            cost: agentResponse.metadata?.cost || 0,
            provider: agentResponse.metadata?.provider || 'unknown',
            model: agentResponse.metadata?.model || 'unknown',
            finishReason: agentResponse.metadata?.finishReason || 'stop',
            responseTime: agentResponse.metadata?.responseTime || 0
          })
          return
        }

        // Check timeout
        const elapsed = Date.now() - startTime
        if (elapsed > maxWaitTime) {
          console.error(`❌ Timeout after ${elapsed}ms. Looking for chatMessageId "${messageId}" but found:`,
            completionMessages.map(msg => msg.metadata?.chatMessageId).filter(Boolean)
          )
          reject(new Error('Agent response timeout - no real LLM response received'))
          return
        }

        // Continue polling
        setTimeout(poll, pollInterval)
      }

      poll()
    })
  }, [sharedState])

  // ✅ Helper function to emit system events
  const emitAgentChatEvent = useCallback((eventType: string, data: any) => {
    try {
      sharedState.addMessage({
        agentId: 'system',
        message: `Agent Chat Event: ${eventType}`,
        timestamp: Date.now(),
        type: 'info',
        metadata: data
      })

      console.log(`🔔 Agent Chat Event: ${eventType}`, data)
    } catch (error) {
      console.warn('Failed to emit agent chat event:', error)
    }
  }, [sharedState])

  // ✅ Clear messages with global state sync
  const clearMessages = useCallback(async () => {
    await globalChatState.clearMessages()
    console.log('🗑️ Messages cleared and synced across windows')
  }, [])

  // ✅ Set streaming preference with global state sync
  const setEnableStreaming = useCallback((enabled: boolean) => {
    globalChatState.setEnableStreaming(enabled)
    console.log('⚡ Streaming preference updated and synced:', enabled)
  }, [])

  // ✅ Test sync functionality (for debugging)
  const testSync = useCallback(() => {
    globalChatState.testSync()
  }, [])

  // ✅ Debug function to check system status
  const debugSystemStatus = useCallback(() => {
    console.log('🔍 Agent Chat System Status:')
    console.log('- LLM Initialized:', isLLMInitialized)
    console.log('- Chat State Loaded:', chatState.isLoaded)
    console.log('- Processing:', chatState.isProcessing)
    console.log('- Streaming Enabled:', chatState.enableStreaming)
    console.log('- Message Count:', chatState.messages.length)

    llmService.logApiKeyStatus()

    // Check if llmIntegration is available
    try {
      if (llmIntegration.isInitialized()) {
        console.log('- LLM Integration: ✅ Initialized')
      } else {
        console.log('- LLM Integration: ❌ Not initialized')
      }
    } catch (error) {
      console.log('- LLM Integration: ❌ Error checking status:', error)
    }
  }, [isLLMInitialized, chatState, llmService])

  return {
    messages: chatState.messages,
    isProcessing: chatState.isProcessing,
    streamingMessageId: chatState.streamingMessageId,
    isLoaded: chatState.isLoaded,
    enableStreaming: chatState.enableStreaming,
    isLLMInitialized,
    setEnableStreaming,
    sendMessage,
    clearMessages,
    testSync, // Expose for debugging
    debugSystemStatus, // Expose for debugging
    chatHistory: globalChatState // Expose global chat state for advanced operations
  }
}
