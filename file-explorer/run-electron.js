const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const electron = require('electron');

console.log('Starting Electron app...');
console.log('Electron path:', electron);

// Get the current directory
const currentDir = process.cwd();
console.log('Current directory:', currentDir);

// Check if the out directory exists
const outDir = path.join(currentDir, 'out');
console.log('Checking if out directory exists:', outDir);
console.log('Directory exists:', fs.existsSync(outDir));

// Check if index.html exists
const indexPath = path.join(outDir, 'index.html');
console.log('Checking if index.html exists:', indexPath);
console.log('File exists:', fs.existsSync(indexPath));

// Launch Electron
const electronProcess = spawn(electron, ['.'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    ELECTRON_ENABLE_LOGGING: '1',
    ELECTRON_ENABLE_STACK_DUMPING: '1',
    DEBUG: '*'
  }
});

electronProcess.on('close', (code) => {
  console.log(`Electron process exited with code ${code}`);
});
