/** @type {import('next').NextConfig} */
const nextConfig = {
  // output: 'export', // Commented out for development - enable for production build
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental: {
    typedRoutes: false,
  },
  images: {
    unoptimized: true,
  },
  // Disable server components for static export
  serverExternalPackages: [],
  // Disable source maps in production
  productionBrowserSourceMaps: false,
  // Disable React strict mode for compatibility
  reactStrictMode: false,
  // Configure webpack for Monaco Editor
  webpack: (config, { isServer }) => {
    // Only modify client-side webpack config
    if (!isServer) {
      // Make sure workers are properly handled
      config.output.globalObject = 'self';

      // Ensure Monaco Editor's workers are properly handled
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }

    // Important: Return the modified config
    return config;
  },
}

export default nextConfig