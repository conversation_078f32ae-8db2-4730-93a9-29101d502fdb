// components/budget/use-threshold-alerts.tsx
"use client"

import { useState, useEffect, useCallback } from 'react';
import { alertManager, ThresholdAlert } from '../../lib/alert-manager';
import { notificationService, showAlertNotification } from '../../lib/notification-service';
import { useToast } from '@/components/ui/use-toast';
import { CostSettings } from '../settings/settings-manager';
import { ToastAction } from '@/components/ui/toast';

/**
 * ✅ Settings-safe function to get cost settings
 * Safely retrieves cost settings without requiring settings context
 */
function getSafeSettings(): CostSettings {
  try {
    // Try to get settings from global settings manager if available
    if (typeof window !== 'undefined') {
      const globalSettings = (window as any).__globalSettingsManager;
      if (globalSettings) {
        const settings = globalSettings.getSettings();
        return settings.cost || getDefaultCostSettings();
      }
    }
    return getDefaultCostSettings();
  } catch (error) {
    console.warn('Failed to get cost settings, using defaults:', error);
    return getDefaultCostSettings();
  }
}

function getDefaultCostSettings(): CostSettings {
  return {
    trackUsage: false,
    budgetLimit: 100,
    alertThreshold: 80,
    showCostEstimates: false,
    preferCheaperModels: false
  };
}

/**
 * ✅ Helper functions for toast notifications
 */
function getToastTitle(alert: ThresholdAlert): string {
  switch (alert.type) {
    case 'threshold_exceeded':
      return 'Alert Threshold Exceeded';
    case 'budget_exceeded':
      return 'Budget Exceeded';
    case 'cost_warning':
      return 'Cost Warning';
    default:
      return 'Budget Alert';
  }
}

function getNotificationMessage(alert: ThresholdAlert): string {
  const { currentCost, budgetLimit, utilizationPercentage } = alert;

  switch (alert.type) {
    case 'threshold_exceeded':
      return `⚠️ You've exceeded your alert threshold: $${currentCost.toFixed(2)} (${utilizationPercentage.toFixed(1)}% of $${budgetLimit.toFixed(2)} budget)`;
    case 'budget_exceeded':
      return `🚨 Budget exceeded: $${currentCost.toFixed(2)} (${utilizationPercentage.toFixed(1)}% of $${budgetLimit.toFixed(2)} budget)`;
    case 'cost_warning':
      return `💰 Cost warning: $${currentCost.toFixed(2)} (${utilizationPercentage.toFixed(1)}% of $${budgetLimit.toFixed(2)} budget)`;
    default:
      return alert.message || 'Budget alert triggered';
  }
}

/**
 * ✅ React Hook for Threshold Alerts
 * Integrates alert system with React components and toast notifications
 * Settings-safe: doesn't require SettingsProvider during initialization
 */
export function useThresholdAlerts() {
  const { toast } = useToast();
  const [alerts, setAlerts] = useState<ThresholdAlert[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [costSettings, setCostSettings] = useState<CostSettings>(getDefaultCostSettings());

  // ✅ Update cost settings periodically
  useEffect(() => {
    const updateSettings = () => {
      const newSettings = getSafeSettings();
      setCostSettings(newSettings);
    };

    // Update immediately
    updateSettings();

    // Update every 10 seconds to catch settings changes
    const interval = setInterval(updateSettings, 10000);
    return () => clearInterval(interval);
  }, []);

  // Initialize notification service with toast function
  useEffect(() => {
    if (!isInitialized) {
      notificationService.initialize(toast);
      setIsInitialized(true);
    }
  }, [toast, isInitialized]);

  // Subscribe to alert notifications
  useEffect(() => {
    const unsubscribe = alertManager.onAlert((alert: ThresholdAlert) => {
      // Update alerts state
      setAlerts(prev => [alert, ...prev.slice(0, 9)]); // Keep last 10 alerts

      // Show toast notification
      showAlertNotification(alert, {
        duration: alert.type === 'budget_exceeded' ? 15000 : 8000,
        showAction: true,
        actionText: 'View Settings',
        onAction: () => {
          // This would typically open settings
          console.log('Open cost settings');
        }
      });

      // Show Electron notification if available
      if (typeof window !== 'undefined' && window.electronAPI?.notification) {
        notificationService.showElectronNotification(alert);
      }
    });

    // Load existing alert history
    const history = alertManager.getAlertHistory();
    setAlerts(history.slice(0, 10)); // Show last 10 alerts

    return unsubscribe;
  }, []);

  // Manual threshold check
  const checkThresholds = useCallback(() => {
    if (!costSettings.trackUsage) {
      return null;
    }

    return alertManager.checkThresholds(costSettings);
  }, [costSettings]);

  // Force threshold check (for testing)
  const forceCheckThresholds = useCallback(() => {
    if (!costSettings.trackUsage) {
      return null;
    }

    return alertManager.forceCheckThresholds(costSettings);
  }, [costSettings]);

  // Acknowledge alert
  const acknowledgeAlert = useCallback((alertId: string) => {
    alertManager.acknowledgeAlert(alertId);
    setAlerts(prev =>
      prev.map(alert =>
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      )
    );
  }, []);

  // Clear alert history
  const clearAlerts = useCallback(() => {
    alertManager.clearAlertHistory();
    setAlerts([]);
  }, []);

  // Reset alerts for testing
  const resetAlertsForTesting = useCallback(() => {
    alertManager.resetAlertsForTesting();
    setAlerts([]);
  }, []);

  // Get alert statistics
  const getAlertStats = useCallback(() => {
    const total = alerts.length;
    const acknowledged = alerts.filter(a => a.acknowledged).length;
    const unacknowledged = total - acknowledged;
    const thresholdAlerts = alerts.filter(a => a.type === 'threshold_exceeded').length;
    const budgetAlerts = alerts.filter(a => a.type === 'budget_exceeded').length;

    return {
      total,
      acknowledged,
      unacknowledged,
      thresholdAlerts,
      budgetAlerts
    };
  }, [alerts]);

  return {
    alerts,
    checkThresholds,
    forceCheckThresholds,
    acknowledgeAlert,
    clearAlerts,
    resetAlertsForTesting,
    getAlertStats,
    isInitialized
  };
}

/**
 * ✅ Simple hook for just checking if alerts are enabled
 * Settings-safe: doesn't require SettingsProvider during initialization
 */
export function useAlertStatus() {
  const [costSettings, setCostSettings] = useState<CostSettings>(getDefaultCostSettings());

  // ✅ Update cost settings periodically
  useEffect(() => {
    const updateSettings = () => {
      const newSettings = getSafeSettings();
      setCostSettings(newSettings);
    };

    // Update immediately
    updateSettings();

    // Update every 10 seconds to catch settings changes
    const interval = setInterval(updateSettings, 10000);
    return () => clearInterval(interval);
  }, []);

  return {
    alertsEnabled: costSettings.trackUsage && costSettings.alertThreshold > 0,
    budgetLimit: costSettings.budgetLimit,
    alertThreshold: costSettings.alertThreshold
  };
}

/**
 * ✅ Hook for displaying alert notifications only
 */
export function useAlertNotifications() {
  const { toast } = useToast();
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize notification service
  useEffect(() => {
    if (!isInitialized) {
      notificationService.initialize(toast);
      setIsInitialized(true);
    }
  }, [toast, isInitialized]);

  // Subscribe to alerts for notifications only
  useEffect(() => {
    const unsubscribe = alertManager.onAlert((alert: ThresholdAlert) => {
      // ✅ Create ToastAction element properly to avoid React child error
      const actionElement = (
        <ToastAction
          altText="View Settings"
          onClick={() => {
            console.log('Navigate to cost settings');
            // TODO: Add navigation to settings
          }}
        >
          Settings
        </ToastAction>
      );

      // ✅ Show toast notification with proper React element action
      const toastOptions = {
        title: getToastTitle(alert),
        description: getNotificationMessage(alert),
        variant: alert.type === 'budget_exceeded' ? 'destructive' : 'default' as const,
        duration: alert.type === 'budget_exceeded' ? 12000 : 6000,
        action: actionElement
      };

      toast(toastOptions);
    });

    return unsubscribe;
  }, [toast]);

  return { isInitialized };
}

/**
 * ✅ Hook for manual alert testing
 * Settings-safe: doesn't require SettingsProvider during initialization
 */
export function useAlertTesting() {
  const { toast } = useToast();
  const [costSettings, setCostSettings] = useState<CostSettings>(getDefaultCostSettings());

  // ✅ Update cost settings periodically
  useEffect(() => {
    const updateSettings = () => {
      const newSettings = getSafeSettings();
      setCostSettings(newSettings);
    };

    // Update immediately
    updateSettings();

    // Update every 10 seconds to catch settings changes
    const interval = setInterval(updateSettings, 10000);
    return () => clearInterval(interval);
  }, []);

  const triggerTestAlert = useCallback((type: 'threshold' | 'budget') => {
    if (!costSettings.trackUsage) {
      console.warn('Alert testing: Cost tracking is disabled');
      return;
    }

    // Create a test alert
    const testAlert: ThresholdAlert = {
      id: `test_${type}_${Date.now()}`,
      type: type === 'threshold' ? 'threshold_exceeded' : 'budget_exceeded',
      message: type === 'threshold'
        ? `⚠️ Test Alert: You've exceeded your alert threshold`
        : `🚨 Test Alert: Budget exceeded`,
      currentCost: type === 'threshold' ? costSettings.budgetLimit * 0.85 : costSettings.budgetLimit * 1.1,
      threshold: type === 'threshold'
        ? (costSettings.budgetLimit * costSettings.alertThreshold) / 100
        : costSettings.budgetLimit,
      budgetLimit: costSettings.budgetLimit,
      utilizationPercentage: type === 'threshold' ? 85 : 110,
      timestamp: Date.now(),
      acknowledged: false,
      month: new Date().toISOString().slice(0, 7) // YYYY-MM
    };

    // ✅ Create ToastAction element properly for test alerts
    const actionElement = (
      <ToastAction
        altText="Test Alert Action"
        onClick={() => console.log('Test alert action clicked')}
      >
        Test Alert
      </ToastAction>
    );

    // ✅ Show test alert with proper React element action
    const toastOptions = {
      title: getToastTitle(testAlert),
      description: getNotificationMessage(testAlert),
      variant: testAlert.type === 'budget_exceeded' ? 'destructive' : 'default' as const,
      duration: 8000,
      action: actionElement
    };

    // ✅ Use the toast hook directly
    toast(toastOptions);

    console.log(`Alert testing: Triggered ${type} alert`);
    return testAlert;
  }, [costSettings, toast]);

  return {
    triggerTestAlert,
    canTest: costSettings.trackUsage
  };
}
