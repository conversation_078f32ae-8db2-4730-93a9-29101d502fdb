// components/budget/budget-status.tsx
"use client"

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DollarSign, AlertTriangle, TrendingUp, Activity } from 'lucide-react';
import { budgetEnforcer } from '../../lib/budget-enforcer';
import { CostSettings } from '../settings/settings-manager';
import { BudgetStatus as BudgetStatusType } from '../../lib/cost-tracker';

/**
 * ✅ Settings-safe function to get cost settings
 */
function getSafeSettings(): CostSettings {
  try {
    if (typeof window !== 'undefined') {
      const globalSettings = (window as any).__globalSettingsManager;
      if (globalSettings) {
        const settings = globalSettings.getSettings();
        return settings.cost || getDefaultCostSettings();
      }
    }
    return getDefaultCostSettings();
  } catch (error) {
    console.warn('Failed to get cost settings, using defaults:', error);
    return getDefaultCostSettings();
  }
}

function getDefaultCostSettings(): CostSettings {
  return {
    trackUsage: false,
    budgetLimit: 100,
    alertThreshold: 80,
    showCostEstimates: false,
    preferCheaperModels: false
  };
}

/**
 * ✅ Budget Status Component
 * Displays current budget utilization and alerts
 * Settings-safe: doesn't require SettingsProvider during initialization
 */
export function BudgetStatus() {
  const [budgetStatus, setBudgetStatus] = useState<BudgetStatusType | null>(null);
  const [monthlySummary, setMonthlySummary] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [costSettings, setCostSettings] = useState<CostSettings>(getDefaultCostSettings());

  // ✅ Update cost settings periodically
  useEffect(() => {
    const updateSettings = () => {
      const newSettings = getSafeSettings();
      setCostSettings(newSettings);
    };

    updateSettings();
    const interval = setInterval(updateSettings, 10000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const updateBudgetStatus = () => {
      try {
        if (!costSettings.trackUsage) {
          setBudgetStatus(null);
          setMonthlySummary(null);
          setLoading(false);
          return;
        }

        const status = budgetEnforcer.getBudgetStatus(costSettings);
        const summary = budgetEnforcer.getMonthlyCostSummary();

        setBudgetStatus(status);
        setMonthlySummary(summary);
        setLoading(false);
      } catch (error) {
        console.error('Failed to update budget status:', error);
        setLoading(false);
      }
    };

    // Update immediately
    updateBudgetStatus();

    // Update every 30 seconds
    const interval = setInterval(updateBudgetStatus, 30000);

    return () => clearInterval(interval);
  }, [costSettings]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Budget Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">Loading budget status...</div>
        </CardContent>
      </Card>
    );
  }

  if (!costSettings.trackUsage || !budgetStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Budget Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">
            Budget tracking is disabled. Enable it in Cost Settings to monitor usage.
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = () => {
    if (budgetStatus.isOverBudget) return 'destructive';
    if (budgetStatus.isNearThreshold) return 'warning';
    return 'default';
  };

  const getStatusText = () => {
    if (budgetStatus.isOverBudget) return 'Over Budget';
    if (budgetStatus.isNearThreshold) return 'Near Limit';
    return 'Within Budget';
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Budget Status
            <Badge variant={getStatusColor()}>{getStatusText()}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Budget Alert */}
          {budgetStatus.isOverBudget && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                You have exceeded your monthly budget limit of ${budgetStatus.budgetLimit.toFixed(2)}.
                New LLM requests will be blocked until next month or budget limit is increased.
              </AlertDescription>
            </Alert>
          )}

          {budgetStatus.isNearThreshold && !budgetStatus.isOverBudget && (
            <Alert variant="default">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                You have used {budgetStatus.utilizationPercentage.toFixed(1)}% of your monthly budget.
                Consider monitoring usage to avoid exceeding the limit.
              </AlertDescription>
            </Alert>
          )}

          {/* Budget Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Monthly Usage</span>
              <span className="font-mono">
                ${budgetStatus.currentMonthlyCost.toFixed(2)} / ${budgetStatus.budgetLimit.toFixed(2)}
              </span>
            </div>
            <Progress
              value={Math.min(budgetStatus.utilizationPercentage, 100)}
              className="h-2"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{budgetStatus.utilizationPercentage.toFixed(1)}% used</span>
              <span>${budgetStatus.remainingBudget.toFixed(2)} remaining</span>
            </div>
          </div>

          {/* Monthly Summary */}
          {monthlySummary && (
            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="space-y-1">
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Activity className="h-3 w-3" />
                  API Calls
                </div>
                <div className="font-mono text-lg">{monthlySummary.callCount}</div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <TrendingUp className="h-3 w-3" />
                  Tokens Used
                </div>
                <div className="font-mono text-lg">{monthlySummary.totalTokens.toLocaleString()}</div>
              </div>
            </div>
          )}

          {/* Provider Breakdown */}
          {monthlySummary && Object.keys(monthlySummary.providerBreakdown).length > 0 && (
            <div className="space-y-2 pt-4 border-t">
              <div className="text-sm font-medium">Provider Breakdown</div>
              {Object.entries(monthlySummary.providerBreakdown).map(([provider, data]: [string, any]) => (
                <div key={provider} className="flex justify-between text-sm">
                  <span className="capitalize">{provider}</span>
                  <span className="font-mono">${data.cost.toFixed(4)}</span>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * ✅ Compact Budget Status for smaller spaces
 * Settings-safe: doesn't require SettingsProvider during initialization
 */
export function CompactBudgetStatus() {
  const [budgetStatus, setBudgetStatus] = useState<BudgetStatusType | null>(null);
  const [costSettings, setCostSettings] = useState<CostSettings>(getDefaultCostSettings());

  // ✅ Update cost settings periodically
  useEffect(() => {
    const updateSettings = () => {
      const newSettings = getSafeSettings();
      setCostSettings(newSettings);
    };

    updateSettings();
    const interval = setInterval(updateSettings, 10000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const updateBudgetStatus = () => {
      try {
        if (!costSettings.trackUsage) {
          setBudgetStatus(null);
          return;
        }

        const status = budgetEnforcer.getBudgetStatus(costSettings);
        setBudgetStatus(status);
      } catch (error) {
        console.error('Failed to update compact budget status:', error);
      }
    };

    updateBudgetStatus();
    const interval = setInterval(updateBudgetStatus, 30000);
    return () => clearInterval(interval);
  }, [costSettings]);

  if (!costSettings.trackUsage || !budgetStatus) {
    return null;
  }

  const getStatusColor = () => {
    if (budgetStatus.isOverBudget) return 'text-red-500';
    if (budgetStatus.isNearThreshold) return 'text-yellow-500';
    return 'text-green-500';
  };

  return (
    <div className="flex items-center gap-2 text-sm">
      <DollarSign className={`h-4 w-4 ${getStatusColor()}`} />
      <span className="font-mono">
        ${budgetStatus.currentMonthlyCost.toFixed(2)} / ${budgetStatus.budgetLimit.toFixed(2)}
      </span>
      <span className="text-muted-foreground">
        ({budgetStatus.utilizationPercentage.toFixed(1)}%)
      </span>
    </div>
  );
}
