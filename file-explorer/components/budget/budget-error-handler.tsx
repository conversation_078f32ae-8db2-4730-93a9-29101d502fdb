// components/budget/budget-error-handler.tsx
"use client"

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, DollarSign, Settings, TrendingUp } from 'lucide-react';
import { BudgetExceededError, isBudgetExceededError } from '../../lib/budget-error';

interface BudgetErrorHandlerProps {
  error: Error | BudgetExceededError;
  onRetry?: () => void;
  onOpenSettings?: () => void;
  className?: string;
}

/**
 * ✅ Budget Error Handler Component
 * Displays budget exceeded errors with actionable information
 */
export function BudgetErrorHandler({ 
  error, 
  onRetry, 
  onOpenSettings,
  className = ""
}: BudgetErrorHandlerProps) {
  if (!isBudgetExceededError(error)) {
    return null;
  }

  const budgetError = error as BudgetExceededError;
  const details = budgetError.getDetails();

  return (
    <div className={`space-y-4 ${className}`}>
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle className="flex items-center gap-2">
          💸 Budget Exceeded
          <Badge variant="destructive">Request Blocked</Badge>
        </AlertTitle>
        <AlertDescription className="mt-2">
          {budgetError.getUserMessage()}
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <DollarSign className="h-4 w-4" />
            Budget Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Cost Breakdown */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Current Monthly Cost</div>
              <div className="font-mono text-lg">${details.currentCost.toFixed(2)}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Budget Limit</div>
              <div className="font-mono text-lg">${details.budgetLimit.toFixed(2)}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Estimated Request Cost</div>
              <div className="font-mono text-lg text-red-500">${details.estimatedCost.toFixed(4)}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Remaining Budget</div>
              <div className="font-mono text-lg">${details.remainingBudget.toFixed(2)}</div>
            </div>
          </div>

          {/* Request Details */}
          <div className="pt-4 border-t space-y-2">
            <div className="text-sm font-medium">Blocked Request</div>
            <div className="flex items-center gap-2 text-sm">
              <Badge variant="outline">{details.provider}</Badge>
              <span className="text-muted-foreground">/</span>
              <Badge variant="outline">{details.model}</Badge>
            </div>
            <div className="text-sm text-muted-foreground">
              This request would have cost ${details.estimatedCost.toFixed(4)} and brought your 
              monthly total to ${details.wouldCost.toFixed(2)}, exceeding your budget by 
              ${details.overageAmount.toFixed(2)}.
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t">
            {onOpenSettings && (
              <Button 
                variant="default" 
                size="sm" 
                onClick={onOpenSettings}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Increase Budget
              </Button>
            )}
            {onRetry && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onRetry}
                className="flex items-center gap-2"
              >
                <TrendingUp className="h-4 w-4" />
                Retry Request
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * ✅ Compact Budget Error for inline display
 */
export function CompactBudgetError({ 
  error, 
  onOpenSettings,
  className = ""
}: Omit<BudgetErrorHandlerProps, 'onRetry'>) {
  if (!isBudgetExceededError(error)) {
    return null;
  }

  const budgetError = error as BudgetExceededError;
  const details = budgetError.getDetails();

  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <strong>Budget exceeded:</strong> ${details.estimatedCost.toFixed(4)} request blocked 
          (${details.remainingBudget.toFixed(2)} remaining)
        </div>
        {onOpenSettings && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onOpenSettings}
            className="ml-2"
          >
            <Settings className="h-4 w-4 mr-1" />
            Settings
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
}

/**
 * ✅ Budget Error Toast Message
 */
export function getBudgetErrorToastMessage(error: Error): string | null {
  if (!isBudgetExceededError(error)) {
    return null;
  }

  const budgetError = error as BudgetExceededError;
  const details = budgetError.getDetails();

  return `💸 Budget exceeded: ${details.provider}/${details.model} request ($${details.estimatedCost.toFixed(4)}) blocked. Remaining budget: $${details.remainingBudget.toFixed(2)}`;
}

/**
 * ✅ Check if error is budget-related for conditional rendering
 */
export function isBudgetError(error: any): boolean {
  return isBudgetExceededError(error);
}
