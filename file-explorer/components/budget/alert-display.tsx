// components/budget/alert-display.tsx
"use client"

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  X, 
  Bell,
  Settings,
  Trash2
} from 'lucide-react';
import { useThresholdAlerts, useAlertTesting } from './use-threshold-alerts';
import { ThresholdAlert } from '../../lib/alert-manager';

/**
 * ✅ Alert Display Component
 * Shows threshold alerts with management actions
 */
export function AlertDisplay() {
  const { 
    alerts, 
    acknowledgeAlert, 
    clearAlerts, 
    resetAlertsForTesting,
    getAlertStats 
  } = useThresholdAlerts();
  
  const { triggerTestAlert, canTest } = useAlertTesting();
  const stats = getAlertStats();

  const getAlertIcon = (alert: ThresholdAlert) => {
    switch (alert.type) {
      case 'budget_exceeded':
        return <DollarSign className="h-4 w-4 text-red-500" />;
      case 'threshold_exceeded':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Bell className="h-4 w-4 text-blue-500" />;
    }
  };

  const getAlertVariant = (alert: ThresholdAlert) => {
    switch (alert.type) {
      case 'budget_exceeded':
        return 'destructive';
      case 'threshold_exceeded':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  if (alerts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Budget Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <p className="text-muted-foreground">No budget alerts</p>
            <p className="text-sm text-muted-foreground mt-1">
              You'll be notified when you approach your budget limits
            </p>
            
            {/* Test Controls */}
            {canTest && (
              <div className="mt-6 space-y-2">
                <p className="text-xs text-muted-foreground">Test Alerts:</p>
                <div className="flex gap-2 justify-center">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => triggerTestAlert('threshold')}
                  >
                    Test Threshold
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => triggerTestAlert('budget')}
                  >
                    Test Budget
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Budget Alerts
            {stats.unacknowledged > 0 && (
              <Badge variant="destructive">{stats.unacknowledged}</Badge>
            )}
          </div>
          <div className="flex gap-2">
            {canTest && (
              <>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => triggerTestAlert('threshold')}
                  title="Test threshold alert"
                >
                  Test
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={resetAlertsForTesting}
                  title="Reset alerts for testing"
                >
                  Reset
                </Button>
              </>
            )}
            <Button 
              variant="ghost" 
              size="sm"
              onClick={clearAlerts}
              title="Clear all alerts"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Alert Statistics */}
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-600">{stats.thresholdAlerts}</div>
            <div className="text-xs text-muted-foreground">Threshold</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-red-600">{stats.budgetAlerts}</div>
            <div className="text-xs text-muted-foreground">Budget</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">{stats.acknowledged}</div>
            <div className="text-xs text-muted-foreground">Resolved</div>
          </div>
        </div>

        {/* Alert List */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {alerts.map((alert) => (
            <Alert 
              key={alert.id} 
              variant={alert.acknowledged ? 'default' : getAlertVariant(alert)}
              className={alert.acknowledged ? 'opacity-60' : ''}
            >
              <div className="flex items-start justify-between w-full">
                <div className="flex items-start gap-3 flex-1">
                  {getAlertIcon(alert)}
                  <div className="flex-1 min-w-0">
                    <AlertDescription className="text-sm">
                      {alert.message}
                    </AlertDescription>
                    <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatTimestamp(alert.timestamp)}
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        ${alert.currentCost.toFixed(2)} / ${alert.budgetLimit.toFixed(2)}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {alert.utilizationPercentage.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  {alert.acknowledged ? (
                    <Badge variant="outline" className="text-xs">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Resolved
                    </Badge>
                  ) : (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => acknowledgeAlert(alert.id)}
                      title="Mark as resolved"
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </Alert>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {stats.unacknowledged > 0 
              ? `${stats.unacknowledged} unresolved alert${stats.unacknowledged > 1 ? 's' : ''}`
              : 'All alerts resolved'
            }
          </div>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Cost Settings
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * ✅ Compact Alert Banner for header/status bar
 */
export function AlertBanner() {
  const { alerts, getAlertStats } = useThresholdAlerts();
  const stats = getAlertStats();

  if (stats.unacknowledged === 0) {
    return null;
  }

  const latestAlert = alerts.find(a => !a.acknowledged);
  if (!latestAlert) {
    return null;
  }

  return (
    <Alert variant={latestAlert.type === 'budget_exceeded' ? 'destructive' : 'default'}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <span>{latestAlert.message}</span>
        <Badge variant="outline">
          {stats.unacknowledged} alert{stats.unacknowledged > 1 ? 's' : ''}
        </Badge>
      </AlertDescription>
    </Alert>
  );
}
