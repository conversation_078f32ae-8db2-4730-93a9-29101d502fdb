// components/settings/isolated-privacy-tab.tsx
import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { PrivacySettings } from './settings-manager';

interface IsolatedPrivacyTabProps {
  settings: PrivacySettings;
  updatePrivacySettings: (updates: Partial<PrivacySettings>) => void;
}

/**
 * ✅ Isolated Privacy Tab Component
 * Uses local state for immediate UI feedback
 * Follows the same pattern as the working Agents tab
 */
export const IsolatedPrivacyTab = React.memo<IsolatedPrivacyTabProps>(({
  settings,
  updatePrivacySettings
}) => {
  // ✅ Local state for immediate UI feedback
  const [localSettings, setLocalSettings] = useState<PrivacySettings>(settings);

  useEffect(() => {
    console.log('🔄 IsolatedPrivacyTab rendered');
  });

  // ✅ Sync local state when global settings change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  // ✅ Immediate toggle handler with local state
  const handleToggle = useCallback((key: keyof PrivacySettings) => {
    console.time('privacy-toggle-latency');
    const newValue = !localSettings[key];
    setLocalSettings(prev => ({
      ...prev,
      [key]: newValue
    }));
    updatePrivacySettings({ [key]: newValue });
    console.timeEnd('privacy-toggle-latency');
  }, [localSettings, updatePrivacySettings]);

  // ✅ Individual slider handlers (matching Agents tab pattern)
  const handleMaxHistoryDaysChange = useCallback((value: number) => {
    console.time('privacy-slider-latency');
    setLocalSettings(prev => ({ ...prev, maxHistoryDays: value }));
    console.timeEnd('privacy-slider-latency');
  }, []);

  // ✅ Commit handlers (matching Agents tab pattern)
  const commitMaxHistoryDays = useCallback(() => {
    console.time('privacy-slider-commit');
    updatePrivacySettings({ maxHistoryDays: localSettings.maxHistoryDays });
    console.timeEnd('privacy-slider-commit');
  }, [localSettings.maxHistoryDays, updatePrivacySettings]);

  // ✅ Memoized toggle component
  const PrivacyToggle = React.memo(({
    id,
    label,
    settingKey
  }: {
    id: string;
    label: string;
    settingKey: keyof PrivacySettings;
  }) => (
    <div className="flex items-center justify-between">
      <Label htmlFor={id}>{label}</Label>
      <Switch
        id={id}
        checked={localSettings[settingKey] as boolean}
        onCheckedChange={() => handleToggle(settingKey)}
      />
    </div>
  ));

  // ✅ Memoized slider component (matching Agents tab pattern)
  const MaxHistoryDaysSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Max History Days</Label>
      <Slider
        value={[localSettings.maxHistoryDays]}
        onValueChange={([value]) => handleMaxHistoryDaysChange(value)}
        onPointerUp={commitMaxHistoryDays}
        min={1}
        max={365}
        step={1}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.maxHistoryDays} days
      </div>
    </div>
  ));

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Privacy Settings</CardTitle>
          <CardDescription>Control data sharing and privacy</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <PrivacyToggle
            id="local-only"
            label="Local Only Mode"
            settingKey="localOnly"
          />

          <PrivacyToggle
            id="encrypt-prompts"
            label="Encrypt Prompts"
            settingKey="encryptPrompts"
          />

          <PrivacyToggle
            id="clear-history"
            label="Clear History on Exit"
            settingKey="clearHistoryOnExit"
          />

          <MaxHistoryDaysSlider />
        </CardContent>
      </Card>
    </div>
  );
});

IsolatedPrivacyTab.displayName = 'IsolatedPrivacyTab';
