// components/settings/isolated-cost-tab.tsx
import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { CostSettings } from './settings-manager';
import { BudgetStatus } from '../budget/budget-status';
import { AlertDisplay } from '../budget/alert-display';
import { useAlertNotifications } from '../budget/use-threshold-alerts';

interface IsolatedCostTabProps {
  settings: CostSettings;
  updateCostSettings: (updates: Partial<CostSettings>) => void;
}

/**
 * ✅ Isolated Cost Tab Component
 * Uses local state for immediate UI feedback
 * Follows the same pattern as the working Agents tab
 */
export const IsolatedCostTab = React.memo<IsolatedCostTabProps>(({
  settings,
  updateCostSettings
}) => {
  // ✅ Local state for immediate UI feedback
  const [localSettings, setLocalSettings] = useState<CostSettings>(settings);

  // ✅ Initialize alert notifications
  useAlertNotifications();

  useEffect(() => {
    console.log('🔄 IsolatedCostTab rendered');
  });

  // ✅ Sync local state when global settings change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  // ✅ Immediate toggle handler with local state
  const handleToggle = useCallback((key: keyof CostSettings) => {
    console.time('cost-toggle-latency');
    const newValue = !localSettings[key];
    setLocalSettings(prev => ({
      ...prev,
      [key]: newValue
    }));
    updateCostSettings({ [key]: newValue });
    console.timeEnd('cost-toggle-latency');
  }, [localSettings, updateCostSettings]);

  // ✅ Individual slider handlers (matching Agents tab pattern)
  const handleAlertThresholdChange = useCallback((value: number) => {
    console.time('cost-slider-latency');
    setLocalSettings(prev => ({ ...prev, alertThreshold: value }));
    console.timeEnd('cost-slider-latency');
  }, []);

  // ✅ Commit handlers (matching Agents tab pattern)
  const commitAlertThreshold = useCallback(() => {
    console.time('cost-slider-commit');
    updateCostSettings({ alertThreshold: localSettings.alertThreshold });
    console.timeEnd('cost-slider-commit');
  }, [localSettings.alertThreshold, updateCostSettings]);

  // ✅ Input handler with debounced commit
  const handleInputChange = useCallback((key: keyof CostSettings, value: string) => {
    const numericValue = parseFloat(value) || 0;
    setLocalSettings(prev => ({
      ...prev,
      [key]: numericValue
    }));

    // Debounced commit for input fields
    setTimeout(() => {
      updateCostSettings({ [key]: numericValue });
    }, 500);
  }, [updateCostSettings]);

  // ✅ Memoized toggle component
  const CostToggle = React.memo(({
    id,
    label,
    settingKey
  }: {
    id: string;
    label: string;
    settingKey: keyof CostSettings;
  }) => (
    <div className="flex items-center justify-between">
      <Label htmlFor={id}>{label}</Label>
      <Switch
        id={id}
        checked={localSettings[settingKey] as boolean}
        onCheckedChange={() => handleToggle(settingKey)}
      />
    </div>
  ));

  // ✅ Memoized slider component (matching Agents tab pattern)
  const AlertThresholdSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Alert Threshold (%)</Label>
      <Slider
        value={[localSettings.alertThreshold]}
        onValueChange={([value]) => handleAlertThresholdChange(value)}
        onPointerUp={commitAlertThreshold}
        min={10}
        max={100}
        step={5}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.alertThreshold}%
      </div>
    </div>
  ));

  // ✅ Memoized input component
  const CostInput = React.memo(({
    label,
    settingKey,
    type = "number"
  }: {
    label: string;
    settingKey: keyof CostSettings;
    type?: string;
  }) => (
    <div className="space-y-2">
      <Label>{label}</Label>
      <Input
        type={type}
        value={localSettings[settingKey]}
        onChange={(e) => handleInputChange(settingKey, e.target.value)}
      />
    </div>
  ));

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Cost Management</CardTitle>
          <CardDescription>Configure budget and cost tracking</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <CostInput
            label="Monthly Budget Limit ($)"
            settingKey="budgetLimit"
          />

          <AlertThresholdSlider />

          <CostToggle
            id="track-usage"
            label="Track Usage"
            settingKey="trackUsage"
          />

          <CostToggle
            id="show-estimates"
            label="Show Cost Estimates"
            settingKey="showCostEstimates"
          />

          <CostToggle
            id="prefer-cheaper"
            label="Prefer Cheaper Models"
            settingKey="preferCheaperModels"
          />
        </CardContent>
      </Card>

      {/* ✅ Budget Status Display */}
      <BudgetStatus />

      {/* ✅ Alert Display */}
      <AlertDisplay />
    </div>
  );
});

IsolatedCostTab.displayName = 'IsolatedCostTab';
