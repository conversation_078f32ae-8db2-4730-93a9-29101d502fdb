// components/settings/global-settings.ts
import { SettingsManager } from './settings-manager';

/**
 * ✅ Global SettingsManager Instance
 * Ensures single source of truth for settings across the app
 */
let globalSettingsManager: SettingsManager | null = null;

export const getGlobalSettingsManager = (): SettingsManager => {
  if (!globalSettingsManager) {
    globalSettingsManager = new SettingsManager();

    // ✅ Expose to window for settings-safe hooks
    if (typeof window !== 'undefined') {
      (window as any).__globalSettingsManager = globalSettingsManager;
    }
  }
  return globalSettingsManager;
};
