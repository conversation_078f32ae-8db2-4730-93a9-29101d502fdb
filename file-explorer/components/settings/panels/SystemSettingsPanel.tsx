// components/settings/panels/SystemSettingsPanel.tsx
import React from 'react';
import { SettingsManager } from '../settings-manager';
import { IsolatedSystemTab } from '../isolated-system-tab';
import { useSettings } from '../settings-context';

interface SystemSettingsPanelProps {
  settingsManager: SettingsManager;
}

/**
 * ✅ System Settings Panel
 * Wraps the existing IsolatedSystemTab for the centralized settings UI
 */
const SystemSettingsPanel: React.FC<SystemSettingsPanelProps> = ({ settingsManager }) => {
  const { settings } = useSettings();

  const updateSystemSettings = (updates: any) => {
    settingsManager.updateSystemSettings(updates);
  };

  return (
    <IsolatedSystemTab
      settings={settings.system}
      updateSystemSettings={updateSystemSettings}
    />
  );
};

export default SystemSettingsPanel;
