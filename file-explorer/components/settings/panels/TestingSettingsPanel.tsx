// components/settings/panels/TestingSettingsPanel.tsx
import React from 'react';
import { SettingsManager } from '../settings-manager';
import { CompleteAgentManager } from '../../agents/agent-manager-complete';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TestTube, Database, Zap, Bug } from 'lucide-react';

interface TestingSettingsPanelProps {
  settingsManager: SettingsManager;
  agentManager?: CompleteAgentManager;
}

/**
 * ✅ Testing Settings Panel
 * Development and testing tools for the application
 */
const TestingSettingsPanel: React.FC<TestingSettingsPanelProps> = ({ 
  settingsManager, 
  agentManager 
}) => {
  const handleDatabaseBackup = async () => {
    try {
      const backupPath = `backup-${Date.now()}.db`;
      await settingsManager.backupDatabase(backupPath);
      console.log('Database backup created:', backupPath);
    } catch (error) {
      console.error('Database backup failed:', error);
    }
  };

  const handleDatabaseRestore = async () => {
    try {
      // This would need file picker implementation
      console.log('Database restore functionality would go here');
    } catch (error) {
      console.error('Database restore failed:', error);
    }
  };

  const handleClearCache = async () => {
    try {
      // Clear various caches
      if (typeof window !== 'undefined') {
        localStorage.removeItem('settings-cache');
        sessionStorage.clear();
      }
      console.log('Cache cleared');
    } catch (error) {
      console.error('Cache clear failed:', error);
    }
  };

  const handleTestAgents = async () => {
    if (!agentManager) {
      console.warn('Agent manager not available');
      return;
    }

    try {
      console.log('Testing agent connectivity...');
      // This would test agent connections
    } catch (error) {
      console.error('Agent test failed:', error);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <TestTube className="h-5 w-5" />
        <h3 className="text-lg font-semibold">Development & Testing Tools</h3>
        <Badge variant="outline" className="text-xs">DEV ONLY</Badge>
      </div>

      {/* Database Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Database Management
          </CardTitle>
          <CardDescription>
            Backup and restore application database
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex gap-2">
            <Button onClick={handleDatabaseBackup} variant="outline">
              Create Backup
            </Button>
            <Button onClick={handleDatabaseRestore} variant="outline">
              Restore Backup
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Cache Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Cache Management
          </CardTitle>
          <CardDescription>
            Clear application caches and temporary data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleClearCache} variant="outline">
            Clear All Caches
          </Button>
        </CardContent>
      </Card>

      {/* Agent Testing */}
      {agentManager && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bug className="h-4 w-4" />
              Agent Testing
            </CardTitle>
            <CardDescription>
              Test agent connectivity and functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleTestAgents} variant="outline">
              Test Agent Connections
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Development Info */}
      <Card>
        <CardHeader>
          <CardTitle>Development Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="text-sm">
            <strong>Environment:</strong> {process.env.NODE_ENV || 'development'}
          </div>
          <div className="text-sm">
            <strong>Build Time:</strong> {new Date().toISOString()}
          </div>
          <div className="text-sm">
            <strong>Agent Manager:</strong> {agentManager ? 'Available' : 'Not Available'}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestingSettingsPanel;
