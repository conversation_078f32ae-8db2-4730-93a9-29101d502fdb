// components/settings/panels/AgentSettingsPanel.tsx
import React from 'react';
import { SettingsManager } from '../settings-manager';
import { CompleteAgentManager } from '../../agents/agent-manager-complete';
import { IsolatedAgentCard } from '../isolated-agent-card';
import { useSettings } from '../settings-context';
import { getAllProviders, getProviderModels } from '../../agents/llm-provider-registry';

interface AgentSettingsPanelProps {
  settingsManager: SettingsManager;
  agentManager?: CompleteAgentManager;
}

/**
 * ✅ Agent Settings Panel
 * Uses the same pattern as the main settings UI with IsolatedAgentCard components
 */
const AgentSettingsPanel: React.FC<AgentSettingsPanelProps> = ({
  settingsManager,
  agentManager
}) => {
  const { settings } = useSettings();

  // ✅ Provider data for agent configuration
  const providerData = {
    providers: getAllProviders(),
    getModelsForProvider: getProviderModels
  };

  const updateAgent = (agentId: string, updates: any) => {
    settingsManager.updateAgentSettings(agentId, updates);
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-4">
        {settings.agents.map((agent) => (
          <IsolatedAgentCard
            key={agent.id}
            agent={agent}
            providers={providerData.providers}
            getModelsForProvider={providerData.getModelsForProvider}
            updateAgent={updateAgent}
          />
        ))}
      </div>
    </div>
  );
};

export default AgentSettingsPanel;
