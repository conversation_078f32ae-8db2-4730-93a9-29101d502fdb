// components/settings/panels/CostSettingsPanel.tsx
import React from 'react';
import { SettingsManager } from '../settings-manager';
import { IsolatedCostTab } from '../isolated-cost-tab';
import { useSettings } from '../settings-context';

interface CostSettingsPanelProps {
  settingsManager: SettingsManager;
}

/**
 * ✅ Cost Settings Panel
 * Wraps the existing IsolatedCostTab for the centralized settings UI
 */
const CostSettingsPanel: React.FC<CostSettingsPanelProps> = ({ settingsManager }) => {
  const { settings } = useSettings();

  const updateCostSettings = (updates: any) => {
    settingsManager.updateCostSettings(updates);
  };

  return (
    <IsolatedCostTab
      settings={settings.cost}
      updateCostSettings={updateCostSettings}
    />
  );
};

export default CostSettingsPanel;
