// components/settings/panels/EditorSettingsPanel.tsx
import React from 'react';
import { SettingsManager } from '../settings-manager';
import { IsolatedEditorTab } from '../isolated-editor-tab';
import { useSettings } from '../settings-context';

interface EditorSettingsPanelProps {
  settingsManager: SettingsManager;
}

/**
 * ✅ Editor Settings Panel
 * Wraps the existing IsolatedEditorTab for the centralized settings UI
 */
const EditorSettingsPanel: React.FC<EditorSettingsPanelProps> = ({ settingsManager }) => {
  const { settings } = useSettings();

  const updateEditorSettings = (updates: any) => {
    settingsManager.updateEditorSettings(updates);
  };

  return (
    <IsolatedEditorTab
      settings={settings.editor}
      updateEditorSettings={updateEditorSettings}
    />
  );
};

export default EditorSettingsPanel;
