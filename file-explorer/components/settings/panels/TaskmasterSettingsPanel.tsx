// components/settings/panels/TaskmasterSettingsPanel.tsx
import React from 'react';
import { SettingsManager } from '../settings-manager';
import { IsolatedTaskmasterTab } from '../isolated-taskmaster-tab';
import { useSettings } from '../settings-context';

interface TaskmasterSettingsPanelProps {
  settingsManager: SettingsManager;
}

/**
 * ✅ Taskmaster Settings Panel
 * Wraps the existing IsolatedTaskmasterTab for the centralized settings UI
 */
const TaskmasterSettingsPanel: React.FC<TaskmasterSettingsPanelProps> = ({ settingsManager }) => {
  const { settings } = useSettings();

  const updateTaskmasterSettings = (updates: any) => {
    settingsManager.updateTaskmasterSettings(updates);
  };

  return (
    <IsolatedTaskmasterTab
      settings={settings.taskmaster}
      updateTaskmasterSettings={updateTaskmasterSettings}
    />
  );
};

export default TaskmasterSettingsPanel;
