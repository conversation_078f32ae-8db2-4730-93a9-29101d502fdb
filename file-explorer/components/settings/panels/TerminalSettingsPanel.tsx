// components/settings/panels/TerminalSettingsPanel.tsx
import React from 'react';
import { SettingsManager } from '../settings-manager';
import { IsolatedTerminalTab } from '../isolated-terminal-tab';
import { useSettings } from '../settings-context';

interface TerminalSettingsPanelProps {
  settingsManager: SettingsManager;
}

/**
 * ✅ Terminal Settings Panel
 * Wraps the existing IsolatedTerminalTab for the centralized settings UI
 */
const TerminalSettingsPanel: React.FC<TerminalSettingsPanelProps> = ({ settingsManager }) => {
  const { settings } = useSettings();

  const updateTerminalSettings = (updates: any) => {
    settingsManager.updateTerminalSettings(updates);
  };

  return (
    <IsolatedTerminalTab
      settings={settings.terminal}
      updateTerminalSettings={updateTerminalSettings}
    />
  );
};

export default TerminalSettingsPanel;
