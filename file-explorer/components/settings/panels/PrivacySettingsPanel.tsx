// components/settings/panels/PrivacySettingsPanel.tsx
import React from 'react';
import { SettingsManager } from '../settings-manager';
import { IsolatedPrivacyTab } from '../isolated-privacy-tab';
import { useSettings } from '../settings-context';

interface PrivacySettingsPanelProps {
  settingsManager: SettingsManager;
}

/**
 * ✅ Privacy Settings Panel
 * Wraps the existing IsolatedPrivacyTab for the centralized settings UI
 */
const PrivacySettingsPanel: React.FC<PrivacySettingsPanelProps> = ({ settingsManager }) => {
  const { settings } = useSettings();

  const updatePrivacySettings = (updates: any) => {
    settingsManager.updatePrivacySettings(updates);
  };

  return (
    <IsolatedPrivacyTab
      settings={settings.privacy}
      updatePrivacySettings={updatePrivacySettings}
    />
  );
};

export default PrivacySettingsPanel;
