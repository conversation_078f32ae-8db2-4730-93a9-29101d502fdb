// components/settings/isolated-agent-card.tsx
import React, { useEffect, useMemo, useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Info } from 'lucide-react';
import { AgentSettings } from './settings-manager';
import {
  TemperatureSlider,
  ToggleSwitch,
  AgentProviderSelect,
  AgentModelSelect,
  AgentMaxTokensInput,
  AgentCustomPromptTextarea
} from './isolated-agent-controls';

/**
 * Temperature compatibility check (from Task 26)
 */
function getTemperatureSupportInfo(provider: string, modelId: string): {
  supported: boolean;
  reason: string;
} {
  const TEMPERATURE_COMPATIBILITY: Record<string, { supported: boolean; excludedModels?: string[]; reason: string }> = {
    openai: { supported: true, reason: 'OpenAI API supports temperature for all models' },
    anthropic: { supported: true, reason: 'Anthropic Claude API supports temperature for all models' },
    openrouter: { supported: true, reason: 'OpenRouter proxies temperature to underlying providers' },
    azure: { supported: true, reason: 'Azure OpenAI uses same API as OpenAI' },
    google: { supported: true, reason: 'Google Gemini API supports temperature parameter' },
    deepseek: {
      supported: true,
      excludedModels: ['deepseek-reasoner'],
      reason: 'DeepSeek API supports temperature except for reasoning models'
    },
    fireworks: { supported: true, reason: 'Fireworks AI API supports temperature for all models' }
  };

  const config = TEMPERATURE_COMPATIBILITY[provider];

  if (!config) {
    return { supported: false, reason: 'Temperature support unknown for this provider' };
  }

  if (!config.supported) {
    return { supported: false, reason: config.reason };
  }

  // Check if model is specifically excluded
  if (config.excludedModels && config.excludedModels.some(excluded =>
    modelId.toLowerCase().includes(excluded.toLowerCase())
  )) {
    return { supported: false, reason: 'This specific model does not support temperature tuning' };
  }

  return { supported: true, reason: config.reason };
}

interface IsolatedAgentCardProps {
  agent: AgentSettings;
  providers: string[];
  getModelsForProvider: (provider: string) => string[];
  updateAgent: (agentId: string, updates: Partial<AgentSettings>) => void;
}

/**
 * Completely isolated AgentCard component with local state management
 * Each agent row manages its own state and only commits changes when needed
 * Follows fundamental principles: UI inputs use local state first
 */
export const IsolatedAgentCard = React.memo<IsolatedAgentCardProps>(({
  agent,
  providers,
  getModelsForProvider,
  updateAgent
}) => {
  // ✅ Principle: UI inputs must use local state first
  const [enabled, setEnabled] = useState(agent.enabled);
  const [temperature, setTemperature] = useState(agent.temperature);
  const [provider, setProvider] = useState(agent.provider);
  const [model, setModel] = useState(agent.model);
  const [maxTokens, setMaxTokens] = useState(agent.maxTokens);
  const [customPrompt, setCustomPrompt] = useState(agent.customPrompt || '');

  useEffect(() => {
    console.log(`🔄 IsolatedAgentCard rendered for ${agent.name}`);
  });

  // ✅ Principle: Tab switching must retain state unless explicitly reset
  // Sync local state when agent prop changes (external updates)
  useEffect(() => {
    setEnabled(agent.enabled);
    setTemperature(agent.temperature);
    setProvider(agent.provider);
    setModel(agent.model);
    setMaxTokens(agent.maxTokens);
    setCustomPrompt(agent.customPrompt || '');
  }, [agent.enabled, agent.temperature, agent.provider, agent.model, agent.maxTokens, agent.customPrompt]);

  // ✅ Principle: Each agent row handles its own inputs
  const handleToggle = useCallback(() => {
    console.time('toggle-latency');
    const newEnabled = !enabled;
    setEnabled(newEnabled);
    updateAgent(agent.id, { enabled: newEnabled });
    console.timeEnd('toggle-latency');
  }, [enabled, agent.id, updateAgent]);

  const handleTempChange = useCallback((value: number) => {
    console.time('slider-latency');
    setTemperature(value);
    console.timeEnd('slider-latency');
  }, []);

  const commitTempChange = useCallback(() => {
    console.time('slider-commit');
    updateAgent(agent.id, { temperature });
    console.timeEnd('slider-commit');
  }, [temperature, agent.id, updateAgent]);

  const handleProviderChange = useCallback((newProvider: string) => {
    setProvider(newProvider as AgentSettings['provider']);
    updateAgent(agent.id, { provider: newProvider as AgentSettings['provider'] });
  }, [agent.id, updateAgent]);

  const handleModelChange = useCallback((newModel: string) => {
    setModel(newModel);
    updateAgent(agent.id, { model: newModel });
  }, [agent.id, updateAgent]);

  const handleMaxTokensChange = useCallback((newMaxTokens: number) => {
    setMaxTokens(newMaxTokens);
    updateAgent(agent.id, { maxTokens: newMaxTokens });
  }, [agent.id, updateAgent]);

  const handleCustomPromptChange = useCallback((newPrompt: string) => {
    setCustomPrompt(newPrompt);
    updateAgent(agent.id, { customPrompt: newPrompt });
  }, [agent.id, updateAgent]);

  // Memoize temperature support calculation using local provider/model state
  const temperatureSupport = useMemo(() =>
    getTemperatureSupportInfo(provider, model),
    [provider, model]
  );

  // Memoize available models for current provider using local state
  const availableModels = useMemo(() =>
    getModelsForProvider(provider),
    [provider, getModelsForProvider]
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {agent.name}
            <Badge variant={enabled ? 'default' : 'secondary'}>
              {enabled ? 'Enabled' : 'Disabled'}
            </Badge>
          </CardTitle>
          <ToggleSwitch
            enabled={enabled}
            onToggle={handleToggle}
          />
        </div>
        <CardDescription>
          Capabilities: {agent.capabilities.join(', ')}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Provider, Model, Max Tokens Grid */}
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>Provider</Label>
            <AgentProviderSelect
              agentId={agent.id}
              value={provider}
              providers={providers}
              onChange={(_, newProvider) => handleProviderChange(newProvider)}
            />
          </div>

          <div className="space-y-2">
            <Label>Model</Label>
            <AgentModelSelect
              agentId={agent.id}
              value={model}
              models={availableModels}
              onChange={(_, newModel) => handleModelChange(newModel)}
            />
          </div>

          <div className="space-y-2">
            <Label>Max Tokens</Label>
            <AgentMaxTokensInput
              agentId={agent.id}
              value={maxTokens}
              onChange={(_, newMaxTokens) => handleMaxTokensChange(newMaxTokens)}
            />
          </div>
        </div>

        {/* Temperature Control with Conditional Display */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label>Temperature</Label>
            {!temperatureSupport.supported && (
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">{temperatureSupport.reason}</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>

          {temperatureSupport.supported ? (
            <TemperatureSlider
              value={temperature}
              onChange={handleTempChange}
              onCommit={commitTempChange}
              min={0}
              max={provider === 'anthropic' ? 1 : 2}
              step={0.1}
              provider={provider}
            />
          ) : (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
              <Info className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="text-sm text-muted-foreground">
                Temperature not supported for this model
              </span>
            </div>
          )}
        </div>

        {/* Custom Prompt (if defined) */}
        {customPrompt !== undefined && (
          <div className="space-y-2">
            <Label>Custom Prompt</Label>
            <AgentCustomPromptTextarea
              agentId={agent.id}
              value={customPrompt}
              onChange={(_, newPrompt) => handleCustomPromptChange(newPrompt)}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
});

IsolatedAgentCard.displayName = 'IsolatedAgentCard';
