// components/settings/isolated-agent-controls.tsx
import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { AgentSettings } from './settings-manager';

/**
 * ✅ Memoized Toggle Switch - Completely isolated
 * Only re-renders when its own props change
 * Uses console.time for latency testing
 */
export const ToggleSwitch = React.memo(({
  enabled,
  onToggle
}: {
  enabled: boolean;
  onToggle: () => void;
}) => {
  const handleChange = useCallback(() => {
    console.time('toggle-latency');
    onToggle();
    console.timeEnd('toggle-latency');
  }, [onToggle]);

  return <Switch checked={enabled} onCheckedChange={handleChange} />;
});

ToggleSwitch.displayName = 'ToggleSwitch';

/**
 * ✅ Memoized Temperature Slider - Uses onChange for immediate feedback, onCommit for persistence
 * Follows fundamental principle: local state first, commit on drag end
 * Uses console.time for latency testing
 */
export const TemperatureSlider = React.memo(({
  value,
  onChange,
  onCommit,
  min,
  max,
  step,
  provider
}: {
  value: number;
  onChange: (value: number) => void;
  onCommit: () => void;
  min: number;
  max: number;
  step: number;
  provider: string;
}) => {
  const handleChange = useCallback((values: number[]) => {
    console.time('slider-latency');
    onChange(values[0]);
    console.timeEnd('slider-latency');
  }, [onChange]);

  const handleCommit = useCallback(() => {
    console.time('slider-commit-latency');
    onCommit();
    console.timeEnd('slider-commit-latency');
  }, [onCommit]);

  return (
    <div className="space-y-2">
      <Slider
        value={[value]}
        onValueChange={handleChange}
        onPointerUp={handleCommit}
        min={min}
        max={max}
        step={step}
        className="w-full"
      />
      <div className="text-sm text-muted-foreground">
        {value.toFixed(1)} {provider === 'anthropic' ? '(0.0-1.0)' : '(0.0-2.0)'}
      </div>
    </div>
  );
});

TemperatureSlider.displayName = 'TemperatureSlider';

/**
 * Isolated Provider Select - Immediate updates
 */
export const AgentProviderSelect = React.memo(({
  agentId,
  value,
  providers,
  onChange
}: {
  agentId: string;
  value: string;
  providers: string[];
  onChange: (agentId: string, provider: string) => void;
}) => {
  useEffect(() => {
    console.log(`🔄 AgentProviderSelect rendered for ${agentId}`);
  });

  const handleChange = useCallback((newValue: string) => {
    console.time(`provider-${agentId}`);
    onChange(agentId, newValue);
    console.timeEnd(`provider-${agentId}`);
  }, [agentId, onChange]);

  return (
    <Select value={value} onValueChange={handleChange}>
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {providers.map((provider) => (
          <SelectItem key={provider} value={provider}>
            {provider.charAt(0).toUpperCase() + provider.slice(1)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
});

AgentProviderSelect.displayName = 'AgentProviderSelect';

/**
 * Isolated Model Select - Immediate updates
 */
export const AgentModelSelect = React.memo(({
  agentId,
  value,
  models,
  onChange
}: {
  agentId: string;
  value: string;
  models: string[];
  onChange: (agentId: string, model: string) => void;
}) => {
  useEffect(() => {
    console.log(`🔄 AgentModelSelect rendered for ${agentId}`);
  });

  const handleChange = useCallback((newValue: string) => {
    console.time(`model-${agentId}`);
    onChange(agentId, newValue);
    console.timeEnd(`model-${agentId}`);
  }, [agentId, onChange]);

  return (
    <Select value={value} onValueChange={handleChange}>
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {models.map((model) => (
          <SelectItem key={model} value={model}>
            {model}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
});

AgentModelSelect.displayName = 'AgentModelSelect';

/**
 * Isolated Max Tokens Input - Immediate updates
 */
export const AgentMaxTokensInput = React.memo(({
  agentId,
  value,
  onChange
}: {
  agentId: string;
  value: number;
  onChange: (agentId: string, maxTokens: number) => void;
}) => {
  useEffect(() => {
    console.log(`🔄 AgentMaxTokensInput rendered for ${agentId}`);
  });

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    console.time(`maxTokens-${agentId}`);
    const newValue = parseInt(e.target.value) || 0;
    onChange(agentId, newValue);
    console.timeEnd(`maxTokens-${agentId}`);
  }, [agentId, onChange]);

  return (
    <Input
      type="number"
      value={value}
      onChange={handleChange}
      min={1}
      max={100000}
    />
  );
});

AgentMaxTokensInput.displayName = 'AgentMaxTokensInput';

/**
 * Isolated Custom Prompt Textarea - Debounced updates
 */
export const AgentCustomPromptTextarea = React.memo(({
  agentId,
  value,
  onChange
}: {
  agentId: string;
  value: string;
  onChange: (agentId: string, customPrompt: string) => void;
}) => {
  const [localValue, setLocalValue] = useState(value);

  useEffect(() => {
    console.log(`🔄 AgentCustomPromptTextarea rendered for ${agentId}`);
  });

  // Update local value when prop changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Debounced update to parent
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localValue !== value) {
        console.time(`customPrompt-${agentId}`);
        onChange(agentId, localValue);
        console.timeEnd(`customPrompt-${agentId}`);
      }
    }, 500); // 500ms debounce for text input

    return () => clearTimeout(timer);
  }, [localValue, value, agentId, onChange]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setLocalValue(e.target.value);
  }, []);

  return (
    <Textarea
      value={localValue}
      onChange={handleChange}
      rows={3}
      placeholder="Enter custom prompt for this agent..."
    />
  );
});

AgentCustomPromptTextarea.displayName = 'AgentCustomPromptTextarea';
