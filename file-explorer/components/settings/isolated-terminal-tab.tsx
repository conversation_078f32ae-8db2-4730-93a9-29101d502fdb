// components/settings/isolated-terminal-tab.tsx
import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TerminalSettings } from './settings-manager';

interface IsolatedTerminalTabProps {
  settings: TerminalSettings;
  updateTerminalSettings: (updates: Partial<TerminalSettings>) => void;
}

/**
 * ✅ Isolated Terminal Tab Component
 * Uses local state for immediate UI feedback
 * Follows the same pattern as the working Editor tab
 */
export const IsolatedTerminalTab = React.memo<IsolatedTerminalTabProps>(({
  settings,
  updateTerminalSettings
}) => {
  // ✅ Local state for immediate UI feedback
  const [localSettings, setLocalSettings] = useState<TerminalSettings>(settings);

  useEffect(() => {
    console.log('🔄 IsolatedTerminalTab rendered');
  });

  // ✅ Sync local state when global settings change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  // ✅ Immediate toggle handler with local state
  const handleToggle = useCallback((key: keyof TerminalSettings) => {
    console.time('terminal-toggle-latency');
    const newValue = !localSettings[key];
    setLocalSettings(prev => ({
      ...prev,
      [key]: newValue
    }));
    updateTerminalSettings({ [key]: newValue });
    console.timeEnd('terminal-toggle-latency');
  }, [localSettings, updateTerminalSettings]);

  // ✅ Individual slider handlers (matching Editor tab pattern)
  const handleFontSizeChange = useCallback((value: number) => {
    console.time('terminal-slider-latency');
    setLocalSettings(prev => ({ ...prev, fontSize: value }));
    console.timeEnd('terminal-slider-latency');
  }, []);

  const handleColsChange = useCallback((value: number) => {
    console.time('terminal-slider-latency');
    setLocalSettings(prev => ({ ...prev, cols: value }));
    console.timeEnd('terminal-slider-latency');
  }, []);

  const handleRowsChange = useCallback((value: number) => {
    console.time('terminal-slider-latency');
    setLocalSettings(prev => ({ ...prev, rows: value }));
    console.timeEnd('terminal-slider-latency');
  }, []);

  const handleScrollbackChange = useCallback((value: number) => {
    console.time('terminal-slider-latency');
    setLocalSettings(prev => ({ ...prev, scrollback: value }));
    console.timeEnd('terminal-slider-latency');
  }, []);

  const handleLineHeightChange = useCallback((value: number) => {
    console.time('terminal-slider-latency');
    setLocalSettings(prev => ({ ...prev, lineHeight: value }));
    console.timeEnd('terminal-slider-latency');
  }, []);

  // ✅ Commit handlers (matching Editor tab pattern)
  const commitFontSize = useCallback(() => {
    console.time('terminal-slider-commit');
    updateTerminalSettings({ fontSize: localSettings.fontSize });
    console.timeEnd('terminal-slider-commit');
  }, [localSettings.fontSize, updateTerminalSettings]);

  const commitCols = useCallback(() => {
    console.time('terminal-slider-commit');
    updateTerminalSettings({ cols: localSettings.cols });
    console.timeEnd('terminal-slider-commit');
  }, [localSettings.cols, updateTerminalSettings]);

  const commitRows = useCallback(() => {
    console.time('terminal-slider-commit');
    updateTerminalSettings({ rows: localSettings.rows });
    console.timeEnd('terminal-slider-commit');
  }, [localSettings.rows, updateTerminalSettings]);

  const commitScrollback = useCallback(() => {
    console.time('terminal-slider-commit');
    updateTerminalSettings({ scrollback: localSettings.scrollback });
    console.timeEnd('terminal-slider-commit');
  }, [localSettings.scrollback, updateTerminalSettings]);

  const commitLineHeight = useCallback(() => {
    console.time('terminal-slider-commit');
    updateTerminalSettings({ lineHeight: localSettings.lineHeight });
    console.timeEnd('terminal-slider-commit');
  }, [localSettings.lineHeight, updateTerminalSettings]);

  // ✅ Input handler with debounced commit
  const handleInputChange = useCallback((key: keyof TerminalSettings, value: string) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));

    // Debounced commit for input fields
    setTimeout(() => {
      updateTerminalSettings({ [key]: value });
    }, 500);
  }, [updateTerminalSettings]);

  // ✅ Select handler for theme and shell
  const handleSelectChange = useCallback((key: keyof TerminalSettings, value: string) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
    updateTerminalSettings({ [key]: value });
  }, [updateTerminalSettings]);

  // ✅ Memoized toggle component
  const TerminalToggle = React.memo(({
    id,
    label,
    settingKey
  }: {
    id: string;
    label: string;
    settingKey: keyof TerminalSettings;
  }) => (
    <div className="flex items-center justify-between">
      <Label htmlFor={id}>{label}</Label>
      <Switch
        id={id}
        checked={localSettings[settingKey] as boolean}
        onCheckedChange={() => handleToggle(settingKey)}
      />
    </div>
  ));

  // ✅ Memoized slider components (matching Editor tab pattern)
  const FontSizeSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Font Size</Label>
      <Slider
        value={[localSettings.fontSize]}
        onValueChange={([value]) => handleFontSizeChange(value)}
        onPointerUp={commitFontSize}
        min={8}
        max={24}
        step={1}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.fontSize}px
      </div>
    </div>
  ));

  const ColsSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Columns</Label>
      <Slider
        value={[localSettings.cols]}
        onValueChange={([value]) => handleColsChange(value)}
        onPointerUp={commitCols}
        min={40}
        max={200}
        step={1}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.cols} columns
      </div>
    </div>
  ));

  const RowsSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Rows</Label>
      <Slider
        value={[localSettings.rows]}
        onValueChange={([value]) => handleRowsChange(value)}
        onPointerUp={commitRows}
        min={10}
        max={60}
        step={1}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.rows} rows
      </div>
    </div>
  ));

  const ScrollbackSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Scrollback Buffer</Label>
      <Slider
        value={[localSettings.scrollback]}
        onValueChange={([value]) => handleScrollbackChange(value)}
        onPointerUp={commitScrollback}
        min={100}
        max={10000}
        step={100}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.scrollback} lines
      </div>
    </div>
  ));

  const LineHeightSlider = React.memo(() => (
    <div className="space-y-2">
      <Label>Line Height</Label>
      <Slider
        value={[localSettings.lineHeight]}
        onValueChange={([value]) => handleLineHeightChange(value)}
        onPointerUp={commitLineHeight}
        min={1.0}
        max={2.0}
        step={0.1}
      />
      <div className="text-sm text-muted-foreground">
        {localSettings.lineHeight.toFixed(1)}
      </div>
    </div>
  ));

  // ✅ Memoized input component
  const TerminalInput = React.memo(({
    label,
    settingKey
  }: {
    label: string;
    settingKey: keyof TerminalSettings;
  }) => (
    <div className="space-y-2">
      <Label>{label}</Label>
      <Input
        value={localSettings[settingKey] as string}
        onChange={(e) => handleInputChange(settingKey, e.target.value)}
      />
    </div>
  ));

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Terminal Settings</CardTitle>
          <CardDescription>Configure terminal behavior and appearance</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Theme and Shell Selection */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Theme</Label>
              <Select
                value={localSettings.theme}
                onValueChange={(value) => handleSelectChange('theme', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Default Shell</Label>
              <Input
                value={localSettings.shell}
                onChange={(e) => handleInputChange('shell', e.target.value)}
                placeholder="bash, zsh, powershell.exe, etc."
              />
            </div>
          </div>

          {/* Font Settings */}
          <div className="grid grid-cols-2 gap-4">
            <TerminalInput
              label="Font Family"
              settingKey="fontFamily"
            />

            <FontSizeSlider />
          </div>

          <LineHeightSlider />

          {/* Terminal Dimensions */}
          <div className="grid grid-cols-2 gap-4">
            <ColsSlider />
            <RowsSlider />
          </div>

          <ScrollbackSlider />

          {/* Terminal Behavior */}
          <div className="grid grid-cols-1 gap-4">
            <TerminalToggle
              id="cursor-blink"
              label="Cursor Blink"
              settingKey="cursorBlink"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
});

IsolatedTerminalTab.displayName = 'IsolatedTerminalTab';
