// components/settings/setting-status-indicator.tsx
import React from 'react';
import { CheckCircle, AlertTriangle, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export type SettingStatus = 'active' | 'cosmetic' | 'broken';

export interface SettingStatusIndicatorProps {
  status: SettingStatus;
  statusLabel: string;
  className?: string;
}

/**
 * ✅ Setting Status Indicator Component
 * Displays real-time visual indicators for setting integration status
 * Based on actual runtime logic integration, not UI activity
 */
export const SettingStatusIndicator: React.FC<SettingStatusIndicatorProps> = ({
  status,
  statusLabel,
  className
}) => {
  const getStatusConfig = (status: SettingStatus) => {
    switch (status) {
      case 'active':
        return {
          icon: CheckCircle,
          color: 'text-green-500',
          bgColor: 'bg-green-50 dark:bg-green-950',
          borderColor: 'border-green-200 dark:border-green-800',
          label: '✅ Active'
        };
      case 'cosmetic':
        return {
          icon: AlertTriangle,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50 dark:bg-yellow-950',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          label: '⚠️ Cosmetic Only'
        };
      case 'broken':
        return {
          icon: XCircle,
          color: 'text-red-500',
          bgColor: 'bg-red-50 dark:bg-red-950',
          borderColor: 'border-red-200 dark:border-red-800',
          label: '❌ Broken'
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <div
      className={cn(
        'inline-flex items-center gap-1 px-2 py-1 rounded-md border text-xs font-medium',
        config.color,
        config.bgColor,
        config.borderColor,
        className
      )}
      title={statusLabel}
      role="status"
      aria-label={`Setting status: ${config.label}. ${statusLabel}`}
    >
      <Icon className="h-3 w-3" />
      <span className="sr-only">{config.label}</span>
    </div>
  );
};

/**
 * ✅ Compact Status Indicator (icon only)
 * For use in tight spaces where full badge would be too large
 */
export const CompactStatusIndicator: React.FC<SettingStatusIndicatorProps> = ({
  status,
  statusLabel,
  className
}) => {
  const getStatusConfig = (status: SettingStatus) => {
    switch (status) {
      case 'active':
        return {
          icon: CheckCircle,
          color: 'text-green-500',
          label: '✅ Active'
        };
      case 'cosmetic':
        return {
          icon: AlertTriangle,
          color: 'text-yellow-500',
          label: '⚠️ Cosmetic Only'
        };
      case 'broken':
        return {
          icon: XCircle,
          color: 'text-red-500',
          label: '❌ Broken'
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <Icon
      className={cn('h-4 w-4', config.color, className)}
      title={statusLabel}
      role="status"
      aria-label={`Setting status: ${config.label}. ${statusLabel}`}
    />
  );
};
