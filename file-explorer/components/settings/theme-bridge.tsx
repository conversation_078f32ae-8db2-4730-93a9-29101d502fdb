"use client"

import { useEffect, useRef } from 'react';
import { useTheme } from 'next-themes';
import { useSystemSettings } from './settings-context';

/**
 * ✅ Theme Bridge Component
 * Synchronizes SystemSettings.theme with next-themes
 * Ensures theme changes in System tab immediately affect UI
 */
export const ThemeBridge: React.FC = () => {
  const { systemSettings } = useSystemSettings();
  const { setTheme, theme: currentTheme } = useTheme();
  const lastSystemTheme = useRef<string | null>(null);

  useEffect(() => {
    // Only update if the system theme actually changed
    // Prevents infinite loops and unnecessary updates
    if (systemSettings.theme !== lastSystemTheme.current) {
      console.log(`🎨 Theme bridge: ${lastSystemTheme.current} → ${systemSettings.theme}`);
      
      // Validate theme value before applying
      if (['light', 'dark', 'system'].includes(systemSettings.theme)) {
        setTheme(systemSettings.theme);
        lastSystemTheme.current = systemSettings.theme;
      } else {
        console.warn(`Invalid theme value: ${systemSettings.theme}`);
      }
    }
  }, [systemSettings.theme, setTheme]);

  // This component doesn't render anything - it's just for side effects
  return null;
};
