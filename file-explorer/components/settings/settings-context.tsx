"use client"

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { SettingsManager, AllSettings, SystemSettings } from './settings-manager';

interface SettingsContextType {
  settings: AllSettings;
  settingsManager: SettingsManager;
  systemSettings: SystemSettings;
  updateSystemSettings: (updates: Partial<SystemSettings>) => void;
}

const SettingsContext = createContext<SettingsContextType | null>(null);

interface SettingsProviderProps {
  children: ReactNode;
  settingsManager: SettingsManager;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ 
  children, 
  settingsManager 
}) => {
  const [settings, setSettings] = useState<AllSettings>(settingsManager.getSettings());

  useEffect(() => {
    const handleSettingsChange = (newSettings: AllSettings) => {
      setSettings(newSettings);
    };

    settingsManager.onSettingsChange(handleSettingsChange);
    return () => settingsManager.offSettingsChange(handleSettingsChange);
  }, [settingsManager]);

  const updateSystemSettings = (updates: Partial<SystemSettings>) => {
    settingsManager.updateSystemSettings(updates);
  };

  const value: SettingsContextType = {
    settings,
    settingsManager,
    systemSettings: settings.system,
    updateSystemSettings
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = (): SettingsContextType => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

// ✅ Hook specifically for system settings (for theme bridge)
export const useSystemSettings = (): {
  systemSettings: SystemSettings;
  updateSystemSettings: (updates: Partial<SystemSettings>) => void;
} => {
  const { systemSettings, updateSystemSettings } = useSettings();
  return { systemSettings, updateSystemSettings };
};
