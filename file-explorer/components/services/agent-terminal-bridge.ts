// ✅ Task 99: Agent Terminal Integration Bridge
// Provides agents with real-time terminal command execution capabilities

export interface AgentTerminalCommandRequest {
  command: string;
  agentId: string;
  sessionId?: string;
  timeout?: number; // milliseconds, default 30000
  workingDirectory?: string;
  environment?: Record<string, string>;
}

export interface AgentTerminalCommandResponse {
  success: boolean;
  output: string;
  error?: string;
  exitCode?: number;
  executionTime: number;
  sessionId: string;
  agentId: string;
  command: string;
}

export interface TerminalSession {
  id: string;
  agentId?: string;
  isShared: boolean;
  createdAt: number;
  lastUsed: number;
  commandCount: number;
  workingDirectory: string;
}

class AgentTerminalBridge {
  private activeSessions: Map<string, TerminalSession> = new Map();
  private defaultSharedSessionId: string | null = null;
  private commandHistory: Map<string, AgentTerminalCommandRequest[]> = new Map();
  private maxHistoryPerAgent = 100;

  /**
   * ✅ Task 99 Step 2: Execute command via agent terminal API
   */
  async executeCommand(request: AgentTerminalCommandRequest): Promise<AgentTerminalCommandResponse> {
    const startTime = Date.now();
    
    try {
      // Validate request
      this.validateCommandRequest(request);
      
      // Get or create session
      const sessionId = await this.getOrCreateSession(request.sessionId, request.agentId);
      
      // Log command to history
      this.addToCommandHistory(request.agentId, { ...request, sessionId });
      
      // Execute via Electron IPC
      const result = await this.executeViaIPC(request, sessionId);
      
      // Update session usage
      this.updateSessionUsage(sessionId);
      
      const executionTime = Date.now() - startTime;
      
      const response: AgentTerminalCommandResponse = {
        success: result.success,
        output: result.output || '',
        error: result.error,
        exitCode: result.exitCode,
        executionTime,
        sessionId,
        agentId: request.agentId,
        command: request.command
      };
      
      console.log(`✅ AgentTerminalBridge: Command executed for ${request.agentId} in ${executionTime}ms`);
      return response;
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`❌ AgentTerminalBridge: Command failed for ${request.agentId}:`, error);
      
      return {
        success: false,
        output: '',
        error: errorMessage,
        executionTime,
        sessionId: request.sessionId || 'unknown',
        agentId: request.agentId,
        command: request.command
      };
    }
  }

  /**
   * ✅ Task 99: Get or create terminal session for agent
   */
  private async getOrCreateSession(sessionId?: string, agentId?: string): Promise<string> {
    // If specific session requested, validate it exists
    if (sessionId) {
      if (this.activeSessions.has(sessionId)) {
        return sessionId;
      } else {
        throw new Error(`Terminal session ${sessionId} not found`);
      }
    }
    
    // Use default shared session if no specific session
    if (!this.defaultSharedSessionId) {
      this.defaultSharedSessionId = await this.createSharedSession();
    }
    
    return this.defaultSharedSessionId;
  }

  /**
   * ✅ Task 99: Create shared agent terminal session
   */
  private async createSharedSession(): Promise<string> {
    const sessionId = `agent-shared-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const session: TerminalSession = {
      id: sessionId,
      isShared: true,
      createdAt: Date.now(),
      lastUsed: Date.now(),
      commandCount: 0,
      workingDirectory: process.cwd()
    };
    
    this.activeSessions.set(sessionId, session);
    console.log(`✅ AgentTerminalBridge: Created shared session ${sessionId}`);
    
    return sessionId;
  }

  /**
   * ✅ Task 99: Create dedicated session for specific agent
   * ✅ Task 101 Step 4: Enhanced with TerminalSessionManager integration
   */
  async createAgentSession(agentId: string, workingDirectory?: string, options?: {
    shell?: string;
    environment?: Record<string, string>;
    cols?: number;
    rows?: number;
  }): Promise<string> {
    const sessionId = `agent-${agentId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // ✅ Task 101: Use TerminalSessionManager for real PTY session creation
    if (typeof window !== 'undefined' && window.electronAPI?.terminal?.createSession) {
      try {
        const result = await window.electronAPI.terminal.createSession(sessionId, agentId, {
          workingDirectory: workingDirectory || process.cwd(),
          shell: options?.shell,
          environment: options?.environment,
          cols: options?.cols || 80,
          rows: options?.rows || 30
        });

        if (result.success) {
          // Track session locally for bridge management
          const session: TerminalSession = {
            id: sessionId,
            agentId,
            isShared: false,
            createdAt: Date.now(),
            lastUsed: Date.now(),
            commandCount: 0,
            workingDirectory: workingDirectory || process.cwd()
          };

          this.activeSessions.set(sessionId, session);
          console.log(`✅ AgentTerminalBridge: Created dedicated session ${sessionId} for agent ${agentId} via TerminalSessionManager`);

          return sessionId;
        } else {
          throw new Error(result.error || 'Failed to create session');
        }
      } catch (error) {
        console.error(`❌ AgentTerminalBridge: Failed to create session via TerminalSessionManager:`, error);
        throw error;
      }
    } else {
      // Fallback to local session tracking (for non-Electron environments)
      const session: TerminalSession = {
        id: sessionId,
        agentId,
        isShared: false,
        createdAt: Date.now(),
        lastUsed: Date.now(),
        commandCount: 0,
        workingDirectory: workingDirectory || process.cwd()
      };

      this.activeSessions.set(sessionId, session);
      console.log(`✅ AgentTerminalBridge: Created local session ${sessionId} for agent ${agentId} (fallback mode)`);

      return sessionId;
    }
  }

  /**
   * ✅ Task 99: Execute command via Electron IPC
   */
  private async executeViaIPC(request: AgentTerminalCommandRequest, sessionId: string): Promise<any> {
    if (typeof window === 'undefined' || !window.electronAPI?.terminal?.agentCommand) {
      throw new Error('Terminal API not available - not running in Electron environment');
    }
    
    const ipcRequest = {
      command: request.command,
      agentId: request.agentId,
      sessionId,
      timeout: request.timeout || 30000,
      workingDirectory: request.workingDirectory,
      environment: request.environment
    };
    
    return await window.electronAPI.terminal.agentCommand(ipcRequest);
  }

  /**
   * ✅ Task 99: Validate command request
   */
  private validateCommandRequest(request: AgentTerminalCommandRequest): void {
    if (!request.command || typeof request.command !== 'string') {
      throw new Error('Command must be a non-empty string');
    }
    
    if (!request.agentId || typeof request.agentId !== 'string') {
      throw new Error('AgentId must be a non-empty string');
    }
    
    if (request.timeout && (typeof request.timeout !== 'number' || request.timeout <= 0)) {
      throw new Error('Timeout must be a positive number');
    }
  }

  /**
   * ✅ Task 99: Update session usage tracking
   */
  private updateSessionUsage(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.lastUsed = Date.now();
      session.commandCount++;
    }
  }

  /**
   * ✅ Task 99: Add command to agent history
   */
  private addToCommandHistory(agentId: string, request: AgentTerminalCommandRequest): void {
    if (!this.commandHistory.has(agentId)) {
      this.commandHistory.set(agentId, []);
    }
    
    const history = this.commandHistory.get(agentId)!;
    history.push(request);
    
    // Limit history size
    if (history.length > this.maxHistoryPerAgent) {
      history.splice(0, history.length - this.maxHistoryPerAgent);
    }
  }

  /**
   * ✅ Task 99: Get command history for agent
   */
  getCommandHistory(agentId: string): AgentTerminalCommandRequest[] {
    return this.commandHistory.get(agentId) || [];
  }

  /**
   * ✅ Task 99: Get active sessions
   */
  getActiveSessions(): TerminalSession[] {
    return Array.from(this.activeSessions.values());
  }

  /**
   * ✅ Task 99: Get session info
   */
  getSessionInfo(sessionId: string): TerminalSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * ✅ Task 99: Close session
   */
  closeSession(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      this.activeSessions.delete(sessionId);
      
      // If this was the default shared session, clear it
      if (sessionId === this.defaultSharedSessionId) {
        this.defaultSharedSessionId = null;
      }
      
      console.log(`✅ AgentTerminalBridge: Closed session ${sessionId}`);
      return true;
    }
    return false;
  }

  /**
   * ✅ Task 99: Cleanup old sessions
   */
  cleanupOldSessions(maxAgeMs: number = 3600000): number { // 1 hour default
    const now = Date.now();
    let cleaned = 0;

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now - session.lastUsed > maxAgeMs && !session.isShared) {
        this.closeSession(sessionId);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`✅ AgentTerminalBridge: Cleaned up ${cleaned} old sessions`);
    }

    return cleaned;
  }

  /**
   * ✅ Task 101 Step 4: Destroy agent session via TerminalSessionManager
   */
  async destroyAgentSession(sessionId: string): Promise<boolean> {
    try {
      // Remove from local tracking
      const session = this.activeSessions.get(sessionId);
      if (session) {
        this.activeSessions.delete(sessionId);
      }

      // Destroy via TerminalSessionManager if available
      if (typeof window !== 'undefined' && window.electronAPI?.terminal?.destroySession) {
        const result = await window.electronAPI.terminal.destroySession(sessionId);
        console.log(`✅ AgentTerminalBridge: Destroyed session ${sessionId} via TerminalSessionManager`);
        return result.success;
      } else {
        console.log(`✅ AgentTerminalBridge: Destroyed local session ${sessionId} (fallback mode)`);
        return true;
      }
    } catch (error) {
      console.error(`❌ AgentTerminalBridge: Error destroying session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Task 101 Step 4: List all sessions for agent via TerminalSessionManager
   */
  async listAgentSessions(agentId: string): Promise<any[]> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI?.terminal?.listSessions) {
        const result = await window.electronAPI.terminal.listSessions(agentId);
        if (result.success) {
          return result.sessions;
        }
      }

      // Fallback to local sessions
      return Array.from(this.activeSessions.values()).filter(s => s.agentId === agentId);
    } catch (error) {
      console.error(`❌ AgentTerminalBridge: Error listing sessions for agent ${agentId}:`, error);
      return [];
    }
  }

  /**
   * ✅ Task 101 Step 4: Cleanup all sessions for agent
   */
  async cleanupAgentSessions(agentId: string): Promise<number> {
    try {
      let cleanedCount = 0;

      if (typeof window !== 'undefined' && window.electronAPI?.terminal?.cleanupAgentSessions) {
        const result = await window.electronAPI.terminal.cleanupAgentSessions(agentId);
        if (result.success) {
          cleanedCount = result.destroyedCount;
        }
      }

      // Also cleanup local tracking
      const localSessions = Array.from(this.activeSessions.entries())
        .filter(([_, session]) => session.agentId === agentId);

      for (const [sessionId] of localSessions) {
        this.activeSessions.delete(sessionId);
      }

      console.log(`✅ AgentTerminalBridge: Cleaned up ${cleanedCount} sessions for agent ${agentId}`);
      return cleanedCount;
    } catch (error) {
      console.error(`❌ AgentTerminalBridge: Error cleaning up sessions for agent ${agentId}:`, error);
      return 0;
    }
  }
}

// Export singleton instance
export const agentTerminalBridge = new AgentTerminalBridge();

/**
 * ✅ Task 99 Step 2: Convenience function for agent command execution
 */
export async function agentExecuteCommand(
  agentId: string, 
  command: string, 
  sessionId?: string,
  options?: {
    timeout?: number;
    workingDirectory?: string;
    environment?: Record<string, string>;
  }
): Promise<AgentTerminalCommandResponse> {
  return await agentTerminalBridge.executeCommand({
    command,
    agentId,
    sessionId,
    timeout: options?.timeout,
    workingDirectory: options?.workingDirectory,
    environment: options?.environment
  });
}
