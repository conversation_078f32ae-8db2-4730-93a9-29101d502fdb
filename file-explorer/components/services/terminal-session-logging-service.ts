// ✅ Task 98: Terminal Session Logging Service
// Manages command input/output logging per terminal session

export interface TerminalLogEntry {
  id: string;
  timestamp: number;
  sessionId: string;
  content: string;
  type: 'input' | 'output' | 'system' | 'agent';
  metadata?: Record<string, any>;
}

export interface TerminalSessionLog {
  sessionId: string;
  sessionName: string;
  startTime: number;
  endTime?: number;
  entries: TerminalLogEntry[];
  totalLines: number;
  isActive: boolean;
}

class TerminalSessionLoggingService {
  private sessionLogs: Map<string, TerminalSessionLog> = new Map();
  private maxEntriesPerSession = 2000; // Higher limit for terminal sessions
  private maxSessions = 50;

  /**
   * ✅ Task 98: Initialize a new session log
   */
  initializeSessionLog(sessionId: string, sessionName: string): void {
    const sessionLog: TerminalSessionLog = {
      sessionId,
      sessionName,
      startTime: Date.now(),
      entries: [],
      totalLines: 0,
      isActive: true
    };

    this.sessionLogs.set(sessionId, sessionLog);

    // Add initial system entry
    this.addLogEntry(sessionId, `[SYSTEM] Terminal session '${sessionName}' started`, 'system');

    console.log(`✅ TerminalSessionLoggingService: Initialized log for session ${sessionId}`);
  }

  /**
   * ✅ Task 98: Add a log entry to a session
   */
  addLogEntry(
    sessionId: string,
    content: string,
    type: 'input' | 'output' | 'system' | 'agent' = 'output',
    metadata?: Record<string, any>
  ): void {
    const sessionLog = this.sessionLogs.get(sessionId);
    if (!sessionLog) {
      console.warn(`⚠️ TerminalSessionLoggingService: No session log found for ${sessionId}`);
      return;
    }

    // Skip empty content
    if (!content.trim()) {
      return;
    }

    const entry: TerminalLogEntry = {
      id: `entry-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      sessionId,
      content: content.replace(/\x1b\[[0-9;]*m/g, ''), // Strip ANSI color codes for clean logs
      type,
      metadata
    };

    sessionLog.entries.push(entry);
    sessionLog.totalLines++;

    // Limit entries per session to prevent memory issues
    if (sessionLog.entries.length > this.maxEntriesPerSession) {
      sessionLog.entries = sessionLog.entries.slice(-this.maxEntriesPerSession);
    }

    // Limit total sessions
    if (this.sessionLogs.size > this.maxSessions) {
      const oldestSession = Array.from(this.sessionLogs.entries())
        .filter(([_, log]) => !log.isActive)
        .sort((a, b) => a[1].startTime - b[1].startTime)[0];
      
      if (oldestSession) {
        this.sessionLogs.delete(oldestSession[0]);
        console.log(`🗑️ TerminalSessionLoggingService: Removed old session log ${oldestSession[0]}`);
      }
    }
  }

  /**
   * ✅ Task 98: Log user input
   */
  logInput(sessionId: string, input: string): void {
    // Format input with timestamp prefix for clarity
    const formattedInput = `$ ${input}`;
    this.addLogEntry(sessionId, formattedInput, 'input');
  }

  /**
   * ✅ Task 98: Log terminal output
   */
  logOutput(sessionId: string, output: string): void {
    // Split multi-line output into separate entries for better readability
    const lines = output.split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        this.addLogEntry(sessionId, line, 'output');
      }
    });
  }

  /**
   * ✅ Task 98: Log agent output
   */
  logAgentOutput(sessionId: string, agentId: string, output: string): void {
    const formattedOutput = `[${agentId}] ${output}`;
    this.addLogEntry(sessionId, formattedOutput, 'agent', { agentId });
  }

  /**
   * ✅ Task 98: Complete session log
   */
  completeSessionLog(sessionId: string): void {
    const sessionLog = this.sessionLogs.get(sessionId);
    if (!sessionLog) {
      console.warn(`⚠️ TerminalSessionLoggingService: No session log found for ${sessionId}`);
      return;
    }

    sessionLog.endTime = Date.now();
    sessionLog.isActive = false;

    // Add final system entry
    const duration = sessionLog.endTime - sessionLog.startTime;
    this.addLogEntry(sessionId, `[SYSTEM] Session ended after ${Math.round(duration / 1000)}s`, 'system');

    console.log(`✅ TerminalSessionLoggingService: Completed log for session ${sessionId}`);
  }

  /**
   * ✅ Task 98: Get session log
   */
  getSessionLog(sessionId: string): TerminalSessionLog | undefined {
    return this.sessionLogs.get(sessionId);
  }

  /**
   * ✅ Task 98: Get all session logs
   */
  getAllSessionLogs(): TerminalSessionLog[] {
    return Array.from(this.sessionLogs.values());
  }

  /**
   * ✅ Task 98: Export session log as text
   */
  exportSessionLog(sessionId: string): string {
    const sessionLog = this.sessionLogs.get(sessionId);
    if (!sessionLog) {
      return '';
    }

    const header = `Terminal Session Log: ${sessionLog.sessionName}\n`;
    const sessionInfo = `Session ID: ${sessionId}\n`;
    const timeInfo = `Started: ${new Date(sessionLog.startTime).toLocaleString()}\n`;
    const endInfo = sessionLog.endTime ? `Ended: ${new Date(sessionLog.endTime).toLocaleString()}\n` : 'Status: Active\n';
    const separator = '='.repeat(50) + '\n\n';

    const entries = sessionLog.entries.map(entry => {
      const timestamp = new Date(entry.timestamp).toLocaleTimeString();
      const typePrefix = entry.type === 'input' ? '>' : entry.type === 'system' ? '[SYS]' : '';
      return `[${timestamp}] ${typePrefix} ${entry.content}`;
    }).join('\n');

    return header + sessionInfo + timeInfo + endInfo + separator + entries;
  }

  /**
   * ✅ Task 98: Clear session log
   */
  clearSessionLog(sessionId: string): void {
    const sessionLog = this.sessionLogs.get(sessionId);
    if (sessionLog) {
      sessionLog.entries = [];
      sessionLog.totalLines = 0;
      this.addLogEntry(sessionId, '[SYSTEM] Log cleared', 'system');
      console.log(`🗑️ TerminalSessionLoggingService: Cleared log for session ${sessionId}`);
    }
  }

  /**
   * ✅ Task 98: Remove session log
   */
  removeSessionLog(sessionId: string): void {
    if (this.sessionLogs.has(sessionId)) {
      this.sessionLogs.delete(sessionId);
      console.log(`🗑️ TerminalSessionLoggingService: Removed log for session ${sessionId}`);
    }
  }

  /**
   * ✅ Task 98: Get session log summary
   */
  getSessionSummary(sessionId: string): { totalLines: number; duration: number; isActive: boolean } | null {
    const sessionLog = this.sessionLogs.get(sessionId);
    if (!sessionLog) {
      return null;
    }

    const duration = sessionLog.endTime ? 
      sessionLog.endTime - sessionLog.startTime : 
      Date.now() - sessionLog.startTime;

    return {
      totalLines: sessionLog.totalLines,
      duration,
      isActive: sessionLog.isActive
    };
  }
}

// Export singleton instance
export const terminalSessionLoggingService = new TerminalSessionLoggingService();
