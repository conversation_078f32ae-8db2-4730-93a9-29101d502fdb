// components/services/agent-task-feedback-service.ts
import { AgentExecutionResult } from '../agents/agent-base';
import { KanbanTaskBridge } from '../agents/kanban-task-bridge';
import { kanbanEvents } from '../kanban/lib/kanban-events';
import { executionLogger } from '../agents/agent-execution-trace';
import { taskStatusService } from '../agents/task-status-service';

export interface TaskFeedbackOptions {
  cardId?: string;
  taskId?: string;
  boardId?: string;
  updateProgress?: boolean;
  logToHistory?: boolean;
  emitEvents?: boolean;
}

export interface TaskFeedbackResult {
  success: boolean;
  cardUpdated: boolean;
  statusUpdated: boolean;
  eventsEmitted: string[];
  error?: string;
}

/**
 * ✅ Task 76: Agent Task Execution Feedback Service
 *
 * Handles structured feedback reporting after agent task execution.
 * Updates Kanban cards, logs results, and emits events for real-time UI sync.
 */
export class AgentTaskFeedbackService {
  private static instance: AgentTaskFeedbackService;

  public static getInstance(): AgentTaskFeedbackService {
    if (!AgentTaskFeedbackService.instance) {
      AgentTaskFeedbackService.instance = new AgentTaskFeedbackService();
    }
    return AgentTaskFeedbackService.instance;
  }

  /**
   * Report agent execution result with comprehensive feedback
   */
  public async reportExecutionResult(
    agentId: string,
    result: AgentExecutionResult,
    options: TaskFeedbackOptions = {}
  ): Promise<TaskFeedbackResult> {
    const {
      cardId,
      taskId,
      boardId = 'main',
      updateProgress = true,
      logToHistory = true,
      emitEvents = true
    } = options;

    const feedbackResult: TaskFeedbackResult = {
      success: false,
      cardUpdated: false,
      statusUpdated: false,
      eventsEmitted: []
    };

    try {
      console.log(`🔄 AgentTaskFeedbackService: Processing execution result for agent ${agentId}`, {
        status: result.status,
        cardId,
        taskId,
        hasOutputs: !!result.outputs?.length,
        hasFiles: !!(result.createdFiles?.length || result.modifiedFiles?.length)
      });

      // 1. Update Kanban card status based on execution result
      if (cardId && updateProgress) {
        const cardUpdateSuccess = await this.updateKanbanCardStatus(
          cardId,
          agentId,
          result,
          boardId
        );
        feedbackResult.cardUpdated = cardUpdateSuccess;
      }

      // 2. Update task status in TaskStatusService
      if (taskId) {
        const statusUpdateSuccess = await this.updateTaskStatus(
          taskId,
          agentId,
          result,
          cardId
        );
        feedbackResult.statusUpdated = statusUpdateSuccess;
      }

      // 3. Log execution result to ExecutionLogStore
      if (logToHistory) {
        this.logExecutionResult(agentId, result, taskId, cardId);
      }

      // 4. Emit events for real-time UI updates
      if (emitEvents) {
        const emittedEvents = this.emitFeedbackEvents(
          agentId,
          result,
          cardId,
          taskId,
          boardId
        );
        feedbackResult.eventsEmitted = emittedEvents;
      }

      feedbackResult.success = true;
      console.log(`✅ AgentTaskFeedbackService: Successfully processed execution result for agent ${agentId}`);

      return feedbackResult;

    } catch (error) {
      console.error(`❌ AgentTaskFeedbackService: Error processing execution result for agent ${agentId}:`, error);
      feedbackResult.error = error instanceof Error ? error.message : 'Unknown error';
      return feedbackResult;
    }
  }

  /**
   * Update Kanban card status based on execution result
   */
  private async updateKanbanCardStatus(
    cardId: string,
    agentId: string,
    result: AgentExecutionResult,
    boardId: string
  ): Promise<boolean> {
    try {
      switch (result.status) {
        case 'success':
          await KanbanTaskBridge.moveCardBasedOnTaskStatus(cardId, 'completed', agentId, boardId);
          await KanbanTaskBridge.updateCardProgress(cardId, 100, agentId);

          // Add completion summary as comment if available
          if (result.message || result.outputs?.length) {
            const summary = this.buildCompletionSummary(result);
            await this.addCardComment(cardId, agentId, summary, boardId);
          }
          break;

        case 'error':
          await KanbanTaskBridge.moveCardBasedOnTaskStatus(cardId, 'failed', agentId, boardId);

          // Add error details as comment
          const errorMessage = result.errorDetails?.type || result.message || 'Task execution failed';
          await this.addCardComment(cardId, agentId, `❌ Error: ${errorMessage}`, boardId);
          break;

        case 'partial':
          // Keep in current column but update progress
          const progress = this.calculatePartialProgress(result);
          await KanbanTaskBridge.updateCardProgress(cardId, progress, agentId);

          if (result.message) {
            await this.addCardComment(cardId, agentId, `⚠️ Partial: ${result.message}`, boardId);
          }
          break;

        case 'timeout':
          await KanbanTaskBridge.moveCardBasedOnTaskStatus(cardId, 'failed', agentId, boardId);
          await this.addCardComment(cardId, agentId, '⏱️ Task execution timed out', boardId);
          break;
      }

      return true;
    } catch (error) {
      console.error(`❌ AgentTaskFeedbackService: Failed to update Kanban card ${cardId}:`, error);
      return false;
    }
  }

  /**
   * Update task status in TaskStatusService
   */
  private async updateTaskStatus(
    taskId: string,
    agentId: string,
    result: AgentExecutionResult,
    cardId?: string
  ): Promise<boolean> {
    try {
      const statusMap = {
        'success': 'completed' as const,
        'error': 'failed' as const,
        'partial': 'running' as const,
        'timeout': 'failed' as const
      };

      const status = statusMap[result.status];
      const progress = result.status === 'success' ? 100 :
                     result.status === 'partial' ? this.calculatePartialProgress(result) : 0;

      await taskStatusService.updateTaskStatus(taskId, agentId, status, {
        progress,
        message: result.message,
        kanbanCardId: cardId,
        metadata: {
          executionResult: result,
          outputs: result.outputs,
          createdFiles: result.createdFiles,
          modifiedFiles: result.modifiedFiles,
          metrics: result.metrics
        }
      });

      return true;
    } catch (error) {
      console.error(`❌ AgentTaskFeedbackService: Failed to update task status for ${taskId}:`, error);
      return false;
    }
  }

  /**
   * Log execution result to ExecutionLogStore
   */
  private logExecutionResult(
    agentId: string,
    result: AgentExecutionResult,
    taskId?: string,
    cardId?: string
  ): void {
    try {
      const action = result.status === 'success' ? 'task_completed' :
                    result.status === 'error' ? 'task_failed' :
                    result.status === 'partial' ? 'task_progress' : 'task_timeout';

      const details = result.status === 'success'
        ? `Task completed successfully. ${result.outputs?.length || 0} outputs generated.`
        : result.status === 'error'
        ? `Task failed: ${result.errorDetails?.type || result.message || 'Unknown error'}`
        : result.message || `Task ${result.status}`;

      executionLogger.logEvent({
        agentId,
        action: action as 'thought' | 'file_operation' | 'api_call' | 'vector_lookup' | 'error' | 'retry' | 'completion',
        details,
        status: result.status === 'success' ? 'completed' :
               result.status === 'error' ? 'failed' : 'running',
        duration: result.metrics?.executionTime,
        metadata: {
          taskId,
          cardId,
          executionResult: result,
          tokensUsed: result.metrics?.tokensUsed,
          createdFiles: result.createdFiles,
          modifiedFiles: result.modifiedFiles,
          outputs: result.outputs?.map(output => output.substring(0, 200)) // Truncate for storage
        }
      });

    } catch (error) {
      console.error(`❌ AgentTaskFeedbackService: Failed to log execution result:`, error);
    }
  }

  /**
   * Emit events for real-time UI updates
   */
  private emitFeedbackEvents(
    agentId: string,
    result: AgentExecutionResult,
    cardId?: string,
    taskId?: string,
    boardId?: string
  ): string[] {
    const emittedEvents: string[] = [];

    try {
      if (cardId && boardId) {
        if (result.status === 'success') {
          kanbanEvents.emit('taskCompleted', {
            cardId,
            agentId,
            result,
            taskId,
            boardId,
            timestamp: Date.now()
          });
          emittedEvents.push('taskCompleted');
        } else if (result.status === 'error') {
          kanbanEvents.emit('taskFailed', {
            cardId,
            agentId,
            reason: result.errorDetails?.type || result.message || 'Unknown error',
            result,
            taskId,
            boardId,
            timestamp: Date.now()
          });
          emittedEvents.push('taskFailed');
        }
      }

    } catch (error) {
      console.error(`❌ AgentTaskFeedbackService: Failed to emit events:`, error);
    }

    return emittedEvents;
  }

  /**
   * Build completion summary from execution result
   */
  private buildCompletionSummary(result: AgentExecutionResult): string {
    const parts: string[] = [];

    if (result.message) {
      parts.push(`✅ ${result.message}`);
    }

    if (result.outputs?.length) {
      parts.push(`📄 Generated ${result.outputs.length} output(s)`);
    }

    if (result.createdFiles?.length) {
      parts.push(`📁 Created ${result.createdFiles.length} file(s): ${result.createdFiles.slice(0, 3).join(', ')}${result.createdFiles.length > 3 ? '...' : ''}`);
    }

    if (result.modifiedFiles?.length) {
      parts.push(`✏️ Modified ${result.modifiedFiles.length} file(s): ${result.modifiedFiles.slice(0, 3).join(', ')}${result.modifiedFiles.length > 3 ? '...' : ''}`);
    }

    if (result.metrics?.tokensUsed) {
      parts.push(`🔢 Used ${result.metrics.tokensUsed} tokens`);
    }

    return parts.join('\n');
  }

  /**
   * Calculate progress for partial completion
   */
  private calculatePartialProgress(result: AgentExecutionResult): number {
    // Base progress on available indicators
    let progress = 50; // Default for partial

    if (result.outputs?.length) {
      progress += 20;
    }

    if (result.createdFiles?.length || result.modifiedFiles?.length) {
      progress += 20;
    }

    return Math.min(progress, 90); // Cap at 90% for partial completion
  }

  /**
   * Add comment to Kanban card
   */
  private async addCardComment(
    cardId: string,
    agentId: string,
    comment: string,
    boardId: string
  ): Promise<void> {
    try {
      // This would use the board IPC bridge to add a comment
      // Implementation depends on the board system's comment API
      console.log(`💬 Adding comment to card ${cardId}: ${comment}`);
    } catch (error) {
      console.error(`❌ Failed to add comment to card ${cardId}:`, error);
    }
  }
}

// Export singleton instance
export const agentTaskFeedbackService = AgentTaskFeedbackService.getInstance();
