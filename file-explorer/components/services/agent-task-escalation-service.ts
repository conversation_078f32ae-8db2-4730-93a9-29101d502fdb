// components/services/agent-task-escalation-service.ts
import { AgentExecutionResult } from '../agents/agent-base';
import { Card } from '../kanban/board-context';
import { boardIPCBridge } from '../kanban/lib/board-ipc-bridge';
import { kanbanEvents } from '../kanban/lib/kanban-events';
import { executionLogger } from '../agents/agent-execution-trace';

export interface EscalationRule {
  maxRetries: number;
  escalationTiers: string[];
  escalationConditions: {
    errorTypes: string[];
    timeoutThreshold?: number;
    complexityThreshold?: string;
  };
}

export interface CardRetryMetadata {
  retryCount: number;
  escalationLevel: number;
  lastAssignedAgentId?: string;
  originalAgentId: string;
  escalationHistory: EscalationHistoryEntry[];
  lastFailureReason?: string;
  lastFailureType?: string;
}

export interface EscalationHistoryEntry {
  timestamp: number;
  fromAgentId: string;
  toAgentId: string;
  reason: string;
  escalationLevel: number;
  retryCount: number;
}

export interface EscalationResult {
  success: boolean;
  action: 'retry' | 'escalate' | 'reassign' | 'abandon';
  newAgentId?: string;
  escalationLevel: number;
  retryCount: number;
  reason: string;
  error?: string;
}

/**
 * ✅ Task 77: Agent Task Escalation Service
 *
 * Handles task retry, escalation, and reassignment logic for failed agent tasks.
 * Implements fault-tolerance by retrying or escalating to appropriate agents.
 */
export class AgentTaskEscalationService {
  private static instance: AgentTaskEscalationService;

  // Agent tier hierarchy for escalation
  private readonly AGENT_TIERS = {
    'intern': { level: 1, next: ['junior', 'midlevel'] },
    'junior': { level: 2, next: ['midlevel', 'senior'] },
    'midlevel': { level: 3, next: ['senior', 'architect'] },
    'senior': { level: 4, next: ['architect'] },
    'architect': { level: 5, next: [] },
    'designer': { level: 3, next: ['senior', 'architect'] },
    'tester': { level: 3, next: ['senior'] }
  };

  private readonly DEFAULT_ESCALATION_RULES: EscalationRule = {
    maxRetries: 2,
    escalationTiers: ['intern', 'junior', 'midlevel', 'senior', 'architect'],
    escalationConditions: {
      errorTypes: ['timeout', 'complexity_exceeded', 'capability_mismatch'],
      timeoutThreshold: 300000, // 5 minutes
      complexityThreshold: 'complex'
    }
  };

  public static getInstance(): AgentTaskEscalationService {
    if (!AgentTaskEscalationService.instance) {
      AgentTaskEscalationService.instance = new AgentTaskEscalationService();
    }
    return AgentTaskEscalationService.instance;
  }

  /**
   * Handle task failure and determine retry/escalation action
   */
  public async handleTaskFailure(
    cardId: string,
    agentId: string,
    result: AgentExecutionResult,
    boardId: string = 'main'
  ): Promise<EscalationResult> {
    try {
      console.log(`🔄 AgentTaskEscalationService: Handling task failure for card ${cardId}, agent ${agentId}`);

      // Get current card state and retry metadata
      const card = await this.getCardWithRetryMetadata(cardId, boardId);
      if (!card) {
        return {
          success: false,
          action: 'abandon',
          escalationLevel: 0,
          retryCount: 0,
          reason: 'Card not found',
          error: `Card ${cardId} not found`
        };
      }

      const retryMetadata = this.getOrCreateRetryMetadata(card, agentId);

      // Determine escalation action based on failure analysis
      const escalationAction = this.determineEscalationAction(
        retryMetadata,
        result,
        agentId
      );

      // Execute the escalation action
      const escalationResult = await this.executeEscalationAction(
        cardId,
        agentId,
        escalationAction,
        retryMetadata,
        result,
        boardId
      );

      // Log escalation event
      this.logEscalationEvent(cardId, agentId, escalationResult, result);

      return escalationResult;

    } catch (error) {
      console.error(`❌ AgentTaskEscalationService: Error handling task failure for card ${cardId}:`, error);
      return {
        success: false,
        action: 'abandon',
        escalationLevel: 0,
        retryCount: 0,
        reason: 'Escalation service error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get card with retry metadata
   */
  private async getCardWithRetryMetadata(cardId: string, boardId: string): Promise<Card | null> {
    try {
      const boardState = await boardIPCBridge.getBoardState(boardId);
      if (!boardState) return null;

      // Find card across all columns
      for (const column of boardState.columns) {
        const card = column.cards.find(c => c.id === cardId);
        if (card) return card;
      }

      return null;
    } catch (error) {
      console.error(`❌ AgentTaskEscalationService: Error getting card ${cardId}:`, error);
      return null;
    }
  }

  /**
   * Get or create retry metadata for card
   */
  private getOrCreateRetryMetadata(card: Card, agentId: string): CardRetryMetadata {
    // Check if retry metadata exists in card metadata
    const existingMetadata = (card as any).retryMetadata as CardRetryMetadata;

    if (existingMetadata) {
      return existingMetadata;
    }

    // Create new retry metadata
    return {
      retryCount: 0,
      escalationLevel: 0,
      originalAgentId: agentId,
      escalationHistory: [],
      lastAssignedAgentId: agentId
    };
  }

  /**
   * Determine what escalation action to take
   */
  private determineEscalationAction(
    retryMetadata: CardRetryMetadata,
    result: AgentExecutionResult,
    currentAgentId: string
  ): 'retry' | 'escalate' | 'reassign' | 'abandon' {
    const rules = this.DEFAULT_ESCALATION_RULES;

    // Check if we've exceeded max retries
    if (retryMetadata.retryCount >= rules.maxRetries) {
      // Check if escalation is possible
      const agentTier = this.getAgentTier(currentAgentId);
      if (agentTier && agentTier.next.length > 0) {
        return 'escalate';
      } else {
        // No higher tier available, try reassignment
        return 'reassign';
      }
    }

    // Check if error type requires immediate escalation
    if (result.errorDetails?.type && rules.escalationConditions.errorTypes.includes(result.errorDetails.type)) {
      return 'escalate';
    }

    // Check timeout threshold
    if (result.status === 'timeout' && result.metrics?.executionTime &&
        result.metrics.executionTime > (rules.escalationConditions.timeoutThreshold || 300000)) {
      return 'escalate';
    }

    // Default to retry
    return 'retry';
  }

  /**
   * Execute the determined escalation action
   */
  private async executeEscalationAction(
    cardId: string,
    currentAgentId: string,
    action: 'retry' | 'escalate' | 'reassign' | 'abandon',
    retryMetadata: CardRetryMetadata,
    result: AgentExecutionResult,
    boardId: string
  ): Promise<EscalationResult> {
    const escalationResult: EscalationResult = {
      success: false,
      action,
      escalationLevel: retryMetadata.escalationLevel,
      retryCount: retryMetadata.retryCount,
      reason: ''
    };

    switch (action) {
      case 'retry':
        escalationResult.success = await this.executeRetry(cardId, currentAgentId, retryMetadata, boardId);
        escalationResult.retryCount = retryMetadata.retryCount + 1;
        escalationResult.reason = `Retrying task with same agent (attempt ${escalationResult.retryCount})`;
        break;

      case 'escalate':
        const escalationTarget = this.selectEscalationTarget(currentAgentId);
        if (escalationTarget) {
          escalationResult.success = await this.executeEscalation(
            cardId, currentAgentId, escalationTarget, retryMetadata, boardId
          );
          escalationResult.newAgentId = escalationTarget;
          escalationResult.escalationLevel = retryMetadata.escalationLevel + 1;
          escalationResult.reason = `Escalated from ${currentAgentId} to ${escalationTarget}`;
        } else {
          escalationResult.action = 'abandon';
          escalationResult.reason = 'No escalation target available';
        }
        break;

      case 'reassign':
        const reassignTarget = this.selectReassignmentTarget(currentAgentId, retryMetadata);
        if (reassignTarget) {
          escalationResult.success = await this.executeReassignment(
            cardId, currentAgentId, reassignTarget, retryMetadata, boardId
          );
          escalationResult.newAgentId = reassignTarget;
          escalationResult.reason = `Reassigned from ${currentAgentId} to ${reassignTarget}`;
        } else {
          escalationResult.action = 'abandon';
          escalationResult.reason = 'No reassignment target available';
        }
        break;

      case 'abandon':
        escalationResult.success = await this.executeAbandon(cardId, retryMetadata, result, boardId);
        escalationResult.reason = 'Task abandoned after exhausting retry/escalation options';
        break;
    }

    // Update card metadata with new retry information
    await this.updateCardRetryMetadata(cardId, retryMetadata, escalationResult, boardId);

    return escalationResult;
  }

  /**
   * Execute retry action
   */
  private async executeRetry(
    cardId: string,
    agentId: string,
    retryMetadata: CardRetryMetadata,
    boardId: string
  ): Promise<boolean> {
    try {
      retryMetadata.retryCount++;
      retryMetadata.lastFailureReason = 'Retrying after failure';

      // Move card back to appropriate column for retry
      // This would trigger the agent system to pick up the task again
      console.log(`🔄 Retrying task for card ${cardId} with agent ${agentId} (attempt ${retryMetadata.retryCount})`);

      return true;
    } catch (error) {
      console.error(`❌ Failed to execute retry for card ${cardId}:`, error);
      return false;
    }
  }

  /**
   * Execute escalation to higher tier agent
   */
  private async executeEscalation(
    cardId: string,
    fromAgentId: string,
    toAgentId: string,
    retryMetadata: CardRetryMetadata,
    boardId: string
  ): Promise<boolean> {
    try {
      retryMetadata.escalationLevel++;
      retryMetadata.lastAssignedAgentId = toAgentId;
      retryMetadata.escalationHistory.push({
        timestamp: Date.now(),
        fromAgentId,
        toAgentId,
        reason: 'Task complexity escalation',
        escalationLevel: retryMetadata.escalationLevel,
        retryCount: retryMetadata.retryCount
      });

      // Update card assignment
      await this.updateCardAssignment(cardId, toAgentId, boardId);

      console.log(`⬆️ Escalated task for card ${cardId} from ${fromAgentId} to ${toAgentId}`);

      return true;
    } catch (error) {
      console.error(`❌ Failed to execute escalation for card ${cardId}:`, error);
      return false;
    }
  }

  /**
   * Execute reassignment to different agent of same tier
   */
  private async executeReassignment(
    cardId: string,
    fromAgentId: string,
    toAgentId: string,
    retryMetadata: CardRetryMetadata,
    boardId: string
  ): Promise<boolean> {
    try {
      retryMetadata.lastAssignedAgentId = toAgentId;
      retryMetadata.escalationHistory.push({
        timestamp: Date.now(),
        fromAgentId,
        toAgentId,
        reason: 'Agent reassignment',
        escalationLevel: retryMetadata.escalationLevel,
        retryCount: retryMetadata.retryCount
      });

      // Update card assignment
      await this.updateCardAssignment(cardId, toAgentId, boardId);

      console.log(`🔄 Reassigned task for card ${cardId} from ${fromAgentId} to ${toAgentId}`);

      return true;
    } catch (error) {
      console.error(`❌ Failed to execute reassignment for card ${cardId}:`, error);
      return false;
    }
  }

  /**
   * Execute task abandonment
   */
  private async executeAbandon(
    cardId: string,
    retryMetadata: CardRetryMetadata,
    result: AgentExecutionResult,
    boardId: string
  ): Promise<boolean> {
    try {
      // Move card to a "Failed" or "Abandoned" column
      // Add comment explaining abandonment
      console.log(`❌ Abandoning task for card ${cardId} after exhausting retry/escalation options`);

      return true;
    } catch (error) {
      console.error(`❌ Failed to execute abandonment for card ${cardId}:`, error);
      return false;
    }
  }

  /**
   * Get agent tier information
   */
  private getAgentTier(agentId: string): { level: number; next: string[] } | null {
    // Extract agent type from ID (e.g., 'intern-agent-1' -> 'intern')
    const agentType = agentId.toLowerCase().split('-')[0];
    return this.AGENT_TIERS[agentType as keyof typeof this.AGENT_TIERS] || null;
  }

  /**
   * Select escalation target agent
   */
  private selectEscalationTarget(currentAgentId: string): string | null {
    const currentTier = this.getAgentTier(currentAgentId);
    if (!currentTier || currentTier.next.length === 0) {
      return null;
    }

    // Select first available higher tier agent
    return `${currentTier.next[0]}-agent`;
  }

  /**
   * Select reassignment target agent
   */
  private selectReassignmentTarget(currentAgentId: string, retryMetadata: CardRetryMetadata): string | null {
    const currentTier = this.getAgentTier(currentAgentId);
    if (!currentTier) return null;

    // Find agents of same tier that haven't been tried yet
    const sameLevel = Object.entries(this.AGENT_TIERS)
      .filter(([_, tier]) => tier.level === currentTier.level)
      .map(([type, _]) => `${type}-agent`)
      .filter(agentId =>
        agentId !== currentAgentId &&
        !retryMetadata.escalationHistory.some(h => h.toAgentId === agentId)
      );

    return sameLevel.length > 0 ? sameLevel[0] : null;
  }

  /**
   * Update card assignment
   */
  private async updateCardAssignment(cardId: string, newAgentId: string, boardId: string): Promise<void> {
    try {
      // This would use the board IPC bridge to update card assignment
      console.log(`🔄 Updating card ${cardId} assignment to agent ${newAgentId}`);
    } catch (error) {
      console.error(`❌ Failed to update card assignment for ${cardId}:`, error);
    }
  }

  /**
   * Update card retry metadata
   */
  private async updateCardRetryMetadata(
    cardId: string,
    retryMetadata: CardRetryMetadata,
    escalationResult: EscalationResult,
    boardId: string
  ): Promise<void> {
    try {
      // This would use the board IPC bridge to update card metadata
      console.log(`📝 Updating retry metadata for card ${cardId}:`, {
        retryCount: retryMetadata.retryCount,
        escalationLevel: retryMetadata.escalationLevel,
        lastAction: escalationResult.action
      });
    } catch (error) {
      console.error(`❌ Failed to update retry metadata for card ${cardId}:`, error);
    }
  }

  /**
   * Log escalation event
   */
  private logEscalationEvent(
    cardId: string,
    agentId: string,
    escalationResult: EscalationResult,
    originalResult: AgentExecutionResult
  ): void {
    try {
      executionLogger.logEvent({
        agentId,
        action: 'retry' as 'thought' | 'file_operation' | 'api_call' | 'vector_lookup' | 'error' | 'retry' | 'completion',
        details: `${escalationResult.action}: ${escalationResult.reason}`,
        status: escalationResult.success ? 'completed' : 'failed',
        metadata: {
          cardId,
          escalationAction: escalationResult.action,
          newAgentId: escalationResult.newAgentId,
          escalationLevel: escalationResult.escalationLevel,
          retryCount: escalationResult.retryCount,
          originalFailure: originalResult.errorDetails?.type || originalResult.status
        }
      });
    } catch (error) {
      console.error(`❌ Failed to log escalation event:`, error);
    }
  }
}

// Export singleton instance
export const agentTaskEscalationService = AgentTaskEscalationService.getInstance();
