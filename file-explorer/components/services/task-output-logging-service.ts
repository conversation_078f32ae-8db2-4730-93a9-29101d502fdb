// ✅ Task 84: Task Output Logging Service
// Manages per-task terminal output logs and session storage

import { kanbanEvents } from '../kanban/lib/kanban-events';

export interface TaskOutputEntry {
  id: string;
  timestamp: number;
  agentId: string;
  taskId: string;
  cardId?: string;
  content: string;
  type: 'output' | 'error' | 'command' | 'system';
  metadata?: Record<string, any>;
}

export interface TaskSessionLog {
  taskId: string;
  cardId?: string;
  agentId: string;
  startTime: number;
  endTime?: number;
  entries: TaskOutputEntry[];
  totalLines: number;
  status: 'active' | 'completed' | 'failed';
}

class TaskOutputLoggingService {
  private taskOutputLogs: Map<string, TaskSessionLog> = new Map();
  private maxEntriesPerTask = 1000;
  private maxTasks = 100;

  /**
   * ✅ Task 84: Initialize a new task session log
   */
  initializeTaskLog(taskId: string, agentId: string, cardId?: string): void {
    const sessionLog: TaskSessionLog = {
      taskId,
      cardId,
      agentId,
      startTime: Date.now(),
      entries: [],
      totalLines: 0,
      status: 'active'
    };

    this.taskOutputLogs.set(taskId, sessionLog);

    // Add initial system entry
    this.addLogEntry(taskId, agentId, `[SYSTEM] Task session started for ${agentId}`, 'system', { cardId });

    console.log(`✅ TaskOutputLoggingService: Initialized log for task ${taskId} (agent: ${agentId})`);
  }

  /**
   * ✅ Task 84: Add a log entry to a task session
   */
  addLogEntry(
    taskId: string,
    agentId: string,
    content: string,
    type: 'output' | 'error' | 'command' | 'system' = 'output',
    metadata?: Record<string, any>
  ): void {
    const sessionLog = this.taskOutputLogs.get(taskId);
    if (!sessionLog) {
      console.warn(`⚠️ TaskOutputLoggingService: No session log found for task ${taskId}`);
      return;
    }

    const entry: TaskOutputEntry = {
      id: `entry-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      agentId,
      taskId,
      cardId: sessionLog.cardId,
      content,
      type,
      metadata
    };

    sessionLog.entries.push(entry);
    sessionLog.totalLines++;

    // Limit entries per task to prevent memory issues
    if (sessionLog.entries.length > this.maxEntriesPerTask) {
      sessionLog.entries = sessionLog.entries.slice(-this.maxEntriesPerTask);
    }

    // ✅ Task 84: Emit real-time log update event for Kanban integration
    if (sessionLog.cardId) {
      kanbanEvents.emit('taskLogUpdated', {
        taskId,
        cardId: sessionLog.cardId,
        agentId,
        lines: sessionLog.entries.slice(-10), // Send last 10 entries for real-time updates
        totalLines: sessionLog.totalLines,
        timestamp: Date.now()
      });
    }

    console.log(`📝 TaskOutputLoggingService: Added ${type} entry to task ${taskId}: ${content.substring(0, 100)}...`);
  }

  /**
   * ✅ Task 84: Write agent output to terminal and log
   */
  writeAgentOutputToTerminal(taskId: string, agentId: string, output: string): void {
    // Format output for terminal display
    const lines = output.split('\n');

    for (const line of lines) {
      if (line.trim()) {
        const formattedLine = `[${agentId}] > ${line}`;



        // ✅ Task 84: Add to task session log
        this.addLogEntry(taskId, agentId, line, 'output', {
          formattedForTerminal: formattedLine,
          originalOutput: output
        });
      }
    }
  }

  /**
   * ✅ Task 84: Write agent error to terminal and log
   */
  writeAgentErrorToTerminal(taskId: string, agentId: string, error: string): void {
    const formattedError = `[${agentId}] ❌ ERROR: ${error}`;



    // Add to task session log
    this.addLogEntry(taskId, agentId, error, 'error', {
      formattedForTerminal: formattedError,
      severity: 'error'
    });
  }

  /**
   * ✅ Task 84: Complete a task session log
   */
  completeTaskLog(taskId: string, status: 'completed' | 'failed'): void {
    const sessionLog = this.taskOutputLogs.get(taskId);
    if (!sessionLog) {
      console.warn(`⚠️ TaskOutputLoggingService: No session log found for task ${taskId}`);
      return;
    }

    sessionLog.endTime = Date.now();
    sessionLog.status = status;

    // Add completion entry
    const duration = sessionLog.endTime - sessionLog.startTime;
    this.addLogEntry(
      taskId,
      sessionLog.agentId,
      `[SYSTEM] Task session ${status} (duration: ${duration}ms, lines: ${sessionLog.totalLines})`,
      'system',
      { duration, status, finalLineCount: sessionLog.totalLines }
    );

    // Emit final log update
    if (sessionLog.cardId) {
      kanbanEvents.emit('taskLogCompleted', {
        taskId,
        cardId: sessionLog.cardId,
        agentId: sessionLog.agentId,
        status,
        duration,
        totalLines: sessionLog.totalLines,
        timestamp: Date.now()
      });
    }

    console.log(`✅ TaskOutputLoggingService: Completed log for task ${taskId} with status ${status}`);
  }

  /**
   * ✅ Task 84: Get task session log
   */
  getTaskLog(taskId: string): TaskSessionLog | undefined {
    return this.taskOutputLogs.get(taskId);
  }

  /**
   * ✅ Task 84: Get task log entries for display
   */
  getTaskLogEntries(taskId: string, limit?: number): TaskOutputEntry[] {
    const sessionLog = this.taskOutputLogs.get(taskId);
    if (!sessionLog) {
      return [];
    }

    const entries = sessionLog.entries;
    return limit ? entries.slice(-limit) : entries;
  }

  /**
   * ✅ Task 84: Get all task logs for an agent
   */
  getAgentTaskLogs(agentId: string): TaskSessionLog[] {
    return Array.from(this.taskOutputLogs.values())
      .filter(log => log.agentId === agentId);
  }

  /**
   * ✅ Task 84: Clean up old task logs
   */
  cleanupOldLogs(): void {
    const logs = Array.from(this.taskOutputLogs.entries());

    // Sort by start time, keep most recent
    logs.sort((a, b) => b[1].startTime - a[1].startTime);

    if (logs.length > this.maxTasks) {
      const toRemove = logs.slice(this.maxTasks);
      for (const [taskId] of toRemove) {
        this.taskOutputLogs.delete(taskId);
      }
      console.log(`🧹 TaskOutputLoggingService: Cleaned up ${toRemove.length} old task logs`);
    }
  }

  /**
   * ✅ Task 84: Get statistics
   */
  getStatistics(): {
    totalTasks: number;
    activeTasks: number;
    completedTasks: number;
    failedTasks: number;
    totalLogEntries: number;
  } {
    const logs = Array.from(this.taskOutputLogs.values());

    return {
      totalTasks: logs.length,
      activeTasks: logs.filter(log => log.status === 'active').length,
      completedTasks: logs.filter(log => log.status === 'completed').length,
      failedTasks: logs.filter(log => log.status === 'failed').length,
      totalLogEntries: logs.reduce((sum, log) => sum + log.entries.length, 0)
    };
  }
}

// ✅ Task 84: Export singleton instance
export const taskOutputLoggingService = new TaskOutputLoggingService();
