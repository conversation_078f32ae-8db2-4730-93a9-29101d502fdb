// components/orchestrators/taskmaster-orchestration-ui.tsx
import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Progress } from '../ui/progress';
import {
  Play,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Loader2,
  FileText,
  Users,
  Layers,
  Target
} from 'lucide-react';
import { kanbanTaskOrchestrator, OrchestrationResult } from './kanban-task-orchestrator';
import { taskmasterAdapter, TaskmasterData } from '../adapters/taskmaster-adapter';

interface TaskmasterOrchestrationUIProps {
  onOrchestrationComplete?: (result: OrchestrationResult) => void;
  className?: string;
}

export const TaskmasterOrchestrationUI: React.FC<TaskmasterOrchestrationUIProps> = ({
  onOrchestrationComplete,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasTasksFile, setHasTasksFile] = useState<boolean | null>(null);
  const [tasksData, setTasksData] = useState<TaskmasterData | null>(null);
  const [orchestrationResult, setOrchestrationResult] = useState<OrchestrationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // ✅ Check for tasks file on component mount
  React.useEffect(() => {
    checkTasksFile();
  }, []);

  const checkTasksFile = useCallback(async () => {
    try {
      // First ensure Taskmaster is initialized for the current project
      try {
        const { taskmasterIntegrationService } = await import('../services/taskmaster-integration-service');
        const initResult = await taskmasterIntegrationService.ensureInitialized();

        if (!initResult.success) {
          console.warn('⚠️ Taskmaster not initialized:', initResult.error);
          setError(`Taskmaster initialization required: ${initResult.error}`);
          setHasTasksFile(false);
          return;
        }
      } catch (integrationError) {
        console.warn('⚠️ Taskmaster integration check failed:', integrationError);
        // Continue with normal check - might be an existing project
      }

      const exists = await taskmasterAdapter.hasTasksFile();
      setHasTasksFile(exists);

      if (exists) {
        // Load tasks data for preview
        const result = await taskmasterAdapter.loadTasks();
        if (result.success && result.data) {
          setTasksData(result.data);
        }
      }
    } catch (error) {
      console.error('Failed to check tasks file:', error);
      setHasTasksFile(false);
    }
  }, []);

  // ✅ Start orchestration process
  const handleStartOrchestration = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setOrchestrationResult(null);

    try {
      console.log('🎯 Starting Taskmaster orchestration...');

      const result = await kanbanTaskOrchestrator.orchestrateTasks(true); // Force reload

      setOrchestrationResult(result);
      onOrchestrationComplete?.(result);

      if (result.success) {
        console.log(`✅ Orchestration completed: ${result.boardsCreated} boards, ${result.cardsCreated} cards`);
      } else {
        setError(result.error || 'Orchestration failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown orchestration error';
      setError(errorMessage);
      console.error('Orchestration failed:', error);
    } finally {
      setIsLoading(false);
    }
  }, [onOrchestrationComplete]);

  // ✅ Refresh tasks data
  const handleRefreshTasks = useCallback(async () => {
    setIsLoading(true);
    try {
      taskmasterAdapter.clearCache();
      await checkTasksFile();
    } catch (error) {
      setError('Failed to refresh tasks data');
    } finally {
      setIsLoading(false);
    }
  }, [checkTasksFile]);

  // ✅ Get status icon based on current state
  const getStatusIcon = () => {
    if (isLoading) return <Loader2 className="h-5 w-5 animate-spin" />;
    if (orchestrationResult?.success) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (error) return <XCircle className="h-5 w-5 text-red-500" />;
    if (hasTasksFile) return <FileText className="h-5 w-5 text-blue-500" />;
    return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Orchestration Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon()}
            Taskmaster Orchestration
          </CardTitle>
          <CardDescription>
            Convert Taskmaster tasks into Kanban boards with automatic agent assignments
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Tasks File Status */}
          {hasTasksFile === null ? (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Checking for tasks file...
            </div>
          ) : hasTasksFile ? (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Tasks file found!</strong> Ready to orchestrate {tasksData?.tasks.length || 0} tasks.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>No tasks file found.</strong> Please upload a PRD (Project Requirements Document) to generate tasks automatically. Taskmaster will parse your PRD and create structured tasks for AI execution.
              </AlertDescription>
            </Alert>
          )}

          {/* Tasks Preview */}
          {tasksData && (
            <div className="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">{tasksData.tasks.length} Tasks</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">
                  {new Set(tasksData.tasks.map(t => t.assignedAgentId)).size} Agents
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Layers className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">
                  {tasksData.metadata?.modules?.length || 0} Modules
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium">
                  {tasksData.metadata?.milestones?.length || 0} Milestones
                </span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleStartOrchestration}
              disabled={!hasTasksFile || isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Orchestrating...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Start Orchestration
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={handleRefreshTasks}
              disabled={isLoading}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Orchestration failed:</strong> {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Success Results */}
          {orchestrationResult?.success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Orchestration completed successfully!</strong>
                <div className="mt-2 space-y-1 text-sm">
                  <div>• {orchestrationResult.boardsCreated} boards created</div>
                  <div>• {orchestrationResult.cardsCreated} cards created</div>
                  <div>• {orchestrationResult.swimlanesCreated} swimlanes created</div>
                  <div>• {orchestrationResult.details?.assignmentsSent || 0} agent assignments sent</div>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Orchestration Progress */}
      {isLoading && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Orchestration Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing tasks...</span>
                <span>Please wait</span>
              </div>
              <Progress value={undefined} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Board Creation Results */}
      {orchestrationResult?.success && orchestrationResult.details?.boards && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Created Boards</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {orchestrationResult.details.boards.map((board, index) => (
                <div key={board.boardId} className="flex items-center justify-between p-2 bg-muted rounded">
                  <div>
                    <div className="font-medium text-sm">{board.boardName}</div>
                    <div className="text-xs text-muted-foreground">
                      {board.cardsCreated} cards, {board.swimlanesCreated} swimlanes
                    </div>
                  </div>
                  <Badge variant="outline">{board.tasks.length} tasks</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Agent Assignments Summary */}
      {tasksData && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Agent Assignment Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(
                tasksData.tasks.reduce((acc, task) => {
                  acc[task.assignedAgentId] = (acc[task.assignedAgentId] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>)
              ).map(([agentId, count]) => (
                <div key={agentId} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{agentId.replace(/([A-Z])/g, ' $1').trim()}</span>
                  <Badge variant="secondary">{count} tasks</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
