// components/orchestrators/kanban-task-orchestrator.ts
import { taskmaster<PERSON><PERSON>pter, AgentTask, TaskmasterData } from '../adapters/taskmaster-adapter';
import { boardIPCBridge } from '../kanban/lib/board-ipc-bridge';
import { Card, Column, Swimlane, BoardFull } from '../kanban/board-context';

export interface OrchestrationResult {
  success: boolean;
  boardsCreated: number;
  cardsCreated: number;
  swimlanesCreated: number;
  error?: string;
  boardIds?: string[];
  details?: {
    boards: BoardCreationResult[];
    totalTasks: number;
    assignmentsSent: number;
  };
}

export interface BoardCreationResult {
  boardId: string;
  boardName: string;
  cardsCreated: number;
  swimlanesCreated: number;
  tasks: string[]; // task IDs included in this board
}

export interface TaskGrouping {
  boardGroups: Map<string, AgentTask[]>; // Group by module/milestone
  swimlaneGroups: Map<string, Map<string, AgentTask[]>>; // Board -> Swimlane -> Tasks
}

export class KanbanTaskOrchestrator {
  private static instance: KanbanTaskOrchestrator;

  private constructor() {}

  public static getInstance(): KanbanTaskOrchestrator {
    if (!KanbanTaskOrchestrator.instance) {
      KanbanTaskOrchestrator.instance = new KanbanTaskOrchestrator();
    }
    return KanbanTaskOrchestrator.instance;
  }

  /**
   * ✅ Main orchestration method - converts Taskmaster tasks into Kanban boards
   */
  public async orchestrateTasks(forceReload = false): Promise<OrchestrationResult> {
    try {
      console.log('🎯 KanbanTaskOrchestrator: Starting task orchestration...');

      // Load tasks from Taskmaster
      const taskResult = await taskmasterAdapter.loadTasks(undefined, forceReload);
      
      if (!taskResult.success || !taskResult.data) {
        return {
          success: false,
          boardsCreated: 0,
          cardsCreated: 0,
          swimlanesCreated: 0,
          error: taskResult.error || 'Failed to load Taskmaster tasks'
        };
      }

      const { tasks, metadata } = taskResult.data;
      console.log(`📋 KanbanTaskOrchestrator: Processing ${tasks.length} tasks`);

      // Group tasks into boards and swimlanes
      const grouping = this.groupTasksForBoards(tasks);

      // Create boards and cards
      const boardResults: BoardCreationResult[] = [];
      let totalCardsCreated = 0;
      let totalSwimlanesCreated = 0;
      let assignmentsSent = 0;

      for (const [boardKey, boardTasks] of grouping.boardGroups) {
        const boardResult = await this.createBoardFromTasks(
          boardKey, 
          boardTasks, 
          grouping.swimlaneGroups.get(boardKey) || new Map(),
          metadata
        );

        if (boardResult) {
          boardResults.push(boardResult);
          totalCardsCreated += boardResult.cardsCreated;
          totalSwimlanesCreated += boardResult.swimlanesCreated;
        }
      }

      // Send agent assignments for all created cards
      assignmentsSent = await this.sendAgentAssignments(tasks);

      console.log(`✅ KanbanTaskOrchestrator: Created ${boardResults.length} boards, ${totalCardsCreated} cards, ${totalSwimlanesCreated} swimlanes`);

      return {
        success: true,
        boardsCreated: boardResults.length,
        cardsCreated: totalCardsCreated,
        swimlanesCreated: totalSwimlanesCreated,
        boardIds: boardResults.map(b => b.boardId),
        details: {
          boards: boardResults,
          totalTasks: tasks.length,
          assignmentsSent
        }
      };

    } catch (error) {
      console.error('KanbanTaskOrchestrator: Orchestration failed:', error);
      return {
        success: false,
        boardsCreated: 0,
        cardsCreated: 0,
        swimlanesCreated: 0,
        error: error instanceof Error ? error.message : 'Unknown orchestration error'
      };
    }
  }

  /**
   * ✅ Group tasks into boards and swimlanes based on metadata
   */
  private groupTasksForBoards(tasks: AgentTask[]): TaskGrouping {
    const boardGroups = new Map<string, AgentTask[]>();
    const swimlaneGroups = new Map<string, Map<string, AgentTask[]>>();

    for (const task of tasks) {
      // Determine board grouping (by module, milestone, or default)
      const boardKey = this.getBoardKey(task);
      
      // Determine swimlane grouping (by phase, agent type, or priority)
      const swimlaneKey = this.getSwimlaneKey(task);

      // Add to board group
      if (!boardGroups.has(boardKey)) {
        boardGroups.set(boardKey, []);
      }
      boardGroups.get(boardKey)!.push(task);

      // Add to swimlane group
      if (!swimlaneGroups.has(boardKey)) {
        swimlaneGroups.set(boardKey, new Map());
      }
      if (!swimlaneGroups.get(boardKey)!.has(swimlaneKey)) {
        swimlaneGroups.get(boardKey)!.set(swimlaneKey, []);
      }
      swimlaneGroups.get(boardKey)!.get(swimlaneKey)!.push(task);
    }

    return { boardGroups, swimlaneGroups };
  }

  /**
   * ✅ Determine board key for task grouping
   */
  private getBoardKey(task: AgentTask): string {
    // Priority: module > milestone > default
    if (task.module) {
      return `module-${task.module.toLowerCase().replace(/\s+/g, '-')}`;
    }
    if (task.milestone) {
      return `milestone-${task.milestone.toLowerCase().replace(/\s+/g, '-')}`;
    }
    return 'taskmaster-main';
  }

  /**
   * ✅ Determine swimlane key for task grouping
   */
  private getSwimlaneKey(task: AgentTask): string {
    // Priority: phase > agent type > priority
    if (task.phase) {
      return `phase-${task.phase.toLowerCase().replace(/\s+/g, '-')}`;
    }
    
    // Group by agent type
    const agentTypeMap: Record<string, string> = {
      'intern': 'junior-dev',
      'junior': 'junior-dev', 
      'midlevel': 'mid-dev',
      'senior': 'senior-dev',
      'architect': 'architecture',
      'designer': 'design',
      'tester': 'qa-testing',
      'researcher': 'research'
    };
    
    const agentType = agentTypeMap[task.assignedAgentId] || 'general';
    return `agent-${agentType}`;
  }

  /**
   * ✅ Create a Kanban board from grouped tasks
   */
  private async createBoardFromTasks(
    boardKey: string,
    tasks: AgentTask[],
    swimlaneGroups: Map<string, AgentTask[]>,
    metadata?: any
  ): Promise<BoardCreationResult | null> {
    try {
      // Generate board name
      const boardName = this.generateBoardName(boardKey, tasks, metadata);
      const boardDescription = `Generated from Taskmaster tasks - ${tasks.length} tasks total`;

      console.log(`🏗️ Creating board: ${boardName} with ${tasks.length} tasks`);

      // Create the board
      const board = await boardIPCBridge.createBoard(boardName, boardDescription);
      if (!board) {
        throw new Error(`Failed to create board: ${boardName}`);
      }

      // Create swimlanes for this board
      const swimlaneResults = await this.createSwimlanes(board.id, swimlaneGroups);

      // Create cards for all tasks in this board
      const cardResults = await this.createCardsFromTasks(board.id, tasks, swimlaneGroups);

      console.log(`✅ Board created: ${boardName} (${board.id}) - ${cardResults.length} cards, ${swimlaneResults.length} swimlanes`);

      return {
        boardId: board.id,
        boardName: board.name,
        cardsCreated: cardResults.length,
        swimlanesCreated: swimlaneResults.length,
        tasks: tasks.map(t => t.id)
      };

    } catch (error) {
      console.error(`Failed to create board for ${boardKey}:`, error);
      return null;
    }
  }

  /**
   * ✅ Generate meaningful board name
   */
  private generateBoardName(boardKey: string, tasks: AgentTask[], metadata?: any): string {
    if (boardKey.startsWith('module-')) {
      const moduleName = boardKey.replace('module-', '').replace(/-/g, ' ');
      return `${moduleName.charAt(0).toUpperCase() + moduleName.slice(1)} Module`;
    }
    
    if (boardKey.startsWith('milestone-')) {
      const milestoneName = boardKey.replace('milestone-', '').replace(/-/g, ' ');
      return `${milestoneName.charAt(0).toUpperCase() + milestoneName.slice(1)} Milestone`;
    }

    // Default board name
    const projectName = metadata?.projectName || 'Project';
    return `${projectName} - Taskmaster Tasks`;
  }

  /**
   * ✅ Create swimlanes for board
   */
  private async createSwimlanes(boardId: string, swimlaneGroups: Map<string, AgentTask[]>): Promise<string[]> {
    const createdSwimlanes: string[] = [];

    for (const [swimlaneKey, tasks] of swimlaneGroups) {
      try {
        const swimlaneName = this.generateSwimlaneName(swimlaneKey, tasks);
        const swimlane = await boardIPCBridge.addSwimlane(boardId, swimlaneName);
        
        if (swimlane) {
          createdSwimlanes.push(swimlane.id);
          console.log(`🏊 Created swimlane: ${swimlaneName} with ${tasks.length} tasks`);
        }
      } catch (error) {
        console.error(`Failed to create swimlane ${swimlaneKey}:`, error);
      }
    }

    return createdSwimlanes;
  }

  /**
   * ✅ Generate meaningful swimlane name
   */
  private generateSwimlaneName(swimlaneKey: string, tasks: AgentTask[]): string {
    if (swimlaneKey.startsWith('phase-')) {
      const phaseName = swimlaneKey.replace('phase-', '').replace(/-/g, ' ');
      return `${phaseName.charAt(0).toUpperCase() + phaseName.slice(1)} (${tasks.length})`;
    }
    
    if (swimlaneKey.startsWith('agent-')) {
      const agentType = swimlaneKey.replace('agent-', '').replace(/-/g, ' ');
      return `${agentType.charAt(0).toUpperCase() + agentType.slice(1)} (${tasks.length})`;
    }

    return `${swimlaneKey.replace(/-/g, ' ')} (${tasks.length})`;
  }

  /**
   * ✅ Create Kanban cards from tasks
   */
  private async createCardsFromTasks(
    boardId: string,
    tasks: AgentTask[],
    swimlaneGroups: Map<string, AgentTask[]>
  ): Promise<string[]> {
    const createdCards: string[] = [];

    for (const task of tasks) {
      try {
        const card = await this.createCardFromTask(boardId, task, swimlaneGroups);
        if (card) {
          createdCards.push(card.id);
        }
      } catch (error) {
        console.error(`Failed to create card for task ${task.id}:`, error);
      }
    }

    return createdCards;
  }

  /**
   * ✅ Create individual Kanban card from AgentTask
   */
  private async createCardFromTask(
    boardId: string,
    task: AgentTask,
    swimlaneGroups: Map<string, AgentTask[]>
  ): Promise<Card | null> {
    const now = new Date().toISOString();

    // Determine swimlane
    const swimlaneKey = this.getSwimlaneKey(task);
    const swimlaneId = this.findSwimlaneId(swimlaneKey, swimlaneGroups);

    // Determine initial column (always start in backlog)
    const columnId = 'column-1'; // Backlog column

    // Create card data
    const cardData = {
      title: task.title,
      description: this.buildCardDescription(task),
      priority: task.priority,
      columnId,
      swimlaneId,
      projectId: task.id,
      tags: this.buildCardTags(task),
      labels: this.buildCardLabels(task),
      progress: 0,
      assignee: this.getAgentDisplayName(task.assignedAgentId),
      assignedAgentId: task.assignedAgentId,
      agentAssignments: [{
        agentId: task.assignedAgentId,
        agentType: 'AI',
        assignmentTime: now,
        role: 'primary',
        status: 'assigned'
      }],
      dependencies: task.dependencies,
      resourceMetrics: {
        tokenUsage: 0,
        cpuTime: 0,
        memoryUsage: 0
      },
      taskHistory: [{
        timestamp: now,
        action: 'created',
        agentId: 'taskmaster-orchestrator',
        details: `Card created from Taskmaster task: ${task.id}`
      }],
      storyPoints: task.storyPoints,
      subtasks: task.subtasks?.map((subtask, index) => ({
        id: `${task.id}-subtask-${index}`,
        title: subtask,
        completed: false
      })) || []
    };

    // Create the card
    const card = await boardIPCBridge.createCard(boardId, columnId, cardData);
    
    if (card) {
      console.log(`📋 Created card: ${task.title} -> ${task.assignedAgentId}`);
    }

    return card;
  }

  /**
   * ✅ Build comprehensive card description
   */
  private buildCardDescription(task: AgentTask): string {
    let description = task.description;

    if (task.acceptanceCriteria && task.acceptanceCriteria.length > 0) {
      description += '\n\n**Acceptance Criteria:**\n';
      task.acceptanceCriteria.forEach((criteria, index) => {
        description += `${index + 1}. ${criteria}\n`;
      });
    }

    if (task.estimatedHours) {
      description += `\n**Estimated Hours:** ${task.estimatedHours}`;
    }

    if (task.complexity) {
      description += `\n**Complexity:** ${task.complexity}`;
    }

    return description;
  }

  /**
   * ✅ Build card tags from task metadata
   */
  private buildCardTags(task: AgentTask): string[] {
    const tags: string[] = [...(task.tags || [])];

    if (task.module) tags.push(`module:${task.module}`);
    if (task.milestone) tags.push(`milestone:${task.milestone}`);
    if (task.phase) tags.push(`phase:${task.phase}`);
    if (task.complexity) tags.push(`complexity:${task.complexity}`);

    return tags;
  }

  /**
   * ✅ Build card labels
   */
  private buildCardLabels(task: AgentTask): any[] {
    const labels = [];

    // Priority label
    labels.push({
      id: `priority-${task.priority}`,
      name: task.priority.charAt(0).toUpperCase() + task.priority.slice(1),
      color: this.getPriorityColor(task.priority)
    });

    // Agent type label
    labels.push({
      id: `agent-${task.assignedAgentId}`,
      name: this.getAgentDisplayName(task.assignedAgentId),
      color: this.getAgentColor(task.assignedAgentId)
    });

    return labels;
  }

  /**
   * ✅ Get priority color
   */
  private getPriorityColor(priority: string): string {
    const colors = {
      'low': '#10b981',
      'medium': '#f59e0b', 
      'high': '#ef4444'
    };
    return colors[priority as keyof typeof colors] || colors.medium;
  }

  /**
   * ✅ Get agent display name
   */
  private getAgentDisplayName(agentId: string): string {
    const names: Record<string, string> = {
      'intern': 'Intern Agent',
      'junior': 'Junior Agent',
      'midlevel': 'Mid-level Agent',
      'senior': 'Senior Agent',
      'architect': 'Architect Agent',
      'designer': 'Designer Agent',
      'tester': 'Tester Agent',
      'researcher': 'Researcher Agent'
    };
    return names[agentId] || 'AI Agent';
  }

  /**
   * ✅ Get agent color
   */
  private getAgentColor(agentId: string): string {
    const colors: Record<string, string> = {
      'intern': '#84cc16',
      'junior': '#06b6d4',
      'midlevel': '#3b82f6',
      'senior': '#8b5cf6',
      'architect': '#f59e0b',
      'designer': '#ec4899',
      'tester': '#ef4444',
      'researcher': '#10b981'
    };
    return colors[agentId] || '#6b7280';
  }

  /**
   * ✅ Find swimlane ID for a given key
   */
  private findSwimlaneId(swimlaneKey: string, swimlaneGroups: Map<string, AgentTask[]>): string | undefined {
    // This would need to be implemented based on actual swimlane creation results
    // For now, return undefined to use default swimlane
    return undefined;
  }

  /**
   * ✅ Send agent assignments for all tasks
   */
  private async sendAgentAssignments(tasks: AgentTask[]): Promise<number> {
    let assignmentsSent = 0;

    for (const task of tasks) {
      try {
        // The agent assignment will be handled automatically when cards are created
        // with assignedAgentId field, triggering the existing agent assignment system
        assignmentsSent++;
      } catch (error) {
        console.error(`Failed to send assignment for task ${task.id}:`, error);
      }
    }

    return assignmentsSent;
  }
}

// Export singleton instance
export const kanbanTaskOrchestrator = KanbanTaskOrchestrator.getInstance();
