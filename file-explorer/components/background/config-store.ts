// components/background/config-store.ts
// Note: This file is for the Electron main process only
// Browser/renderer components should use config-store-browser.ts instead

let DatabaseManager: any;
let getDatabaseManager: any;
let Migration: any;

// Dynamic imports for main process only
async function loadDatabaseManager() {
  if (typeof window !== 'undefined') {
    throw new Error('ConfigStore can only be used in the main process');
  }

  if (!DatabaseManager) {
    const dbModule = await import('./database-manager');
    DatabaseManager = dbModule.DatabaseManager;
    getDatabaseManager = dbModule.getDatabaseManager;
    Migration = dbModule.Migration;
  }
}

export interface ProjectConfig {
  id: string;
  name: string;
  path: string;
  settings: Record<string, any>;
  namingConventions: NamingConventions;
  codeArchitecture: CodeArchitecture;
  styleGuide: StyleGuide;
  createdAt: Date;
  updatedAt: Date;
}

export interface NamingConventions {
  files: {
    caseStyle: 'camelCase' | 'kebab-case' | 'snake_case' | 'PascalCase';
    extensions: string[];
    patterns: Record<string, string>;
  };
  variables: {
    caseStyle: 'camelCase' | 'snake_case' | 'PascalCase';
    constants: 'UPPER_SNAKE_CASE' | 'camelCase';
    private: string; // prefix or suffix pattern
  };
  functions: {
    caseStyle: 'camelCase' | 'snake_case' | 'PascalCase';
    async: string; // prefix or suffix pattern
    handlers: string; // prefix or suffix pattern
  };
  classes: {
    caseStyle: 'PascalCase' | 'camelCase';
    interfaces: string; // prefix pattern (e.g., 'I')
    abstract: string; // prefix pattern (e.g., 'Abstract')
    types: string; // suffix pattern (e.g., 'Type')
  };
}

export interface CodeArchitecture {
  patterns: string[];
  structure: {
    srcDir: string;
    testDir: string;
    docsDir: string;
    configDir: string;
  };
  dependencies: {
    allowed: string[];
    forbidden: string[];
    internal: Record<string, string[]>;
  };
  rules: {
    maxFileSize: number;
    maxFunctionLength: number;
    maxClassSize: number;
    cyclomaticComplexity: number;
  };
}

export interface StyleGuide {
  formatting: {
    indentSize: number;
    indentType: 'spaces' | 'tabs';
    lineLength: number;
    trailingCommas: boolean;
    semicolons: boolean;
  };
  imports: {
    sortOrder: string[];
    groupSeparation: boolean;
    aliasPatterns: Record<string, string>;
  };
  comments: {
    requireJSDoc: boolean;
    headerTemplate: string;
    todoFormat: string;
  };
}

export interface GlobalSettings {
  id: string;
  category: string;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  encrypted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class ConfigStore {
  private db: any;
  private initialized = false;

  constructor(dbManager?: any) {
    this.db = dbManager;
  }

  public async initialize(): Promise<void> {
    if (this.initialized) return;

    // Load database manager dynamically
    await loadDatabaseManager();

    if (!this.db) {
      this.db = getDatabaseManager();
    }

    await this.db.initialize();
    this.addMigrations();
    this.initialized = true;
  }

  private addMigrations(): void {
    const migrations: Migration[] = [
      {
        version: 1,
        name: 'create_projects_table',
        up: `
          CREATE TABLE projects (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            path TEXT NOT NULL UNIQUE,
            settings TEXT NOT NULL DEFAULT '{}',
            naming_conventions TEXT NOT NULL DEFAULT '{}',
            code_architecture TEXT NOT NULL DEFAULT '{}',
            style_guide TEXT NOT NULL DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          );

          CREATE INDEX idx_projects_path ON projects(path);
          CREATE INDEX idx_projects_name ON projects(name);
        `,
        down: 'DROP TABLE projects;'
      },
      {
        version: 2,
        name: 'create_global_settings_table',
        up: `
          CREATE TABLE global_settings (
            id TEXT PRIMARY KEY,
            category TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT NOT NULL,
            type TEXT NOT NULL CHECK (type IN ('string', 'number', 'boolean', 'object', 'array')),
            encrypted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(category, key)
          );

          CREATE INDEX idx_global_settings_category ON global_settings(category);
          CREATE INDEX idx_global_settings_key ON global_settings(category, key);
        `,
        down: 'DROP TABLE global_settings;'
      },
      {
        version: 3,
        name: 'create_project_settings_table',
        up: `
          CREATE TABLE project_settings (
            id TEXT PRIMARY KEY,
            project_id TEXT NOT NULL,
            category TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT NOT NULL,
            type TEXT NOT NULL CHECK (type IN ('string', 'number', 'boolean', 'object', 'array')),
            encrypted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            UNIQUE(project_id, category, key)
          );

          CREATE INDEX idx_project_settings_project ON project_settings(project_id);
          CREATE INDEX idx_project_settings_category ON project_settings(project_id, category);
        `,
        down: 'DROP TABLE project_settings;'
      }
    ];

    this.db.addMigrations(migrations);
  }

  // Project Configuration Methods
  public async createProject(config: Omit<ProjectConfig, 'createdAt' | 'updatedAt'>): Promise<ProjectConfig> {
    await this.initialize();

    const now = new Date();
    const projectConfig: ProjectConfig = {
      ...config,
      createdAt: now,
      updatedAt: now
    };

    this.db.execute(`
      INSERT INTO projects (
        id, name, path, settings, naming_conventions,
        code_architecture, style_guide, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      projectConfig.id,
      projectConfig.name,
      projectConfig.path,
      JSON.stringify(projectConfig.settings),
      JSON.stringify(projectConfig.namingConventions),
      JSON.stringify(projectConfig.codeArchitecture),
      JSON.stringify(projectConfig.styleGuide),
      now.toISOString(),
      now.toISOString()
    ]);

    return projectConfig;
  }

  public async getProject(id: string): Promise<ProjectConfig | null> {
    await this.initialize();

    const row = this.db.queryOne<any>(`
      SELECT * FROM projects WHERE id = ?
    `, [id]);

    if (!row) return null;

    return this.mapRowToProject(row);
  }

  public async getProjectByPath(path: string): Promise<ProjectConfig | null> {
    await this.initialize();

    const row = this.db.queryOne<any>(`
      SELECT * FROM projects WHERE path = ?
    `, [path]);

    if (!row) return null;

    return this.mapRowToProject(row);
  }

  public async getAllProjects(): Promise<ProjectConfig[]> {
    await this.initialize();

    const rows = this.db.query<any>(`
      SELECT * FROM projects ORDER BY updated_at DESC
    `);

    return rows.map(row => this.mapRowToProject(row));
  }

  public async updateProject(id: string, updates: Partial<ProjectConfig>): Promise<ProjectConfig | null> {
    await this.initialize();

    const existing = await this.getProject(id);
    if (!existing) return null;

    const updated = { ...existing, ...updates, updatedAt: new Date() };

    this.db.execute(`
      UPDATE projects SET
        name = ?, path = ?, settings = ?, naming_conventions = ?,
        code_architecture = ?, style_guide = ?, updated_at = ?
      WHERE id = ?
    `, [
      updated.name,
      updated.path,
      JSON.stringify(updated.settings),
      JSON.stringify(updated.namingConventions),
      JSON.stringify(updated.codeArchitecture),
      JSON.stringify(updated.styleGuide),
      updated.updatedAt.toISOString(),
      id
    ]);

    return updated;
  }

  public async deleteProject(id: string): Promise<boolean> {
    await this.initialize();

    const result = this.db.execute(`DELETE FROM projects WHERE id = ?`, [id]);
    return result.changes > 0;
  }

  private mapRowToProject(row: any): ProjectConfig {
    return {
      id: row.id,
      name: row.name,
      path: row.path,
      settings: JSON.parse(row.settings),
      namingConventions: JSON.parse(row.naming_conventions),
      codeArchitecture: JSON.parse(row.code_architecture),
      styleGuide: JSON.parse(row.style_guide),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  // Global Settings Methods
  public async setGlobalSetting(category: string, key: string, value: any, encrypted = false): Promise<void> {
    await this.initialize();

    const id = `${category}_${key}`;
    const type = this.getValueType(value);
    const serializedValue = this.serializeValue(value);

    this.db.execute(`
      INSERT OR REPLACE INTO global_settings
      (id, category, key, value, type, encrypted, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [id, category, key, serializedValue, type, encrypted, new Date().toISOString()]);
  }

  public async getGlobalSetting<T = any>(category: string, key: string): Promise<T | null> {
    await this.initialize();

    const row = this.db.queryOne<any>(`
      SELECT * FROM global_settings WHERE category = ? AND key = ?
    `, [category, key]);

    if (!row) return null;

    return this.deserializeValue(row.value, row.type);
  }

  public async getGlobalSettingsByCategory(category: string): Promise<Record<string, any>> {
    await this.initialize();

    const rows = this.db.query<any>(`
      SELECT key, value, type FROM global_settings WHERE category = ?
    `, [category]);

    const settings: Record<string, any> = {};
    for (const row of rows) {
      settings[row.key] = this.deserializeValue(row.value, row.type);
    }

    return settings;
  }

  public async deleteGlobalSetting(category: string, key: string): Promise<boolean> {
    await this.initialize();

    const result = this.db.execute(`
      DELETE FROM global_settings WHERE category = ? AND key = ?
    `, [category, key]);

    return result.changes > 0;
  }

  // Utility methods
  private getValueType(value: any): string {
    if (typeof value === 'string') return 'string';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    return 'string';
  }

  private serializeValue(value: any): string {
    if (typeof value === 'string') return value;
    return JSON.stringify(value);
  }

  private deserializeValue(value: string, type: string): any {
    if (type === 'string') return value;
    if (type === 'number') return Number(value);
    if (type === 'boolean') return value === 'true';
    return JSON.parse(value);
  }
}

// Singleton instance
let configStore: ConfigStore | null = null;

export function getConfigStore(dbManager?: any): ConfigStore {
  if (!configStore) {
    configStore = new ConfigStore(dbManager);
  }
  return configStore;
}
