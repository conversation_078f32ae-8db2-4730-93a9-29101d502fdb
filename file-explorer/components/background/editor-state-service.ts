// components/background/editor-state-service.ts

export interface EditorTabState {
  id: string;
  name: string;
  type: string;
  path: string;
  content?: string;
  isDirty?: boolean;
}

export interface EditorState {
  openTabs: EditorTabState[];
  activeTabId: string | null;
  editorOptions: {
    theme: string;
    fontSize: number;
    wordWrap: boolean;
    minimap: boolean;
  };
}

export interface EditorStateEvent {
  id: string;
  type: 'tab_opened' | 'tab_closed' | 'tab_switched' | 'tab_updated' | 'options_changed' | 'state_sync';
  timestamp: number;
  windowId: string;
  editorState?: EditorState;
  tabId?: string;
  tabData?: EditorTabState;
  options?: Partial<EditorState['editorOptions']>;
}

export type EditorStateEventHandler = (event: EditorStateEvent) => void;

export class EditorStateService {
  private state: EditorState = {
    openTabs: [],
    activeTabId: null,
    editorOptions: {
      theme: 'dark',
      fontSize: 14,
      wordWrap: true,
      minimap: true
    }
  };

  private eventHandlers: EditorStateEventHandler[] = [];
  private isInitialized = false;
  private windowId: string;

  constructor() {
    this.windowId = this.generateWindowId();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    // Set up IPC event listeners for cross-window synchronization
    this.setupIPCListeners();

    // Request initial state from other windows
    await this.requestInitialState();

    this.isInitialized = true;
    console.log('EditorStateService initialized with windowId:', this.windowId);
  }

  /**
   * Get current editor state
   */
  getState(): EditorState {
    return { ...this.state };
  }

  /**
   * Open a new tab
   */
  async openTab(tabData: EditorTabState): Promise<void> {
    // Check if tab is already open
    const existingTab = this.state.openTabs.find(tab => tab.path === tabData.path);

    if (existingTab) {
      // Tab already exists, just switch to it
      await this.switchToTab(existingTab.id);
      return;
    }

    // Add new tab
    this.state.openTabs.push(tabData);
    this.state.activeTabId = tabData.id;

    // Broadcast tab opened event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'tab_opened',
      timestamp: Date.now(),
      windowId: this.windowId,
      tabData,
      editorState: this.getState()
    });

    console.log(`Tab opened: ${tabData.name} (${tabData.id})`);
  }

  /**
   * Close a tab
   */
  async closeTab(tabId: string): Promise<void> {
    const tabIndex = this.state.openTabs.findIndex(tab => tab.id === tabId);
    if (tabIndex === -1) return;

    const closedTab = this.state.openTabs[tabIndex];
    this.state.openTabs.splice(tabIndex, 1);

    // If this was the active tab, switch to another tab
    if (this.state.activeTabId === tabId) {
      if (this.state.openTabs.length > 0) {
        // Switch to the previous tab or the first available tab
        const newActiveIndex = Math.max(0, tabIndex - 1);
        this.state.activeTabId = this.state.openTabs[newActiveIndex]?.id || null;
      } else {
        this.state.activeTabId = null;
      }
    }

    // Broadcast tab closed event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'tab_closed',
      timestamp: Date.now(),
      windowId: this.windowId,
      tabId,
      editorState: this.getState()
    });

    console.log(`Tab closed: ${closedTab.name} (${tabId})`);
  }

  /**
   * Switch to a specific tab
   */
  async switchToTab(tabId: string): Promise<void> {
    const tab = this.state.openTabs.find(t => t.id === tabId);
    if (!tab || this.state.activeTabId === tabId) return;

    this.state.activeTabId = tabId;

    // Broadcast tab switched event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'tab_switched',
      timestamp: Date.now(),
      windowId: this.windowId,
      tabId,
      editorState: this.getState()
    });

    console.log(`Switched to tab: ${tab.name} (${tabId})`);
  }

  /**
   * Update tab data (e.g., mark as dirty)
   */
  async updateTab(tabId: string, updates: Partial<EditorTabState>): Promise<void> {
    const tab = this.state.openTabs.find(t => t.id === tabId);
    if (!tab) return;

    Object.assign(tab, updates);

    // Broadcast tab updated event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'tab_updated',
      timestamp: Date.now(),
      windowId: this.windowId,
      tabId,
      tabData: tab,
      editorState: this.getState()
    });
  }

  /**
   * Update editor options
   */
  async updateOptions(options: Partial<EditorState['editorOptions']>): Promise<void> {
    Object.assign(this.state.editorOptions, options);

    // Broadcast options changed event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'options_changed',
      timestamp: Date.now(),
      windowId: this.windowId,
      options,
      editorState: this.getState()
    });
  }

  /**
   * Sync complete state (used when a new window opens)
   */
  async syncState(newState: EditorState): Promise<void> {
    this.state = { ...newState };

    // Broadcast state sync event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'state_sync',
      timestamp: Date.now(),
      windowId: this.windowId,
      editorState: this.getState()
    });

    console.log('Editor state synced:', this.state);
  }

  /**
   * Add event handler
   */
  onEditorStateEvent(handler: EditorStateEventHandler): void {
    this.eventHandlers.push(handler);
  }

  /**
   * Remove event handler
   */
  offEditorStateEvent(handler: EditorStateEventHandler): void {
    const index = this.eventHandlers.indexOf(handler);
    if (index > -1) {
      this.eventHandlers.splice(index, 1);
    }
  }

  /**
   * Get active tab
   */
  getActiveTab(): EditorTabState | null {
    if (!this.state.activeTabId) return null;
    return this.state.openTabs.find(tab => tab.id === this.state.activeTabId) || null;
  }

  /**
   * Get tab by path
   */
  getTabByPath(path: string): EditorTabState | null {
    return this.state.openTabs.find(tab => tab.path === path) || null;
  }

  /**
   * Shutdown the service
   */
  shutdown(): void {
    this.state = {
      openTabs: [],
      activeTabId: null,
      editorOptions: {
        theme: 'dark',
        fontSize: 14,
        wordWrap: true,
        minimap: true
      }
    };
    this.eventHandlers = [];
    this.isInitialized = false;
    console.log('EditorStateService shutdown');
  }

  // Private methods
  private generateEventId(): string {
    return `editor-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateWindowId(): string {
    return `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Request initial editor state from other windows
   */
  private async requestInitialState(): Promise<void> {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
      const requestId = this.generateEventId();

      // Send request to all other windows
      window.electronAPI.ipc.send('request-initial-editor-state', {
        requestId,
        windowId: this.windowId
      });

      console.log('EditorStateService: Requested initial state from other windows');
    }
  }

  private setupIPCListeners(): void {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
      // Listen for editor state events from other windows
      window.electronAPI.ipc.on('editor-state-event', (event: EditorStateEvent) => {
        if (event.windowId !== this.windowId) {
          this.handleRemoteEditorStateEvent(event);
        }
      });

      // Listen for initial state requests from other windows
      window.electronAPI.ipc.on('request-initial-editor-state', (requestEvent: { requestId: string; windowId: string }) => {
        if (requestEvent.windowId !== this.windowId && this.state.openTabs.length > 0) {
          // Send our current state to the requesting window
          this.broadcastEvent({
            id: this.generateEventId(),
            type: 'state_sync',
            timestamp: Date.now(),
            windowId: this.windowId,
            editorState: this.getState()
          });
        }
      });

      // Listen for initial state responses
      window.electronAPI.ipc.on('editor-state-init', (initState: EditorState) => {
        if (initState.openTabs.length > 0) {
          this.state = { ...initState };
          console.log('EditorStateService: Received initial state with', initState.openTabs.length, 'tabs');

          // Notify local handlers about the state update
          this.eventHandlers.forEach(handler => {
            try {
              handler({
                id: this.generateEventId(),
                type: 'state_sync',
                timestamp: Date.now(),
                windowId: 'init',
                editorState: this.state
              });
            } catch (error) {
              console.error('Error in initial state handler:', error);
            }
          });
        }
      });
    }
  }

  private handleRemoteEditorStateEvent(event: EditorStateEvent): void {
    // Handle editor state events from other windows
    if (event.editorState) {
      // Update local state to match remote state
      this.state = { ...event.editorState };
    }

    // Notify local handlers
    this.eventHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error in editor state event handler:', error);
      }
    });
  }

  private broadcastEvent(event: EditorStateEvent): void {
    // Broadcast to other windows via IPC
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
      window.electronAPI.ipc.send('editor-state-event', event);
    }

    // Notify local handlers
    this.eventHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error in editor state event handler:', error);
      }
    });
  }
}

// Global editor state service instance
export const globalEditorStateService = new EditorStateService();
