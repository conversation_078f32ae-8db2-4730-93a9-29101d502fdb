// components/background/code-completion-enhancer.ts

import { getMonacoIntegration, EditorEvent, MonacoEditorInstance } from './monaco-integration';
import { getSyntaxAnalyzer } from './syntax-analyzer';
import { BasicVectorDatabase, SearchResult } from './vector-database';

export interface CompletionItem {
  id: string;
  label: string;
  kind: CompletionItemKind;
  detail?: string;
  documentation?: string;
  insertText: string;
  insertTextRules?: number;
  range?: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
  sortText?: string;
  filterText?: string;
  preselect?: boolean;
  additionalTextEdits?: any[];
  command?: any;
  confidence: number;
  source: 'monaco' | 'agent' | 'vector' | 'pattern';
  agentGenerated: boolean;
}

export enum CompletionItemKind {
  Method = 0,
  Function = 1,
  Constructor = 2,
  Field = 3,
  Variable = 4,
  Class = 5,
  Struct = 6,
  Interface = 7,
  Module = 8,
  Property = 9,
  Event = 10,
  Operator = 11,
  Unit = 12,
  Value = 13,
  Constant = 14,
  Enum = 15,
  EnumMember = 16,
  Keyword = 17,
  Text = 18,
  Color = 19,
  File = 20,
  Reference = 21,
  Customcolor = 22,
  Folder = 23,
  TypeParameter = 24,
  User = 25,
  Issue = 26,
  Snippet = 27
}

export interface CompletionContext {
  editorId: string;
  filePath: string;
  language: string;
  position: { line: number; column: number };
  triggerCharacter?: string;
  triggerKind: 'Invoke' | 'TriggerCharacter' | 'TriggerForIncompleteCompletions';
  currentWord: string;
  lineText: string;
  precedingText: string;
  followingText: string;
  scope: string;
  symbols: any[];
}

export interface CompletionResult {
  id: string;
  context: CompletionContext;
  items: CompletionItem[];
  incomplete: boolean;
  timestamp: number;
  processingTime: number;
  sources: {
    monaco: number;
    agent: number;
    vector: number;
    pattern: number;
  };
}

export interface CodeCompletionConfig {
  enableAgentCompletion: boolean;
  enableVectorCompletion: boolean;
  enablePatternCompletion: boolean;
  enableContextualCompletion: boolean;
  maxCompletionItems: number;
  minConfidenceThreshold: number;
  vectorSearchLimit: number;
  patternMatchThreshold: number;
  completionTimeout: number;
}

export class CodeCompletionEnhancer {
  private monacoIntegration = getMonacoIntegration();
  private syntaxAnalyzer: any; // Will be initialized with vector database
  private vectorDb: BasicVectorDatabase;
  private completionResults: Map<string, CompletionResult> = new Map();
  private completionProviders: Map<string, any> = new Map();
  private config: CodeCompletionConfig = {
    enableAgentCompletion: true,
    enableVectorCompletion: true,
    enablePatternCompletion: true,
    enableContextualCompletion: true,
    maxCompletionItems: 50,
    minConfidenceThreshold: 0.3,
    vectorSearchLimit: 20,
    patternMatchThreshold: 0.5,
    completionTimeout: 2000
  };

  constructor(vectorDb: BasicVectorDatabase) {
    this.vectorDb = vectorDb;
    this.syntaxAnalyzer = getSyntaxAnalyzer(vectorDb);
    this.setupEventListeners();
  }

  /**
   * Configure the code completion enhancer
   */
  configure(config: Partial<CodeCompletionConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Register completion provider for a language
   */
  registerCompletionProvider(language: string, editorId: string): void {
    if (typeof window === 'undefined' || !(window as any).monaco) {
      console.warn('Monaco editor not available for completion provider registration');
      return;
    }

    const monaco = (window as any).monaco;

    const provider = monaco.languages.registerCompletionItemProvider(language, {
      provideCompletionItems: async (model: any, position: any, context: any, token: any) => {
        return this.provideCompletionItems(editorId, model, position, context, token);
      },
      triggerCharacters: ['.', '(', '[', '"', "'", '/', '<', ' '],
      resolveCompletionItem: (item: any, token: any) => {
        return this.resolveCompletionItem(item, token);
      }
    });

    this.completionProviders.set(`${language}-${editorId}`, provider);
    console.log(`Completion provider registered for ${language} in editor ${editorId}`);
  }

  /**
   * Unregister completion provider
   */
  unregisterCompletionProvider(language: string, editorId: string): void {
    const key = `${language}-${editorId}`;
    const provider = this.completionProviders.get(key);

    if (provider) {
      provider.dispose();
      this.completionProviders.delete(key);
      console.log(`Completion provider unregistered for ${language} in editor ${editorId}`);
    }
  }

  /**
   * Provide completion items for Monaco editor
   */
  async provideCompletionItems(
    editorId: string,
    model: any,
    position: any,
    context: any,
    token: any
  ): Promise<any> {
    const startTime = Date.now();

    try {
      // Build completion context
      const completionContext = this.buildCompletionContext(editorId, model, position, context);

      // Get completion items from various sources
      const items: CompletionItem[] = [];
      const sources = { monaco: 0, agent: 0, vector: 0, pattern: 0 };

      // Get Monaco's built-in completions first
      const monacoItems = await this.getMonacoCompletions(model, position, context);
      items.push(...monacoItems);
      sources.monaco = monacoItems.length;

      // Get vector-based completions
      if (this.config.enableVectorCompletion) {
        const vectorItems = await this.getVectorCompletions(completionContext);
        items.push(...vectorItems);
        sources.vector = vectorItems.length;
      }

      // Get pattern-based completions
      if (this.config.enablePatternCompletion) {
        const patternItems = await this.getPatternCompletions(completionContext);
        items.push(...patternItems);
        sources.pattern = patternItems.length;
      }

      // Get agent-generated completions
      if (this.config.enableAgentCompletion) {
        const agentItems = await this.getAgentCompletions(completionContext);
        items.push(...agentItems);
        sources.agent = agentItems.length;
      }

      // Filter and sort items
      const filteredItems = this.filterAndSortCompletions(items, completionContext);

      // Create result
      const result: CompletionResult = {
        id: `completion-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        context: completionContext,
        items: filteredItems,
        incomplete: filteredItems.length >= this.config.maxCompletionItems,
        timestamp: Date.now(),
        processingTime: Date.now() - startTime,
        sources
      };

      this.completionResults.set(editorId, result);

      // Convert to Monaco format
      return {
        suggestions: filteredItems.map(item => this.convertToMonacoCompletion(item)),
        incomplete: result.incomplete
      };

    } catch (error) {
      console.error('Error providing completion items:', error);
      return { suggestions: [], incomplete: false };
    }
  }

  /**
   * Resolve completion item with additional details
   */
  async resolveCompletionItem(item: any, token: any): Promise<any> {
    try {
      // Add additional documentation or details if needed
      if (item.agentGenerated) {
        // Could fetch more detailed documentation from agents
        item.documentation = {
          value: `${item.documentation || ''}\n\n*Generated by AI Agent*`,
          isTrusted: true
        };
      }

      return item;
    } catch (error) {
      console.error('Error resolving completion item:', error);
      return item;
    }
  }

  /**
   * Get completion result for an editor
   */
  getCompletionResult(editorId: string): CompletionResult | null {
    return this.completionResults.get(editorId) || null;
  }

  /**
   * Clear completion results
   */
  clearCompletionResults(editorId?: string): void {
    if (editorId) {
      this.completionResults.delete(editorId);
    } else {
      this.completionResults.clear();
    }
  }

  /**
   * Shutdown completion enhancer
   */
  shutdown(): void {
    // Dispose all completion providers
    for (const provider of this.completionProviders.values()) {
      try {
        provider.dispose();
      } catch (error) {
        console.warn('Error disposing completion provider:', error);
      }
    }

    this.completionProviders.clear();
    this.completionResults.clear();
    console.log('Code completion enhancer shutdown');
  }

  private setupEventListeners(): void {
    this.monacoIntegration.onEditorEvent((event: EditorEvent) => {
      if (event.type === 'completion_requested') {
        // Handle completion requests if needed
        console.log('Completion requested for editor:', event.editorId);
      }
    });
  }

  private buildCompletionContext(editorId: string, model: any, position: any, context: any): CompletionContext {
    const editor = this.monacoIntegration.getEditor(editorId);
    if (!editor) {
      throw new Error(`Editor not found: ${editorId}`);
    }

    const lineText = model.getLineContent(position.lineNumber);
    const textBeforeCursor = lineText.substring(0, position.column - 1);
    const textAfterCursor = lineText.substring(position.column - 1);

    // Extract current word being typed
    const wordMatch = textBeforeCursor.match(/\w+$/);
    const currentWord = wordMatch ? wordMatch[0] : '';

    // Get preceding text for context
    const precedingLines = Math.min(5, position.lineNumber - 1);
    const precedingText = model.getValueInRange({
      startLineNumber: Math.max(1, position.lineNumber - precedingLines),
      startColumn: 1,
      endLineNumber: position.lineNumber,
      endColumn: position.column
    });

    // Get following text for context
    const followingLines = Math.min(5, model.getLineCount() - position.lineNumber);
    const followingText = model.getValueInRange({
      startLineNumber: position.lineNumber,
      startColumn: position.column,
      endLineNumber: Math.min(model.getLineCount(), position.lineNumber + followingLines),
      endColumn: model.getLineMaxColumn(Math.min(model.getLineCount(), position.lineNumber + followingLines))
    });

    return {
      editorId,
      filePath: editor.filePath,
      language: editor.language,
      position: { line: position.lineNumber, column: position.column },
      triggerCharacter: context.triggerCharacter,
      triggerKind: context.triggerKind === 1 ? 'Invoke' :
                   context.triggerKind === 2 ? 'TriggerCharacter' : 'TriggerForIncompleteCompletions',
      currentWord,
      lineText,
      precedingText,
      followingText,
      scope: this.detectScope(precedingText),
      symbols: [] // Would be populated from syntax analysis
    };
  }

  private async getMonacoCompletions(model: any, position: any, context: any): Promise<CompletionItem[]> {
    const items: CompletionItem[] = [];

    try {
      // Monaco's built-in completions are handled automatically
      // We can enhance them here if needed

      // For now, return empty array as Monaco handles its own completions
      return items;
    } catch (error) {
      console.warn('Error getting Monaco completions:', error);
      return items;
    }
  }

  private async getVectorCompletions(context: CompletionContext): Promise<CompletionItem[]> {
    const items: CompletionItem[] = [];

    try {
      if (!context.currentWord || context.currentWord.length < 2) {
        return items;
      }

      // Search for similar code patterns
      const searchQuery = `${context.precedingText} ${context.currentWord}`;
      const results = await this.vectorDb.search(searchQuery, {
        limit: this.config.vectorSearchLimit,
        threshold: this.config.minConfidenceThreshold,
        filters: { language: context.language }
      });

      for (const result of results) {
        if (result.similarity > this.config.minConfidenceThreshold) {
          // Extract completion suggestions from similar code
          const suggestions = this.extractCompletionsFromCode(result.document.content, context);
          items.push(...suggestions.map(suggestion => ({
            ...suggestion,
            confidence: result.similarity,
            source: 'vector' as const,
            agentGenerated: false
          })));
        }
      }

      return items.slice(0, 10); // Limit vector completions
    } catch (error) {
      console.warn('Error getting vector completions:', error);
      return items;
    }
  }

  private async getPatternCompletions(context: CompletionContext): Promise<CompletionItem[]> {
    const items: CompletionItem[] = [];

    try {
      // Common patterns based on language and context
      const patterns = this.getLanguagePatterns(context.language, context);

      for (const pattern of patterns) {
        if (pattern.trigger.test(context.precedingText) &&
            pattern.label.toLowerCase().includes(context.currentWord.toLowerCase())) {
          items.push({
            id: `pattern-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            label: pattern.label,
            kind: pattern.kind,
            detail: pattern.detail,
            documentation: pattern.documentation,
            insertText: pattern.insertText,
            confidence: pattern.confidence,
            source: 'pattern',
            agentGenerated: false
          });
        }
      }

      return items;
    } catch (error) {
      console.warn('Error getting pattern completions:', error);
      return items;
    }
  }

  private async getAgentCompletions(context: CompletionContext): Promise<CompletionItem[]> {
    const items: CompletionItem[] = [];

    try {
      // This would integrate with the agent system to generate intelligent completions
      // For now, we'll provide some basic agent-like completions

      if (context.currentWord.length >= 2) {
        // Generate contextual suggestions
        const suggestions = this.generateContextualSuggestions(context);
        items.push(...suggestions);
      }

      return items;
    } catch (error) {
      console.warn('Error getting agent completions:', error);
      return items;
    }
  }

  private filterAndSortCompletions(items: CompletionItem[], context: CompletionContext): CompletionItem[] {
    // Remove duplicates
    const uniqueItems = items.filter((item, index, array) =>
      array.findIndex(i => i.label === item.label && i.insertText === item.insertText) === index
    );

    // Filter by confidence threshold
    const filteredItems = uniqueItems.filter(item =>
      item.confidence >= this.config.minConfidenceThreshold
    );

    // Sort by relevance (confidence, source priority, alphabetical)
    const sortedItems = filteredItems.sort((a, b) => {
      // Source priority: monaco > agent > vector > pattern
      const sourcePriority = { monaco: 4, agent: 3, vector: 2, pattern: 1 };
      const aPriority = sourcePriority[a.source] || 0;
      const bPriority = sourcePriority[b.source] || 0;

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // Then by confidence
      if (a.confidence !== b.confidence) {
        return b.confidence - a.confidence;
      }

      // Finally alphabetical
      return a.label.localeCompare(b.label);
    });

    // Limit results
    return sortedItems.slice(0, this.config.maxCompletionItems);
  }

  private convertToMonacoCompletion(item: CompletionItem): any {
    return {
      label: item.label,
      kind: item.kind,
      detail: item.detail,
      documentation: item.documentation,
      insertText: item.insertText,
      insertTextRules: item.insertTextRules,
      range: item.range,
      sortText: item.sortText || item.label,
      filterText: item.filterText || item.label,
      preselect: item.preselect,
      additionalTextEdits: item.additionalTextEdits,
      command: item.command,
      // Custom properties for tracking
      agentGenerated: item.agentGenerated,
      confidence: item.confidence,
      source: item.source
    };
  }

  // Helper methods
  private detectScope(precedingText: string): string {
    // Simple scope detection based on indentation and keywords
    const lines = precedingText.split('\n');
    const lastLine = lines[lines.length - 1] || '';

    if (lastLine.includes('function') || lastLine.includes('=>')) {
      return 'function';
    } else if (lastLine.includes('class')) {
      return 'class';
    } else if (lastLine.includes('interface')) {
      return 'interface';
    } else if (lastLine.includes('{')) {
      return 'block';
    } else {
      return 'global';
    }
  }

  private extractCompletionsFromCode(code: string, context: CompletionContext): CompletionItem[] {
    const items: CompletionItem[] = [];
    const lines = code.split('\n');

    // Extract function names, variable names, etc.
    const patterns = {
      function: /(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s+)?\()/g,
      variable: /(?:let|const|var)\s+(\w+)/g,
      method: /(\w+)\s*\(/g
    };

    for (const line of lines) {
      Object.entries(patterns).forEach(([type, pattern]) => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          const name = match[1] || match[2];
          if (name && name.toLowerCase().includes(context.currentWord.toLowerCase())) {
            items.push({
              id: `extracted-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              label: name,
              kind: type === 'function' ? CompletionItemKind.Function :
                    type === 'variable' ? CompletionItemKind.Variable :
                    CompletionItemKind.Method,
              detail: `From similar code`,
              insertText: name,
              confidence: 0.6,
              source: 'vector',
              agentGenerated: false
            });
          }
        }
      });
    }

    return items;
  }

  private getLanguagePatterns(language: string, context: CompletionContext): any[] {
    const patterns: any[] = [];

    if (language === 'javascript' || language === 'typescript') {
      patterns.push(
        {
          label: 'console.log',
          kind: CompletionItemKind.Method,
          detail: 'Log to console',
          documentation: 'Outputs a message to the web console',
          insertText: 'console.log($1)',
          trigger: /console\./,
          confidence: 0.9
        },
        {
          label: 'async function',
          kind: CompletionItemKind.Snippet,
          detail: 'Async function declaration',
          documentation: 'Create an asynchronous function',
          insertText: 'async function ${1:name}(${2:params}) {\n\t$0\n}',
          trigger: /async/,
          confidence: 0.8
        },
        {
          label: 'try-catch',
          kind: CompletionItemKind.Snippet,
          detail: 'Try-catch block',
          documentation: 'Error handling with try-catch',
          insertText: 'try {\n\t$1\n} catch (error) {\n\t$2\n}',
          trigger: /try/,
          confidence: 0.8
        }
      );
    }

    return patterns;
  }

  private generateContextualSuggestions(context: CompletionContext): CompletionItem[] {
    const items: CompletionItem[] = [];

    // Generate suggestions based on context
    if (context.precedingText.includes('import')) {
      items.push({
        id: `contextual-${Date.now()}`,
        label: 'from',
        kind: CompletionItemKind.Keyword,
        detail: 'Import from module',
        insertText: 'from "$1"',
        confidence: 0.8,
        source: 'agent',
        agentGenerated: true
      });
    }

    if (context.precedingText.includes('export')) {
      items.push({
        id: `contextual-${Date.now()}`,
        label: 'default',
        kind: CompletionItemKind.Keyword,
        detail: 'Default export',
        insertText: 'default',
        confidence: 0.8,
        source: 'agent',
        agentGenerated: true
      });
    }

    return items;
  }
}

// Singleton instance
let codeCompletionEnhancer: CodeCompletionEnhancer | null = null;

export function getCodeCompletionEnhancer(vectorDb?: BasicVectorDatabase): CodeCompletionEnhancer {
  if (!codeCompletionEnhancer) {
    if (!vectorDb) {
      throw new Error('Vector database is required to initialize CodeCompletionEnhancer');
    }
    codeCompletionEnhancer = new CodeCompletionEnhancer(vectorDb);
  }
  return codeCompletionEnhancer;
}

export function shutdownCodeCompletionEnhancer(): void {
  if (codeCompletionEnhancer) {
    codeCompletionEnhancer.shutdown();
    codeCompletionEnhancer = null;
  }
}
