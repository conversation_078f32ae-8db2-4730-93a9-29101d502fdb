// components/background/semantic-code-analysis.ts
import { KnowledgeGraph, GraphNode, GraphEdge } from './knowledge-graph';
import { ProjectDictionary, DictionaryTerm } from './project-dictionary';
import { BasicVectorDatabase, VectorDocument } from './vector-database';
import { getConfigStoreBrowser } from './config-store-browser';

export interface ASTNode {
  id: string;
  type: 'Program' | 'FunctionDeclaration' | 'ClassDeclaration' | 'VariableDeclaration' | 'ImportDeclaration' | 'ExportDeclaration' | 'MethodDefinition' | 'Property' | 'Identifier' | 'CallExpression' | 'MemberExpression' | 'ArrowFunctionExpression' | 'BlockStatement' | 'ReturnStatement' | 'IfStatement' | 'ForStatement' | 'WhileStatement' | 'TryStatement' | 'ThrowStatement';
  name?: string;
  value?: any;
  children: ASTNode[];
  parent?: ASTNode;
  startLine: number;
  endLine: number;
  startColumn: number;
  endColumn: number;
  filePath: string;
  language: string;
  metadata: {
    visibility?: 'public' | 'private' | 'protected';
    isAsync?: boolean;
    isStatic?: boolean;
    isExported?: boolean;
    isDefault?: boolean;
    parameters?: Parameter[];
    returnType?: string;
    decorators?: string[];
    annotations?: string[];
    complexity?: number;
    dependencies?: string[];
    documentation?: string;
  };
}

export interface Parameter {
  name: string;
  type?: string;
  optional?: boolean;
  defaultValue?: string;
}

export interface CodePattern {
  id: string;
  name: string;
  type: 'design_pattern' | 'anti_pattern' | 'code_smell' | 'best_practice' | 'architectural_pattern' | 'idiom';
  description: string;
  language: string;
  pattern: string; // Regex or AST pattern
  examples: string[];
  severity: 'info' | 'warning' | 'error' | 'critical';
  category: 'structure' | 'performance' | 'maintainability' | 'security' | 'readability' | 'testing';
  confidence: number; // 0-1
  suggestions: string[];
  relatedPatterns: string[];
}

export interface PatternMatch {
  pattern: CodePattern;
  location: {
    filePath: string;
    startLine: number;
    endLine: number;
    startColumn: number;
    endColumn: number;
  };
  confidence: number;
  context: string;
  suggestions: string[];
  severity: CodePattern['severity'];
}

export interface DependencyNode {
  id: string;
  name: string;
  type: 'file' | 'module' | 'package' | 'function' | 'class' | 'variable';
  filePath: string;
  language: string;
  version?: string;
  isExternal: boolean;
  dependencies: string[]; // IDs of dependencies
  dependents: string[]; // IDs of dependents
  metadata: {
    size?: number;
    complexity?: number;
    lastModified?: number;
    isDeprecated?: boolean;
    securityIssues?: string[];
    performanceImpact?: 'low' | 'medium' | 'high';
  };
}

export interface DependencyGraph {
  nodes: Map<string, DependencyNode>;
  edges: Map<string, DependencyEdge>;
  cycles: string[][]; // Arrays of node IDs forming cycles
  layers: string[][]; // Topologically sorted layers
  metrics: {
    totalNodes: number;
    totalEdges: number;
    cycleCount: number;
    maxDepth: number;
    averageDependencies: number;
    criticalPath: string[];
  };
}

export interface DependencyEdge {
  id: string;
  sourceId: string;
  targetId: string;
  type: 'import' | 'require' | 'call' | 'inheritance' | 'composition' | 'aggregation' | 'usage';
  strength: number; // 0-1, how strong the dependency is
  isCircular: boolean;
  metadata: {
    importType?: 'default' | 'named' | 'namespace' | 'dynamic';
    usageCount?: number;
    firstSeen?: number;
    lastSeen?: number;
  };
}

export interface CodeQualityMetrics {
  filePath: string;
  language: string;
  metrics: {
    linesOfCode: number;
    cyclomaticComplexity: number;
    cognitiveComplexity: number;
    maintainabilityIndex: number;
    technicalDebt: number; // minutes
    duplicateLines: number;
    testCoverage?: number;
    codeSmells: number;
    bugs: number;
    vulnerabilities: number;
    hotspots: number;
  };
  scores: {
    overall: number; // 0-100
    maintainability: number;
    reliability: number;
    security: number;
    performance: number;
    readability: number;
  };
  issues: QualityIssue[];
  suggestions: string[];
  trends: {
    complexityTrend: 'improving' | 'stable' | 'degrading';
    maintainabilityTrend: 'improving' | 'stable' | 'degrading';
    lastAnalysis: number;
    previousScores?: CodeQualityMetrics['scores'];
  };
}

export interface QualityIssue {
  id: string;
  type: 'code_smell' | 'bug' | 'vulnerability' | 'hotspot' | 'duplication' | 'complexity';
  severity: 'info' | 'minor' | 'major' | 'critical' | 'blocker';
  message: string;
  description: string;
  location: {
    startLine: number;
    endLine: number;
    startColumn: number;
    endColumn: number;
  };
  rule: string;
  category: string;
  effort: number; // minutes to fix
  suggestions: string[];
  examples?: string[];
}

export interface AnalysisOptions {
  includeAST: boolean;
  includePatterns: boolean;
  includeDependencies: boolean;
  includeQualityMetrics: boolean;
  includeSemanticAnalysis: boolean;
  maxDepth: number;
  languages: string[];
  excludePatterns: string[];
  customPatterns: CodePattern[];
}

export interface AnalysisResult {
  filePath: string;
  language: string;
  timestamp: number;
  ast?: ASTNode;
  patterns: PatternMatch[];
  dependencies: DependencyNode;
  qualityMetrics: CodeQualityMetrics;
  semanticInfo: {
    concepts: string[];
    entities: string[];
    relationships: string[];
    complexity: number;
    readability: number;
  };
  suggestions: string[];
  warnings: string[];
  errors: string[];
}

export interface SemanticAnalysisConfig {
  enableASTAnalysis: boolean;
  enablePatternRecognition: boolean;
  enableDependencyAnalysis: boolean;
  enableQualityMetrics: boolean;
  enableSemanticSearch: boolean;
  maxFileSize: number; // bytes
  maxAnalysisTime: number; // milliseconds
  cacheResults: boolean;
  cacheExpiry: number; // milliseconds
  parallelAnalysis: boolean;
  maxConcurrentAnalyses: number;
  customPatterns: CodePattern[];
  qualityThresholds: {
    maintainability: number;
    complexity: number;
    duplication: number;
    coverage: number;
  };
}

export class SemanticCodeAnalysis {
  private knowledgeGraph: KnowledgeGraph;
  private projectDictionary: ProjectDictionary;
  private vectorDatabase: BasicVectorDatabase;
  private configStore: any;

  private analysisCache: Map<string, AnalysisResult> = new Map();
  private patternCache: Map<string, CodePattern[]> = new Map();
  private dependencyGraph: DependencyGraph = {
    nodes: new Map(),
    edges: new Map(),
    cycles: [],
    layers: [],
    metrics: {
      totalNodes: 0,
      totalEdges: 0,
      cycleCount: 0,
      maxDepth: 0,
      averageDependencies: 0,
      criticalPath: []
    }
  };

  private config: SemanticAnalysisConfig = {
    enableASTAnalysis: true,
    enablePatternRecognition: true,
    enableDependencyAnalysis: true,
    enableQualityMetrics: true,
    enableSemanticSearch: true,
    maxFileSize: 1024 * 1024, // 1MB
    maxAnalysisTime: 30000, // 30 seconds
    cacheResults: true,
    cacheExpiry: 60 * 60 * 1000, // 1 hour
    parallelAnalysis: true,
    maxConcurrentAnalyses: 4,
    customPatterns: [],
    qualityThresholds: {
      maintainability: 70,
      complexity: 10,
      duplication: 5,
      coverage: 80
    }
  };

  private builtInPatterns: CodePattern[] = [
    {
      id: 'singleton-pattern',
      name: 'Singleton Pattern',
      type: 'design_pattern',
      description: 'Ensures a class has only one instance and provides global access',
      language: 'typescript',
      pattern: 'class\\s+\\w+\\s*{[^}]*private\\s+static\\s+instance[^}]*getInstance\\s*\\([^}]*}',
      examples: ['class Singleton { private static instance: Singleton; }'],
      severity: 'info',
      category: 'structure',
      confidence: 0.8,
      suggestions: ['Consider using dependency injection instead'],
      relatedPatterns: ['factory-pattern']
    },
    {
      id: 'god-class',
      name: 'God Class',
      type: 'anti_pattern',
      description: 'A class that knows too much or does too much',
      language: 'typescript',
      pattern: 'class\\s+\\w+\\s*{[^}]{500,}', // Simplified pattern
      examples: ['class GodClass { /* hundreds of lines */ }'],
      severity: 'warning',
      category: 'maintainability',
      confidence: 0.7,
      suggestions: ['Break down into smaller, focused classes', 'Apply Single Responsibility Principle'],
      relatedPatterns: ['large-class']
    }
  ];

  constructor(private projectId: string) {
    this.knowledgeGraph = new KnowledgeGraph(projectId);
    this.projectDictionary = new ProjectDictionary(projectId);
    this.vectorDatabase = new BasicVectorDatabase();
    this.configStore = getConfigStoreBrowser();
  }

  /**
   * Initialize the semantic code analysis
   */
  async initialize(): Promise<void> {
    try {
      await this.knowledgeGraph.initialize();
      await this.projectDictionary.initialize();
      await this.vectorDatabase.initialize();
      await this.loadConfiguration();
      await this.loadBuiltInPatterns();

      console.log('Semantic code analysis initialized');
    } catch (error) {
      console.error('Failed to initialize semantic code analysis:', error);
      throw error;
    }
  }

  /**
   * Analyze a single file
   */
  async analyzeFile(filePath: string, content: string, options?: Partial<AnalysisOptions>): Promise<AnalysisResult> {
    const analysisOptions: AnalysisOptions = {
      includeAST: true,
      includePatterns: true,
      includeDependencies: true,
      includeQualityMetrics: true,
      includeSemanticAnalysis: true,
      maxDepth: 5,
      languages: ['typescript', 'javascript', 'tsx', 'jsx'],
      excludePatterns: [],
      customPatterns: [],
      ...options
    };

    // Check cache first
    const cacheKey = this.generateCacheKey(filePath, content);
    if (this.config.cacheResults && this.analysisCache.has(cacheKey)) {
      const cached = this.analysisCache.get(cacheKey)!;
      if (Date.now() - cached.timestamp < this.config.cacheExpiry) {
        return cached;
      }
    }

    // Validate file size
    if (content.length > this.config.maxFileSize) {
      throw new Error(`File too large for analysis: ${filePath}`);
    }

    const language = this.detectLanguage(filePath);
    const startTime = Date.now();

    try {
      const result: AnalysisResult = {
        filePath,
        language,
        timestamp: startTime,
        patterns: [],
        dependencies: this.createEmptyDependencyNode(filePath, language),
        qualityMetrics: this.createEmptyQualityMetrics(filePath, language),
        semanticInfo: {
          concepts: [],
          entities: [],
          relationships: [],
          complexity: 0,
          readability: 0
        },
        suggestions: [],
        warnings: [],
        errors: []
      };

      // Perform AST analysis
      if (analysisOptions.includeAST && this.config.enableASTAnalysis) {
        result.ast = await this.parseAST(content, language);
      }

      // Perform pattern recognition
      if (analysisOptions.includePatterns && this.config.enablePatternRecognition) {
        result.patterns = await this.recognizePatterns(content, language, analysisOptions.customPatterns);
      }

      // Perform dependency analysis
      if (analysisOptions.includeDependencies && this.config.enableDependencyAnalysis) {
        result.dependencies = await this.analyzeDependencies(content, filePath, language);
      }

      // Perform quality metrics analysis
      if (analysisOptions.includeQualityMetrics && this.config.enableQualityMetrics) {
        result.qualityMetrics = await this.calculateQualityMetrics(content, filePath, language, result.ast);
      }

      // Perform semantic analysis
      if (analysisOptions.includeSemanticAnalysis && this.config.enableSemanticSearch) {
        result.semanticInfo = await this.performSemanticAnalysis(content, language, result.ast);
      }

      // Generate suggestions
      result.suggestions = this.generateSuggestions(result);

      // Check analysis time
      const analysisTime = Date.now() - startTime;
      if (analysisTime > this.config.maxAnalysisTime) {
        result.warnings.push(`Analysis took ${analysisTime}ms, consider optimizing`);
      }

      // Cache result
      if (this.config.cacheResults) {
        this.analysisCache.set(cacheKey, result);

        // Limit cache size
        if (this.analysisCache.size > 1000) {
          const oldestKey = this.analysisCache.keys().next().value;
          this.analysisCache.delete(oldestKey);
        }
      }

      // Update knowledge graph
      await this.updateKnowledgeGraph(result);

      console.log(`Analyzed file: ${filePath} (${analysisTime}ms)`);
      return result;

    } catch (error) {
      const analysisTime = Date.now() - startTime;
      console.error(`Analysis failed for ${filePath} after ${analysisTime}ms:`, error);

      // Return minimal result with error
      return {
        filePath,
        language,
        timestamp: startTime,
        patterns: [],
        dependencies: this.createEmptyDependencyNode(filePath, language),
        qualityMetrics: this.createEmptyQualityMetrics(filePath, language),
        semanticInfo: {
          concepts: [],
          entities: [],
          relationships: [],
          complexity: 0,
          readability: 0
        },
        suggestions: [],
        warnings: [],
        errors: [error.message]
      };
    }
  }

  /**
   * Analyze multiple files in parallel
   */
  async analyzeFiles(files: { filePath: string; content: string }[], options?: Partial<AnalysisOptions>): Promise<AnalysisResult[]> {
    const results: AnalysisResult[] = [];
    const chunks = this.chunkArray(files, this.config.maxConcurrentAnalyses);

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(file =>
        this.analyzeFile(file.filePath, file.content, options)
      );

      const chunkResults = await Promise.allSettled(chunkPromises);

      for (const result of chunkResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error('File analysis failed:', result.reason);
        }
      }
    }

    // Update dependency graph with all results
    await this.updateDependencyGraph(results);

    return results;
  }

  /**
   * Get dependency graph for the project
   */
  getDependencyGraph(): DependencyGraph {
    return {
      nodes: new Map(this.dependencyGraph.nodes),
      edges: new Map(this.dependencyGraph.edges),
      cycles: [...this.dependencyGraph.cycles],
      layers: [...this.dependencyGraph.layers],
      metrics: { ...this.dependencyGraph.metrics }
    };
  }

  /**
   * Find code patterns across multiple files
   */
  async findPatterns(files: string[], patternTypes?: CodePattern['type'][]): Promise<PatternMatch[]> {
    const allMatches: PatternMatch[] = [];

    for (const filePath of files) {
      try {
        // Get cached analysis or perform new analysis
        const cacheKey = this.generateCacheKey(filePath, ''); // Simplified for pattern search
        let result = this.analysisCache.get(cacheKey);

        if (!result) {
          // Would need to read file content here
          console.warn(`No cached analysis for ${filePath}, skipping pattern search`);
          continue;
        }

        const filteredPatterns = patternTypes
          ? result.patterns.filter(p => patternTypes.includes(p.pattern.type))
          : result.patterns;

        allMatches.push(...filteredPatterns);
      } catch (error) {
        console.error(`Error finding patterns in ${filePath}:`, error);
      }
    }

    return allMatches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Get quality metrics summary for multiple files
   */
  getQualityMetricsSummary(results: AnalysisResult[]): {
    overall: CodeQualityMetrics['scores'];
    totals: CodeQualityMetrics['metrics'];
    averages: CodeQualityMetrics['metrics'];
    trends: { improving: number; stable: number; degrading: number };
  } {
    if (results.length === 0) {
      return {
        overall: { overall: 0, maintainability: 0, reliability: 0, security: 0, performance: 0, readability: 0 },
        totals: { linesOfCode: 0, cyclomaticComplexity: 0, cognitiveComplexity: 0, maintainabilityIndex: 0, technicalDebt: 0, duplicateLines: 0, codeSmells: 0, bugs: 0, vulnerabilities: 0, hotspots: 0 },
        averages: { linesOfCode: 0, cyclomaticComplexity: 0, cognitiveComplexity: 0, maintainabilityIndex: 0, technicalDebt: 0, duplicateLines: 0, codeSmells: 0, bugs: 0, vulnerabilities: 0, hotspots: 0 },
        trends: { improving: 0, stable: 0, degrading: 0 }
      };
    }

    const totals = results.reduce((acc, result) => ({
      linesOfCode: acc.linesOfCode + result.qualityMetrics.metrics.linesOfCode,
      cyclomaticComplexity: acc.cyclomaticComplexity + result.qualityMetrics.metrics.cyclomaticComplexity,
      cognitiveComplexity: acc.cognitiveComplexity + result.qualityMetrics.metrics.cognitiveComplexity,
      maintainabilityIndex: acc.maintainabilityIndex + result.qualityMetrics.metrics.maintainabilityIndex,
      technicalDebt: acc.technicalDebt + result.qualityMetrics.metrics.technicalDebt,
      duplicateLines: acc.duplicateLines + result.qualityMetrics.metrics.duplicateLines,
      codeSmells: acc.codeSmells + result.qualityMetrics.metrics.codeSmells,
      bugs: acc.bugs + result.qualityMetrics.metrics.bugs,
      vulnerabilities: acc.vulnerabilities + result.qualityMetrics.metrics.vulnerabilities,
      hotspots: acc.hotspots + result.qualityMetrics.metrics.hotspots
    }), { linesOfCode: 0, cyclomaticComplexity: 0, cognitiveComplexity: 0, maintainabilityIndex: 0, technicalDebt: 0, duplicateLines: 0, testCoverage: 0, codeSmells: 0, bugs: 0, vulnerabilities: 0, hotspots: 0 });

    const averages = Object.fromEntries(
      Object.entries(totals).map(([key, value]) => [key, value / results.length])
    ) as CodeQualityMetrics['metrics'];

    const overallScores = results.reduce((acc, result) => ({
      overall: acc.overall + result.qualityMetrics.scores.overall,
      maintainability: acc.maintainability + result.qualityMetrics.scores.maintainability,
      reliability: acc.reliability + result.qualityMetrics.scores.reliability,
      security: acc.security + result.qualityMetrics.scores.security,
      performance: acc.performance + result.qualityMetrics.scores.performance,
      readability: acc.readability + result.qualityMetrics.scores.readability
    }), { overall: 0, maintainability: 0, reliability: 0, security: 0, performance: 0, readability: 0 });

    const overall = Object.fromEntries(
      Object.entries(overallScores).map(([key, value]) => [key, value / results.length])
    ) as CodeQualityMetrics['scores'];

    const trends = results.reduce((acc, result) => {
      const trend = result.qualityMetrics.trends.maintainabilityTrend;
      acc[trend]++;
      return acc;
    }, { improving: 0, stable: 0, degrading: 0 });

    return { overall, totals, averages, trends };
  }

  /**
   * Update configuration
   */
  async updateConfig(newConfig: Partial<SemanticAnalysisConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();
    console.log('Semantic code analysis configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): SemanticAnalysisConfig {
    return { ...this.config };
  }

  // Private implementation methods

  /**
   * Parse AST from code content
   */
  private async parseAST(content: string, language: string): Promise<ASTNode> {
    // This is a simplified AST parser
    // In a real implementation, you would use language-specific parsers like:
    // - TypeScript: typescript compiler API
    // - JavaScript: @babel/parser, acorn, or esprima
    // - Python: ast module
    // - Java: JavaParser

    const lines = content.split('\n');

    const rootNode: ASTNode = {
      id: 'root',
      type: 'Program',
      children: [],
      startLine: 1,
      endLine: lines.length,
      startColumn: 0,
      endColumn: 0,
      filePath: '',
      language,
      metadata: {}
    };

    // Simple pattern-based parsing for demonstration
    const functionPattern = /(?:function|const|let|var)\s+(\w+)\s*(?:=\s*)?(?:\([^)]*\))?\s*(?:=>)?\s*{/g;
    const classPattern = /class\s+(\w+)(?:\s+extends\s+\w+)?\s*{/g;
    const importPattern = /import\s+(?:{[^}]+}|\w+|\*\s+as\s+\w+)\s+from\s+['"][^'"]+['"]/g;

    let match;
    let nodeId = 1;

    // Parse functions
    while ((match = functionPattern.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const functionNode: ASTNode = {
        id: `func_${nodeId++}`,
        type: 'FunctionDeclaration',
        name: match[1],
        children: [],
        parent: rootNode,
        startLine: lineNumber,
        endLine: lineNumber + 10, // Simplified
        startColumn: match.index - content.lastIndexOf('\n', match.index) - 1,
        endColumn: match.index + match[0].length,
        filePath: '',
        language,
        metadata: {
          isExported: content.includes(`export ${match[0]}`),
          parameters: []
        }
      };
      rootNode.children.push(functionNode);
    }

    // Parse classes
    while ((match = classPattern.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const classNode: ASTNode = {
        id: `class_${nodeId++}`,
        type: 'ClassDeclaration',
        name: match[1],
        children: [],
        parent: rootNode,
        startLine: lineNumber,
        endLine: lineNumber + 20, // Simplified
        startColumn: match.index - content.lastIndexOf('\n', match.index) - 1,
        endColumn: match.index + match[0].length,
        filePath: '',
        language,
        metadata: {
          isExported: content.includes(`export class ${match[1]}`),
          extends: match[0].includes('extends') ? ['BaseClass'] : []
        }
      };
      rootNode.children.push(classNode);
    }

    // Parse imports
    while ((match = importPattern.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const importNode: ASTNode = {
        id: `import_${nodeId++}`,
        type: 'ImportDeclaration',
        children: [],
        parent: rootNode,
        startLine: lineNumber,
        endLine: lineNumber,
        startColumn: match.index - content.lastIndexOf('\n', match.index) - 1,
        endColumn: match.index + match[0].length,
        filePath: '',
        language,
        metadata: {}
      };
      rootNode.children.push(importNode);
    }

    return rootNode;
  }

  /**
   * Recognize code patterns in content
   */
  private async recognizePatterns(content: string, language: string, customPatterns: CodePattern[] = []): Promise<PatternMatch[]> {
    const patterns = [...this.builtInPatterns, ...customPatterns, ...this.config.customPatterns];
    const matches: PatternMatch[] = [];

    for (const pattern of patterns) {
      if (pattern.language !== language && pattern.language !== 'any') {
        continue;
      }

      try {
        const regex = new RegExp(pattern.pattern, 'gi');
        let match;

        while ((match = regex.exec(content)) !== null) {
          const lineNumber = content.substring(0, match.index).split('\n').length;
          const endLineNumber = content.substring(0, match.index + match[0].length).split('\n').length;

          const patternMatch: PatternMatch = {
            pattern,
            location: {
              filePath: '',
              startLine: lineNumber,
              endLine: endLineNumber,
              startColumn: match.index - content.lastIndexOf('\n', match.index) - 1,
              endColumn: match.index + match[0].length - content.lastIndexOf('\n', match.index) - 1
            },
            confidence: pattern.confidence,
            context: match[0],
            suggestions: pattern.suggestions,
            severity: pattern.severity
          };

          matches.push(patternMatch);
        }
      } catch (error) {
        console.error(`Error applying pattern ${pattern.id}:`, error);
      }
    }

    return matches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Analyze dependencies in code content
   */
  private async analyzeDependencies(content: string, filePath: string, language: string): Promise<DependencyNode> {
    const dependencies: string[] = [];
    const dependents: string[] = [];

    // Extract imports/requires
    const importPattern = /import\s+(?:{[^}]+}|\w+|\*\s+as\s+\w+)\s+from\s+['"]([^'"]+)['"]/g;
    const requirePattern = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;

    let match;
    while ((match = importPattern.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    while ((match = requirePattern.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    const node: DependencyNode = {
      id: this.generateDependencyId(filePath),
      name: filePath.split('/').pop() || filePath,
      type: 'file',
      filePath,
      language,
      isExternal: false,
      dependencies,
      dependents,
      metadata: {
        size: content.length,
        complexity: this.calculateSimpleComplexity(content),
        lastModified: Date.now(),
        isDeprecated: false,
        securityIssues: [],
        performanceImpact: 'low'
      }
    };

    return node;
  }

  /**
   * Calculate quality metrics for code
   */
  private async calculateQualityMetrics(content: string, filePath: string, language: string, ast?: ASTNode): Promise<CodeQualityMetrics> {
    const lines = content.split('\n');
    const linesOfCode = lines.filter(line => line.trim() && !line.trim().startsWith('//')).length;

    // Calculate complexity metrics
    const cyclomaticComplexity = this.calculateCyclomaticComplexity(content);
    const cognitiveComplexity = this.calculateCognitiveComplexity(content);
    const maintainabilityIndex = this.calculateMaintainabilityIndex(linesOfCode, cyclomaticComplexity);

    // Calculate technical debt (simplified)
    const technicalDebt = Math.max(0, (cyclomaticComplexity - 10) * 5 + (linesOfCode > 300 ? (linesOfCode - 300) * 0.1 : 0));

    // Detect code smells and issues
    const codeSmells = this.detectCodeSmells(content);
    const bugs = this.detectPotentialBugs(content);
    const vulnerabilities = this.detectSecurityVulnerabilities(content);
    const hotspots = this.detectPerformanceHotspots(content);

    // Calculate scores
    const maintainability = Math.max(0, Math.min(100, 100 - (cyclomaticComplexity * 2) - (technicalDebt * 0.1)));
    const reliability = Math.max(0, Math.min(100, 100 - (bugs * 10) - (codeSmells * 2)));
    const security = Math.max(0, Math.min(100, 100 - (vulnerabilities * 20)));
    const performance = Math.max(0, Math.min(100, 100 - (hotspots * 15)));
    const readability = this.calculateReadabilityScore(content);
    const overall = (maintainability + reliability + security + performance + readability) / 5;

    const issues: QualityIssue[] = [
      ...this.createIssuesFromSmells(codeSmells),
      ...this.createIssuesFromBugs(bugs),
      ...this.createIssuesFromVulnerabilities(vulnerabilities),
      ...this.createIssuesFromHotspots(hotspots)
    ];

    return {
      filePath,
      language,
      metrics: {
        linesOfCode,
        cyclomaticComplexity,
        cognitiveComplexity,
        maintainabilityIndex,
        technicalDebt,
        duplicateLines: 0, // Would need more sophisticated analysis
        codeSmells,
        bugs,
        vulnerabilities,
        hotspots
      },
      scores: {
        overall,
        maintainability,
        reliability,
        security,
        performance,
        readability
      },
      issues,
      suggestions: this.generateQualitySuggestions(cyclomaticComplexity, linesOfCode, codeSmells),
      trends: {
        complexityTrend: 'stable',
        maintainabilityTrend: 'stable',
        lastAnalysis: Date.now()
      }
    };
  }

  /**
   * Perform semantic analysis on code
   */
  private async performSemanticAnalysis(content: string, language: string, ast?: ASTNode): Promise<AnalysisResult['semanticInfo']> {
    const concepts: string[] = [];
    const entities: string[] = [];
    const relationships: string[] = [];

    // Extract concepts from comments and naming
    const commentPattern = /\/\*[\s\S]*?\*\/|\/\/.*$/gm;
    const comments = content.match(commentPattern) || [];

    for (const comment of comments) {
      const words = comment.replace(/[^\w\s]/g, '').split(/\s+/).filter(word => word.length > 3);
      concepts.push(...words);
    }

    // Extract entities from AST
    if (ast) {
      this.extractEntitiesFromAST(ast, entities);
    }

    // Extract relationships
    const importPattern = /import.*from/g;
    const extendsPattern = /extends\s+\w+/g;
    const implementsPattern = /implements\s+\w+/g;

    relationships.push(...(content.match(importPattern) || []).map(() => 'imports'));
    relationships.push(...(content.match(extendsPattern) || []).map(() => 'extends'));
    relationships.push(...(content.match(implementsPattern) || []).map(() => 'implements'));

    const complexity = this.calculateSemanticComplexity(content, ast);
    const readability = this.calculateReadabilityScore(content);

    return {
      concepts: [...new Set(concepts)].slice(0, 20), // Limit and deduplicate
      entities: [...new Set(entities)].slice(0, 20),
      relationships: [...new Set(relationships)],
      complexity,
      readability
    };
  }

  /**
   * Generate suggestions based on analysis results
   */
  private generateSuggestions(result: AnalysisResult): string[] {
    const suggestions: string[] = [];

    // Complexity suggestions
    if (result.qualityMetrics.metrics.cyclomaticComplexity > 10) {
      suggestions.push('Consider breaking down complex functions into smaller ones');
    }

    // Size suggestions
    if (result.qualityMetrics.metrics.linesOfCode > 300) {
      suggestions.push('File is quite large, consider splitting into multiple files');
    }

    // Pattern suggestions
    for (const pattern of result.patterns) {
      if (pattern.pattern.type === 'anti_pattern') {
        suggestions.push(`Avoid ${pattern.pattern.name}: ${pattern.pattern.description}`);
      }
    }

    // Quality suggestions
    if (result.qualityMetrics.scores.maintainability < 70) {
      suggestions.push('Focus on improving code maintainability');
    }

    if (result.qualityMetrics.scores.security < 80) {
      suggestions.push('Review code for security vulnerabilities');
    }

    return suggestions;
  }

  /**
   * Update knowledge graph with analysis results
   */
  private async updateKnowledgeGraph(result: AnalysisResult): Promise<void> {
    try {
      // Add file node
      const fileNode = await this.knowledgeGraph.addNode({
        type: 'file',
        name: result.filePath.split('/').pop() || result.filePath,
        filePath: result.filePath,
        language: result.language,
        metadata: {
          lastModified: result.timestamp,
          complexity: result.qualityMetrics.metrics.cyclomaticComplexity,
          size: result.qualityMetrics.metrics.linesOfCode
        },
        tags: ['analyzed'],
        weight: 0.5
      });

      // Add AST nodes if available
      if (result.ast) {
        await this.addASTNodesToGraph(result.ast, fileNode);
      }

    } catch (error) {
      console.error('Error updating knowledge graph:', error);
    }
  }

  /**
   * Add AST nodes to knowledge graph
   */
  private async addASTNodesToGraph(ast: ASTNode, parentNodeId: string): Promise<void> {
    for (const child of ast.children) {
      if (child.type === 'FunctionDeclaration' || child.type === 'ClassDeclaration') {
        try {
          const nodeId = await this.knowledgeGraph.addNode({
            type: child.type === 'FunctionDeclaration' ? 'function' : 'class',
            name: child.name || 'anonymous',
            filePath: child.filePath,
            language: child.language,
            metadata: {
              startLine: child.startLine,
              endLine: child.endLine,
              complexity: child.metadata.complexity,
              isExported: child.metadata.isExported
            },
            tags: ['ast-node'],
            weight: 0.3
          });

          // Add edge from file to function/class
          await this.knowledgeGraph.addEdge({
            sourceId: parentNodeId,
            targetId: nodeId,
            type: 'contains',
            weight: 0.8,
            metadata: {
              context: 'AST relationship',
              direction: 'unidirectional',
              confidence: 0.9,
              lastSeen: Date.now()
            },
            tags: ['ast-edge']
          });

        } catch (error) {
          console.error('Error adding AST node to graph:', error);
        }
      }
    }
  }

  // Utility methods

  private generateCacheKey(filePath: string, content: string): string {
    // Simple hash function for cache key
    const hash = content.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return `${filePath}-${hash}`;
  }

  private detectLanguage(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust'
    };
    return languageMap[extension || ''] || 'unknown';
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  private createEmptyDependencyNode(filePath: string, language: string): DependencyNode {
    return {
      id: this.generateDependencyId(filePath),
      name: filePath.split('/').pop() || filePath,
      type: 'file',
      filePath,
      language,
      isExternal: false,
      dependencies: [],
      dependents: [],
      metadata: {}
    };
  }

  private createEmptyQualityMetrics(filePath: string, language: string): CodeQualityMetrics {
    return {
      filePath,
      language,
      metrics: {
        linesOfCode: 0,
        cyclomaticComplexity: 0,
        cognitiveComplexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0,
        duplicateLines: 0,
        codeSmells: 0,
        bugs: 0,
        vulnerabilities: 0,
        hotspots: 0
      },
      scores: {
        overall: 0,
        maintainability: 0,
        reliability: 0,
        security: 0,
        performance: 0,
        readability: 0
      },
      issues: [],
      suggestions: [],
      trends: {
        complexityTrend: 'stable',
        maintainabilityTrend: 'stable',
        lastAnalysis: Date.now()
      }
    };
  }

  // Helper methods for complexity and quality calculations

  private calculateCyclomaticComplexity(content: string): number {
    // Count decision points: if, while, for, case, catch, &&, ||, ?:
    const patterns = [
      /\bif\s*\(/g,
      /\bwhile\s*\(/g,
      /\bfor\s*\(/g,
      /\bcase\s+/g,
      /\bcatch\s*\(/g,
      /&&/g,
      /\|\|/g,
      /\?/g
    ];

    let complexity = 1; // Base complexity
    for (const pattern of patterns) {
      const matches = content.match(pattern);
      if (matches) {
        complexity += matches.length;
      }
    }

    return complexity;
  }

  private calculateCognitiveComplexity(content: string): number {
    // Simplified cognitive complexity calculation
    let complexity = 0;
    const lines = content.split('\n');
    let nestingLevel = 0;

    for (const line of lines) {
      const trimmed = line.trim();

      // Increase nesting for blocks
      if (trimmed.includes('{')) nestingLevel++;
      if (trimmed.includes('}')) nestingLevel = Math.max(0, nestingLevel - 1);

      // Add complexity for control structures
      if (/\b(if|while|for|switch)\s*\(/.test(trimmed)) {
        complexity += nestingLevel + 1;
      }

      // Add complexity for logical operators
      const logicalOps = (trimmed.match(/&&|\|\|/g) || []).length;
      complexity += logicalOps;
    }

    return complexity;
  }

  private calculateMaintainabilityIndex(linesOfCode: number, cyclomaticComplexity: number): number {
    // Simplified maintainability index calculation
    const halsteadVolume = linesOfCode * 4.7; // Simplified
    const maintainabilityIndex = Math.max(0,
      171 - 5.2 * Math.log(halsteadVolume) - 0.23 * cyclomaticComplexity - 16.2 * Math.log(linesOfCode)
    );
    return Math.round(maintainabilityIndex);
  }

  private calculateSimpleComplexity(content: string): number {
    // Simple complexity based on various factors
    const lines = content.split('\n').length;
    const functions = (content.match(/function|=>/g) || []).length;
    const classes = (content.match(/class\s+\w+/g) || []).length;
    const imports = (content.match(/import|require/g) || []).length;

    return Math.round((lines * 0.1) + (functions * 2) + (classes * 3) + (imports * 0.5));
  }

  private detectCodeSmells(content: string): number {
    let smells = 0;

    // Long parameter lists
    const longParamLists = content.match(/\([^)]{50,}\)/g) || [];
    smells += longParamLists.length;

    // Long lines
    const lines = content.split('\n');
    const longLines = lines.filter(line => line.length > 120);
    smells += Math.floor(longLines.length / 5); // Group every 5 long lines as one smell

    // Deeply nested code
    let maxNesting = 0;
    let currentNesting = 0;
    for (const line of lines) {
      currentNesting += (line.match(/{/g) || []).length;
      currentNesting -= (line.match(/}/g) || []).length;
      maxNesting = Math.max(maxNesting, currentNesting);
    }
    if (maxNesting > 4) smells++;

    // Magic numbers
    const magicNumbers = content.match(/\b\d{2,}\b/g) || [];
    smells += Math.floor(magicNumbers.length / 3);

    return smells;
  }

  private detectPotentialBugs(content: string): number {
    let bugs = 0;

    // Potential null pointer dereferences
    const nullChecks = content.match(/\w+\.\w+/g) || [];
    const nullGuards = content.match(/if\s*\(\s*\w+\s*[!=]=\s*null/g) || [];
    if (nullChecks.length > nullGuards.length * 2) bugs++;

    // Unhandled promises
    const promises = content.match(/\.then\(|\.catch\(/g) || [];
    const awaitUsage = content.match(/await\s+/g) || [];
    if (promises.length > awaitUsage.length && promises.length > 2) bugs++;

    // Potential infinite loops
    const whileLoops = content.match(/while\s*\(\s*true\s*\)/g) || [];
    bugs += whileLoops.length;

    return bugs;
  }

  private detectSecurityVulnerabilities(content: string): number {
    let vulnerabilities = 0;

    // SQL injection potential
    if (content.includes('SELECT') && content.includes('+')) vulnerabilities++;

    // XSS potential
    if (content.includes('innerHTML') || content.includes('dangerouslySetInnerHTML')) vulnerabilities++;

    // Hardcoded secrets
    const secretPatterns = [
      /password\s*=\s*['"][^'"]+['"]/gi,
      /api[_-]?key\s*=\s*['"][^'"]+['"]/gi,
      /secret\s*=\s*['"][^'"]+['"]/gi
    ];

    for (const pattern of secretPatterns) {
      const matches = content.match(pattern) || [];
      vulnerabilities += matches.length;
    }

    return vulnerabilities;
  }

  private detectPerformanceHotspots(content: string): number {
    let hotspots = 0;

    // Nested loops
    const forLoops = content.match(/for\s*\(/g) || [];
    const whileLoops = content.match(/while\s*\(/g) || [];
    const totalLoops = forLoops.length + whileLoops.length;

    // Estimate nested loops (simplified)
    if (totalLoops > 2) {
      const lines = content.split('\n');
      let loopNesting = 0;
      for (const line of lines) {
        if (/for\s*\(|while\s*\(/.test(line)) loopNesting++;
        if (line.includes('}')) loopNesting = Math.max(0, loopNesting - 1);
        if (loopNesting > 1) {
          hotspots++;
          break;
        }
      }
    }

    // Large object creation in loops
    if (content.includes('new ') && totalLoops > 0) hotspots++;

    // Synchronous file operations
    const syncOps = content.match(/readFileSync|writeFileSync/g) || [];
    hotspots += syncOps.length;

    return hotspots;
  }

  private calculateReadabilityScore(content: string): number {
    const lines = content.split('\n');
    const totalLines = lines.length;
    const nonEmptyLines = lines.filter(line => line.trim()).length;
    const commentLines = lines.filter(line => line.trim().startsWith('//') || line.trim().startsWith('/*')).length;

    // Calculate various readability factors
    const commentRatio = commentLines / nonEmptyLines;
    const avgLineLength = lines.reduce((sum, line) => sum + line.length, 0) / totalLines;
    const emptyLineRatio = (totalLines - nonEmptyLines) / totalLines;

    // Score based on good practices
    let score = 50; // Base score

    // Good comment ratio (10-30%)
    if (commentRatio >= 0.1 && commentRatio <= 0.3) score += 20;
    else if (commentRatio > 0.05) score += 10;

    // Reasonable line length (< 100 chars)
    if (avgLineLength < 80) score += 15;
    else if (avgLineLength < 120) score += 10;
    else score -= 10;

    // Good use of whitespace (5-15% empty lines)
    if (emptyLineRatio >= 0.05 && emptyLineRatio <= 0.15) score += 15;
    else if (emptyLineRatio > 0.02) score += 5;

    return Math.max(0, Math.min(100, score));
  }

  private calculateSemanticComplexity(content: string, ast?: ASTNode): number {
    let complexity = 0;

    // Base complexity from AST depth
    if (ast) {
      complexity += this.calculateASTDepth(ast) * 2;
    }

    // Complexity from imports
    const imports = content.match(/import|require/g) || [];
    complexity += imports.length * 0.5;

    // Complexity from function calls
    const functionCalls = content.match(/\w+\s*\(/g) || [];
    complexity += functionCalls.length * 0.2;

    // Complexity from object property access
    const propertyAccess = content.match(/\w+\.\w+/g) || [];
    complexity += propertyAccess.length * 0.1;

    return Math.round(complexity);
  }

  private calculateASTDepth(node: ASTNode): number {
    if (node.children.length === 0) return 1;

    const childDepths = node.children.map(child => this.calculateASTDepth(child));
    return 1 + Math.max(...childDepths);
  }

  private extractEntitiesFromAST(ast: ASTNode, entities: string[]): void {
    if (ast.name) {
      entities.push(ast.name);
    }

    for (const child of ast.children) {
      this.extractEntitiesFromAST(child, entities);
    }
  }

  private generateQualitySuggestions(complexity: number, linesOfCode: number, codeSmells: number): string[] {
    const suggestions: string[] = [];

    if (complexity > 15) {
      suggestions.push('High complexity detected - consider refactoring into smaller functions');
    }

    if (linesOfCode > 500) {
      suggestions.push('Large file detected - consider splitting into multiple modules');
    }

    if (codeSmells > 5) {
      suggestions.push('Multiple code smells detected - review for maintainability improvements');
    }

    return suggestions;
  }

  private createIssuesFromSmells(count: number): QualityIssue[] {
    if (count === 0) return [];

    return [{
      id: 'code-smells',
      type: 'code_smell',
      severity: count > 5 ? 'major' : 'minor',
      message: `${count} code smell${count > 1 ? 's' : ''} detected`,
      description: 'Code smells indicate areas that may need refactoring',
      location: { startLine: 1, endLine: 1, startColumn: 1, endColumn: 1 },
      rule: 'code-quality',
      category: 'maintainability',
      effort: count * 15,
      suggestions: ['Review code for refactoring opportunities', 'Apply clean code principles']
    }];
  }

  private createIssuesFromBugs(count: number): QualityIssue[] {
    if (count === 0) return [];

    return [{
      id: 'potential-bugs',
      type: 'bug',
      severity: count > 2 ? 'critical' : 'major',
      message: `${count} potential bug${count > 1 ? 's' : ''} detected`,
      description: 'Potential bugs that may cause runtime errors',
      location: { startLine: 1, endLine: 1, startColumn: 1, endColumn: 1 },
      rule: 'bug-detection',
      category: 'reliability',
      effort: count * 30,
      suggestions: ['Add null checks', 'Handle promises properly', 'Review loop conditions']
    }];
  }

  private createIssuesFromVulnerabilities(count: number): QualityIssue[] {
    if (count === 0) return [];

    return [{
      id: 'security-vulnerabilities',
      type: 'vulnerability',
      severity: 'critical',
      message: `${count} security vulnerabilit${count > 1 ? 'ies' : 'y'} detected`,
      description: 'Security vulnerabilities that may expose the application to attacks',
      location: { startLine: 1, endLine: 1, startColumn: 1, endColumn: 1 },
      rule: 'security-check',
      category: 'security',
      effort: count * 60,
      suggestions: ['Remove hardcoded secrets', 'Sanitize user inputs', 'Use parameterized queries']
    }];
  }

  private createIssuesFromHotspots(count: number): QualityIssue[] {
    if (count === 0) return [];

    return [{
      id: 'performance-hotspots',
      type: 'hotspot',
      severity: count > 2 ? 'major' : 'minor',
      message: `${count} performance hotspot${count > 1 ? 's' : ''} detected`,
      description: 'Performance hotspots that may impact application speed',
      location: { startLine: 1, endLine: 1, startColumn: 1, endColumn: 1 },
      rule: 'performance-check',
      category: 'performance',
      effort: count * 45,
      suggestions: ['Optimize nested loops', 'Use asynchronous operations', 'Cache expensive computations']
    }];
  }

  private generateDependencyId(filePath: string): string {
    return `dep-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`;
  }

  private async updateDependencyGraph(results: AnalysisResult[]): Promise<void> {
    // Update the global dependency graph with all analysis results
    for (const result of results) {
      this.dependencyGraph.nodes.set(result.dependencies.id, result.dependencies);
    }

    // Calculate graph metrics
    this.dependencyGraph.metrics = {
      totalNodes: this.dependencyGraph.nodes.size,
      totalEdges: this.dependencyGraph.edges.size,
      cycleCount: this.dependencyGraph.cycles.length,
      maxDepth: this.calculateMaxDepth(),
      averageDependencies: this.calculateAverageDependencies(),
      criticalPath: this.findCriticalPath()
    };
  }

  private calculateMaxDepth(): number {
    // Simplified depth calculation
    return Math.max(...Array.from(this.dependencyGraph.nodes.values()).map(node => node.dependencies.length));
  }

  private calculateAverageDependencies(): number {
    const nodes = Array.from(this.dependencyGraph.nodes.values());
    if (nodes.length === 0) return 0;

    const totalDeps = nodes.reduce((sum, node) => sum + node.dependencies.length, 0);
    return totalDeps / nodes.length;
  }

  private findCriticalPath(): string[] {
    // Simplified critical path - nodes with most dependencies
    const nodes = Array.from(this.dependencyGraph.nodes.values());
    return nodes
      .sort((a, b) => b.dependencies.length - a.dependencies.length)
      .slice(0, 5)
      .map(node => node.id);
  }

  private async loadBuiltInPatterns(): Promise<void> {
    // Built-in patterns are already defined in the constructor
    console.log(`Loaded ${this.builtInPatterns.length} built-in patterns`);
  }

  private async loadConfiguration(): Promise<void> {
    try {
      const stored = await this.configStore.getGlobalSetting('semanticCodeAnalysis.config');
      if (stored) {
        this.config = { ...this.config, ...stored };
      }
    } catch (error) {
      console.error('Failed to load semantic code analysis configuration:', error);
    }
  }

  private async saveConfiguration(): Promise<void> {
    try {
      await this.configStore.setGlobalSetting('semanticCodeAnalysis.config', this.config);
    } catch (error) {
      console.error('Failed to save semantic code analysis configuration:', error);
    }
  }

  /**
   * Shutdown the semantic code analysis
   */
  async shutdown(): Promise<void> {
    try {
      // Clear caches
      this.analysisCache.clear();
      this.patternCache.clear();

      // Clear dependency graph
      this.dependencyGraph.nodes.clear();
      this.dependencyGraph.edges.clear();
      this.dependencyGraph.cycles = [];
      this.dependencyGraph.layers = [];

      console.log('Semantic code analysis shut down');
    } catch (error) {
      console.error('Error during semantic code analysis shutdown:', error);
    }
  }
}

// Global instances per project
const globalSemanticAnalysis: Map<string, SemanticCodeAnalysis> = new Map();

/**
 * Get the semantic code analysis instance for a project
 */
export function getSemanticCodeAnalysis(projectId: string): SemanticCodeAnalysis {
  if (!globalSemanticAnalysis.has(projectId)) {
    globalSemanticAnalysis.set(projectId, new SemanticCodeAnalysis(projectId));
  }
  return globalSemanticAnalysis.get(projectId)!;
}