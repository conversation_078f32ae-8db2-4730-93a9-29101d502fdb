// components/background/context-cache-manager.ts
import { PrefetchedContext, ContextItem } from './context-prefetcher';

export interface CacheEntry {
  id: string;
  context: PrefetchedContext;
  accessCount: number;
  lastAccessed: number;
  createdAt: number;
  size: number; // in bytes
  priority: number; // 0-1, higher is more important
  tags: string[];
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  averageAccessTime: number;
  memoryUsage: number;
  oldestEntry: number;
  newestEntry: number;
}

export interface CacheConfig {
  maxSize: number; // in bytes
  maxEntries: number;
  defaultTTL: number; // time to live in milliseconds
  evictionPolicy: 'LRU' | 'LFU' | 'FIFO' | 'PRIORITY';
  compressionEnabled: boolean;
  persistToDisk: boolean;
  cleanupInterval: number; // milliseconds
}

export class ContextCacheManager {
  private cache: Map<string, CacheEntry> = new Map();
  private accessOrder: string[] = []; // For LRU tracking
  private stats: CacheStats = {
    totalEntries: 0,
    totalSize: 0,
    hitRate: 0,
    missRate: 0,
    evictionCount: 0,
    averageAccessTime: 0,
    memoryUsage: 0,
    oldestEntry: 0,
    newestEntry: 0
  };

  private config: CacheConfig = {
    maxSize: 100 * 1024 * 1024, // 100MB
    maxEntries: 1000,
    defaultTTL: 30 * 60 * 1000, // 30 minutes
    evictionPolicy: 'LRU',
    compressionEnabled: true,
    persistToDisk: false,
    cleanupInterval: 5 * 60 * 1000 // 5 minutes
  };

  private hitCount = 0;
  private missCount = 0;
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.startCleanupProcess();
  }

  /**
   * Initialize the cache manager
   */
  async initialize(): Promise<void> {
    try {
      await this.loadConfiguration();
      if (this.config.persistToDisk) {
        await this.loadFromDisk();
      }
      console.log('Context cache manager initialized');
    } catch (error) {
      console.error('Failed to initialize context cache manager:', error);
      throw error;
    }
  }

  /**
   * Store context in cache
   */
  async set(key: string, context: PrefetchedContext, options?: { ttl?: number; priority?: number; tags?: string[] }): Promise<void> {
    try {
      const size = this.calculateSize(context);
      const now = Date.now();
      const ttl = options?.ttl || this.config.defaultTTL;

      // Check if we need to make space
      await this.ensureSpace(size);

      const entry: CacheEntry = {
        id: key,
        context: this.config.compressionEnabled ? this.compressContext(context) : context,
        accessCount: 0,
        lastAccessed: now,
        createdAt: now,
        size,
        priority: options?.priority || 0.5,
        tags: options?.tags || []
      };

      // Remove existing entry if it exists
      if (this.cache.has(key)) {
        await this.remove(key);
      }

      this.cache.set(key, entry);
      this.updateAccessOrder(key);
      this.updateStats();

      console.log(`Cached context: ${key} (${this.formatSize(size)})`);
    } catch (error) {
      console.error(`Failed to cache context ${key}:`, error);
      throw error;
    }
  }

  /**
   * Retrieve context from cache
   */
  async get(key: string): Promise<PrefetchedContext | null> {
    const startTime = Date.now();

    try {
      const entry = this.cache.get(key);

      if (!entry) {
        this.missCount++;
        this.updateHitRate();
        return null;
      }

      // Check if entry has expired
      const now = Date.now();
      const age = now - entry.createdAt;
      if (age > this.config.defaultTTL) {
        await this.remove(key);
        this.missCount++;
        this.updateHitRate();
        return null;
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = now;
      this.updateAccessOrder(key);

      this.hitCount++;
      this.updateHitRate();

      const accessTime = Date.now() - startTime;
      this.updateAverageAccessTime(accessTime);

      // Decompress if needed
      const context = this.config.compressionEnabled ?
        this.decompressContext(entry.context) : entry.context;

      console.log(`Cache hit: ${key} (accessed ${entry.accessCount} times)`);
      return context;
    } catch (error) {
      console.error(`Failed to retrieve context ${key}:`, error);
      this.missCount++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * Remove context from cache
   */
  async remove(key: string): Promise<boolean> {
    try {
      const entry = this.cache.get(key);
      if (!entry) {
        return false;
      }

      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      this.updateStats();

      console.log(`Removed from cache: ${key}`);
      return true;
    } catch (error) {
      console.error(`Failed to remove context ${key}:`, error);
      return false;
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    try {
      this.cache.clear();
      this.accessOrder = [];
      this.hitCount = 0;
      this.missCount = 0;
      this.updateStats();

      console.log('Cache cleared');
    } catch (error) {
      console.error('Failed to clear cache:', error);
      throw error;
    }
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    // Check expiration
    const age = Date.now() - entry.createdAt;
    if (age > this.config.defaultTTL) {
      this.remove(key);
      return false;
    }

    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Get cache configuration
   */
  getConfig(): CacheConfig {
    return { ...this.config };
  }

  /**
   * Update cache configuration
   */
  async updateConfig(newConfig: Partial<CacheConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();

    // Restart cleanup process if interval changed
    if (newConfig.cleanupInterval) {
      this.stopCleanupProcess();
      this.startCleanupProcess();
    }

    console.log('Cache configuration updated');
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache entries by tags
   */
  getByTags(tags: string[]): CacheEntry[] {
    const entries: CacheEntry[] = [];

    for (const entry of this.cache.values()) {
      if (tags.some(tag => entry.tags.includes(tag))) {
        entries.push(entry);
      }
    }

    return entries;
  }

  /**
   * Remove expired entries
   */
  async cleanup(): Promise<number> {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache) {
      const age = now - entry.createdAt;
      if (age > this.config.defaultTTL) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      await this.remove(key);
    }

    if (expiredKeys.length > 0) {
      console.log(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }

    return expiredKeys.length;
  }

  /**
   * Force garbage collection
   */
  async forceEviction(targetSize?: number): Promise<number> {
    const target = targetSize || Math.floor(this.config.maxSize * 0.8);
    let evicted = 0;

    while (this.stats.totalSize > target && this.cache.size > 0) {
      const keyToEvict = this.selectEvictionCandidate();
      if (keyToEvict) {
        await this.remove(keyToEvict);
        evicted++;
        this.stats.evictionCount++;
      } else {
        break;
      }
    }

    if (evicted > 0) {
      console.log(`Force evicted ${evicted} cache entries`);
    }

    return evicted;
  }

  // Private implementation methods

  /**
   * Calculate size of context in bytes
   */
  private calculateSize(context: PrefetchedContext): number {
    try {
      const serialized = JSON.stringify(context);
      return new Blob([serialized]).size;
    } catch (error) {
      // Fallback estimation
      return context.contexts.reduce((sum, ctx) => sum + ctx.tokens * 4, 0);
    }
  }

  /**
   * Ensure there's enough space for new entry
   */
  private async ensureSpace(requiredSize: number): Promise<void> {
    // Check size limit
    while (this.stats.totalSize + requiredSize > this.config.maxSize && this.cache.size > 0) {
      const keyToEvict = this.selectEvictionCandidate();
      if (keyToEvict) {
        await this.remove(keyToEvict);
        this.stats.evictionCount++;
      } else {
        break;
      }
    }

    // Check entry count limit
    while (this.cache.size >= this.config.maxEntries && this.cache.size > 0) {
      const keyToEvict = this.selectEvictionCandidate();
      if (keyToEvict) {
        await this.remove(keyToEvict);
        this.stats.evictionCount++;
      } else {
        break;
      }
    }
  }

  /**
   * Select candidate for eviction based on policy
   */
  private selectEvictionCandidate(): string | null {
    if (this.cache.size === 0) return null;

    switch (this.config.evictionPolicy) {
      case 'LRU':
        return this.accessOrder[0] || null;

      case 'LFU':
        let minAccess = Infinity;
        let lfu: string | null = null;
        for (const [key, entry] of this.cache) {
          if (entry.accessCount < minAccess) {
            minAccess = entry.accessCount;
            lfu = key;
          }
        }
        return lfu;

      case 'FIFO':
        let oldest = Infinity;
        let fifo: string | null = null;
        for (const [key, entry] of this.cache) {
          if (entry.createdAt < oldest) {
            oldest = entry.createdAt;
            fifo = key;
          }
        }
        return fifo;

      case 'PRIORITY':
        let minPriority = Infinity;
        let lowPriority: string | null = null;
        for (const [key, entry] of this.cache) {
          if (entry.priority < minPriority) {
            minPriority = entry.priority;
            lowPriority = key;
          }
        }
        return lowPriority;

      default:
        return this.accessOrder[0] || null;
    }
  }

  /**
   * Update access order for LRU tracking
   */
  private updateAccessOrder(key: string): void {
    // Remove from current position
    this.removeFromAccessOrder(key);
    // Add to end (most recently used)
    this.accessOrder.push(key);
  }

  /**
   * Remove key from access order
   */
  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  /**
   * Update cache statistics
   */
  private updateStats(): void {
    this.stats.totalEntries = this.cache.size;
    this.stats.totalSize = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.size, 0);

    if (this.cache.size > 0) {
      const entries = Array.from(this.cache.values());
      this.stats.oldestEntry = Math.min(...entries.map(e => e.createdAt));
      this.stats.newestEntry = Math.max(...entries.map(e => e.createdAt));
    } else {
      this.stats.oldestEntry = 0;
      this.stats.newestEntry = 0;
    }

    this.stats.memoryUsage = this.stats.totalSize / this.config.maxSize;
  }

  /**
   * Update hit rate statistics
   */
  private updateHitRate(): void {
    const total = this.hitCount + this.missCount;
    if (total > 0) {
      this.stats.hitRate = this.hitCount / total;
      this.stats.missRate = this.missCount / total;
    }
  }

  /**
   * Update average access time
   */
  private updateAverageAccessTime(accessTime: number): void {
    // Exponential moving average
    const alpha = 0.1;
    this.stats.averageAccessTime = this.stats.averageAccessTime * (1 - alpha) + accessTime * alpha;
  }

  /**
   * Compress context for storage
   */
  private compressContext(context: PrefetchedContext): PrefetchedContext {
    // Simple compression - remove unnecessary whitespace from content
    const compressedContexts = context.contexts.map(ctx => ({
      ...ctx,
      content: ctx.content.replace(/\s+/g, ' ').trim()
    }));

    return {
      ...context,
      contexts: compressedContexts
    };
  }

  /**
   * Decompress context after retrieval
   */
  private decompressContext(context: PrefetchedContext): PrefetchedContext {
    // For simple compression, no decompression needed
    return context;
  }

  /**
   * Format size for display
   */
  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * Start cleanup process
   */
  private startCleanupProcess(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup().catch(error => {
        console.error('Error during cache cleanup:', error);
      });
    }, this.config.cleanupInterval);
  }

  /**
   * Stop cleanup process
   */
  private stopCleanupProcess(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      // Implementation would load from config store
      console.log('Loading cache configuration...');
    } catch (error) {
      console.error('Failed to load cache configuration:', error);
    }
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    try {
      // Implementation would save to config store
      console.log('Saving cache configuration...');
    } catch (error) {
      console.error('Failed to save cache configuration:', error);
    }
  }

  /**
   * Load cache from disk
   */
  private async loadFromDisk(): Promise<void> {
    try {
      // Implementation would load persisted cache from disk
      console.log('Loading cache from disk...');
    } catch (error) {
      console.error('Failed to load cache from disk:', error);
    }
  }

  /**
   * Save cache to disk
   */
  private async saveToDisk(): Promise<void> {
    try {
      if (!this.config.persistToDisk) return;

      // Implementation would persist cache to disk
      console.log('Saving cache to disk...');
    } catch (error) {
      console.error('Failed to save cache to disk:', error);
    }
  }

  /**
   * Cleanup on shutdown
   */
  async shutdown(): Promise<void> {
    try {
      this.stopCleanupProcess();

      if (this.config.persistToDisk) {
        await this.saveToDisk();
      }

      await this.clear();
      console.log('Context cache manager shut down');
    } catch (error) {
      console.error('Error during cache shutdown:', error);
    }
  }
}

// Global instance
let globalCacheManager: ContextCacheManager | null = null;

/**
 * Get the global context cache manager instance
 */
export function getContextCacheManager(): ContextCacheManager {
  if (!globalCacheManager) {
    globalCacheManager = new ContextCacheManager();
  }
  return globalCacheManager;
}
