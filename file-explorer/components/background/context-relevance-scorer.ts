// components/background/context-relevance-scorer.ts
import { ContextItem, PrefetchRequest } from './context-prefetcher';
import { SemanticSearchService } from './semantic-search';
import { ProjectDictionary } from './project-dictionary';

export interface RelevanceFactors {
  semanticSimilarity: number;
  keywordMatch: number;
  contextType: number;
  recency: number;
  agentPreference: number;
  taskTypeAlignment: number;
  dependencyRelevance: number;
  terminologyMatch: number;
}

export interface ScoringWeights {
  semanticSimilarity: number;
  keywordMatch: number;
  contextType: number;
  recency: number;
  agentPreference: number;
  taskTypeAlignment: number;
  dependencyRelevance: number;
  terminologyMatch: number;
}

export interface ScoringConfig {
  weights: ScoringWeights;
  enableAdaptiveWeights: boolean;
  learningRate: number;
  minRelevanceThreshold: number;
  maxAge: number; // milliseconds
  contextTypePreferences: Record<string, number>;
}

export interface ScoringResult {
  contextId: string;
  relevanceScore: number;
  confidence: number;
  factors: RelevanceFactors;
  explanation: string[];
  recommendations: string[];
}

export class ContextRelevanceScorer {
  private semanticSearch: SemanticSearchService;
  private projectDictionary: ProjectDictionary;

  private config: ScoringConfig = {
    weights: {
      semanticSimilarity: 0.25,
      keywordMatch: 0.20,
      contextType: 0.15,
      recency: 0.10,
      agentPreference: 0.10,
      taskTypeAlignment: 0.10,
      dependencyRelevance: 0.05,
      terminologyMatch: 0.05
    },
    enableAdaptiveWeights: true,
    learningRate: 0.1,
    minRelevanceThreshold: 0.3,
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    contextTypePreferences: {
      'code': 1.0,
      'documentation': 0.8,
      'pattern': 0.9,
      'dependency': 0.7,
      'terminology': 0.6,
      'example': 0.8
    }
  };

  private performanceHistory: Map<string, number[]> = new Map(); // agentId -> effectiveness scores
  private taskTypePatterns: Map<string, string[]> = new Map(); // taskType -> important keywords

  constructor() {
    this.semanticSearch = new SemanticSearchService();
    this.projectDictionary = new ProjectDictionary();
    this.initializeTaskTypePatterns();
  }

  /**
   * Initialize the relevance scorer
   */
  async initialize(): Promise<void> {
    try {
      await this.semanticSearch.initialize();
      await this.loadConfiguration();
      await this.loadPerformanceHistory();

      console.log('Context relevance scorer initialized');
    } catch (error) {
      console.error('Failed to initialize context relevance scorer:', error);
      throw error;
    }
  }

  /**
   * Score the relevance of contexts for a request
   */
  async scoreContexts(contexts: ContextItem[], request: PrefetchRequest): Promise<ScoringResult[]> {
    const results: ScoringResult[] = [];

    for (const context of contexts) {
      try {
        const result = await this.scoreContext(context, request);
        results.push(result);
      } catch (error) {
        console.error(`Failed to score context ${context.id}:`, error);
        // Add a default low score for failed contexts
        results.push({
          contextId: context.id,
          relevanceScore: 0.1,
          confidence: 0.1,
          factors: this.getDefaultFactors(),
          explanation: ['Failed to score context'],
          recommendations: ['Review context quality']
        });
      }
    }

    // Sort by relevance score
    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Score a single context item
   */
  async scoreContext(context: ContextItem, request: PrefetchRequest): Promise<ScoringResult> {
    const factors = await this.calculateRelevanceFactors(context, request);
    const relevanceScore = this.calculateWeightedScore(factors);
    const confidence = this.calculateConfidence(factors, context, request);
    const explanation = this.generateExplanation(factors, context, request);
    const recommendations = this.generateRecommendations(factors, context, request);

    return {
      contextId: context.id,
      relevanceScore,
      confidence,
      factors,
      explanation,
      recommendations
    };
  }

  /**
   * Learn from scoring effectiveness
   */
  async learnFromFeedback(agentId: string, contextId: string, effectiveness: number): Promise<void> {
    if (!this.config.enableAdaptiveWeights) {
      return;
    }

    try {
      // Store performance history
      const history = this.performanceHistory.get(agentId) || [];
      history.push(effectiveness);

      // Keep only recent history
      if (history.length > 100) {
        history.splice(0, history.length - 100);
      }

      this.performanceHistory.set(agentId, history);

      // Adapt weights based on performance
      await this.adaptWeights(agentId, effectiveness);

      await this.savePerformanceHistory();
      console.log(`Learned from feedback for agent ${agentId}: effectiveness ${effectiveness}`);
    } catch (error) {
      console.error(`Failed to learn from feedback for agent ${agentId}:`, error);
    }
  }

  /**
   * Update scoring configuration
   */
  async updateConfig(newConfig: Partial<ScoringConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();
    console.log('Context relevance scorer configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): ScoringConfig {
    return { ...this.config };
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): Record<string, any> {
    const stats: Record<string, any> = {};

    for (const [agentId, history] of this.performanceHistory) {
      if (history.length > 0) {
        const avg = history.reduce((sum, val) => sum + val, 0) / history.length;
        const recent = history.slice(-10);
        const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;

        stats[agentId] = {
          averageEffectiveness: avg,
          recentEffectiveness: recentAvg,
          totalFeedback: history.length,
          trend: recentAvg - avg
        };
      }
    }

    return stats;
  }

  // Private implementation methods

  /**
   * Calculate all relevance factors for a context
   */
  private async calculateRelevanceFactors(context: ContextItem, request: PrefetchRequest): Promise<RelevanceFactors> {
    const factors: RelevanceFactors = {
      semanticSimilarity: await this.calculateSemanticSimilarity(context, request),
      keywordMatch: this.calculateKeywordMatch(context, request),
      contextType: this.calculateContextTypeScore(context, request),
      recency: this.calculateRecencyScore(context),
      agentPreference: this.calculateAgentPreferenceScore(context, request),
      taskTypeAlignment: this.calculateTaskTypeAlignment(context, request),
      dependencyRelevance: this.calculateDependencyRelevance(context, request),
      terminologyMatch: await this.calculateTerminologyMatch(context, request)
    };

    return factors;
  }

  /**
   * Calculate semantic similarity score
   */
  private async calculateSemanticSimilarity(context: ContextItem, request: PrefetchRequest): Promise<number> {
    try {
      // Use the existing similarity score if available
      if (context.relevanceScore !== undefined) {
        return Math.min(context.relevanceScore, 1.0);
      }

      // Calculate similarity between context content and task description
      const searchResults = await this.semanticSearch.searchCode({
        query: request.taskDescription,
        maxResults: 1,
        minSimilarity: 0.0
      });

      // Find matching result
      const match = searchResults.find(result => result.id === context.id);
      return match ? match.similarity : 0.0;
    } catch (error) {
      console.error('Error calculating semantic similarity:', error);
      return 0.0;
    }
  }

  /**
   * Calculate keyword match score
   */
  private calculateKeywordMatch(context: ContextItem, request: PrefetchRequest): number {
    const contextText = context.content.toLowerCase();
    const taskText = request.taskDescription.toLowerCase();
    const requiredContext = request.requiredContext.map(c => c.toLowerCase());
    const optionalContext = request.optionalContext.map(c => c.toLowerCase());

    let matches = 0;
    let total = 0;

    // Check task description keywords
    const taskWords = this.extractKeywords(taskText);
    for (const word of taskWords) {
      total++;
      if (contextText.includes(word)) {
        matches++;
      }
    }

    // Check required context (weighted higher)
    for (const req of requiredContext) {
      total += 2; // Weight required context more
      if (contextText.includes(req)) {
        matches += 2;
      }
    }

    // Check optional context
    for (const opt of optionalContext) {
      total++;
      if (contextText.includes(opt)) {
        matches++;
      }
    }

    return total > 0 ? matches / total : 0;
  }

  /**
   * Calculate context type score based on preferences
   */
  private calculateContextTypeScore(context: ContextItem, request: PrefetchRequest): number {
    const baseScore = this.config.contextTypePreferences[context.type] || 0.5;

    // Boost score for certain task types
    if (request.taskType === 'code-generation' && context.type === 'code') {
      return Math.min(baseScore * 1.2, 1.0);
    }

    if (request.taskType === 'documentation' && context.type === 'documentation') {
      return Math.min(baseScore * 1.2, 1.0);
    }

    return baseScore;
  }

  /**
   * Calculate recency score
   */
  private calculateRecencyScore(context: ContextItem): number {
    const now = Date.now();
    const lastModified = context.metadata.lastModified || 0;
    const age = now - lastModified;

    if (age <= 0) return 1.0;

    // Exponential decay based on age
    const normalizedAge = age / this.config.maxAge;
    return Math.max(0, Math.exp(-normalizedAge * 2));
  }

  /**
   * Calculate agent preference score
   */
  private calculateAgentPreferenceScore(context: ContextItem, request: PrefetchRequest): number {
    // Get agent performance history
    const history = this.performanceHistory.get(request.agentId) || [];
    if (history.length === 0) return 0.5; // Neutral score for new agents

    // Calculate average effectiveness for this agent
    const avgEffectiveness = history.reduce((sum, val) => sum + val, 0) / history.length;

    // Boost score for context types that this agent has been effective with
    const recentHistory = history.slice(-10);
    const recentAvg = recentHistory.reduce((sum, val) => sum + val, 0) / recentHistory.length;

    // Use recent performance as preference indicator
    return Math.min(recentAvg, 1.0);
  }

  /**
   * Calculate task type alignment score
   */
  private calculateTaskTypeAlignment(context: ContextItem, request: PrefetchRequest): number {
    const taskPatterns = this.taskTypePatterns.get(request.taskType) || [];
    if (taskPatterns.length === 0) return 0.5;

    const contextText = context.content.toLowerCase();
    let matches = 0;

    for (const pattern of taskPatterns) {
      if (contextText.includes(pattern.toLowerCase())) {
        matches++;
      }
    }

    return matches / taskPatterns.length;
  }

  /**
   * Calculate dependency relevance score
   */
  private calculateDependencyRelevance(context: ContextItem, request: PrefetchRequest): number {
    const contextDeps = context.metadata.dependencies || [];
    const agentCapabilities = request.agentCapabilities;

    if (contextDeps.length === 0 || agentCapabilities.length === 0) {
      return 0.5;
    }

    let matches = 0;
    for (const dep of contextDeps) {
      if (agentCapabilities.some(cap => cap.toLowerCase().includes(dep.toLowerCase()))) {
        matches++;
      }
    }

    return matches / Math.max(contextDeps.length, agentCapabilities.length);
  }

  /**
   * Calculate terminology match score
   */
  private async calculateTerminologyMatch(context: ContextItem, request: PrefetchRequest): Promise<number> {
    try {
      const contextText = context.content.toLowerCase();
      const taskText = request.taskDescription.toLowerCase();

      // Extract terms from both texts
      const contextTerms = await this.extractTermsFromText(contextText);
      const taskTerms = await this.extractTermsFromText(taskText);

      if (contextTerms.length === 0 || taskTerms.length === 0) {
        return 0.5;
      }

      // Calculate overlap
      const commonTerms = contextTerms.filter(term =>
        taskTerms.some(taskTerm => taskTerm.toLowerCase() === term.toLowerCase())
      );

      return commonTerms.length / Math.max(contextTerms.length, taskTerms.length);
    } catch (error) {
      console.error('Error calculating terminology match:', error);
      return 0.5;
    }
  }

  /**
   * Calculate weighted relevance score
   */
  private calculateWeightedScore(factors: RelevanceFactors): number {
    const weights = this.config.weights;

    const score =
      factors.semanticSimilarity * weights.semanticSimilarity +
      factors.keywordMatch * weights.keywordMatch +
      factors.contextType * weights.contextType +
      factors.recency * weights.recency +
      factors.agentPreference * weights.agentPreference +
      factors.taskTypeAlignment * weights.taskTypeAlignment +
      factors.dependencyRelevance * weights.dependencyRelevance +
      factors.terminologyMatch * weights.terminologyMatch;

    return Math.min(Math.max(score, 0), 1);
  }

  /**
   * Calculate confidence in the relevance score
   */
  private calculateConfidence(factors: RelevanceFactors, context: ContextItem, request: PrefetchRequest): number {
    // Base confidence on the consistency of factors
    const factorValues = Object.values(factors);
    const mean = factorValues.reduce((sum, val) => sum + val, 0) / factorValues.length;
    const variance = factorValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / factorValues.length;
    const consistency = 1 - Math.min(variance, 1);

    // Boost confidence for high-quality contexts
    const qualityBoost = context.confidence || 0.5;

    // Reduce confidence for very old contexts
    const ageConfidence = this.calculateRecencyScore(context);

    return (consistency * 0.4 + qualityBoost * 0.4 + ageConfidence * 0.2);
  }

  /**
   * Generate explanation for the relevance score
   */
  private generateExplanation(factors: RelevanceFactors, context: ContextItem, request: PrefetchRequest): string[] {
    const explanations: string[] = [];

    if (factors.semanticSimilarity > 0.7) {
      explanations.push(`High semantic similarity (${(factors.semanticSimilarity * 100).toFixed(1)}%) to task description`);
    } else if (factors.semanticSimilarity < 0.3) {
      explanations.push(`Low semantic similarity (${(factors.semanticSimilarity * 100).toFixed(1)}%) to task description`);
    }

    if (factors.keywordMatch > 0.6) {
      explanations.push(`Strong keyword match (${(factors.keywordMatch * 100).toFixed(1)}%) with requirements`);
    }

    if (factors.contextType > 0.8) {
      explanations.push(`Preferred context type (${context.type}) for this task`);
    }

    if (factors.recency < 0.3) {
      explanations.push(`Context is relatively old (low recency score: ${(factors.recency * 100).toFixed(1)}%)`);
    }

    if (factors.taskTypeAlignment > 0.7) {
      explanations.push(`Well-aligned with ${request.taskType} task patterns`);
    }

    return explanations;
  }

  /**
   * Generate recommendations for improving relevance
   */
  private generateRecommendations(factors: RelevanceFactors, context: ContextItem, request: PrefetchRequest): string[] {
    const recommendations: string[] = [];

    if (factors.semanticSimilarity < 0.4) {
      recommendations.push('Consider more specific search queries to find better semantic matches');
    }

    if (factors.keywordMatch < 0.3) {
      recommendations.push('Include more specific keywords in context requirements');
    }

    if (factors.recency < 0.5) {
      recommendations.push('Prioritize more recently modified code for better relevance');
    }

    if (factors.terminologyMatch < 0.3) {
      recommendations.push('Ensure project dictionary is up-to-date with current terminology');
    }

    if (factors.dependencyRelevance < 0.4) {
      recommendations.push('Review agent capabilities to ensure they match context dependencies');
    }

    return recommendations;
  }

  /**
   * Adapt weights based on performance feedback
   */
  private async adaptWeights(agentId: string, effectiveness: number): Promise<void> {
    // Simple adaptive learning - can be enhanced with more sophisticated algorithms
    const learningRate = this.config.learningRate;
    const target = effectiveness;
    const current = this.performanceHistory.get(agentId)?.slice(-1)[0] || 0.5;

    const error = target - current;

    // Adjust weights slightly based on error
    if (Math.abs(error) > 0.1) {
      const adjustment = error * learningRate;

      // Adjust semantic similarity weight (most important factor)
      this.config.weights.semanticSimilarity = Math.max(0.1,
        Math.min(0.5, this.config.weights.semanticSimilarity + adjustment * 0.3));

      // Adjust keyword match weight
      this.config.weights.keywordMatch = Math.max(0.1,
        Math.min(0.4, this.config.weights.keywordMatch + adjustment * 0.2));

      // Normalize weights to sum to 1
      this.normalizeWeights();

      await this.saveConfiguration();
    }
  }

  /**
   * Normalize weights to sum to 1
   */
  private normalizeWeights(): void {
    const weights = this.config.weights;
    const sum = Object.values(weights).reduce((total, weight) => total + weight, 0);

    if (sum > 0) {
      for (const key in weights) {
        weights[key as keyof ScoringWeights] /= sum;
      }
    }
  }

  /**
   * Extract keywords from text
   */
  private extractKeywords(text: string): string[] {
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !this.isStopWord(word));
  }

  /**
   * Check if word is a stop word
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'this', 'that', 'with', 'have', 'will', 'from', 'they', 'been',
      'were', 'said', 'each', 'which', 'their', 'time', 'would', 'there',
      'could', 'other', 'after', 'first', 'well', 'also', 'where', 'much'
    ]);
    return stopWords.has(word.toLowerCase());
  }

  /**
   * Extract terms from text using project dictionary
   */
  private async extractTermsFromText(text: string): Promise<string[]> {
    const words = this.extractKeywords(text);
    const terms: string[] = [];

    for (const word of words) {
      try {
        const searchResults = await this.projectDictionary.searchTerms(word, { fuzzy: true });
        terms.push(...searchResults.map(result => result.term));
      } catch (error) {
        // Ignore errors in term extraction
      }
    }

    return [...new Set(terms)];
  }

  /**
   * Get default factors for error cases
   */
  private getDefaultFactors(): RelevanceFactors {
    return {
      semanticSimilarity: 0.1,
      keywordMatch: 0.1,
      contextType: 0.5,
      recency: 0.5,
      agentPreference: 0.5,
      taskTypeAlignment: 0.1,
      dependencyRelevance: 0.1,
      terminologyMatch: 0.1
    };
  }

  /**
   * Initialize task type patterns
   */
  private initializeTaskTypePatterns(): void {
    this.taskTypePatterns.set('code-generation', [
      'function', 'class', 'component', 'interface', 'type', 'method',
      'variable', 'constant', 'import', 'export', 'async', 'await'
    ]);

    this.taskTypePatterns.set('bug-fix', [
      'error', 'exception', 'bug', 'fix', 'debug', 'issue', 'problem',
      'try', 'catch', 'throw', 'console', 'log', 'trace'
    ]);

    this.taskTypePatterns.set('refactoring', [
      'refactor', 'extract', 'rename', 'move', 'optimize', 'clean',
      'improve', 'restructure', 'pattern', 'architecture'
    ]);

    this.taskTypePatterns.set('testing', [
      'test', 'spec', 'mock', 'stub', 'assert', 'expect', 'should',
      'describe', 'it', 'beforeEach', 'afterEach', 'jest', 'mocha'
    ]);

    this.taskTypePatterns.set('documentation', [
      'comment', 'doc', 'readme', 'guide', 'tutorial', 'example',
      'usage', 'api', 'reference', 'manual', 'help'
    ]);
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    // Implementation would load from config store
    console.log('Loading relevance scorer configuration...');
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    // Implementation would save to config store
    console.log('Saving relevance scorer configuration...');
  }

  /**
   * Load performance history from storage
   */
  private async loadPerformanceHistory(): Promise<void> {
    // Implementation would load from config store
    console.log('Loading performance history...');
  }

  /**
   * Save performance history to storage
   */
  private async savePerformanceHistory(): Promise<void> {
    // Implementation would save to config store
    console.log('Saving performance history...');
  }
}

// Global instance
let globalRelevanceScorer: ContextRelevanceScorer | null = null;

/**
 * Get the global context relevance scorer instance
 */
export function getContextRelevanceScorer(): ContextRelevanceScorer {
  if (!globalRelevanceScorer) {
    globalRelevanceScorer = new ContextRelevanceScorer();
  }
  return globalRelevanceScorer;
}
