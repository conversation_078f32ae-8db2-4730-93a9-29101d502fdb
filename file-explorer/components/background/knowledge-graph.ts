// components/background/knowledge-graph.ts
import { SemanticSearchService, CodeContext } from './semantic-search';
import { ProjectDictionary, DictionaryTerm } from './project-dictionary';
import { getConfigStoreBrowser } from './config-store-browser';

export interface GraphNode {
  id: string;
  type: 'file' | 'class' | 'function' | 'variable' | 'interface' | 'type' | 'component' | 'module' | 'package';
  name: string;
  filePath: string;
  language: string;
  metadata: {
    startLine?: number;
    endLine?: number;
    visibility?: 'public' | 'private' | 'protected' | 'internal';
    isExported?: boolean;
    isDefault?: boolean;
    parameters?: string[];
    returnType?: string;
    extends?: string[];
    implements?: string[];
    decorators?: string[];
    annotations?: string[];
    documentation?: string;
    complexity?: number;
    size?: number;
    lastModified: number;
  };
  tags: string[];
  weight: number; // Importance score 0-1
}

export interface GraphEdge {
  id: string;
  sourceId: string;
  targetId: string;
  type: 'imports' | 'calls' | 'extends' | 'implements' | 'uses' | 'contains' | 'depends_on' | 'references' | 'instantiates' | 'overrides';
  weight: number; // Relationship strength 0-1
  metadata: {
    lineNumber?: number;
    context?: string;
    frequency?: number; // How often this relationship is used
    direction: 'unidirectional' | 'bidirectional';
    confidence: number; // How confident we are in this relationship
    lastSeen: number;
  };
  tags: string[];
}

export interface GraphPath {
  nodes: GraphNode[];
  edges: GraphEdge[];
  totalWeight: number;
  pathType: 'dependency' | 'inheritance' | 'usage' | 'reference';
  description: string;
}

export interface ImpactAnalysis {
  affectedNodes: GraphNode[];
  affectedEdges: GraphEdge[];
  impactScore: number; // 0-1, how significant the impact is
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  recommendations: string[];
  cascadeEffects: {
    node: GraphNode;
    impactType: 'direct' | 'indirect';
    severity: number;
    description: string;
  }[];
}

export interface GraphQuery {
  nodeTypes?: string[];
  edgeTypes?: string[];
  nodeNames?: string[];
  filePaths?: string[];
  tags?: string[];
  minWeight?: number;
  maxDepth?: number;
  includeMetadata?: boolean;
}

export interface GraphStats {
  totalNodes: number;
  totalEdges: number;
  nodesByType: Record<string, number>;
  edgesByType: Record<string, number>;
  averageConnectivity: number;
  maxDepth: number;
  stronglyConnectedComponents: number;
  cyclicDependencies: number;
  orphanedNodes: number;
  hubNodes: GraphNode[]; // Nodes with many connections
  leafNodes: GraphNode[]; // Nodes with few connections
}

export interface GraphConfig {
  maxNodes: number;
  maxEdges: number;
  defaultWeight: number;
  minConfidence: number;
  enableCaching: boolean;
  cacheSize: number;
  autoCleanup: boolean;
  cleanupInterval: number; // milliseconds
  analysisDepth: number;
  impactThreshold: number;
}

export class KnowledgeGraph {
  private nodes: Map<string, GraphNode> = new Map();
  private edges: Map<string, GraphEdge> = new Map();
  private nodesByType: Map<string, Set<string>> = new Map();
  private edgesByType: Map<string, Set<string>> = new Map();
  private adjacencyList: Map<string, Set<string>> = new Map(); // nodeId -> connected nodeIds
  private reverseAdjacencyList: Map<string, Set<string>> = new Map(); // nodeId -> nodes that connect to it

  private semanticSearch: SemanticSearchService;
  private projectDictionary: ProjectDictionary;
  private configStore: any;

  private config: GraphConfig = {
    maxNodes: 10000,
    maxEdges: 50000,
    defaultWeight: 0.5,
    minConfidence: 0.3,
    enableCaching: true,
    cacheSize: 1000,
    autoCleanup: true,
    cleanupInterval: 30 * 60 * 1000, // 30 minutes
    analysisDepth: 5,
    impactThreshold: 0.1
  };

  private pathCache: Map<string, GraphPath[]> = new Map();
  private impactCache: Map<string, ImpactAnalysis> = new Map();
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(private projectId: string) {
    this.semanticSearch = new SemanticSearchService();
    this.projectDictionary = new ProjectDictionary(projectId);
    this.configStore = getConfigStoreBrowser();

    this.startCleanupProcess();
  }

  /**
   * Initialize the knowledge graph
   */
  async initialize(): Promise<void> {
    try {
      await this.semanticSearch.initialize();
      await this.loadConfiguration();
      await this.loadGraphFromStorage();

      console.log('Knowledge graph initialized');
    } catch (error) {
      console.error('Failed to initialize knowledge graph:', error);
      throw error;
    }
  }

  /**
   * Add a node to the graph
   */
  async addNode(node: Omit<GraphNode, 'id'>): Promise<string> {
    const nodeId = this.generateNodeId();

    const fullNode: GraphNode = {
      ...node,
      id: nodeId
    };

    // Validate node
    this.validateNode(fullNode);

    // Check capacity
    if (this.nodes.size >= this.config.maxNodes) {
      await this.cleanupOldNodes();
    }

    // Store node
    this.nodes.set(nodeId, fullNode);

    // Update type index
    this.addToTypeIndex('node', fullNode.type, nodeId);

    // Initialize adjacency lists
    this.adjacencyList.set(nodeId, new Set());
    this.reverseAdjacencyList.set(nodeId, new Set());

    // Persist to storage
    await this.saveNodeToStorage(fullNode);

    console.log(`Added node to knowledge graph: ${fullNode.name} (${fullNode.type})`);
    return nodeId;
  }

  /**
   * Add an edge to the graph
   */
  async addEdge(edge: Omit<GraphEdge, 'id'>): Promise<string> {
    const edgeId = this.generateEdgeId();

    const fullEdge: GraphEdge = {
      ...edge,
      id: edgeId
    };

    // Validate edge
    this.validateEdge(fullEdge);

    // Check that source and target nodes exist
    if (!this.nodes.has(fullEdge.sourceId) || !this.nodes.has(fullEdge.targetId)) {
      throw new Error('Source or target node does not exist');
    }

    // Check capacity
    if (this.edges.size >= this.config.maxEdges) {
      await this.cleanupOldEdges();
    }

    // Store edge
    this.edges.set(edgeId, fullEdge);

    // Update type index
    this.addToTypeIndex('edge', fullEdge.type, edgeId);

    // Update adjacency lists
    this.adjacencyList.get(fullEdge.sourceId)?.add(fullEdge.targetId);
    this.reverseAdjacencyList.get(fullEdge.targetId)?.add(fullEdge.sourceId);

    // Handle bidirectional edges
    if (fullEdge.metadata.direction === 'bidirectional') {
      this.adjacencyList.get(fullEdge.targetId)?.add(fullEdge.sourceId);
      this.reverseAdjacencyList.get(fullEdge.sourceId)?.add(fullEdge.targetId);
    }

    // Clear relevant caches
    this.clearPathCache(fullEdge.sourceId, fullEdge.targetId);

    // Persist to storage
    await this.saveEdgeToStorage(fullEdge);

    console.log(`Added edge to knowledge graph: ${fullEdge.sourceId} -> ${fullEdge.targetId} (${fullEdge.type})`);
    return edgeId;
  }

  /**
   * Find nodes by query
   */
  findNodes(query: GraphQuery): GraphNode[] {
    let candidates = Array.from(this.nodes.values());

    // Filter by node types
    if (query.nodeTypes && query.nodeTypes.length > 0) {
      candidates = candidates.filter(node => query.nodeTypes!.includes(node.type));
    }

    // Filter by node names
    if (query.nodeNames && query.nodeNames.length > 0) {
      candidates = candidates.filter(node =>
        query.nodeNames!.some(name =>
          node.name.toLowerCase().includes(name.toLowerCase())
        )
      );
    }

    // Filter by file paths
    if (query.filePaths && query.filePaths.length > 0) {
      candidates = candidates.filter(node =>
        query.filePaths!.some(path =>
          node.filePath.includes(path)
        )
      );
    }

    // Filter by tags
    if (query.tags && query.tags.length > 0) {
      candidates = candidates.filter(node =>
        query.tags!.some(tag => node.tags.includes(tag))
      );
    }

    // Filter by weight
    if (query.minWeight !== undefined) {
      candidates = candidates.filter(node => node.weight >= query.minWeight!);
    }

    // Sort by weight (descending)
    candidates.sort((a, b) => b.weight - a.weight);

    return candidates;
  }

  /**
   * Find edges by query
   */
  findEdges(query: GraphQuery): GraphEdge[] {
    let candidates = Array.from(this.edges.values());

    // Filter by edge types
    if (query.edgeTypes && query.edgeTypes.length > 0) {
      candidates = candidates.filter(edge => query.edgeTypes!.includes(edge.type));
    }

    // Filter by weight
    if (query.minWeight !== undefined) {
      candidates = candidates.filter(edge => edge.weight >= query.minWeight!);
    }

    // Sort by weight (descending)
    candidates.sort((a, b) => b.weight - a.weight);

    return candidates;
  }

  /**
   * Find path between two nodes
   */
  async findPath(sourceId: string, targetId: string, maxDepth?: number): Promise<GraphPath[]> {
    const cacheKey = `${sourceId}->${targetId}`;

    // Check cache first
    if (this.config.enableCaching && this.pathCache.has(cacheKey)) {
      return this.pathCache.get(cacheKey)!;
    }

    const depth = maxDepth || this.config.analysisDepth;
    const paths = this.findPathsBFS(sourceId, targetId, depth);

    // Cache results
    if (this.config.enableCaching) {
      this.pathCache.set(cacheKey, paths);

      // Limit cache size
      if (this.pathCache.size > this.config.cacheSize) {
        const firstKey = this.pathCache.keys().next().value;
        this.pathCache.delete(firstKey);
      }
    }

    return paths;
  }

  /**
   * Analyze impact of changing a node
   */
  async analyzeImpact(nodeId: string): Promise<ImpactAnalysis> {
    // Check cache first
    if (this.config.enableCaching && this.impactCache.has(nodeId)) {
      return this.impactCache.get(nodeId)!;
    }

    const node = this.nodes.get(nodeId);
    if (!node) {
      throw new Error(`Node ${nodeId} not found`);
    }

    const analysis = await this.performImpactAnalysis(node);

    // Cache results
    if (this.config.enableCaching) {
      this.impactCache.set(nodeId, analysis);

      // Limit cache size
      if (this.impactCache.size > this.config.cacheSize) {
        const firstKey = this.impactCache.keys().next().value;
        this.impactCache.delete(firstKey);
      }
    }

    return analysis;
  }

  /**
   * Get graph statistics
   */
  getStats(): GraphStats {
    const nodesByType: Record<string, number> = {};
    const edgesByType: Record<string, number> = {};

    // Count nodes by type
    for (const node of this.nodes.values()) {
      nodesByType[node.type] = (nodesByType[node.type] || 0) + 1;
    }

    // Count edges by type
    for (const edge of this.edges.values()) {
      edgesByType[edge.type] = (edgesByType[edge.type] || 0) + 1;
    }

    // Calculate connectivity
    const totalConnections = Array.from(this.adjacencyList.values())
      .reduce((sum, connections) => sum + connections.size, 0);
    const averageConnectivity = this.nodes.size > 0 ? totalConnections / this.nodes.size : 0;

    // Find hub and leaf nodes
    const connectionCounts = Array.from(this.nodes.keys()).map(nodeId => ({
      nodeId,
      connections: (this.adjacencyList.get(nodeId)?.size || 0) + (this.reverseAdjacencyList.get(nodeId)?.size || 0)
    }));

    connectionCounts.sort((a, b) => b.connections - a.connections);

    const hubNodes = connectionCounts.slice(0, 10)
      .map(item => this.nodes.get(item.nodeId)!)
      .filter(node => node);

    const leafNodes = connectionCounts.slice(-10)
      .map(item => this.nodes.get(item.nodeId)!)
      .filter(node => node);

    // Count orphaned nodes (no connections)
    const orphanedNodes = connectionCounts.filter(item => item.connections === 0).length;

    return {
      totalNodes: this.nodes.size,
      totalEdges: this.edges.size,
      nodesByType,
      edgesByType,
      averageConnectivity,
      maxDepth: this.calculateMaxDepth(),
      stronglyConnectedComponents: this.findStronglyConnectedComponents(),
      cyclicDependencies: this.detectCycles(),
      orphanedNodes,
      hubNodes,
      leafNodes
    };
  }

  // Private implementation methods

  /**
   * Perform breadth-first search to find paths
   */
  private findPathsBFS(sourceId: string, targetId: string, maxDepth: number): GraphPath[] {
    const paths: GraphPath[] = [];
    const queue: { nodeId: string; path: string[]; edges: string[]; depth: number }[] = [
      { nodeId: sourceId, path: [sourceId], edges: [], depth: 0 }
    ];
    const visited = new Set<string>();

    while (queue.length > 0) {
      const current = queue.shift()!;

      if (current.depth > maxDepth) continue;
      if (visited.has(current.nodeId)) continue;

      visited.add(current.nodeId);

      // Check if we reached the target
      if (current.nodeId === targetId && current.path.length > 1) {
        const pathNodes = current.path.map(id => this.nodes.get(id)!).filter(node => node);
        const pathEdges = current.edges.map(id => this.edges.get(id)!).filter(edge => edge);

        if (pathNodes.length === current.path.length && pathEdges.length === current.edges.length) {
          const totalWeight = pathEdges.reduce((sum, edge) => sum + edge.weight, 0) / pathEdges.length;

          paths.push({
            nodes: pathNodes,
            edges: pathEdges,
            totalWeight,
            pathType: this.determinePathType(pathEdges),
            description: this.generatePathDescription(pathNodes, pathEdges)
          });
        }
        continue;
      }

      // Explore neighbors
      const neighbors = this.adjacencyList.get(current.nodeId) || new Set();
      for (const neighborId of neighbors) {
        if (!current.path.includes(neighborId)) {
          // Find the edge connecting current node to neighbor
          const connectingEdge = Array.from(this.edges.values()).find(edge =>
            edge.sourceId === current.nodeId && edge.targetId === neighborId
          );

          if (connectingEdge) {
            queue.push({
              nodeId: neighborId,
              path: [...current.path, neighborId],
              edges: [...current.edges, connectingEdge.id],
              depth: current.depth + 1
            });
          }
        }
      }
    }

    // Sort paths by total weight (descending)
    return paths.sort((a, b) => b.totalWeight - a.totalWeight);
  }

  /**
   * Perform impact analysis for a node
   */
  private async performImpactAnalysis(node: GraphNode): Promise<ImpactAnalysis> {
    const affectedNodes: GraphNode[] = [];
    const affectedEdges: GraphEdge[] = [];
    const cascadeEffects: ImpactAnalysis['cascadeEffects'] = [];

    // Find directly connected nodes
    const directlyConnected = new Set<string>();

    // Outgoing connections
    const outgoing = this.adjacencyList.get(node.id) || new Set();
    for (const nodeId of outgoing) {
      directlyConnected.add(nodeId);
    }

    // Incoming connections
    const incoming = this.reverseAdjacencyList.get(node.id) || new Set();
    for (const nodeId of incoming) {
      directlyConnected.add(nodeId);
    }

    // Analyze direct impacts
    for (const nodeId of directlyConnected) {
      const affectedNode = this.nodes.get(nodeId);
      if (affectedNode) {
        affectedNodes.push(affectedNode);

        cascadeEffects.push({
          node: affectedNode,
          impactType: 'direct',
          severity: this.calculateImpactSeverity(node, affectedNode),
          description: `Directly connected to ${node.name}`
        });
      }
    }

    // Find affected edges
    for (const edge of this.edges.values()) {
      if (edge.sourceId === node.id || edge.targetId === node.id) {
        affectedEdges.push(edge);
      }
    }

    // Analyze indirect impacts (2 levels deep)
    for (const directNodeId of directlyConnected) {
      const indirectConnected = this.adjacencyList.get(directNodeId) || new Set();
      for (const indirectNodeId of indirectConnected) {
        if (indirectNodeId !== node.id && !directlyConnected.has(indirectNodeId)) {
          const indirectNode = this.nodes.get(indirectNodeId);
          if (indirectNode) {
            affectedNodes.push(indirectNode);

            cascadeEffects.push({
              node: indirectNode,
              impactType: 'indirect',
              severity: this.calculateImpactSeverity(node, indirectNode) * 0.5, // Reduced severity for indirect
              description: `Indirectly connected through ${this.nodes.get(directNodeId)?.name || 'unknown'}`
            });
          }
        }
      }
    }

    // Calculate overall impact score
    const impactScore = this.calculateOverallImpactScore(node, affectedNodes, cascadeEffects);

    // Determine risk level
    const riskLevel = this.determineRiskLevel(impactScore, affectedNodes.length);

    // Generate recommendations
    const recommendations = this.generateImpactRecommendations(node, affectedNodes, riskLevel);

    return {
      affectedNodes: [...new Set(affectedNodes)], // Remove duplicates
      affectedEdges,
      impactScore,
      riskLevel,
      recommendations,
      cascadeEffects
    };
  }

  /**
   * Calculate impact severity between two nodes
   */
  private calculateImpactSeverity(sourceNode: GraphNode, targetNode: GraphNode): number {
    let severity = 0.5; // Base severity

    // Increase severity based on node types
    if (sourceNode.type === 'class' && targetNode.type === 'function') {
      severity += 0.2; // Class changes affecting functions are significant
    }

    if (sourceNode.type === 'interface' && targetNode.type === 'class') {
      severity += 0.3; // Interface changes affecting classes are very significant
    }

    // Increase severity based on node importance (weight)
    severity += (sourceNode.weight + targetNode.weight) / 4;

    // Increase severity if nodes are in the same file
    if (sourceNode.filePath === targetNode.filePath) {
      severity += 0.1;
    }

    return Math.min(severity, 1.0);
  }

  /**
   * Calculate overall impact score
   */
  private calculateOverallImpactScore(node: GraphNode, affectedNodes: GraphNode[], cascadeEffects: ImpactAnalysis['cascadeEffects']): number {
    let score = 0;

    // Base score from node importance
    score += node.weight * 0.3;

    // Score from number of affected nodes
    score += Math.min(affectedNodes.length / 10, 0.4);

    // Score from cascade effects
    const cascadeScore = cascadeEffects.reduce((sum, effect) => sum + effect.severity, 0) / cascadeEffects.length;
    score += cascadeScore * 0.3;

    return Math.min(score, 1.0);
  }

  /**
   * Determine risk level based on impact score
   */
  private determineRiskLevel(impactScore: number, affectedCount: number): ImpactAnalysis['riskLevel'] {
    if (impactScore > 0.8 || affectedCount > 20) return 'critical';
    if (impactScore > 0.6 || affectedCount > 10) return 'high';
    if (impactScore > 0.3 || affectedCount > 5) return 'medium';
    return 'low';
  }

  /**
   * Generate impact recommendations
   */
  private generateImpactRecommendations(node: GraphNode, affectedNodes: GraphNode[], riskLevel: ImpactAnalysis['riskLevel']): string[] {
    const recommendations: string[] = [];

    if (riskLevel === 'critical') {
      recommendations.push('Consider breaking this change into smaller, incremental updates');
      recommendations.push('Implement comprehensive testing before making changes');
      recommendations.push('Create backup/rollback plan');
    }

    if (riskLevel === 'high') {
      recommendations.push('Review all affected components before making changes');
      recommendations.push('Update documentation for affected areas');
    }

    if (affectedNodes.length > 10) {
      recommendations.push('Consider using deprecation warnings before removing functionality');
    }

    if (node.type === 'interface' || node.type === 'type') {
      recommendations.push('Ensure backward compatibility or provide migration path');
    }

    return recommendations;
  }

  /**
   * Determine path type based on edges
   */
  private determinePathType(edges: GraphEdge[]): GraphPath['pathType'] {
    const edgeTypes = edges.map(edge => edge.type);

    if (edgeTypes.includes('extends') || edgeTypes.includes('implements')) {
      return 'inheritance';
    }

    if (edgeTypes.includes('imports') || edgeTypes.includes('depends_on')) {
      return 'dependency';
    }

    if (edgeTypes.includes('calls') || edgeTypes.includes('uses')) {
      return 'usage';
    }

    return 'reference';
  }

  /**
   * Generate path description
   */
  private generatePathDescription(nodes: GraphNode[], edges: GraphEdge[]): string {
    if (nodes.length < 2) return 'Empty path';

    const start = nodes[0];
    const end = nodes[nodes.length - 1];
    const pathLength = nodes.length - 1;

    return `${start.name} (${start.type}) → ${end.name} (${end.type}) via ${pathLength} step${pathLength !== 1 ? 's' : ''}`;
  }

  /**
   * Calculate maximum depth of the graph
   */
  private calculateMaxDepth(): number {
    let maxDepth = 0;

    for (const nodeId of this.nodes.keys()) {
      const depth = this.calculateNodeDepth(nodeId, new Set());
      maxDepth = Math.max(maxDepth, depth);
    }

    return maxDepth;
  }

  /**
   * Calculate depth from a specific node
   */
  private calculateNodeDepth(nodeId: string, visited: Set<string>): number {
    if (visited.has(nodeId)) return 0; // Avoid cycles

    visited.add(nodeId);
    let maxChildDepth = 0;

    const children = this.adjacencyList.get(nodeId) || new Set();
    for (const childId of children) {
      const childDepth = this.calculateNodeDepth(childId, new Set(visited));
      maxChildDepth = Math.max(maxChildDepth, childDepth);
    }

    return maxChildDepth + 1;
  }

  /**
   * Find strongly connected components using Tarjan's algorithm
   */
  private findStronglyConnectedComponents(): number {
    const visited = new Set<string>();
    const stack: string[] = [];
    const indices = new Map<string, number>();
    const lowLinks = new Map<string, number>();
    const onStack = new Set<string>();
    let index = 0;
    let sccCount = 0;

    const strongConnect = (nodeId: string) => {
      indices.set(nodeId, index);
      lowLinks.set(nodeId, index);
      index++;
      stack.push(nodeId);
      onStack.add(nodeId);

      const neighbors = this.adjacencyList.get(nodeId) || new Set();
      for (const neighborId of neighbors) {
        if (!indices.has(neighborId)) {
          strongConnect(neighborId);
          lowLinks.set(nodeId, Math.min(lowLinks.get(nodeId)!, lowLinks.get(neighborId)!));
        } else if (onStack.has(neighborId)) {
          lowLinks.set(nodeId, Math.min(lowLinks.get(nodeId)!, indices.get(neighborId)!));
        }
      }

      if (lowLinks.get(nodeId) === indices.get(nodeId)) {
        sccCount++;
        let w: string;
        do {
          w = stack.pop()!;
          onStack.delete(w);
        } while (w !== nodeId);
      }
    };

    for (const nodeId of this.nodes.keys()) {
      if (!indices.has(nodeId)) {
        strongConnect(nodeId);
      }
    }

    return sccCount;
  }

  /**
   * Detect cycles in the graph
   */
  private detectCycles(): number {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    let cycleCount = 0;

    const hasCycle = (nodeId: string): boolean => {
      visited.add(nodeId);
      recursionStack.add(nodeId);

      const neighbors = this.adjacencyList.get(nodeId) || new Set();
      for (const neighborId of neighbors) {
        if (!visited.has(neighborId)) {
          if (hasCycle(neighborId)) {
            cycleCount++;
            return true;
          }
        } else if (recursionStack.has(neighborId)) {
          cycleCount++;
          return true;
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const nodeId of this.nodes.keys()) {
      if (!visited.has(nodeId)) {
        hasCycle(nodeId);
      }
    }

    return cycleCount;
  }

  /**
   * Validate node data
   */
  private validateNode(node: GraphNode): void {
    if (!node.name || node.name.trim().length === 0) {
      throw new Error('Node name is required');
    }

    if (!node.type || node.type.trim().length === 0) {
      throw new Error('Node type is required');
    }

    if (!node.filePath || node.filePath.trim().length === 0) {
      throw new Error('Node file path is required');
    }

    if (node.weight < 0 || node.weight > 1) {
      throw new Error('Node weight must be between 0 and 1');
    }
  }

  /**
   * Validate edge data
   */
  private validateEdge(edge: GraphEdge): void {
    if (!edge.sourceId || edge.sourceId.trim().length === 0) {
      throw new Error('Edge source ID is required');
    }

    if (!edge.targetId || edge.targetId.trim().length === 0) {
      throw new Error('Edge target ID is required');
    }

    if (!edge.type || edge.type.trim().length === 0) {
      throw new Error('Edge type is required');
    }

    if (edge.weight < 0 || edge.weight > 1) {
      throw new Error('Edge weight must be between 0 and 1');
    }

    if (edge.metadata.confidence < 0 || edge.metadata.confidence > 1) {
      throw new Error('Edge confidence must be between 0 and 1');
    }
  }

  /**
   * Add item to type index
   */
  private addToTypeIndex(indexType: 'node' | 'edge', type: string, id: string): void {
    const index = indexType === 'node' ? this.nodesByType : this.edgesByType;

    if (!index.has(type)) {
      index.set(type, new Set());
    }

    index.get(type)!.add(id);
  }

  /**
   * Remove item from type index
   */
  private removeFromTypeIndex(indexType: 'node' | 'edge', type: string, id: string): void {
    const index = indexType === 'node' ? this.nodesByType : this.edgesByType;

    const typeSet = index.get(type);
    if (typeSet) {
      typeSet.delete(id);
      if (typeSet.size === 0) {
        index.delete(type);
      }
    }
  }

  /**
   * Clear path cache for specific nodes
   */
  private clearPathCache(sourceId: string, targetId: string): void {
    const keysToRemove: string[] = [];

    for (const key of this.pathCache.keys()) {
      if (key.includes(sourceId) || key.includes(targetId)) {
        keysToRemove.push(key);
      }
    }

    for (const key of keysToRemove) {
      this.pathCache.delete(key);
    }
  }

  /**
   * Cleanup old nodes when at capacity
   */
  private async cleanupOldNodes(): Promise<void> {
    const nodes = Array.from(this.nodes.values());

    // Sort by last modified (oldest first)
    nodes.sort((a, b) => a.metadata.lastModified - b.metadata.lastModified);

    // Remove oldest 10% of nodes
    const toRemove = Math.floor(nodes.length * 0.1);

    for (let i = 0; i < toRemove; i++) {
      await this.removeNode(nodes[i].id);
    }
  }

  /**
   * Cleanup old edges when at capacity
   */
  private async cleanupOldEdges(): Promise<void> {
    const edges = Array.from(this.edges.values());

    // Sort by last seen (oldest first)
    edges.sort((a, b) => a.metadata.lastSeen - b.metadata.lastSeen);

    // Remove oldest 10% of edges
    const toRemove = Math.floor(edges.length * 0.1);

    for (let i = 0; i < toRemove; i++) {
      await this.removeEdge(edges[i].id);
    }
  }

  /**
   * Remove a node and all its edges
   */
  private async removeNode(nodeId: string): Promise<boolean> {
    const node = this.nodes.get(nodeId);
    if (!node) return false;

    // Remove all edges connected to this node
    const edgesToRemove: string[] = [];
    for (const edge of this.edges.values()) {
      if (edge.sourceId === nodeId || edge.targetId === nodeId) {
        edgesToRemove.push(edge.id);
      }
    }

    for (const edgeId of edgesToRemove) {
      await this.removeEdge(edgeId);
    }

    // Remove node
    this.nodes.delete(nodeId);
    this.removeFromTypeIndex('node', node.type, nodeId);
    this.adjacencyList.delete(nodeId);
    this.reverseAdjacencyList.delete(nodeId);

    // Clear caches
    this.clearPathCache(nodeId, nodeId);
    this.impactCache.delete(nodeId);

    await this.removeNodeFromStorage(nodeId);
    return true;
  }

  /**
   * Remove an edge
   */
  private async removeEdge(edgeId: string): Promise<boolean> {
    const edge = this.edges.get(edgeId);
    if (!edge) return false;

    // Remove from adjacency lists
    this.adjacencyList.get(edge.sourceId)?.delete(edge.targetId);
    this.reverseAdjacencyList.get(edge.targetId)?.delete(edge.sourceId);

    if (edge.metadata.direction === 'bidirectional') {
      this.adjacencyList.get(edge.targetId)?.delete(edge.sourceId);
      this.reverseAdjacencyList.get(edge.sourceId)?.delete(edge.targetId);
    }

    // Remove edge
    this.edges.delete(edgeId);
    this.removeFromTypeIndex('edge', edge.type, edgeId);

    // Clear caches
    this.clearPathCache(edge.sourceId, edge.targetId);

    await this.removeEdgeFromStorage(edgeId);
    return true;
  }

  /**
   * Generate unique node ID
   */
  private generateNodeId(): string {
    return `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique edge ID
   */
  private generateEdgeId(): string {
    return `edge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Start cleanup process
   */
  private startCleanupProcess(): void {
    if (!this.config.autoCleanup) return;

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.performCleanup().catch(error => {
        console.error('Error during knowledge graph cleanup:', error);
      });
    }, this.config.cleanupInterval);
  }

  /**
   * Stop cleanup process
   */
  private stopCleanupProcess(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Perform periodic cleanup
   */
  private async performCleanup(): Promise<void> {
    try {
      // Clean up expired cache entries
      const now = Date.now();
      const maxAge = 60 * 60 * 1000; // 1 hour

      // Clear old path cache entries
      for (const [key, paths] of this.pathCache.entries()) {
        if (paths.length > 0 && now - paths[0].nodes[0].metadata.lastModified > maxAge) {
          this.pathCache.delete(key);
        }
      }

      // Clear old impact cache entries
      for (const [nodeId, analysis] of this.impactCache.entries()) {
        const node = this.nodes.get(nodeId);
        if (!node || now - node.metadata.lastModified > maxAge) {
          this.impactCache.delete(nodeId);
        }
      }

      console.log('Knowledge graph cleanup completed');
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const stored = await this.configStore.getGlobalSetting('knowledgeGraph.config');
      if (stored) {
        this.config = { ...this.config, ...stored };
      }
    } catch (error) {
      console.error('Failed to load knowledge graph configuration:', error);
    }
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    try {
      await this.configStore.setGlobalSetting('knowledgeGraph.config', this.config);
    } catch (error) {
      console.error('Failed to save knowledge graph configuration:', error);
    }
  }

  /**
   * Load graph from storage
   */
  private async loadGraphFromStorage(): Promise<void> {
    try {
      // Load nodes
      const storedNodes = await this.configStore.getGlobalSetting(`knowledgeGraph.${this.projectId}.nodes`);
      if (storedNodes && Array.isArray(storedNodes)) {
        for (const nodeData of storedNodes) {
          const node = nodeData as GraphNode;
          this.nodes.set(node.id, node);
          this.addToTypeIndex('node', node.type, node.id);
          this.adjacencyList.set(node.id, new Set());
          this.reverseAdjacencyList.set(node.id, new Set());
        }
      }

      // Load edges
      const storedEdges = await this.configStore.getGlobalSetting(`knowledgeGraph.${this.projectId}.edges`);
      if (storedEdges && Array.isArray(storedEdges)) {
        for (const edgeData of storedEdges) {
          const edge = edgeData as GraphEdge;
          this.edges.set(edge.id, edge);
          this.addToTypeIndex('edge', edge.type, edge.id);

          // Rebuild adjacency lists
          this.adjacencyList.get(edge.sourceId)?.add(edge.targetId);
          this.reverseAdjacencyList.get(edge.targetId)?.add(edge.sourceId);

          if (edge.metadata.direction === 'bidirectional') {
            this.adjacencyList.get(edge.targetId)?.add(edge.sourceId);
            this.reverseAdjacencyList.get(edge.sourceId)?.add(edge.targetId);
          }
        }
      }

      console.log(`Loaded knowledge graph: ${this.nodes.size} nodes, ${this.edges.size} edges`);
    } catch (error) {
      console.error('Failed to load knowledge graph from storage:', error);
    }
  }

  /**
   * Save graph to storage
   */
  private async saveGraphToStorage(): Promise<void> {
    try {
      // Save nodes
      const nodeArray = Array.from(this.nodes.values());
      await this.configStore.setGlobalSetting(`knowledgeGraph.${this.projectId}.nodes`, nodeArray);

      // Save edges
      const edgeArray = Array.from(this.edges.values());
      await this.configStore.setGlobalSetting(`knowledgeGraph.${this.projectId}.edges`, edgeArray);

      console.log('Knowledge graph saved to storage');
    } catch (error) {
      console.error('Failed to save knowledge graph to storage:', error);
    }
  }

  /**
   * Save node to storage
   */
  private async saveNodeToStorage(node: GraphNode): Promise<void> {
    try {
      // For individual node saves, we'll just trigger a full save
      // In a production system, this could be optimized
      await this.saveGraphToStorage();
    } catch (error) {
      console.error(`Failed to save node ${node.id} to storage:`, error);
    }
  }

  /**
   * Save edge to storage
   */
  private async saveEdgeToStorage(edge: GraphEdge): Promise<void> {
    try {
      // For individual edge saves, we'll just trigger a full save
      // In a production system, this could be optimized
      await this.saveGraphToStorage();
    } catch (error) {
      console.error(`Failed to save edge ${edge.id} to storage:`, error);
    }
  }

  /**
   * Remove node from storage
   */
  private async removeNodeFromStorage(nodeId: string): Promise<void> {
    try {
      // Trigger a full save to update storage
      await this.saveGraphToStorage();
    } catch (error) {
      console.error(`Failed to remove node ${nodeId} from storage:`, error);
    }
  }

  /**
   * Remove edge from storage
   */
  private async removeEdgeFromStorage(edgeId: string): Promise<void> {
    try {
      // Trigger a full save to update storage
      await this.saveGraphToStorage();
    } catch (error) {
      console.error(`Failed to remove edge ${edgeId} from storage:`, error);
    }
  }

  /**
   * Update configuration
   */
  async updateConfig(newConfig: Partial<GraphConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();

    // Restart cleanup process if interval changed
    if (newConfig.cleanupInterval || newConfig.autoCleanup !== undefined) {
      this.stopCleanupProcess();
      this.startCleanupProcess();
    }

    console.log('Knowledge graph configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): GraphConfig {
    return { ...this.config };
  }

  /**
   * Shutdown the knowledge graph
   */
  async shutdown(): Promise<void> {
    try {
      this.stopCleanupProcess();
      await this.saveGraphToStorage();

      // Clear all data
      this.nodes.clear();
      this.edges.clear();
      this.nodesByType.clear();
      this.edgesByType.clear();
      this.adjacencyList.clear();
      this.reverseAdjacencyList.clear();
      this.pathCache.clear();
      this.impactCache.clear();

      console.log('Knowledge graph shut down');
    } catch (error) {
      console.error('Error during knowledge graph shutdown:', error);
    }
  }
}

// Global instances per project
const globalKnowledgeGraphs: Map<string, KnowledgeGraph> = new Map();

/**
 * Get the knowledge graph instance for a project
 */
export function getKnowledgeGraph(projectId: string): KnowledgeGraph {
  if (!globalKnowledgeGraphs.has(projectId)) {
    globalKnowledgeGraphs.set(projectId, new KnowledgeGraph(projectId));
  }
  return globalKnowledgeGraphs.get(projectId)!;
}
