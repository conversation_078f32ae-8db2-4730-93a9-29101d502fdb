// components/background/context-prefetcher.ts
import { SemanticSearchService, CodeContext, SemanticSearchQuery } from './semantic-search';
import { AgentRegistry, AgentRegistration } from './agent-registry';
import { TaskQueue, AgentTask } from './task-queue';
import { ProjectDictionary, DictionaryTerm } from './project-dictionary';
import { getConfigStoreBrowser } from './config-store-browser';

export interface PrefetchRequest {
  id: string;
  taskType: string;
  agentId: string;
  agentCapabilities: string[];
  taskDescription: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  requiredContext: string[];
  optionalContext: string[];
  maxContextSize: number; // in tokens
  deadline?: number; // timestamp
  metadata?: Record<string, any>;
}

export interface PrefetchedContext {
  id: string;
  requestId: string;
  taskType: string;
  agentId: string;
  contexts: ContextItem[];
  totalTokens: number;
  relevanceScore: number;
  freshness: number; // 0-1, how recent the context is
  completeness: number; // 0-1, how complete the context is
  confidence: number; // 0-1, confidence in context relevance
  generatedAt: number;
  expiresAt: number;
  metadata: {
    searchQueries: string[];
    sourceFiles: string[];
    patterns: string[];
    dependencies: string[];
    terminology: string[];
  };
}

export interface ContextItem {
  id: string;
  type: 'code' | 'documentation' | 'pattern' | 'dependency' | 'terminology' | 'example';
  source: string; // file path or source identifier
  content: string;
  language?: string;
  relevanceScore: number;
  confidence: number;
  tokens: number;
  metadata: {
    startLine?: number;
    endLine?: number;
    symbols?: string[];
    imports?: string[];
    exports?: string[];
    dependencies?: string[];
    patterns?: string[];
    lastModified: number;
  };
}

export interface PrefetchConfig {
  maxConcurrentRequests: number;
  defaultContextSize: number;
  maxContextAge: number; // milliseconds
  prefetchAheadTime: number; // milliseconds
  relevanceThreshold: number;
  confidenceThreshold: number;
  enablePredictivePrefetch: boolean;
  enablePatternLearning: boolean;
  cacheSize: number;
}

export interface PrefetchStats {
  totalRequests: number;
  completedRequests: number;
  failedRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageProcessingTime: number;
  averageRelevanceScore: number;
  averageConfidence: number;
  totalTokensGenerated: number;
  patternMatches: number;
}

export class ContextPrefetcher {
  private semanticSearch: SemanticSearchService;
  private agentRegistry: AgentRegistry;
  private taskQueue: TaskQueue;
  private projectDictionary: ProjectDictionary;
  private configStore: any;

  private activeRequests: Map<string, PrefetchRequest> = new Map();
  private processingQueue: PrefetchRequest[] = [];
  private taskPatterns: Map<string, string[]> = new Map(); // taskType -> common context patterns
  private agentPreferences: Map<string, string[]> = new Map(); // agentId -> preferred context types

  private config: PrefetchConfig = {
    maxConcurrentRequests: 5,
    defaultContextSize: 4000,
    maxContextAge: 30 * 60 * 1000, // 30 minutes
    prefetchAheadTime: 5 * 60 * 1000, // 5 minutes
    relevanceThreshold: 0.3,
    confidenceThreshold: 0.5,
    enablePredictivePrefetch: true,
    enablePatternLearning: true,
    cacheSize: 100
  };

  private stats: PrefetchStats = {
    totalRequests: 0,
    completedRequests: 0,
    failedRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageProcessingTime: 0,
    averageRelevanceScore: 0,
    averageConfidence: 0,
    totalTokensGenerated: 0,
    patternMatches: 0
  };

  constructor() {
    this.semanticSearch = new SemanticSearchService();
    this.agentRegistry = new AgentRegistry();
    this.taskQueue = new TaskQueue();
    this.projectDictionary = new ProjectDictionary();
    this.configStore = getConfigStoreBrowser();

    this.initializePatterns();
    this.startPredictivePrefetching();
  }

  /**
   * Initialize the context prefetcher
   */
  async initialize(): Promise<void> {
    try {
      await this.semanticSearch.initialize();
      await this.loadConfiguration();
      await this.loadLearningData();

      console.log('Context prefetcher initialized');
    } catch (error) {
      console.error('Failed to initialize context prefetcher:', error);
      throw error;
    }
  }

  /**
   * Request context prefetching for a task
   */
  async requestPrefetch(request: Omit<PrefetchRequest, 'id'>): Promise<string> {
    const requestId = this.generateRequestId();

    const fullRequest: PrefetchRequest = {
      ...request,
      id: requestId
    };

    // Validate request
    this.validatePrefetchRequest(fullRequest);

    // Add to processing queue
    this.activeRequests.set(requestId, fullRequest);
    this.processingQueue.push(fullRequest);

    // Update statistics
    this.stats.totalRequests++;

    // Start processing if not at capacity
    this.processQueue();

    console.log(`Context prefetch requested: ${fullRequest.taskType} for agent ${fullRequest.agentId}`);
    return requestId;
  }

  /**
   * Get prefetched context by request ID
   */
  async getPrefetchedContext(requestId: string): Promise<PrefetchedContext | null> {
    try {
      // Check if request exists
      const request = this.activeRequests.get(requestId);
      if (!request) {
        console.warn(`Prefetch request not found: ${requestId}`);
        return null;
      }

      // Generate context for the request
      const context = await this.generateContext(request);

      if (context) {
        this.stats.completedRequests++;
        this.stats.cacheHits++;
      } else {
        this.stats.cacheMisses++;
      }

      return context;
    } catch (error) {
      console.error(`Failed to get prefetched context for ${requestId}:`, error);
      this.stats.failedRequests++;
      return null;
    }
  }

  /**
   * Cancel a prefetch request
   */
  async cancelPrefetch(requestId: string): Promise<boolean> {
    const request = this.activeRequests.get(requestId);
    if (!request) {
      return false;
    }

    // Remove from active requests and queue
    this.activeRequests.delete(requestId);
    this.processingQueue = this.processingQueue.filter(r => r.id !== requestId);

    console.log(`Prefetch request cancelled: ${requestId}`);
    return true;
  }

  /**
   * Get prefetcher statistics
   */
  getStats(): PrefetchStats {
    return { ...this.stats };
  }

  /**
   * Update prefetcher configuration
   */
  async updateConfig(newConfig: Partial<PrefetchConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();
    console.log('Context prefetcher configuration updated');
  }

  /**
   * Learn from task completion patterns
   */
  async learnFromTask(taskId: string, usedContext: string[], effectiveness: number): Promise<void> {
    if (!this.config.enablePatternLearning) {
      return;
    }

    try {
      const task = await this.taskQueue.getTask(taskId);
      if (!task) {
        return;
      }

      // Update task patterns
      const existingPatterns = this.taskPatterns.get(task.type) || [];
      const newPatterns = [...new Set([...existingPatterns, ...usedContext])];
      this.taskPatterns.set(task.type, newPatterns);

      // Update agent preferences if effectiveness is high
      if (effectiveness > 0.7 && task.assignedAgentId) {
        const agentPrefs = this.agentPreferences.get(task.assignedAgentId) || [];
        const updatedPrefs = [...new Set([...agentPrefs, ...usedContext])];
        this.agentPreferences.set(task.assignedAgentId, updatedPrefs);
      }

      await this.saveLearningData();
      console.log(`Learned from task ${taskId} with effectiveness ${effectiveness}`);
    } catch (error) {
      console.error(`Failed to learn from task ${taskId}:`, error);
    }
  }

  // Private implementation methods

  /**
   * Generate context for a prefetch request
   */
  private async generateContext(request: PrefetchRequest): Promise<PrefetchedContext | null> {
    const startTime = Date.now();

    try {
      const contexts: ContextItem[] = [];
      const searchQueries: string[] = [];
      const sourceFiles: string[] = [];
      const patterns: string[] = [];
      const dependencies: string[] = [];
      const terminology: string[] = [];

      // 1. Generate search queries from task description and requirements
      const queries = await this.generateSearchQueries(request);
      searchQueries.push(...queries);

      // 2. Search for relevant code context
      for (const query of queries) {
        const codeContexts = await this.searchCodeContext(query, request);
        contexts.push(...codeContexts);
      }

      // 3. Add pattern-based context
      const patternContexts = await this.getPatternBasedContext(request);
      contexts.push(...patternContexts);
      patterns.push(...patternContexts.map(c => c.metadata.patterns || []).flat());

      // 4. Add terminology context
      const termContexts = await this.getTerminologyContext(request);
      contexts.push(...termContexts);
      terminology.push(...termContexts.map(c => c.content));

      // 5. Add dependency context
      const depContexts = await this.getDependencyContext(request);
      contexts.push(...depContexts);
      dependencies.push(...depContexts.map(c => c.metadata.dependencies || []).flat());

      // 6. Rank and filter contexts
      const rankedContexts = this.rankContexts(contexts, request);
      const filteredContexts = this.filterContextsBySize(rankedContexts, request.maxContextSize);

      // 7. Calculate metrics
      const totalTokens = filteredContexts.reduce((sum, ctx) => sum + ctx.tokens, 0);
      const relevanceScore = this.calculateAverageRelevance(filteredContexts);
      const confidence = this.calculateConfidence(filteredContexts, request);
      const freshness = this.calculateFreshness(filteredContexts);
      const completeness = this.calculateCompleteness(filteredContexts, request);

      // 8. Create prefetched context
      const prefetchedContext: PrefetchedContext = {
        id: this.generateContextId(),
        requestId: request.id,
        taskType: request.taskType,
        agentId: request.agentId,
        contexts: filteredContexts,
        totalTokens,
        relevanceScore,
        freshness,
        completeness,
        confidence,
        generatedAt: Date.now(),
        expiresAt: Date.now() + this.config.maxContextAge,
        metadata: {
          searchQueries: [...new Set(searchQueries)],
          sourceFiles: [...new Set(sourceFiles)],
          patterns: [...new Set(patterns)],
          dependencies: [...new Set(dependencies)],
          terminology: [...new Set(terminology)]
        }
      };

      // Update statistics
      const processingTime = Date.now() - startTime;
      this.updateProcessingStats(processingTime, relevanceScore, confidence, totalTokens);

      console.log(`Generated context for ${request.taskType}: ${filteredContexts.length} items, ${totalTokens} tokens`);
      return prefetchedContext;

    } catch (error) {
      console.error(`Failed to generate context for request ${request.id}:`, error);
      return null;
    }
  }

  /**
   * Generate search queries from task description and requirements
   */
  private async generateSearchQueries(request: PrefetchRequest): Promise<string[]> {
    const queries: string[] = [];

    // Add task description as primary query
    queries.push(request.taskDescription);

    // Add required context as queries
    queries.push(...request.requiredContext);

    // Add optional context as lower priority queries
    queries.push(...request.optionalContext);

    // Generate queries based on task type patterns
    const taskPatterns = this.taskPatterns.get(request.taskType) || [];
    queries.push(...taskPatterns);

    // Generate queries based on agent preferences
    const agentPrefs = this.agentPreferences.get(request.agentId) || [];
    queries.push(...agentPrefs);

    // Extract keywords from task description
    const keywords = this.extractKeywords(request.taskDescription);
    queries.push(...keywords);

    // Remove duplicates and empty queries
    return [...new Set(queries.filter(q => q && q.trim().length > 0))];
  }

  /**
   * Search for code context using semantic search
   */
  private async searchCodeContext(query: string, request: PrefetchRequest): Promise<ContextItem[]> {
    try {
      const searchQuery: SemanticSearchQuery = {
        query,
        maxResults: 10,
        minSimilarity: this.config.relevanceThreshold
      };

      const codeContexts = await this.semanticSearch.searchCode(searchQuery);

      return codeContexts.map(ctx => this.convertCodeContextToContextItem(ctx, 'code'));
    } catch (error) {
      console.error(`Failed to search code context for query "${query}":`, error);
      return [];
    }
  }

  /**
   * Get pattern-based context from learned patterns
   */
  private async getPatternBasedContext(request: PrefetchRequest): Promise<ContextItem[]> {
    const contexts: ContextItem[] = [];

    try {
      // Get patterns for this task type
      const patterns = this.taskPatterns.get(request.taskType) || [];

      for (const pattern of patterns) {
        const patternContexts = await this.searchCodeContext(pattern, request);
        contexts.push(...patternContexts.map(ctx => ({
          ...ctx,
          type: 'pattern' as const,
          metadata: {
            ...ctx.metadata,
            patterns: [pattern]
          }
        })));
      }

      this.stats.patternMatches += contexts.length;
      return contexts;
    } catch (error) {
      console.error('Failed to get pattern-based context:', error);
      return [];
    }
  }

  /**
   * Get terminology context from project dictionary
   */
  private async getTerminologyContext(request: PrefetchRequest): Promise<ContextItem[]> {
    const contexts: ContextItem[] = [];

    try {
      // Extract terms from task description
      const terms = await this.extractTermsFromText(request.taskDescription);

      for (const term of terms) {
        const termData = await this.projectDictionary.getTerm(term);
        if (termData) {
          contexts.push({
            id: `term-${termData.id}`,
            type: 'terminology',
            source: 'project-dictionary',
            content: `${termData.term}: ${termData.definition}`,
            relevanceScore: 0.8,
            confidence: 0.9,
            tokens: this.estimateTokens(`${termData.term}: ${termData.definition}`),
            metadata: {
              lastModified: termData.updatedAt
            }
          });
        }
      }

      return contexts;
    } catch (error) {
      console.error('Failed to get terminology context:', error);
      return [];
    }
  }

  /**
   * Get dependency context based on agent capabilities
   */
  private async getDependencyContext(request: PrefetchRequest): Promise<ContextItem[]> {
    const contexts: ContextItem[] = [];

    try {
      // Search for context related to agent capabilities
      for (const capability of request.agentCapabilities) {
        const depContexts = await this.searchCodeContext(capability, request);
        contexts.push(...depContexts.map(ctx => ({
          ...ctx,
          type: 'dependency' as const,
          metadata: {
            ...ctx.metadata,
            dependencies: [capability]
          }
        })));
      }

      return contexts;
    } catch (error) {
      console.error('Failed to get dependency context:', error);
      return [];
    }
  }

  /**
   * Rank contexts by relevance and other factors
   */
  private rankContexts(contexts: ContextItem[], request: PrefetchRequest): ContextItem[] {
    return contexts.sort((a, b) => {
      // Primary sort by relevance score
      if (a.relevanceScore !== b.relevanceScore) {
        return b.relevanceScore - a.relevanceScore;
      }

      // Secondary sort by confidence
      if (a.confidence !== b.confidence) {
        return b.confidence - a.confidence;
      }

      // Tertiary sort by recency
      const aTime = a.metadata.lastModified || 0;
      const bTime = b.metadata.lastModified || 0;
      return bTime - aTime;
    });
  }

  /**
   * Filter contexts by total token size
   */
  private filterContextsBySize(contexts: ContextItem[], maxTokens: number): ContextItem[] {
    const filtered: ContextItem[] = [];
    let totalTokens = 0;

    for (const context of contexts) {
      if (totalTokens + context.tokens <= maxTokens) {
        filtered.push(context);
        totalTokens += context.tokens;
      } else {
        break;
      }
    }

    return filtered;
  }

  /**
   * Convert CodeContext to ContextItem
   */
  private convertCodeContextToContextItem(codeContext: CodeContext, type: ContextItem['type']): ContextItem {
    return {
      id: codeContext.id,
      type,
      source: codeContext.filePath,
      content: codeContext.content,
      language: codeContext.language,
      relevanceScore: codeContext.relevanceScore,
      confidence: codeContext.similarity,
      tokens: this.estimateTokens(codeContext.content),
      metadata: {
        startLine: codeContext.metadata.startLine,
        endLine: codeContext.metadata.endLine,
        lastModified: codeContext.metadata.timestamp
      }
    };
  }

  /**
   * Calculate average relevance score
   */
  private calculateAverageRelevance(contexts: ContextItem[]): number {
    if (contexts.length === 0) return 0;
    const sum = contexts.reduce((acc, ctx) => acc + ctx.relevanceScore, 0);
    return sum / contexts.length;
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(contexts: ContextItem[], request: PrefetchRequest): number {
    if (contexts.length === 0) return 0;

    // Base confidence on average context confidence
    const avgConfidence = contexts.reduce((acc, ctx) => acc + ctx.confidence, 0) / contexts.length;

    // Boost confidence if we have required context
    const hasRequiredContext = request.requiredContext.every(req =>
      contexts.some(ctx => ctx.content.toLowerCase().includes(req.toLowerCase()))
    );

    return hasRequiredContext ? Math.min(avgConfidence * 1.2, 1.0) : avgConfidence;
  }

  /**
   * Calculate freshness score
   */
  private calculateFreshness(contexts: ContextItem[]): number {
    if (contexts.length === 0) return 0;

    const now = Date.now();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days

    const freshnessScores = contexts.map(ctx => {
      const age = now - (ctx.metadata.lastModified || 0);
      return Math.max(0, 1 - (age / maxAge));
    });

    return freshnessScores.reduce((acc, score) => acc + score, 0) / freshnessScores.length;
  }

  /**
   * Calculate completeness score
   */
  private calculateCompleteness(contexts: ContextItem[], request: PrefetchRequest): number {
    const requiredCount = request.requiredContext.length;
    const optionalCount = request.optionalContext.length;

    if (requiredCount === 0 && optionalCount === 0) return 1.0;

    const foundRequired = request.requiredContext.filter(req =>
      contexts.some(ctx => ctx.content.toLowerCase().includes(req.toLowerCase()))
    ).length;

    const foundOptional = request.optionalContext.filter(opt =>
      contexts.some(ctx => ctx.content.toLowerCase().includes(opt.toLowerCase()))
    ).length;

    const requiredScore = requiredCount > 0 ? foundRequired / requiredCount : 1.0;
    const optionalScore = optionalCount > 0 ? foundOptional / optionalCount : 1.0;

    // Weight required context more heavily
    return (requiredScore * 0.8) + (optionalScore * 0.2);
  }

  /**
   * Extract keywords from text
   */
  private extractKeywords(text: string): string[] {
    // Simple keyword extraction - can be enhanced with NLP
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);

    // Remove common stop words
    const stopWords = new Set(['this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other']);

    return words.filter(word => !stopWords.has(word));
  }

  /**
   * Extract terms from text using project dictionary
   */
  private async extractTermsFromText(text: string): Promise<string[]> {
    const words = this.extractKeywords(text);
    const terms: string[] = [];

    for (const word of words) {
      const searchResults = await this.projectDictionary.searchTerms(word, { fuzzy: true });
      terms.push(...searchResults.map(result => result.term));
    }

    return [...new Set(terms)];
  }

  /**
   * Estimate token count for text
   */
  private estimateTokens(text: string): number {
    // Rough estimation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  /**
   * Update processing statistics
   */
  private updateProcessingStats(processingTime: number, relevanceScore: number, confidence: number, tokens: number): void {
    // Update averages using exponential moving average
    const alpha = 0.1;

    this.stats.averageProcessingTime = this.stats.averageProcessingTime * (1 - alpha) + processingTime * alpha;
    this.stats.averageRelevanceScore = this.stats.averageRelevanceScore * (1 - alpha) + relevanceScore * alpha;
    this.stats.averageConfidence = this.stats.averageConfidence * (1 - alpha) + confidence * alpha;
    this.stats.totalTokensGenerated += tokens;
  }

  /**
   * Process the prefetch queue
   */
  private async processQueue(): Promise<void> {
    const activeCount = this.activeRequests.size;
    const availableSlots = this.config.maxConcurrentRequests - activeCount;

    if (availableSlots <= 0 || this.processingQueue.length === 0) {
      return;
    }

    // Sort queue by priority
    this.processingQueue.sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    // Process up to available slots
    const toProcess = this.processingQueue.splice(0, availableSlots);

    for (const request of toProcess) {
      this.processRequest(request).catch(error => {
        console.error(`Failed to process prefetch request ${request.id}:`, error);
        this.stats.failedRequests++;
      });
    }
  }

  /**
   * Process a single prefetch request
   */
  private async processRequest(request: PrefetchRequest): Promise<void> {
    try {
      console.log(`Processing prefetch request: ${request.taskType} for agent ${request.agentId}`);

      // Generate context (this will be cached for later retrieval)
      await this.generateContext(request);

    } catch (error) {
      console.error(`Error processing request ${request.id}:`, error);
      throw error;
    }
  }

  /**
   * Start predictive prefetching based on patterns
   */
  private startPredictivePrefetching(): void {
    if (!this.config.enablePredictivePrefetch) {
      return;
    }

    // Check for predictive prefetch opportunities every minute
    setInterval(() => {
      this.performPredictivePrefetch().catch(error => {
        console.error('Error in predictive prefetching:', error);
      });
    }, 60000);
  }

  /**
   * Perform predictive prefetching
   */
  private async performPredictivePrefetch(): Promise<void> {
    try {
      // Get upcoming tasks from task queue
      const upcomingTasks = await this.getUpcomingTasks();

      for (const task of upcomingTasks) {
        // Check if we should prefetch for this task
        if (this.shouldPrefetch(task)) {
          await this.createPredictivePrefetchRequest(task);
        }
      }
    } catch (error) {
      console.error('Error in predictive prefetching:', error);
    }
  }

  /**
   * Get upcoming tasks that might need prefetching
   */
  private async getUpcomingTasks(): Promise<AgentTask[]> {
    try {
      const allTasks = await this.taskQueue.getAllTasks();
      const now = Date.now();

      return allTasks.filter(task =>
        task.status === 'pending' &&
        (!task.assignedAt || task.assignedAt > now - this.config.prefetchAheadTime)
      );
    } catch (error) {
      console.error('Error getting upcoming tasks:', error);
      return [];
    }
  }

  /**
   * Determine if we should prefetch for a task
   */
  private shouldPrefetch(task: AgentTask): boolean {
    // Don't prefetch if already requested
    const existingRequest = Array.from(this.activeRequests.values())
      .find(req => req.taskType === task.type && req.agentId === task.assignedAgentId);

    if (existingRequest) {
      return false;
    }

    // Prefetch for high priority tasks
    if (task.priority === 'urgent' || task.priority === 'high') {
      return true;
    }

    // Prefetch if we have learned patterns for this task type
    const hasPatterns = this.taskPatterns.has(task.type);
    return hasPatterns;
  }

  /**
   * Create a predictive prefetch request
   */
  private async createPredictivePrefetchRequest(task: AgentTask): Promise<void> {
    if (!task.assignedAgentId) {
      return;
    }

    try {
      const agent = await this.agentRegistry.getAgent(task.assignedAgentId);
      if (!agent) {
        return;
      }

      const request: Omit<PrefetchRequest, 'id'> = {
        taskType: task.type,
        agentId: task.assignedAgentId,
        agentCapabilities: agent.capabilities,
        taskDescription: task.description,
        priority: task.priority,
        requiredContext: this.taskPatterns.get(task.type) || [],
        optionalContext: this.agentPreferences.get(task.assignedAgentId) || [],
        maxContextSize: this.config.defaultContextSize,
        deadline: task.assignedAt ? task.assignedAt + this.config.prefetchAheadTime : undefined,
        metadata: {
          predictive: true,
          originalTaskId: task.id
        }
      };

      await this.requestPrefetch(request);
      console.log(`Created predictive prefetch for task ${task.id}`);
    } catch (error) {
      console.error(`Failed to create predictive prefetch for task ${task.id}:`, error);
    }
  }

  /**
   * Initialize default patterns
   */
  private initializePatterns(): void {
    // Initialize with common task patterns
    this.taskPatterns.set('code-generation', [
      'function', 'class', 'component', 'interface', 'type',
      'import', 'export', 'async', 'await', 'promise'
    ]);

    this.taskPatterns.set('bug-fix', [
      'error', 'exception', 'try', 'catch', 'debug',
      'console.log', 'throw', 'stack trace'
    ]);

    this.taskPatterns.set('refactoring', [
      'extract', 'rename', 'move', 'optimize', 'clean',
      'pattern', 'architecture', 'design'
    ]);

    this.taskPatterns.set('testing', [
      'test', 'spec', 'mock', 'assert', 'expect',
      'describe', 'it', 'beforeEach', 'afterEach'
    ]);
  }

  /**
   * Validate prefetch request
   */
  private validatePrefetchRequest(request: PrefetchRequest): void {
    if (!request.taskType || !request.agentId) {
      throw new Error('Task type and agent ID are required');
    }

    if (!request.agentCapabilities || request.agentCapabilities.length === 0) {
      throw new Error('Agent capabilities are required');
    }

    if (request.maxContextSize <= 0) {
      throw new Error('Max context size must be positive');
    }
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `prefetch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique context ID
   */
  private generateContextId(): string {
    return `context-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const stored = await this.configStore.getGlobalSetting('contextPrefetcher.config');
      if (stored) {
        this.config = { ...this.config, ...stored };
      }
    } catch (error) {
      console.error('Failed to load prefetcher configuration:', error);
    }
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    try {
      await this.configStore.setGlobalSetting('contextPrefetcher.config', this.config);
    } catch (error) {
      console.error('Failed to save prefetcher configuration:', error);
    }
  }

  /**
   * Load learning data from storage
   */
  private async loadLearningData(): Promise<void> {
    try {
      const patterns = await this.configStore.getGlobalSetting('contextPrefetcher.taskPatterns');
      if (patterns) {
        this.taskPatterns = new Map(Object.entries(patterns));
      }

      const preferences = await this.configStore.getGlobalSetting('contextPrefetcher.agentPreferences');
      if (preferences) {
        this.agentPreferences = new Map(Object.entries(preferences));
      }
    } catch (error) {
      console.error('Failed to load learning data:', error);
    }
  }

  /**
   * Save learning data to storage
   */
  private async saveLearningData(): Promise<void> {
    try {
      await this.configStore.setGlobalSetting('contextPrefetcher.taskPatterns',
        Object.fromEntries(this.taskPatterns));

      await this.configStore.setGlobalSetting('contextPrefetcher.agentPreferences',
        Object.fromEntries(this.agentPreferences));
    } catch (error) {
      console.error('Failed to save learning data:', error);
    }
  }
}

// Global instance
let globalContextPrefetcher: ContextPrefetcher | null = null;

/**
 * Get the global context prefetcher instance
 */
export function getContextPrefetcher(): ContextPrefetcher {
  if (!globalContextPrefetcher) {
    globalContextPrefetcher = new ContextPrefetcher();
  }
  return globalContextPrefetcher;
}
