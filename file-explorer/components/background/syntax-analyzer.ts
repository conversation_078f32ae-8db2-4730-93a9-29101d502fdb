// components/background/syntax-analyzer.ts

import { BasicVectorDatabase, VectorDocument, SearchResult } from './vector-database';
import { getMonacoIntegration, EditorEvent, MonacoEditorInstance } from './monaco-integration';

export interface SyntaxAnalysisResult {
  id: string;
  editorId: string;
  filePath: string;
  language: string;
  timestamp: number;

  // Syntax analysis results
  tokens: SyntaxToken[];
  ast?: any; // Abstract Syntax Tree (if available)
  symbols: SymbolInfo[];
  dependencies: DependencyInfo[];

  // Semantic analysis
  semanticMatches: SearchResult[];
  similarPatterns: SearchResult[];
  suggestedImports: string[];

  // Quality metrics
  complexity: number;
  maintainabilityIndex: number;
  duplicateCodeScore: number;

  // Issues and suggestions
  issues: SyntaxIssue[];
  suggestions: CodeSuggestion[];
}

export interface SyntaxToken {
  type: string;
  value: string;
  startLine: number;
  startColumn: number;
  endLine: number;
  endColumn: number;
  scopes?: string[];
}

export interface SymbolInfo {
  name: string;
  type: 'function' | 'variable' | 'class' | 'interface' | 'type' | 'constant' | 'import' | 'export';
  line: number;
  column: number;
  scope: string;
  definition?: string;
  usages: Array<{ line: number; column: number }>;
}

export interface DependencyInfo {
  name: string;
  type: 'import' | 'require' | 'dynamic';
  source: string;
  line: number;
  isExternal: boolean;
  isUsed: boolean;
}

export interface SyntaxIssue {
  id: string;
  type: 'error' | 'warning' | 'info' | 'hint';
  severity: number;
  message: string;
  line: number;
  column: number;
  endLine?: number;
  endColumn?: number;
  code?: string;
  source: string;
  fixable: boolean;
  suggestedFix?: string;
}

export interface CodeSuggestion {
  id: string;
  type: 'refactor' | 'optimize' | 'pattern' | 'import' | 'naming' | 'style';
  priority: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  line: number;
  column: number;
  endLine?: number;
  endColumn?: number;
  replacement?: string;
  confidence: number;
}

export interface SyntaxAnalyzerConfig {
  enableSemanticAnalysis: boolean;
  enablePatternMatching: boolean;
  enableComplexityAnalysis: boolean;
  enableDuplicateDetection: boolean;
  maxSimilarityResults: number;
  minConfidenceThreshold: number;
  analysisTimeout: number;
}

export class SyntaxAnalyzer {
  private vectorDb: BasicVectorDatabase;
  private monacoIntegration = getMonacoIntegration();
  private analysisResults: Map<string, SyntaxAnalysisResult> = new Map();
  private analysisQueue: string[] = [];
  private isProcessing = false;
  private config: SyntaxAnalyzerConfig = {
    enableSemanticAnalysis: true,
    enablePatternMatching: true,
    enableComplexityAnalysis: true,
    enableDuplicateDetection: true,
    maxSimilarityResults: 10,
    minConfidenceThreshold: 0.3,
    analysisTimeout: 5000
  };

  constructor(vectorDb: BasicVectorDatabase) {
    this.vectorDb = vectorDb;
    this.setupEventListeners();
  }

  /**
   * Configure the syntax analyzer
   */
  configure(config: Partial<SyntaxAnalyzerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Analyze syntax for a specific editor
   */
  async analyzeEditor(editorId: string): Promise<SyntaxAnalysisResult | null> {
    const editor = this.monacoIntegration.getEditor(editorId);
    if (!editor) {
      console.warn(`Editor not found: ${editorId}`);
      return null;
    }

    try {
      const result = await this.performAnalysis(editor);
      this.analysisResults.set(editorId, result);
      return result;
    } catch (error) {
      console.error(`Error analyzing editor ${editorId}:`, error);
      return null;
    }
  }

  /**
   * Get cached analysis result
   */
  getAnalysisResult(editorId: string): SyntaxAnalysisResult | null {
    return this.analysisResults.get(editorId) || null;
  }

  /**
   * Get all analysis results
   */
  getAllAnalysisResults(): SyntaxAnalysisResult[] {
    return Array.from(this.analysisResults.values());
  }

  /**
   * Clear analysis results
   */
  clearAnalysisResults(editorId?: string): void {
    if (editorId) {
      this.analysisResults.delete(editorId);
    } else {
      this.analysisResults.clear();
    }
  }

  /**
   * Find similar code patterns across the codebase
   */
  async findSimilarPatterns(code: string, language: string): Promise<SearchResult[]> {
    if (!this.config.enablePatternMatching) {
      return [];
    }

    try {
      const results = await this.vectorDb.search(code, {
        limit: this.config.maxSimilarityResults,
        threshold: this.config.minConfidenceThreshold,
        filters: { language }
      });

      return results.filter(result => result.similarity > this.config.minConfidenceThreshold);
    } catch (error) {
      console.error('Error finding similar patterns:', error);
      return [];
    }
  }

  /**
   * Analyze code complexity
   */
  analyzeComplexity(content: string, language: string): number {
    if (!this.config.enableComplexityAnalysis) {
      return 0;
    }

    // Simple complexity analysis based on language patterns
    let complexity = 1; // Base complexity

    // Count control flow statements
    const controlFlowPatterns = {
      javascript: /\b(if|else|for|while|switch|case|try|catch|finally)\b/g,
      typescript: /\b(if|else|for|while|switch|case|try|catch|finally)\b/g,
      python: /\b(if|elif|else|for|while|try|except|finally|with)\b/g,
      java: /\b(if|else|for|while|switch|case|try|catch|finally)\b/g
    };

    const pattern = controlFlowPatterns[language as keyof typeof controlFlowPatterns];
    if (pattern) {
      const matches = content.match(pattern);
      complexity += matches ? matches.length : 0;
    }

    // Count nested functions/methods
    const functionPatterns = {
      javascript: /function\s+\w+|=>\s*{|\w+\s*:\s*function/g,
      typescript: /function\s+\w+|=>\s*{|\w+\s*:\s*function/g,
      python: /def\s+\w+/g,
      java: /\w+\s+\w+\s*\([^)]*\)\s*{/g
    };

    const funcPattern = functionPatterns[language as keyof typeof functionPatterns];
    if (funcPattern) {
      const matches = content.match(funcPattern);
      complexity += matches ? matches.length * 0.5 : 0;
    }

    return Math.round(complexity * 10) / 10;
  }

  /**
   * Calculate maintainability index
   */
  calculateMaintainabilityIndex(content: string, complexity: number): number {
    const lines = content.split('\n').length;
    const volume = content.length;

    // Simplified maintainability index calculation
    // Real implementation would use Halstead metrics
    const maintainabilityIndex = Math.max(0,
      171 - 5.2 * Math.log(volume) - 0.23 * complexity - 16.2 * Math.log(lines)
    );

    return Math.round(maintainabilityIndex * 10) / 10;
  }

  /**
   * Detect duplicate code
   */
  async detectDuplicateCode(content: string, language: string): Promise<number> {
    if (!this.config.enableDuplicateDetection) {
      return 0;
    }

    try {
      // Split content into chunks for duplicate detection
      const chunks = this.splitIntoChunks(content, 100); // 100 character chunks
      let duplicateScore = 0;
      let totalChunks = chunks.length;

      for (const chunk of chunks) {
        if (chunk.trim().length < 20) continue; // Skip very small chunks

        const results = await this.vectorDb.search(chunk, {
          limit: 5,
          threshold: 0.8,
          filters: { language }
        });

        // Count high-similarity matches as potential duplicates
        const duplicates = results.filter(result => result.similarity > 0.9);
        if (duplicates.length > 0) {
          duplicateScore += duplicates.length;
        }
      }

      return totalChunks > 0 ? Math.round((duplicateScore / totalChunks) * 100) / 100 : 0;
    } catch (error) {
      console.error('Error detecting duplicate code:', error);
      return 0;
    }
  }

  /**
   * Extract symbols from content
   */
  extractSymbols(content: string, language: string): SymbolInfo[] {
    const symbols: SymbolInfo[] = [];
    const lines = content.split('\n');

    // Language-specific symbol extraction patterns
    const patterns = {
      javascript: {
        function: /(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s+)?(?:\([^)]*\)\s*=>|\([^)]*\)\s*{)|(\w+)\s*:\s*(?:async\s+)?function)/g,
        variable: /(?:let|const|var)\s+(\w+)/g,
        class: /class\s+(\w+)/g,
        import: /import\s+(?:{[^}]+}|\w+)\s+from\s+['"]([^'"]+)['"]/g
      },
      typescript: {
        function: /(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s+)?(?:\([^)]*\)\s*=>|\([^)]*\)\s*{)|(\w+)\s*:\s*(?:async\s+)?function)/g,
        variable: /(?:let|const|var)\s+(\w+)/g,
        class: /class\s+(\w+)/g,
        interface: /interface\s+(\w+)/g,
        type: /type\s+(\w+)/g,
        import: /import\s+(?:{[^}]+}|\w+)\s+from\s+['"]([^'"]+)['"]/g
      }
    };

    const langPatterns = patterns[language as keyof typeof patterns];
    if (!langPatterns) return symbols;

    lines.forEach((line, lineIndex) => {
      Object.entries(langPatterns).forEach(([type, pattern]) => {
        let match;
        const regex = new RegExp(pattern.source, pattern.flags);

        while ((match = regex.exec(line)) !== null) {
          const name = match[1] || match[2] || match[3];
          if (name) {
            symbols.push({
              name,
              type: type as any,
              line: lineIndex + 1,
              column: match.index + 1,
              scope: 'global', // Simplified scope detection
              usages: []
            });
          }
        }
      });
    });

    return symbols;
  }

  /**
   * Generate code suggestions
   */
  generateSuggestions(analysis: SyntaxAnalysisResult): CodeSuggestion[] {
    const suggestions: CodeSuggestion[] = [];

    // Complexity suggestions
    if (analysis.complexity > 10) {
      suggestions.push({
        id: `complexity-${Date.now()}`,
        type: 'refactor',
        priority: 'high',
        title: 'High Complexity Detected',
        description: `This code has high complexity (${analysis.complexity}). Consider breaking it into smaller functions.`,
        line: 1,
        column: 1,
        confidence: 0.8
      });
    }

    // Maintainability suggestions
    if (analysis.maintainabilityIndex < 50) {
      suggestions.push({
        id: `maintainability-${Date.now()}`,
        type: 'refactor',
        priority: 'medium',
        title: 'Low Maintainability',
        description: `Maintainability index is ${analysis.maintainabilityIndex}. Consider refactoring for better readability.`,
        line: 1,
        column: 1,
        confidence: 0.7
      });
    }

    // Duplicate code suggestions
    if (analysis.duplicateCodeScore > 0.5) {
      suggestions.push({
        id: `duplicate-${Date.now()}`,
        type: 'refactor',
        priority: 'medium',
        title: 'Duplicate Code Detected',
        description: `Found potential duplicate code (score: ${analysis.duplicateCodeScore}). Consider extracting common functionality.`,
        line: 1,
        column: 1,
        confidence: 0.6
      });
    }

    return suggestions;
  }

  private setupEventListeners(): void {
    this.monacoIntegration.onEditorEvent((event: EditorEvent) => {
      if (event.type === 'content_changed') {
        // Queue analysis for content changes
        this.queueAnalysis(event.editorId);
      }
    });
  }

  private queueAnalysis(editorId: string): void {
    if (!this.analysisQueue.includes(editorId)) {
      this.analysisQueue.push(editorId);
    }

    // Process queue if not already processing
    if (!this.isProcessing) {
      this.processAnalysisQueue();
    }
  }

  private async processAnalysisQueue(): Promise<void> {
    if (this.isProcessing || this.analysisQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.analysisQueue.length > 0) {
      const editorId = this.analysisQueue.shift();
      if (editorId) {
        try {
          await this.analyzeEditor(editorId);
        } catch (error) {
          console.error(`Error processing analysis queue for ${editorId}:`, error);
        }
      }
    }

    this.isProcessing = false;
  }

  private async performAnalysis(editor: MonacoEditorInstance): Promise<SyntaxAnalysisResult> {
    const startTime = Date.now();
    const { id: editorId, filePath, language, content } = editor;

    // Initialize result
    const result: SyntaxAnalysisResult = {
      id: `analysis-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      editorId,
      filePath,
      language,
      timestamp: startTime,
      tokens: [],
      symbols: [],
      dependencies: [],
      semanticMatches: [],
      similarPatterns: [],
      suggestedImports: [],
      complexity: 0,
      maintainabilityIndex: 0,
      duplicateCodeScore: 0,
      issues: [],
      suggestions: []
    };

    try {
      // Extract symbols
      result.symbols = this.extractSymbols(content, language);

      // Analyze complexity
      result.complexity = this.analyzeComplexity(content, language);

      // Calculate maintainability index
      result.maintainabilityIndex = this.calculateMaintainabilityIndex(content, result.complexity);

      // Detect duplicate code
      if (this.config.enableDuplicateDetection) {
        result.duplicateCodeScore = await this.detectDuplicateCode(content, language);
      }

      // Find similar patterns
      if (this.config.enablePatternMatching && content.trim().length > 50) {
        result.similarPatterns = await this.findSimilarPatterns(content, language);
      }

      // Semantic analysis
      if (this.config.enableSemanticAnalysis) {
        result.semanticMatches = await this.performSemanticAnalysis(content, language);
      }

      // Generate suggestions
      result.suggestions = this.generateSuggestions(result);

      console.log(`Analysis completed for ${filePath} in ${Date.now() - startTime}ms`);
      return result;

    } catch (error) {
      console.error(`Error performing analysis for ${filePath}:`, error);

      // Return partial result with error
      result.issues.push({
        id: `error-${Date.now()}`,
        type: 'error',
        severity: 1,
        message: `Analysis failed: ${error instanceof Error ? error.message : String(error)}`,
        line: 1,
        column: 1,
        code: 'ANALYSIS_ERROR',
        source: 'syntax-analyzer',
        fixable: false
      });

      return result;
    }
  }

  private async performSemanticAnalysis(content: string, language: string): Promise<SearchResult[]> {
    try {
      // Extract meaningful code snippets for semantic analysis
      const codeSnippets = this.extractCodeSnippets(content, language);
      const semanticMatches: SearchResult[] = [];

      for (const snippet of codeSnippets) {
        if (snippet.length > 20) { // Only analyze meaningful snippets
          const matches = await this.vectorDb.search(snippet, {
            limit: 3,
            threshold: this.config.minConfidenceThreshold,
            filters: { language }
          });

          semanticMatches.push(...matches);
        }
      }

      // Remove duplicates and sort by similarity
      const uniqueMatches = semanticMatches
        .filter((match, index, array) =>
          array.findIndex(m => m.document.id === match.document.id) === index
        )
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, this.config.maxSimilarityResults);

      return uniqueMatches;
    } catch (error) {
      console.error('Error in semantic analysis:', error);
      return [];
    }
  }

  private extractCodeSnippets(content: string, language: string): string[] {
    const snippets: string[] = [];
    const lines = content.split('\n');

    // Extract function bodies, class definitions, etc.
    let currentSnippet = '';
    let braceCount = 0;
    let inFunction = false;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Skip empty lines and comments
      if (!trimmedLine || trimmedLine.startsWith('//') || trimmedLine.startsWith('/*')) {
        continue;
      }

      // Detect function/method/class starts
      if (this.isFunctionStart(trimmedLine, language)) {
        if (currentSnippet) {
          snippets.push(currentSnippet.trim());
        }
        currentSnippet = line + '\n';
        inFunction = true;
        braceCount = (line.match(/{/g) || []).length - (line.match(/}/g) || []).length;
      } else if (inFunction) {
        currentSnippet += line + '\n';
        braceCount += (line.match(/{/g) || []).length - (line.match(/}/g) || []).length;

        if (braceCount <= 0) {
          snippets.push(currentSnippet.trim());
          currentSnippet = '';
          inFunction = false;
        }
      } else {
        // Collect standalone statements
        if (trimmedLine.length > 20) {
          snippets.push(trimmedLine);
        }
      }
    }

    // Add remaining snippet
    if (currentSnippet.trim()) {
      snippets.push(currentSnippet.trim());
    }

    return snippets;
  }

  private isFunctionStart(line: string, language: string): boolean {
    const patterns = {
      javascript: /(?:function\s+\w+|const\s+\w+\s*=\s*(?:async\s+)?\(|class\s+\w+)/,
      typescript: /(?:function\s+\w+|const\s+\w+\s*=\s*(?:async\s+)?\(|class\s+\w+|interface\s+\w+)/,
      python: /(?:def\s+\w+|class\s+\w+)/,
      java: /(?:public|private|protected)?\s*(?:static\s+)?(?:class\s+\w+|\w+\s+\w+\s*\()/
    };

    const pattern = patterns[language as keyof typeof patterns];
    return pattern ? pattern.test(line) : false;
  }

  private splitIntoChunks(content: string, chunkSize: number): string[] {
    const chunks: string[] = [];
    const lines = content.split('\n');
    let currentChunk = '';

    for (const line of lines) {
      if (currentChunk.length + line.length > chunkSize) {
        if (currentChunk.trim()) {
          chunks.push(currentChunk.trim());
        }
        currentChunk = line + '\n';
      } else {
        currentChunk += line + '\n';
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }
}

// Singleton instance
let syntaxAnalyzer: SyntaxAnalyzer | null = null;

export function getSyntaxAnalyzer(vectorDb?: BasicVectorDatabase): SyntaxAnalyzer {
  if (!syntaxAnalyzer) {
    if (!vectorDb) {
      throw new Error('Vector database is required to initialize SyntaxAnalyzer');
    }
    syntaxAnalyzer = new SyntaxAnalyzer(vectorDb);
  }
  return syntaxAnalyzer;
}

export function shutdownSyntaxAnalyzer(): void {
  if (syntaxAnalyzer) {
    syntaxAnalyzer.clearAnalysisResults();
    syntaxAnalyzer = null;
  }
}
