// components/background/project-dictionary.ts

export interface DictionaryTerm {
  id: string;
  term: string;
  definition: string;
  category: 'domain' | 'technical' | 'business' | 'ui' | 'api' | 'database' | 'custom';
  aliases: string[];
  relatedTerms: string[]; // term IDs
  examples: string[];
  usageContext: string[];
  namingPattern?: string; // regex pattern for naming
  caseStyle?: 'camelCase' | 'PascalCase' | 'kebab-case' | 'snake_case' | 'UPPER_SNAKE_CASE';
  projectId: string;
  createdAt: number;
  updatedAt: number;
  createdBy: string;
  tags: string[];
  deprecated?: boolean;
  deprecationReason?: string;
  replacedBy?: string; // term ID
}

export interface NamingRule {
  id: string;
  name: string;
  description: string;
  pattern: string; // regex pattern
  category: 'file' | 'variable' | 'function' | 'class' | 'interface' | 'type' | 'constant' | 'component';
  severity: 'error' | 'warning' | 'info';
  autoFix?: boolean;
  suggestion?: string; // template for suggestions
  projectId: string;
  enabled: boolean;
  createdAt: number;
  updatedAt: number;
}

export interface ConsistencyViolation {
  id: string;
  type: 'naming' | 'terminology' | 'pattern' | 'convention';
  severity: 'error' | 'warning' | 'info';
  message: string;
  filePath: string;
  lineNumber?: number;
  columnNumber?: number;
  actualValue: string;
  expectedValue?: string;
  suggestedFix?: string;
  ruleId?: string;
  termId?: string;
  projectId: string;
  detectedAt: number;
  resolved: boolean;
  resolvedAt?: number;
  resolvedBy?: string;
}

export interface DictionaryStats {
  totalTerms: number;
  termsByCategory: Record<string, number>;
  totalRules: number;
  activeRules: number;
  totalViolations: number;
  unresolvedViolations: number;
  violationsBySeverity: Record<string, number>;
  lastScanTime?: number;
  scanCoverage: number; // percentage of files scanned
}

export interface TermRelationship {
  id: string;
  fromTermId: string;
  toTermId: string;
  relationshipType: 'synonym' | 'antonym' | 'parent' | 'child' | 'related' | 'implements' | 'extends' | 'uses' | 'contains';
  strength: number; // 0-1, how strong the relationship is
  bidirectional: boolean;
  projectId: string;
  createdAt: number;
}

export interface TermSearchQuery {
  query: string;
  categories?: string[];
  tags?: string[];
  includeAliases?: boolean;
  includeDefinitions?: boolean;
  includeExamples?: boolean;
  includeDeprecated?: boolean;
  fuzzyMatch?: boolean;
  limit?: number;
}

export interface TermSearchResult {
  term: DictionaryTerm;
  score: number;
  matchType: 'exact' | 'alias' | 'definition' | 'example' | 'fuzzy';
  matchedText: string;
  relatedTerms: DictionaryTerm[];
}

export class ProjectDictionary {
  private terms: Map<string, DictionaryTerm> = new Map();
  private rules: Map<string, NamingRule> = new Map();
  private violations: Map<string, ConsistencyViolation> = new Map();
  private relationships: Map<string, TermRelationship> = new Map();
  private termIndex: Map<string, Set<string>> = new Map(); // word -> term IDs
  private stats: DictionaryStats = {
    totalTerms: 0,
    termsByCategory: {},
    totalRules: 0,
    activeRules: 0,
    totalViolations: 0,
    unresolvedViolations: 0,
    violationsBySeverity: {},
    scanCoverage: 0
  };

  constructor(private projectId: string) {
    this.initializeDefaultRules();
  }

  /**
   * Add a new term to the dictionary
   */
  async addTerm(term: Omit<DictionaryTerm, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const termId = this.generateTermId();
    const now = Date.now();

    const fullTerm: DictionaryTerm = {
      ...term,
      id: termId,
      createdAt: now,
      updatedAt: now
    };

    // Validate term
    this.validateTerm(fullTerm);

    // Check for duplicates
    const existing = this.findTermByName(fullTerm.term);
    if (existing) {
      throw new Error(`Term "${fullTerm.term}" already exists`);
    }

    // Store term
    this.terms.set(termId, fullTerm);

    // Update search index
    this.updateTermIndex(fullTerm);

    // Update statistics
    this.updateStats();

    console.log(`Term added to dictionary: ${fullTerm.term} (${termId})`);
    return termId;
  }

  /**
   * Update an existing term
   */
  async updateTerm(termId: string, updates: Partial<DictionaryTerm>): Promise<boolean> {
    const term = this.terms.get(termId);
    if (!term) {
      return false;
    }

    // Remove from old index
    this.removeFromTermIndex(term);

    // Apply updates
    const updatedTerm: DictionaryTerm = {
      ...term,
      ...updates,
      id: termId, // Ensure ID doesn't change
      updatedAt: Date.now()
    };

    // Validate updated term
    this.validateTerm(updatedTerm);

    // Store updated term
    this.terms.set(termId, updatedTerm);

    // Update search index
    this.updateTermIndex(updatedTerm);

    // Update statistics
    this.updateStats();

    console.log(`Term updated: ${updatedTerm.term} (${termId})`);
    return true;
  }

  /**
   * Remove a term from the dictionary
   */
  async removeTerm(termId: string): Promise<boolean> {
    const term = this.terms.get(termId);
    if (!term) {
      return false;
    }

    // Remove from search index
    this.removeFromTermIndex(term);

    // Remove relationships
    this.removeTermRelationships(termId);

    // Remove term
    this.terms.delete(termId);

    // Update statistics
    this.updateStats();

    console.log(`Term removed: ${term.term} (${termId})`);
    return true;
  }

  /**
   * Add a naming rule
   */
  async addNamingRule(rule: Omit<NamingRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const ruleId = this.generateRuleId();
    const now = Date.now();

    const fullRule: NamingRule = {
      ...rule,
      id: ruleId,
      createdAt: now,
      updatedAt: now
    };

    // Validate rule pattern
    try {
      new RegExp(fullRule.pattern);
    } catch (error) {
      throw new Error(`Invalid regex pattern: ${fullRule.pattern}`);
    }

    this.rules.set(ruleId, fullRule);
    this.updateStats();

    console.log(`Naming rule added: ${fullRule.name} (${ruleId})`);
    return ruleId;
  }

  /**
   * Search for terms
   */
  searchTerms(query: TermSearchQuery): TermSearchResult[] {
    const results: TermSearchResult[] = [];
    const searchTerms = query.query.toLowerCase().split(/\s+/);

    for (const term of this.terms.values()) {
      // Skip if not in requested categories
      if (query.categories && !query.categories.includes(term.category)) {
        continue;
      }

      // Skip if not matching tags
      if (query.tags && !query.tags.some(tag => term.tags.includes(tag))) {
        continue;
      }

      // Skip deprecated terms unless explicitly requested
      if (term.deprecated && !query.includeDeprecated) {
        continue;
      }

      // Calculate match score
      const matchResult = this.calculateTermMatch(term, searchTerms, query);
      if (matchResult.score > 0) {
        const relatedTerms = this.getRelatedTerms(term.id);
        results.push({
          term,
          score: matchResult.score,
          matchType: matchResult.matchType,
          matchedText: matchResult.matchedText,
          relatedTerms
        });
      }
    }

    // Sort by score (descending)
    results.sort((a, b) => b.score - a.score);

    // Apply limit
    if (query.limit && query.limit > 0) {
      return results.slice(0, query.limit);
    }

    return results;
  }

  /**
   * Get term by ID
   */
  getTerm(termId: string): DictionaryTerm | undefined {
    return this.terms.get(termId);
  }

  /**
   * Get all terms
   */
  getAllTerms(): DictionaryTerm[] {
    return Array.from(this.terms.values());
  }

  /**
   * Get terms by category
   */
  getTermsByCategory(category: string): DictionaryTerm[] {
    return Array.from(this.terms.values()).filter(term => term.category === category);
  }

  /**
   * Get naming rules
   */
  getNamingRules(): NamingRule[] {
    return Array.from(this.rules.values());
  }

  /**
   * Get active naming rules
   */
  getActiveNamingRules(): NamingRule[] {
    return Array.from(this.rules.values()).filter(rule => rule.enabled);
  }

  /**
   * Get dictionary statistics
   */
  getStats(): DictionaryStats {
    return { ...this.stats };
  }

  // Private methods will be added in the next chunk
  private generateTermId(): string {
    return `term-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRuleId(): string {
    return `rule-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private validateTerm(term: DictionaryTerm): void {
    if (!term.term || term.term.trim() === '') {
      throw new Error('Term name is required');
    }

    if (!term.definition || term.definition.trim() === '') {
      throw new Error('Term definition is required');
    }

    if (term.namingPattern) {
      try {
        new RegExp(term.namingPattern);
      } catch (error) {
        throw new Error(`Invalid naming pattern: ${term.namingPattern}`);
      }
    }
  }

  private findTermByName(name: string): DictionaryTerm | undefined {
    return Array.from(this.terms.values()).find(term =>
      term.term.toLowerCase() === name.toLowerCase() ||
      term.aliases.some(alias => alias.toLowerCase() === name.toLowerCase())
    );
  }

  private updateTermIndex(term: DictionaryTerm): void {
    // Index term name
    const words = this.extractWords(term.term);
    words.forEach(word => {
      if (!this.termIndex.has(word)) {
        this.termIndex.set(word, new Set());
      }
      this.termIndex.get(word)!.add(term.id);
    });

    // Index aliases
    term.aliases.forEach(alias => {
      const aliasWords = this.extractWords(alias);
      aliasWords.forEach(word => {
        if (!this.termIndex.has(word)) {
          this.termIndex.set(word, new Set());
        }
        this.termIndex.get(word)!.add(term.id);
      });
    });
  }

  private removeFromTermIndex(term: DictionaryTerm): void {
    // Remove from term name index
    const words = this.extractWords(term.term);
    words.forEach(word => {
      const termIds = this.termIndex.get(word);
      if (termIds) {
        termIds.delete(term.id);
        if (termIds.size === 0) {
          this.termIndex.delete(word);
        }
      }
    });

    // Remove from aliases index
    term.aliases.forEach(alias => {
      const aliasWords = this.extractWords(alias);
      aliasWords.forEach(word => {
        const termIds = this.termIndex.get(word);
        if (termIds) {
          termIds.delete(term.id);
          if (termIds.size === 0) {
            this.termIndex.delete(word);
          }
        }
      });
    });
  }

  private extractWords(text: string): string[] {
    return text.toLowerCase()
      .replace(/[^a-z0-9\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  /**
   * Add a relationship between terms
   */
  async addTermRelationship(
    fromTermId: string,
    toTermId: string,
    relationshipType: TermRelationship['relationshipType'],
    strength: number = 1.0,
    bidirectional: boolean = false
  ): Promise<string> {
    const relationshipId = this.generateRelationshipId();

    const relationship: TermRelationship = {
      id: relationshipId,
      fromTermId,
      toTermId,
      relationshipType,
      strength: Math.max(0, Math.min(1, strength)),
      bidirectional,
      projectId: this.projectId,
      createdAt: Date.now()
    };

    this.relationships.set(relationshipId, relationship);

    console.log(`Term relationship added: ${fromTermId} -> ${toTermId} (${relationshipType})`);
    return relationshipId;
  }

  /**
   * Validate naming against rules
   */
  validateNaming(name: string, category: NamingRule['category'], filePath?: string): ConsistencyViolation[] {
    const violations: ConsistencyViolation[] = [];
    const activeRules = this.getActiveNamingRules().filter(rule => rule.category === category);

    for (const rule of activeRules) {
      try {
        const regex = new RegExp(rule.pattern);
        if (!regex.test(name)) {
          const violationId = this.generateViolationId();

          const violation: ConsistencyViolation = {
            id: violationId,
            type: 'naming',
            severity: rule.severity,
            message: `Name "${name}" does not match pattern for ${category}: ${rule.description}`,
            filePath: filePath || 'unknown',
            actualValue: name,
            expectedValue: rule.suggestion,
            suggestedFix: this.generateSuggestion(name, rule),
            ruleId: rule.id,
            projectId: this.projectId,
            detectedAt: Date.now(),
            resolved: false
          };

          violations.push(violation);
          this.violations.set(violationId, violation);
        }
      } catch (error) {
        console.error(`Error validating rule ${rule.id}:`, error);
      }
    }

    this.updateStats();
    return violations;
  }

  /**
   * Get suggestions for a term
   */
  getSuggestions(partialTerm: string, category?: string): string[] {
    const suggestions: string[] = [];
    const searchQuery = partialTerm.toLowerCase();

    // Find terms that start with the partial term
    for (const term of this.terms.values()) {
      if (category && term.category !== category) {
        continue;
      }

      if (term.term.toLowerCase().startsWith(searchQuery)) {
        suggestions.push(term.term);
      }

      // Check aliases
      for (const alias of term.aliases) {
        if (alias.toLowerCase().startsWith(searchQuery)) {
          suggestions.push(alias);
        }
      }
    }

    // Sort by length (shorter first) and remove duplicates
    return Array.from(new Set(suggestions)).sort((a, b) => a.length - b.length);
  }

  /**
   * Scan codebase for consistency violations
   */
  async scanForViolations(filePaths: string[]): Promise<ConsistencyViolation[]> {
    const violations: ConsistencyViolation[] = [];

    // This would integrate with the actual file system and AST parsing
    // For now, return a mock implementation
    console.log(`Scanning ${filePaths.length} files for consistency violations...`);

    this.stats.lastScanTime = Date.now();
    this.stats.scanCoverage = 100; // Mock 100% coverage

    return violations;
  }

  /**
   * Resolve a violation
   */
  async resolveViolation(violationId: string, resolvedBy: string): Promise<boolean> {
    const violation = this.violations.get(violationId);
    if (!violation) {
      return false;
    }

    violation.resolved = true;
    violation.resolvedAt = Date.now();
    violation.resolvedBy = resolvedBy;

    this.updateStats();
    return true;
  }

  /**
   * Get unresolved violations
   */
  getUnresolvedViolations(): ConsistencyViolation[] {
    return Array.from(this.violations.values()).filter(v => !v.resolved);
  }

  /**
   * Export dictionary data
   */
  exportDictionary(): {
    terms: DictionaryTerm[];
    rules: NamingRule[];
    relationships: TermRelationship[];
    stats: DictionaryStats;
  } {
    return {
      terms: Array.from(this.terms.values()),
      rules: Array.from(this.rules.values()),
      relationships: Array.from(this.relationships.values()),
      stats: this.getStats()
    };
  }

  /**
   * Import dictionary data
   */
  async importDictionary(data: {
    terms?: DictionaryTerm[];
    rules?: NamingRule[];
    relationships?: TermRelationship[];
  }): Promise<void> {
    if (data.terms) {
      for (const term of data.terms) {
        this.terms.set(term.id, term);
        this.updateTermIndex(term);
      }
    }

    if (data.rules) {
      for (const rule of data.rules) {
        this.rules.set(rule.id, rule);
      }
    }

    if (data.relationships) {
      for (const relationship of data.relationships) {
        this.relationships.set(relationship.id, relationship);
      }
    }

    this.updateStats();
    console.log('Dictionary data imported successfully');
  }

  /**
   * Clear all data
   */
  clear(): void {
    this.terms.clear();
    this.rules.clear();
    this.violations.clear();
    this.relationships.clear();
    this.termIndex.clear();
    this.updateStats();
    console.log('Dictionary cleared');
  }

  // Private implementation methods
  private generateRelationshipId(): string {
    return `rel-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateViolationId(): string {
    return `viol-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSuggestion(name: string, rule: NamingRule): string | undefined {
    if (!rule.suggestion) {
      return undefined;
    }

    // Simple template replacement
    return rule.suggestion.replace(/\{name\}/g, name);
  }

  private initializeDefaultRules(): void {
    const defaultRules: Omit<NamingRule, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'React Component Naming',
        description: 'React components should use PascalCase',
        pattern: '^[A-Z][a-zA-Z0-9]*$',
        category: 'component',
        severity: 'error',
        autoFix: true,
        suggestion: 'Convert to PascalCase: {name}',
        projectId: this.projectId,
        enabled: true
      },
      {
        name: 'Variable Naming',
        description: 'Variables should use camelCase',
        pattern: '^[a-z][a-zA-Z0-9]*$',
        category: 'variable',
        severity: 'warning',
        autoFix: true,
        suggestion: 'Convert to camelCase: {name}',
        projectId: this.projectId,
        enabled: true
      },
      {
        name: 'Function Naming',
        description: 'Functions should use camelCase',
        pattern: '^[a-z][a-zA-Z0-9]*$',
        category: 'function',
        severity: 'warning',
        autoFix: true,
        suggestion: 'Convert to camelCase: {name}',
        projectId: this.projectId,
        enabled: true
      },
      {
        name: 'Class Naming',
        description: 'Classes should use PascalCase',
        pattern: '^[A-Z][a-zA-Z0-9]*$',
        category: 'class',
        severity: 'error',
        autoFix: true,
        suggestion: 'Convert to PascalCase: {name}',
        projectId: this.projectId,
        enabled: true
      },
      {
        name: 'Interface Naming',
        description: 'Interfaces should use PascalCase and may start with I',
        pattern: '^I?[A-Z][a-zA-Z0-9]*$',
        category: 'interface',
        severity: 'warning',
        autoFix: false,
        suggestion: 'Use PascalCase, optionally prefix with I: {name}',
        projectId: this.projectId,
        enabled: true
      },
      {
        name: 'Type Naming',
        description: 'Types should use PascalCase',
        pattern: '^[A-Z][a-zA-Z0-9]*$',
        category: 'type',
        severity: 'warning',
        autoFix: true,
        suggestion: 'Convert to PascalCase: {name}',
        projectId: this.projectId,
        enabled: true
      },
      {
        name: 'Constant Naming',
        description: 'Constants should use UPPER_SNAKE_CASE',
        pattern: '^[A-Z][A-Z0-9_]*$',
        category: 'constant',
        severity: 'info',
        autoFix: true,
        suggestion: 'Convert to UPPER_SNAKE_CASE: {name}',
        projectId: this.projectId,
        enabled: true
      },
      {
        name: 'File Naming',
        description: 'Files should use kebab-case',
        pattern: '^[a-z][a-z0-9-]*\\.[a-z]+$',
        category: 'file',
        severity: 'info',
        autoFix: false,
        suggestion: 'Use kebab-case: {name}',
        projectId: this.projectId,
        enabled: true
      }
    ];

    // Add default rules
    for (const ruleData of defaultRules) {
      const ruleId = this.generateRuleId();
      const rule: NamingRule = {
        ...ruleData,
        id: ruleId,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
      this.rules.set(ruleId, rule);
    }
  }

  private updateStats(): void {
    const terms = Array.from(this.terms.values());
    const violations = Array.from(this.violations.values());

    this.stats.totalTerms = terms.length;

    // Count terms by category
    this.stats.termsByCategory = {};
    terms.forEach(term => {
      this.stats.termsByCategory[term.category] = (this.stats.termsByCategory[term.category] || 0) + 1;
    });

    this.stats.totalRules = this.rules.size;
    this.stats.activeRules = Array.from(this.rules.values()).filter(rule => rule.enabled).length;

    this.stats.totalViolations = violations.length;
    this.stats.unresolvedViolations = violations.filter(v => !v.resolved).length;

    // Count violations by severity
    this.stats.violationsBySeverity = {};
    violations.forEach(violation => {
      this.stats.violationsBySeverity[violation.severity] = (this.stats.violationsBySeverity[violation.severity] || 0) + 1;
    });
  }

  private calculateTermMatch(
    term: DictionaryTerm,
    searchTerms: string[],
    query: TermSearchQuery
  ): { score: number; matchType: 'exact' | 'alias' | 'definition' | 'example' | 'fuzzy'; matchedText: string } {
    let bestScore = 0;
    let bestMatchType: 'exact' | 'alias' | 'definition' | 'example' | 'fuzzy' = 'exact';
    let bestMatchedText = '';

    const termLower = term.term.toLowerCase();
    const queryLower = query.query.toLowerCase();

    // Exact term match
    if (termLower === queryLower) {
      return { score: 100, matchType: 'exact', matchedText: term.term };
    }

    // Partial term match
    if (termLower.includes(queryLower)) {
      const score = (queryLower.length / termLower.length) * 80;
      if (score > bestScore) {
        bestScore = score;
        bestMatchType = 'exact';
        bestMatchedText = term.term;
      }
    }

    // Alias matches
    if (query.includeAliases !== false) {
      for (const alias of term.aliases) {
        const aliasLower = alias.toLowerCase();
        if (aliasLower === queryLower) {
          return { score: 95, matchType: 'alias', matchedText: alias };
        }
        if (aliasLower.includes(queryLower)) {
          const score = (queryLower.length / aliasLower.length) * 75;
          if (score > bestScore) {
            bestScore = score;
            bestMatchType = 'alias';
            bestMatchedText = alias;
          }
        }
      }
    }

    // Definition matches
    if (query.includeDefinitions !== false) {
      const definitionLower = term.definition.toLowerCase();
      if (definitionLower.includes(queryLower)) {
        const score = (queryLower.length / definitionLower.length) * 60;
        if (score > bestScore) {
          bestScore = score;
          bestMatchType = 'definition';
          bestMatchedText = term.definition.substring(0, 100) + '...';
        }
      }
    }

    // Example matches
    if (query.includeExamples !== false) {
      for (const example of term.examples) {
        const exampleLower = example.toLowerCase();
        if (exampleLower.includes(queryLower)) {
          const score = (queryLower.length / exampleLower.length) * 50;
          if (score > bestScore) {
            bestScore = score;
            bestMatchType = 'example';
            bestMatchedText = example.substring(0, 100) + '...';
          }
        }
      }
    }

    // Fuzzy matching
    if (query.fuzzyMatch !== false && bestScore < 30) {
      const fuzzyScore = this.calculateFuzzyScore(termLower, queryLower);
      if (fuzzyScore > bestScore) {
        bestScore = fuzzyScore;
        bestMatchType = 'fuzzy';
        bestMatchedText = term.term;
      }
    }

    return { score: bestScore, matchType: bestMatchType, matchedText: bestMatchedText };
  }

  private calculateFuzzyScore(text: string, query: string): number {
    // Simple fuzzy matching based on character overlap
    const textChars = new Set(text.split(''));
    const queryChars = new Set(query.split(''));

    let overlap = 0;
    for (const char of queryChars) {
      if (textChars.has(char)) {
        overlap++;
      }
    }

    return (overlap / Math.max(textChars.size, queryChars.size)) * 30;
  }

  private getRelatedTerms(termId: string): DictionaryTerm[] {
    const relatedTermIds = new Set<string>();

    // Find relationships where this term is involved
    for (const relationship of this.relationships.values()) {
      if (relationship.fromTermId === termId) {
        relatedTermIds.add(relationship.toTermId);
      }
      if (relationship.bidirectional && relationship.toTermId === termId) {
        relatedTermIds.add(relationship.fromTermId);
      }
    }

    // Get the actual terms
    const relatedTerms: DictionaryTerm[] = [];
    for (const relatedTermId of relatedTermIds) {
      const term = this.terms.get(relatedTermId);
      if (term) {
        relatedTerms.push(term);
      }
    }

    return relatedTerms;
  }

  private removeTermRelationships(termId: string): void {
    const relationshipsToRemove: string[] = [];

    for (const [relationshipId, relationship] of this.relationships.entries()) {
      if (relationship.fromTermId === termId || relationship.toTermId === termId) {
        relationshipsToRemove.push(relationshipId);
      }
    }

    for (const relationshipId of relationshipsToRemove) {
      this.relationships.delete(relationshipId);
    }
  }
}

// Global project dictionary instances
const projectDictionaries = new Map<string, ProjectDictionary>();

export function getProjectDictionary(projectId: string): ProjectDictionary {
  if (!projectDictionaries.has(projectId)) {
    projectDictionaries.set(projectId, new ProjectDictionary(projectId));
  }
  return projectDictionaries.get(projectId)!;
}

export function clearProjectDictionary(projectId: string): void {
  const dictionary = projectDictionaries.get(projectId);
  if (dictionary) {
    dictionary.clear();
    projectDictionaries.delete(projectId);
  }
}

export function getAllProjectDictionaries(): Map<string, ProjectDictionary> {
  return new Map(projectDictionaries);
}