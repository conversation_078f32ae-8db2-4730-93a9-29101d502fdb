// components/background/task-queue.ts

export interface AgentTask {
  id: string;
  type: string;
  title: string;
  description: string;
  payload: any;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'pending' | 'assigned' | 'running' | 'completed' | 'failed' | 'cancelled';
  assignedAgentId?: string;
  requiredCapabilities: string[];
  estimatedDuration?: number; // in milliseconds
  maxRetries: number;
  currentRetries: number;
  createdAt: number;
  assignedAt?: number;
  startedAt?: number;
  completedAt?: number;
  result?: any;
  error?: string;
  metadata?: Record<string, any>;
  dependencies?: string[]; // task IDs that must complete first
  tags?: string[];
}

export interface AgentCapability {
  agentId: string;
  capabilities: string[];
  currentLoad: number; // 0-100 percentage
  maxConcurrentTasks: number;
  averageTaskDuration: number;
  successRate: number;
  lastActiveAt: number;
}

export interface TaskQueueStats {
  totalTasks: number;
  pendingTasks: number;
  runningTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageWaitTime: number;
  averageExecutionTime: number;
  throughputPerMinute: number;
  activeAgents: number;
}

export interface TaskFilter {
  status?: AgentTask['status'][];
  priority?: AgentTask['priority'][];
  assignedAgentId?: string;
  type?: string;
  tags?: string[];
  createdAfter?: number;
  createdBefore?: number;
}

export interface TaskAssignmentStrategy {
  name: string;
  assignTask: (task: AgentTask, availableAgents: AgentCapability[]) => string | null;
}

export class TaskQueue {
  private tasks: Map<string, AgentTask> = new Map();
  private agents: Map<string, AgentCapability> = new Map();
  private taskHistory: AgentTask[] = [];
  private maxHistorySize = 1000;
  private processingInterval: NodeJS.Timeout | null = null;
  private isProcessing = false;
  private stats: TaskQueueStats = {
    totalTasks: 0,
    pendingTasks: 0,
    runningTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    averageWaitTime: 0,
    averageExecutionTime: 0,
    throughputPerMinute: 0,
    activeAgents: 0
  };

  // Built-in assignment strategies
  private assignmentStrategies: Map<string, TaskAssignmentStrategy> = new Map();

  constructor() {
    this.initializeAssignmentStrategies();
    this.startProcessing();
  }

  /**
   * Add a new task to the queue
   */
  async addTask(task: Omit<AgentTask, 'id' | 'status' | 'currentRetries' | 'createdAt'>): Promise<string> {
    const taskId = this.generateTaskId();

    const fullTask: AgentTask = {
      ...task,
      id: taskId,
      status: 'pending',
      currentRetries: 0,
      createdAt: Date.now()
    };

    // Validate dependencies
    if (fullTask.dependencies) {
      for (const depId of fullTask.dependencies) {
        const depTask = this.tasks.get(depId);
        if (!depTask || depTask.status !== 'completed') {
          throw new Error(`Task dependency ${depId} not found or not completed`);
        }
      }
    }

    this.tasks.set(taskId, fullTask);
    this.updateStats();

    console.log(`Task added to queue: ${fullTask.title} (${taskId})`);
    return taskId;
  }

  /**
   * Register an agent with its capabilities
   */
  registerAgent(
    agentId: string,
    capabilities: string[],
    maxConcurrentTasks: number = 3
  ): void {
    const agentCapability: AgentCapability = {
      agentId,
      capabilities,
      currentLoad: 0,
      maxConcurrentTasks,
      averageTaskDuration: 0,
      successRate: 100,
      lastActiveAt: Date.now()
    };

    this.agents.set(agentId, agentCapability);
    this.updateStats();

    console.log(`Agent registered: ${agentId} with capabilities: ${capabilities.join(', ')}`);
  }

  /**
   * Unregister an agent
   */
  unregisterAgent(agentId: string): void {
    // Cancel all tasks assigned to this agent
    for (const task of this.tasks.values()) {
      if (task.assignedAgentId === agentId && task.status === 'assigned') {
        task.status = 'pending';
        task.assignedAgentId = undefined;
        task.assignedAt = undefined;
      }
    }

    this.agents.delete(agentId);
    this.updateStats();

    console.log(`Agent unregistered: ${agentId}`);
  }

  /**
   * Get task by ID
   */
  getTask(taskId: string): AgentTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * Get tasks with optional filtering
   */
  getTasks(filter?: TaskFilter): AgentTask[] {
    let tasks = Array.from(this.tasks.values());

    if (filter) {
      if (filter.status) {
        tasks = tasks.filter(task => filter.status!.includes(task.status));
      }
      if (filter.priority) {
        tasks = tasks.filter(task => filter.priority!.includes(task.priority));
      }
      if (filter.assignedAgentId) {
        tasks = tasks.filter(task => task.assignedAgentId === filter.assignedAgentId);
      }
      if (filter.type) {
        tasks = tasks.filter(task => task.type === filter.type);
      }
      if (filter.tags) {
        tasks = tasks.filter(task =>
          task.tags && filter.tags!.some(tag => task.tags!.includes(tag))
        );
      }
      if (filter.createdAfter) {
        tasks = tasks.filter(task => task.createdAt >= filter.createdAfter!);
      }
      if (filter.createdBefore) {
        tasks = tasks.filter(task => task.createdAt <= filter.createdBefore!);
      }
    }

    return tasks;
  }

  /**
   * Cancel a task
   */
  async cancelTask(taskId: string, reason?: string): Promise<boolean> {
    const task = this.tasks.get(taskId);
    if (!task) {
      return false;
    }

    if (task.status === 'completed' || task.status === 'cancelled') {
      return false;
    }

    task.status = 'cancelled';
    task.error = reason || 'Task cancelled';
    task.completedAt = Date.now();

    // Update agent load if task was assigned
    if (task.assignedAgentId) {
      this.updateAgentLoad(task.assignedAgentId, -1);
    }

    this.addToHistory(task);
    this.updateStats();

    console.log(`Task cancelled: ${taskId} - ${reason || 'No reason provided'}`);
    return true;
  }

  /**
   * Retry a failed task
   */
  async retryTask(taskId: string): Promise<boolean> {
    const task = this.tasks.get(taskId);
    if (!task || task.status !== 'failed') {
      return false;
    }

    if (task.currentRetries >= task.maxRetries) {
      console.log(`Task ${taskId} has exceeded max retries (${task.maxRetries})`);
      return false;
    }

    task.status = 'pending';
    task.assignedAgentId = undefined;
    task.assignedAt = undefined;
    task.startedAt = undefined;
    task.error = undefined;
    task.currentRetries++;

    this.updateStats();

    console.log(`Task retried: ${taskId} (attempt ${task.currentRetries + 1}/${task.maxRetries + 1})`);
    return true;
  }

  /**
   * Mark task as started
   */
  async startTask(taskId: string): Promise<boolean> {
    const task = this.tasks.get(taskId);
    if (!task || task.status !== 'assigned') {
      return false;
    }

    task.status = 'running';
    task.startedAt = Date.now();
    this.updateStats();

    console.log(`Task started: ${taskId}`);
    return true;
  }

  /**
   * Complete a task with result
   */
  async completeTask(taskId: string, result?: any): Promise<boolean> {
    const task = this.tasks.get(taskId);
    if (!task || task.status !== 'running') {
      return false;
    }

    task.status = 'completed';
    task.result = result;
    task.completedAt = Date.now();

    // Update agent statistics
    if (task.assignedAgentId) {
      this.updateAgentStats(task.assignedAgentId, task, true);
      this.updateAgentLoad(task.assignedAgentId, -1);
    }

    this.addToHistory(task);
    this.updateStats();

    console.log(`Task completed: ${taskId}`);
    return true;
  }

  /**
   * Fail a task with error
   */
  async failTask(taskId: string, error: string): Promise<boolean> {
    const task = this.tasks.get(taskId);
    if (!task || task.status !== 'running') {
      return false;
    }

    task.error = error;

    // Check if we should retry
    if (task.currentRetries < task.maxRetries) {
      task.status = 'pending';
      task.assignedAgentId = undefined;
      task.assignedAt = undefined;
      task.startedAt = undefined;
      task.currentRetries++;
      console.log(`Task failed, will retry: ${taskId} (attempt ${task.currentRetries + 1}/${task.maxRetries + 1})`);
    } else {
      task.status = 'failed';
      task.completedAt = Date.now();
      this.addToHistory(task);
      console.log(`Task failed permanently: ${taskId} - ${error}`);
    }

    // Update agent statistics
    if (task.assignedAgentId) {
      this.updateAgentStats(task.assignedAgentId, task, false);
      this.updateAgentLoad(task.assignedAgentId, -1);
    }

    this.updateStats();
    return true;
  }

  /**
   * Get queue statistics
   */
  getStats(): TaskQueueStats {
    return { ...this.stats };
  }

  /**
   * Get task history
   */
  getTaskHistory(limit?: number): AgentTask[] {
    const history = [...this.taskHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Get registered agents
   */
  getAgents(): AgentCapability[] {
    return Array.from(this.agents.values());
  }

  /**
   * Get agent by ID
   */
  getAgent(agentId: string): AgentCapability | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Clear completed tasks from queue
   */
  clearCompletedTasks(): number {
    let cleared = 0;
    for (const [taskId, task] of this.tasks.entries()) {
      if (task.status === 'completed' || task.status === 'cancelled') {
        this.tasks.delete(taskId);
        cleared++;
      }
    }

    this.updateStats();
    console.log(`Cleared ${cleared} completed tasks`);
    return cleared;
  }

  /**
   * Shutdown the task queue
   */
  shutdown(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    // Cancel all pending and assigned tasks
    for (const task of this.tasks.values()) {
      if (task.status === 'pending' || task.status === 'assigned') {
        task.status = 'cancelled';
        task.error = 'Task queue shutdown';
        task.completedAt = Date.now();
      }
    }

    this.tasks.clear();
    this.agents.clear();
    this.taskHistory = [];
    console.log('Task queue shutdown');
  }

  // Private implementation methods
  private generateTaskId(): string {
    return `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateStats(): void {
    const tasks = Array.from(this.tasks.values());

    this.stats.totalTasks = tasks.length + this.taskHistory.length;
    this.stats.pendingTasks = tasks.filter(t => t.status === 'pending').length;
    this.stats.runningTasks = tasks.filter(t => t.status === 'running').length;
    this.stats.completedTasks = tasks.filter(t => t.status === 'completed').length +
                               this.taskHistory.filter(t => t.status === 'completed').length;
    this.stats.failedTasks = tasks.filter(t => t.status === 'failed').length +
                            this.taskHistory.filter(t => t.status === 'failed').length;
    this.stats.activeAgents = this.agents.size;

    // Calculate average wait time
    const completedTasks = [...tasks, ...this.taskHistory].filter(t =>
      t.status === 'completed' && t.assignedAt && t.startedAt
    );

    if (completedTasks.length > 0) {
      this.stats.averageWaitTime = completedTasks.reduce((sum, task) =>
        sum + (task.startedAt! - task.assignedAt!), 0
      ) / completedTasks.length;

      this.stats.averageExecutionTime = completedTasks.reduce((sum, task) =>
        sum + (task.completedAt! - task.startedAt!), 0
      ) / completedTasks.length;
    }

    // Calculate throughput (tasks completed in last minute)
    const oneMinuteAgo = Date.now() - 60000;
    const recentCompletions = [...tasks, ...this.taskHistory].filter(t =>
      t.status === 'completed' && t.completedAt && t.completedAt > oneMinuteAgo
    );
    this.stats.throughputPerMinute = recentCompletions.length;
  }

  private updateAgentLoad(agentId: string, delta: number): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      const currentTasks = Array.from(this.tasks.values()).filter(t =>
        t.assignedAgentId === agentId && (t.status === 'assigned' || t.status === 'running')
      ).length;

      agent.currentLoad = Math.round((currentTasks / agent.maxConcurrentTasks) * 100);
      agent.lastActiveAt = Date.now();
    }
  }

  private updateAgentStats(agentId: string, task: AgentTask, success: boolean): void {
    const agent = this.agents.get(agentId);
    if (!agent || !task.startedAt || !task.completedAt) return;

    const duration = task.completedAt - task.startedAt;

    // Update average task duration (simple moving average)
    agent.averageTaskDuration = agent.averageTaskDuration === 0
      ? duration
      : (agent.averageTaskDuration + duration) / 2;

    // Update success rate (simple moving average)
    const currentSuccessRate = agent.successRate;
    agent.successRate = success
      ? Math.min(100, currentSuccessRate + (100 - currentSuccessRate) * 0.1)
      : Math.max(0, currentSuccessRate - currentSuccessRate * 0.1);
  }

  private addToHistory(task: AgentTask): void {
    this.taskHistory.push({ ...task });

    // Maintain history size limit
    if (this.taskHistory.length > this.maxHistorySize) {
      this.taskHistory = this.taskHistory.slice(-this.maxHistorySize);
    }
  }

  private initializeAssignmentStrategies(): void {
    // Load-balanced assignment strategy
    this.assignmentStrategies.set('load-balanced', {
      name: 'load-balanced',
      assignTask: (task: AgentTask, availableAgents: AgentCapability[]) => {
        const capableAgents = availableAgents.filter(agent =>
          task.requiredCapabilities.every(cap => agent.capabilities.includes(cap)) &&
          agent.currentLoad < 100
        );

        if (capableAgents.length === 0) return null;

        // Sort by current load (ascending)
        capableAgents.sort((a, b) => a.currentLoad - b.currentLoad);
        return capableAgents[0].agentId;
      }
    });

    // Performance-based assignment strategy
    this.assignmentStrategies.set('performance-based', {
      name: 'performance-based',
      assignTask: (task: AgentTask, availableAgents: AgentCapability[]) => {
        const capableAgents = availableAgents.filter(agent =>
          task.requiredCapabilities.every(cap => agent.capabilities.includes(cap)) &&
          agent.currentLoad < 100
        );

        if (capableAgents.length === 0) return null;

        // Sort by success rate and average duration (best performers first)
        capableAgents.sort((a, b) => {
          const scoreA = a.successRate - (a.averageTaskDuration / 1000); // Penalize slow agents
          const scoreB = b.successRate - (b.averageTaskDuration / 1000);
          return scoreB - scoreA;
        });

        return capableAgents[0].agentId;
      }
    });

    // Round-robin assignment strategy
    this.assignmentStrategies.set('round-robin', {
      name: 'round-robin',
      assignTask: (task: AgentTask, availableAgents: AgentCapability[]) => {
        const capableAgents = availableAgents.filter(agent =>
          task.requiredCapabilities.every(cap => agent.capabilities.includes(cap)) &&
          agent.currentLoad < 100
        );

        if (capableAgents.length === 0) return null;

        // Sort by last active time (least recently used first)
        capableAgents.sort((a, b) => a.lastActiveAt - b.lastActiveAt);
        return capableAgents[0].agentId;
      }
    });
  }

  private startProcessing(): void {
    // Process tasks every 100ms
    this.processingInterval = setInterval(() => {
      this.processTasks();
    }, 100);
  }

  private async processTasks(): Promise<void> {
    if (this.isProcessing) return;
    this.isProcessing = true;

    try {
      // Get pending tasks sorted by priority
      const pendingTasks = Array.from(this.tasks.values())
        .filter(task => task.status === 'pending')
        .sort((a, b) => {
          const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        });

      // Get available agents
      const availableAgents = Array.from(this.agents.values())
        .filter(agent => agent.currentLoad < 100);

      // Try to assign tasks to agents
      for (const task of pendingTasks) {
        const assignedAgentId = this.assignTask(task, availableAgents);
        if (assignedAgentId) {
          task.status = 'assigned';
          task.assignedAgentId = assignedAgentId;
          task.assignedAt = Date.now();

          this.updateAgentLoad(assignedAgentId, 1);

          console.log(`Task assigned: ${task.id} -> ${assignedAgentId}`);
        }
      }

      this.updateStats();
    } catch (error) {
      console.error('Error processing tasks:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private assignTask(task: AgentTask, availableAgents: AgentCapability[]): string | null {
    // Use load-balanced strategy by default
    const strategy = this.assignmentStrategies.get('load-balanced');
    if (!strategy) return null;

    return strategy.assignTask(task, availableAgents);
  }
}

// Global task queue instance
export const globalTaskQueue = new TaskQueue();
