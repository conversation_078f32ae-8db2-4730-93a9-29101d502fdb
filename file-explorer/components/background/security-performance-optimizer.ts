// components/background/security-performance-optimizer.ts
import { getConfigStoreBrowser } from './config-store-browser';

export interface SecurityConfig {
  enableApiKeyEncryption: boolean;
  enablePromptProtection: boolean;
  enableAccessControl: boolean;
  enableAuditLogging: boolean;
  encryptionAlgorithm: 'AES-256-GCM' | 'ChaCha20-Poly1305';
  keyRotationInterval: number; // hours
  maxFailedAttempts: number;
  sessionTimeout: number; // minutes
  enableRateLimiting: boolean;
  rateLimitRequests: number;
  rateLimitWindow: number; // seconds
  trustedDomains: string[];
  blockedPatterns: string[];
}

export interface PerformanceConfig {
  enableLazyLoading: boolean;
  enableResourceManagement: boolean;
  enableContextOptimization: boolean;
  enableBackgroundProcessing: boolean;
  enableConcurrentTaskHandling: boolean;
  enableResourceThrottling: boolean;
  maxConcurrentTasks: number;
  maxMemoryUsage: number; // MB
  maxCpuUsage: number; // percentage
  cacheSize: number; // MB
  gcInterval: number; // minutes
  performanceMonitoring: boolean;
  enableProfiling: boolean;
  optimizationLevel: 'conservative' | 'balanced' | 'aggressive';
}

export interface SecurityMetrics {
  encryptedKeys: number;
  failedAttempts: number;
  blockedRequests: number;
  auditLogEntries: number;
  lastKeyRotation: number;
  activeSessions: number;
  securityScore: number; // 0-100
  vulnerabilities: SecurityVulnerability[];
  lastSecurityScan: number;
}

export interface PerformanceMetrics {
  memoryUsage: number; // MB
  cpuUsage: number; // percentage
  taskQueueSize: number;
  averageResponseTime: number; // ms
  throughput: number; // requests/second
  cacheHitRate: number; // percentage
  gcCollections: number;
  performanceScore: number; // 0-100
  bottlenecks: PerformanceBottleneck[];
  lastOptimization: number;
}

export interface SecurityVulnerability {
  id: string;
  type: 'api_key_exposure' | 'prompt_injection' | 'unauthorized_access' | 'data_leak' | 'xss' | 'csrf';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location: string;
  detectedAt: number;
  mitigated: boolean;
  mitigation?: string;
}

export interface PerformanceBottleneck {
  id: string;
  type: 'memory' | 'cpu' | 'io' | 'network' | 'database' | 'cache';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  component: string;
  impact: number; // 0-100
  detectedAt: number;
  resolved: boolean;
  optimization?: string;
}

export interface OptimizationResult {
  id: string;
  type: 'security' | 'performance' | 'both';
  description: string;
  beforeMetrics: Partial<SecurityMetrics & PerformanceMetrics>;
  afterMetrics: Partial<SecurityMetrics & PerformanceMetrics>;
  improvement: number; // percentage
  appliedAt: number;
  techniques: string[];
}

export interface EncryptedData {
  data: string;
  iv: string;
  tag: string;
  algorithm: string;
  timestamp: number;
}

export interface AccessControlRule {
  id: string;
  name: string;
  type: 'allow' | 'deny';
  resource: string;
  action: string;
  conditions: Record<string, any>;
  priority: number;
  enabled: boolean;
}

export interface AuditLogEntry {
  id: string;
  timestamp: number;
  userId?: string;
  action: string;
  resource: string;
  result: 'success' | 'failure' | 'blocked';
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  riskScore: number;
}

export interface ResourceUsage {
  memory: {
    used: number;
    available: number;
    percentage: number;
  };
  cpu: {
    usage: number;
    cores: number;
    load: number[];
  };
  storage: {
    used: number;
    available: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    connections: number;
  };
}

export class SecurityPerformanceOptimizer {
  private configStore: any;
  private encryptionKey: CryptoKey | null = null;
  private accessControlRules: Map<string, AccessControlRule> = new Map();
  private auditLog: AuditLogEntry[] = [];
  private rateLimitMap: Map<string, { count: number; resetTime: number }> = new Map();
  private performanceMonitor: PerformanceObserver | null = null;
  private resourceMonitor: NodeJS.Timeout | null = null;
  private optimizationHistory: OptimizationResult[] = [];

  private securityConfig: SecurityConfig = {
    enableApiKeyEncryption: true,
    enablePromptProtection: true,
    enableAccessControl: true,
    enableAuditLogging: true,
    encryptionAlgorithm: 'AES-256-GCM',
    keyRotationInterval: 24,
    maxFailedAttempts: 5,
    sessionTimeout: 60,
    enableRateLimiting: true,
    rateLimitRequests: 100,
    rateLimitWindow: 60,
    trustedDomains: ['localhost', '127.0.0.1'],
    blockedPatterns: ['<script>', 'javascript:', 'data:', 'vbscript:']
  };

  private performanceConfig: PerformanceConfig = {
    enableLazyLoading: true,
    enableResourceManagement: true,
    enableContextOptimization: true,
    enableBackgroundProcessing: true,
    enableConcurrentTaskHandling: true,
    enableResourceThrottling: true,
    maxConcurrentTasks: 10,
    maxMemoryUsage: 512,
    maxCpuUsage: 80,
    cacheSize: 128,
    gcInterval: 5,
    performanceMonitoring: true,
    enableProfiling: false,
    optimizationLevel: 'balanced'
  };

  private securityMetrics: SecurityMetrics = {
    encryptedKeys: 0,
    failedAttempts: 0,
    blockedRequests: 0,
    auditLogEntries: 0,
    lastKeyRotation: 0,
    activeSessions: 0,
    securityScore: 0,
    vulnerabilities: [],
    lastSecurityScan: 0
  };

  private performanceMetrics: PerformanceMetrics = {
    memoryUsage: 0,
    cpuUsage: 0,
    taskQueueSize: 0,
    averageResponseTime: 0,
    throughput: 0,
    cacheHitRate: 0,
    gcCollections: 0,
    performanceScore: 0,
    bottlenecks: [],
    lastOptimization: 0
  };

  constructor(private projectId: string) {
    this.configStore = getConfigStoreBrowser();
  }

  /**
   * Initialize the security and performance optimization system
   */
  async initialize(): Promise<void> {
    try {
      await this.loadConfiguration();
      await this.initializeSecurity();
      await this.initializePerformanceMonitoring();
      await this.loadAccessControlRules();
      await this.startResourceMonitoring();

      console.log('Security and performance optimization system initialized');
    } catch (error) {
      console.error('Failed to initialize security and performance optimization:', error);
      throw error;
    }
  }

  /**
   * Encrypt sensitive data (API keys, tokens, etc.)
   */
  async encryptData(data: string): Promise<EncryptedData> {
    if (!this.securityConfig.enableApiKeyEncryption) {
      throw new Error('API key encryption is disabled');
    }

    if (!this.encryptionKey) {
      await this.generateEncryptionKey();
    }

    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      const iv = crypto.getRandomValues(new Uint8Array(12));

      const encrypted = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        this.encryptionKey!,
        dataBuffer
      );

      const encryptedArray = new Uint8Array(encrypted);
      const tag = encryptedArray.slice(-16);
      const ciphertext = encryptedArray.slice(0, -16);

      this.securityMetrics.encryptedKeys++;

      return {
        data: this.arrayBufferToBase64(ciphertext),
        iv: this.arrayBufferToBase64(iv),
        tag: this.arrayBufferToBase64(tag),
        algorithm: this.securityConfig.encryptionAlgorithm,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data
   */
  async decryptData(encryptedData: EncryptedData): Promise<string> {
    if (!this.encryptionKey) {
      await this.generateEncryptionKey();
    }

    try {
      const ciphertext = this.base64ToArrayBuffer(encryptedData.data);
      const iv = this.base64ToArrayBuffer(encryptedData.iv);
      const tag = this.base64ToArrayBuffer(encryptedData.tag);

      // Combine ciphertext and tag
      const encryptedBuffer = new Uint8Array(ciphertext.byteLength + tag.byteLength);
      encryptedBuffer.set(new Uint8Array(ciphertext));
      encryptedBuffer.set(new Uint8Array(tag), ciphertext.byteLength);

      const decrypted = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        this.encryptionKey!,
        encryptedBuffer
      );

      const decoder = new TextDecoder();
      return decoder.decode(decrypted);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Validate and sanitize prompts to prevent injection attacks
   */
  validatePrompt(prompt: string): { isValid: boolean; sanitized: string; threats: string[] } {
    if (!this.securityConfig.enablePromptProtection) {
      return { isValid: true, sanitized: prompt, threats: [] };
    }

    const threats: string[] = [];
    let sanitized = prompt;

    // Check for blocked patterns
    for (const pattern of this.securityConfig.blockedPatterns) {
      if (prompt.toLowerCase().includes(pattern.toLowerCase())) {
        threats.push(`Blocked pattern detected: ${pattern}`);
        sanitized = sanitized.replace(new RegExp(pattern, 'gi'), '[BLOCKED]');
      }
    }

    // Check for potential injection attempts
    const injectionPatterns = [
      /system\s*:/i,
      /assistant\s*:/i,
      /ignore\s+previous\s+instructions/i,
      /forget\s+everything/i,
      /new\s+instructions/i,
      /override\s+instructions/i
    ];

    for (const pattern of injectionPatterns) {
      if (pattern.test(prompt)) {
        threats.push(`Potential injection attempt: ${pattern.source}`);
      }
    }

    // Log security event
    if (threats.length > 0) {
      this.logSecurityEvent('prompt_validation', 'blocked', {
        originalPrompt: prompt.substring(0, 100),
        threats
      });
      this.securityMetrics.blockedRequests++;
    }

    return {
      isValid: threats.length === 0,
      sanitized,
      threats
    };
  }

  /**
   * Check access control permissions
   */
  checkAccess(resource: string, action: string, context: Record<string, any> = {}): boolean {
    if (!this.securityConfig.enableAccessControl) {
      return true;
    }

    const applicableRules = Array.from(this.accessControlRules.values())
      .filter(rule => rule.enabled && this.matchesResource(rule.resource, resource))
      .sort((a, b) => b.priority - a.priority);

    for (const rule of applicableRules) {
      if (this.evaluateConditions(rule.conditions, context)) {
        const allowed = rule.type === 'allow';

        this.logSecurityEvent('access_control', allowed ? 'success' : 'blocked', {
          resource,
          action,
          rule: rule.name,
          context
        });

        return allowed;
      }
    }

    // Default deny
    this.logSecurityEvent('access_control', 'blocked', {
      resource,
      action,
      reason: 'No matching allow rule',
      context
    });

    return false;
  }

  /**
   * Apply rate limiting
   */
  checkRateLimit(identifier: string): { allowed: boolean; resetTime: number; remaining: number } {
    if (!this.securityConfig.enableRateLimiting) {
      return { allowed: true, resetTime: 0, remaining: this.securityConfig.rateLimitRequests };
    }

    const now = Date.now();
    const windowMs = this.securityConfig.rateLimitWindow * 1000;
    const limit = this.securityConfig.rateLimitRequests;

    let entry = this.rateLimitMap.get(identifier);

    if (!entry || now > entry.resetTime) {
      entry = { count: 0, resetTime: now + windowMs };
      this.rateLimitMap.set(identifier, entry);
    }

    entry.count++;
    const allowed = entry.count <= limit;
    const remaining = Math.max(0, limit - entry.count);

    if (!allowed) {
      this.logSecurityEvent('rate_limit', 'blocked', { identifier, count: entry.count, limit });
      this.securityMetrics.blockedRequests++;
    }

    return { allowed, resetTime: entry.resetTime, remaining };
  }

  /**
   * Optimize performance based on current metrics
   */
  async optimizePerformance(): Promise<OptimizationResult> {
    const beforeMetrics = { ...this.performanceMetrics };
    const techniques: string[] = [];

    try {
      // Memory optimization
      if (this.performanceMetrics.memoryUsage > this.performanceConfig.maxMemoryUsage * 0.8) {
        await this.optimizeMemoryUsage();
        techniques.push('memory_optimization');
      }

      // CPU optimization
      if (this.performanceMetrics.cpuUsage > this.performanceConfig.maxCpuUsage * 0.8) {
        await this.optimizeCpuUsage();
        techniques.push('cpu_optimization');
      }

      // Cache optimization
      if (this.performanceMetrics.cacheHitRate < 0.8) {
        await this.optimizeCacheUsage();
        techniques.push('cache_optimization');
      }

      // Task queue optimization
      if (this.performanceMetrics.taskQueueSize > this.performanceConfig.maxConcurrentTasks * 2) {
        await this.optimizeTaskQueue();
        techniques.push('task_queue_optimization');
      }

      // Garbage collection optimization
      if (this.performanceConfig.enableResourceManagement) {
        await this.triggerGarbageCollection();
        techniques.push('garbage_collection');
      }

      // Update metrics after optimization
      await this.updatePerformanceMetrics();
      const afterMetrics = { ...this.performanceMetrics };

      // Calculate improvement
      const improvement = this.calculatePerformanceImprovement(beforeMetrics, afterMetrics);

      const result: OptimizationResult = {
        id: this.generateOptimizationId(),
        type: 'performance',
        description: `Applied ${techniques.length} optimization techniques`,
        beforeMetrics,
        afterMetrics,
        improvement,
        appliedAt: Date.now(),
        techniques
      };

      this.optimizationHistory.push(result);
      this.performanceMetrics.lastOptimization = Date.now();

      console.log(`Performance optimization completed: ${improvement.toFixed(1)}% improvement`);
      return result;

    } catch (error) {
      console.error('Performance optimization failed:', error);
      throw error;
    }
  }

  /**
   * Run security scan
   */
  async runSecurityScan(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    try {
      // Check for API key exposure
      const apiKeyVulns = await this.scanForApiKeyExposure();
      vulnerabilities.push(...apiKeyVulns);

      // Check for prompt injection vulnerabilities
      const promptVulns = await this.scanForPromptInjection();
      vulnerabilities.push(...promptVulns);

      // Check for unauthorized access patterns
      const accessVulns = await this.scanForUnauthorizedAccess();
      vulnerabilities.push(...accessVulns);

      // Check for data leaks
      const dataLeakVulns = await this.scanForDataLeaks();
      vulnerabilities.push(...dataLeakVulns);

      // Update security metrics
      this.securityMetrics.vulnerabilities = vulnerabilities;
      this.securityMetrics.lastSecurityScan = Date.now();
      this.securityMetrics.securityScore = this.calculateSecurityScore(vulnerabilities);

      console.log(`Security scan completed: ${vulnerabilities.length} vulnerabilities found`);
      return vulnerabilities;

    } catch (error) {
      console.error('Security scan failed:', error);
      throw error;
    }
  }

  /**
   * Get current resource usage
   */
  async getResourceUsage(): Promise<ResourceUsage> {
    const memoryInfo = (performance as any).memory || {};

    return {
      memory: {
        used: Math.round((memoryInfo.usedJSHeapSize || 0) / 1024 / 1024),
        available: Math.round((memoryInfo.jsHeapSizeLimit || 0) / 1024 / 1024),
        percentage: memoryInfo.jsHeapSizeLimit ?
          Math.round((memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100) : 0
      },
      cpu: {
        usage: this.performanceMetrics.cpuUsage,
        cores: navigator.hardwareConcurrency || 4,
        load: [this.performanceMetrics.cpuUsage / 100]
      },
      storage: {
        used: 0, // Would need to implement storage usage tracking
        available: 0,
        percentage: 0
      },
      network: {
        bytesIn: 0, // Would need to implement network monitoring
        bytesOut: 0,
        connections: 0
      }
    };
  }

  /**
   * Get security metrics
   */
  getSecurityMetrics(): SecurityMetrics {
    return { ...this.securityMetrics };
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Update security configuration
   */
  async updateSecurityConfig(newConfig: Partial<SecurityConfig>): Promise<void> {
    this.securityConfig = { ...this.securityConfig, ...newConfig };
    await this.saveConfiguration();

    // Reinitialize if encryption settings changed
    if (newConfig.enableApiKeyEncryption !== undefined || newConfig.encryptionAlgorithm) {
      await this.initializeSecurity();
    }

    console.log('Security configuration updated');
  }

  /**
   * Update performance configuration
   */
  async updatePerformanceConfig(newConfig: Partial<PerformanceConfig>): Promise<void> {
    this.performanceConfig = { ...this.performanceConfig, ...newConfig };
    await this.saveConfiguration();

    // Restart monitoring if settings changed
    if (newConfig.performanceMonitoring !== undefined) {
      await this.initializePerformanceMonitoring();
    }

    console.log('Performance configuration updated');
  }

  /**
   * Get optimization history
   */
  getOptimizationHistory(): OptimizationResult[] {
    return [...this.optimizationHistory];
  }

  /**
   * Add access control rule
   */
  addAccessControlRule(rule: Omit<AccessControlRule, 'id'>): string {
    const id = this.generateRuleId();
    const fullRule: AccessControlRule = { ...rule, id };
    this.accessControlRules.set(id, fullRule);
    this.saveAccessControlRules();
    return id;
  }

  /**
   * Remove access control rule
   */
  removeAccessControlRule(id: string): boolean {
    const deleted = this.accessControlRules.delete(id);
    if (deleted) {
      this.saveAccessControlRules();
    }
    return deleted;
  }

  /**
   * Get audit log entries
   */
  getAuditLog(limit: number = 100): AuditLogEntry[] {
    return this.auditLog.slice(-limit);
  }

  // Private implementation methods

  private async initializeSecurity(): Promise<void> {
    if (this.securityConfig.enableApiKeyEncryption) {
      await this.generateEncryptionKey();
    }

    // Initialize key rotation timer
    if (this.securityConfig.keyRotationInterval > 0) {
      setInterval(() => {
        this.rotateEncryptionKey();
      }, this.securityConfig.keyRotationInterval * 60 * 60 * 1000);
    }
  }

  private async initializePerformanceMonitoring(): Promise<void> {
    if (!this.performanceConfig.performanceMonitoring) {
      return;
    }

    try {
      // Initialize Performance Observer for browser environments
      if (typeof PerformanceObserver !== 'undefined') {
        this.performanceMonitor = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          for (const entry of entries) {
            if (entry.entryType === 'measure') {
              this.updateResponseTimeMetrics(entry.duration);
            }
          }
        });

        this.performanceMonitor.observe({ entryTypes: ['measure', 'navigation'] });
      }
    } catch (error) {
      console.warn('Performance monitoring not available:', error);
    }
  }

  private async startResourceMonitoring(): Promise<void> {
    if (!this.performanceConfig.enableResourceManagement) {
      return;
    }

    this.resourceMonitor = setInterval(async () => {
      await this.updatePerformanceMetrics();
      await this.checkResourceThresholds();
    }, 30000); // Check every 30 seconds
  }

  private async generateEncryptionKey(): Promise<void> {
    try {
      this.encryptionKey = await crypto.subtle.generateKey(
        {
          name: 'AES-GCM',
          length: 256
        },
        false,
        ['encrypt', 'decrypt']
      );

      this.securityMetrics.lastKeyRotation = Date.now();
    } catch (error) {
      console.error('Failed to generate encryption key:', error);
      throw error;
    }
  }

  private async rotateEncryptionKey(): Promise<void> {
    console.log('Rotating encryption key...');
    await this.generateEncryptionKey();
    this.securityMetrics.lastKeyRotation = Date.now();
  }

  // Utility methods for security and performance operations

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }

  private logSecurityEvent(action: string, result: 'success' | 'failure' | 'blocked', details: Record<string, any>): void {
    if (!this.securityConfig.enableAuditLogging) {
      return;
    }

    const entry: AuditLogEntry = {
      id: this.generateAuditId(),
      timestamp: Date.now(),
      action,
      resource: details.resource || 'unknown',
      result,
      details,
      riskScore: this.calculateRiskScore(action, result, details)
    };

    this.auditLog.push(entry);
    this.securityMetrics.auditLogEntries++;

    // Limit audit log size
    if (this.auditLog.length > 10000) {
      this.auditLog = this.auditLog.slice(-5000);
    }
  }

  private matchesResource(pattern: string, resource: string): boolean {
    // Simple pattern matching - could be enhanced with regex
    if (pattern === '*') return true;
    if (pattern === resource) return true;
    if (pattern.endsWith('*') && resource.startsWith(pattern.slice(0, -1))) return true;
    return false;
  }

  private evaluateConditions(conditions: Record<string, any>, context: Record<string, any>): boolean {
    for (const [key, value] of Object.entries(conditions)) {
      if (context[key] !== value) {
        return false;
      }
    }
    return true;
  }

  private async optimizeMemoryUsage(): Promise<void> {
    // Clear caches if memory usage is high
    if (typeof (globalThis as any).gc === 'function') {
      (globalThis as any).gc();
    }

    // Clear expired cache entries
    // This would integrate with other cache systems
    console.log('Memory optimization applied');
  }

  private async optimizeCpuUsage(): Promise<void> {
    // Reduce concurrent tasks
    const currentTasks = this.performanceMetrics.taskQueueSize;
    const targetTasks = Math.floor(this.performanceConfig.maxConcurrentTasks * 0.7);

    if (currentTasks > targetTasks) {
      // Would need to integrate with task management system
      console.log(`CPU optimization: reducing tasks from ${currentTasks} to ${targetTasks}`);
    }
  }

  private async optimizeCacheUsage(): Promise<void> {
    // Optimize cache hit rates
    // This would integrate with cache management systems
    console.log('Cache optimization applied');
  }

  private async optimizeTaskQueue(): Promise<void> {
    // Optimize task queue processing
    // This would integrate with task management systems
    console.log('Task queue optimization applied');
  }

  private async triggerGarbageCollection(): Promise<void> {
    if (typeof (globalThis as any).gc === 'function') {
      (globalThis as any).gc();
      this.performanceMetrics.gcCollections++;
    }
  }

  private async updatePerformanceMetrics(): Promise<void> {
    const memoryInfo = (performance as any).memory || {};

    this.performanceMetrics.memoryUsage = Math.round((memoryInfo.usedJSHeapSize || 0) / 1024 / 1024);
    this.performanceMetrics.cpuUsage = this.estimateCpuUsage();
    this.performanceMetrics.performanceScore = this.calculatePerformanceScore();

    // Update bottlenecks
    this.performanceMetrics.bottlenecks = this.detectPerformanceBottlenecks();
  }

  private estimateCpuUsage(): number {
    // Simplified CPU usage estimation
    const taskQueueRatio = this.performanceMetrics.taskQueueSize / this.performanceConfig.maxConcurrentTasks;
    return Math.min(100, taskQueueRatio * 100);
  }

  private calculatePerformanceScore(): number {
    const memoryScore = Math.max(0, 100 - (this.performanceMetrics.memoryUsage / this.performanceConfig.maxMemoryUsage) * 100);
    const cpuScore = Math.max(0, 100 - this.performanceMetrics.cpuUsage);
    const cacheScore = this.performanceMetrics.cacheHitRate * 100;
    const responseScore = Math.max(0, 100 - (this.performanceMetrics.averageResponseTime / 1000) * 10);

    return Math.round((memoryScore + cpuScore + cacheScore + responseScore) / 4);
  }

  private detectPerformanceBottlenecks(): PerformanceBottleneck[] {
    const bottlenecks: PerformanceBottleneck[] = [];

    if (this.performanceMetrics.memoryUsage > this.performanceConfig.maxMemoryUsage * 0.9) {
      bottlenecks.push({
        id: this.generateBottleneckId(),
        type: 'memory',
        severity: 'high',
        description: 'High memory usage detected',
        component: 'memory_manager',
        impact: 80,
        detectedAt: Date.now(),
        resolved: false
      });
    }

    if (this.performanceMetrics.cpuUsage > this.performanceConfig.maxCpuUsage * 0.9) {
      bottlenecks.push({
        id: this.generateBottleneckId(),
        type: 'cpu',
        severity: 'high',
        description: 'High CPU usage detected',
        component: 'task_processor',
        impact: 75,
        detectedAt: Date.now(),
        resolved: false
      });
    }

    return bottlenecks;
  }

  private calculatePerformanceImprovement(before: PerformanceMetrics, after: PerformanceMetrics): number {
    const beforeScore = this.calculateScoreFromMetrics(before);
    const afterScore = this.calculateScoreFromMetrics(after);
    return ((afterScore - beforeScore) / beforeScore) * 100;
  }

  private calculateScoreFromMetrics(metrics: PerformanceMetrics): number {
    return metrics.performanceScore || 50;
  }

  private async scanForApiKeyExposure(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // This would scan for exposed API keys in various locations
    // For now, return empty array as this is a simplified implementation

    return vulnerabilities;
  }

  private async scanForPromptInjection(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // This would scan for prompt injection vulnerabilities
    // For now, return empty array as this is a simplified implementation

    return vulnerabilities;
  }

  private async scanForUnauthorizedAccess(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // This would scan for unauthorized access patterns
    // For now, return empty array as this is a simplified implementation

    return vulnerabilities;
  }

  private async scanForDataLeaks(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // This would scan for potential data leaks
    // For now, return empty array as this is a simplified implementation

    return vulnerabilities;
  }

  private calculateSecurityScore(vulnerabilities: SecurityVulnerability[]): number {
    if (vulnerabilities.length === 0) return 100;

    const severityWeights = { low: 1, medium: 3, high: 7, critical: 15 };
    const totalWeight = vulnerabilities.reduce((sum, vuln) => sum + severityWeights[vuln.severity], 0);

    return Math.max(0, 100 - totalWeight);
  }

  private calculateRiskScore(action: string, result: string, details: Record<string, any>): number {
    let score = 0;

    if (result === 'failure' || result === 'blocked') score += 30;
    if (action.includes('access')) score += 20;
    if (action.includes('auth')) score += 25;
    if (details.threats && details.threats.length > 0) score += 40;

    return Math.min(100, score);
  }

  private updateResponseTimeMetrics(duration: number): void {
    const currentAvg = this.performanceMetrics.averageResponseTime;
    this.performanceMetrics.averageResponseTime = (currentAvg + duration) / 2;
  }

  private async checkResourceThresholds(): Promise<void> {
    if (this.performanceMetrics.memoryUsage > this.performanceConfig.maxMemoryUsage) {
      console.warn('Memory usage threshold exceeded');
      await this.optimizeMemoryUsage();
    }

    if (this.performanceMetrics.cpuUsage > this.performanceConfig.maxCpuUsage) {
      console.warn('CPU usage threshold exceeded');
      await this.optimizeCpuUsage();
    }
  }

  private async loadConfiguration(): Promise<void> {
    try {
      const securityConfig = await this.configStore.getGlobalSetting('security.config');
      if (securityConfig) {
        this.securityConfig = { ...this.securityConfig, ...securityConfig };
      }

      const performanceConfig = await this.configStore.getGlobalSetting('performance.config');
      if (performanceConfig) {
        this.performanceConfig = { ...this.performanceConfig, ...performanceConfig };
      }
    } catch (error) {
      console.error('Failed to load security/performance configuration:', error);
    }
  }

  private async saveConfiguration(): Promise<void> {
    try {
      await this.configStore.setGlobalSetting('security.config', this.securityConfig);
      await this.configStore.setGlobalSetting('performance.config', this.performanceConfig);
    } catch (error) {
      console.error('Failed to save security/performance configuration:', error);
    }
  }

  private async loadAccessControlRules(): Promise<void> {
    try {
      const rules = await this.configStore.getGlobalSetting('security.accessControlRules');
      if (rules && Array.isArray(rules)) {
        this.accessControlRules.clear();
        for (const rule of rules) {
          this.accessControlRules.set(rule.id, rule);
        }
      }
    } catch (error) {
      console.error('Failed to load access control rules:', error);
    }
  }

  private async saveAccessControlRules(): Promise<void> {
    try {
      const rules = Array.from(this.accessControlRules.values());
      await this.configStore.setGlobalSetting('security.accessControlRules', rules);
    } catch (error) {
      console.error('Failed to save access control rules:', error);
    }
  }

  // ID generation methods
  private generateOptimizationId(): string {
    return `opt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRuleId(): string {
    return `rule-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAuditId(): string {
    return `audit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateBottleneckId(): string {
    return `bottleneck-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Shutdown the security and performance optimization system
   */
  async shutdown(): Promise<void> {
    try {
      // Stop monitoring
      if (this.performanceMonitor) {
        this.performanceMonitor.disconnect();
        this.performanceMonitor = null;
      }

      if (this.resourceMonitor) {
        clearInterval(this.resourceMonitor);
        this.resourceMonitor = null;
      }

      // Clear sensitive data
      this.encryptionKey = null;
      this.rateLimitMap.clear();
      this.auditLog = [];

      console.log('Security and performance optimization system shut down');
    } catch (error) {
      console.error('Error during security/performance shutdown:', error);
    }
  }
}

// Global instances per project
const globalSecurityPerformanceOptimizer: Map<string, SecurityPerformanceOptimizer> = new Map();

/**
 * Get the security and performance optimizer instance for a project
 */
export function getSecurityPerformanceOptimizer(projectId: string): SecurityPerformanceOptimizer {
  if (!globalSecurityPerformanceOptimizer.has(projectId)) {
    globalSecurityPerformanceOptimizer.set(projectId, new SecurityPerformanceOptimizer(projectId));
  }
  return globalSecurityPerformanceOptimizer.get(projectId)!;
}
