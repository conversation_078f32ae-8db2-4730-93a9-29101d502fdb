// components/background/vector-database.ts
import { getConfigStoreBrowser } from './config-store-browser';

export interface VectorDocument {
  id: string;
  content: string;
  metadata: {
    filePath?: string;
    fileType?: string;
    language?: string;
    timestamp: number;
    size: number;
  };
  vector: number[];
  embedding?: string; // Serialized vector for storage
}

export interface SearchResult {
  document: VectorDocument;
  similarity: number;
  relevanceScore: number;
}

export interface VectorSearchOptions {
  limit?: number;
  threshold?: number;
  includeMetadata?: boolean;
  filterByType?: string[];
  filterByLanguage?: string[];
}

export class BasicVectorDatabase {
  private documents: Map<string, VectorDocument> = new Map();
  private isInitialized = false;
  private configStore = getConfigStoreBrowser();

  constructor() {
    this.initialize();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load existing vectors from storage
      await this.loadVectorsFromStorage();
      this.isInitialized = true;
      console.log('Vector database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize vector database:', error);
      throw error;
    }
  }

  /**
   * Add a document to the vector database
   */
  async addDocument(document: Omit<VectorDocument, 'vector' | 'embedding'>): Promise<void> {
    try {
      // Generate vector embedding for the content
      const vector = await this.generateEmbedding(document.content);

      const vectorDoc: VectorDocument = {
        ...document,
        vector,
        embedding: JSON.stringify(vector)
      };

      this.documents.set(document.id, vectorDoc);

      // Persist to storage
      await this.saveVectorToStorage(vectorDoc);

      console.log(`Added document to vector database: ${document.id}`);
    } catch (error) {
      console.error(`Failed to add document ${document.id}:`, error);
      throw error;
    }
  }

  /**
   * Search for similar documents using semantic similarity
   */
  async search(query: string, options: VectorSearchOptions = {}): Promise<SearchResult[]> {
    const {
      limit = 10,
      threshold = 0.1,
      includeMetadata = true,
      filterByType,
      filterByLanguage
    } = options;

    try {
      // Generate embedding for the query
      const queryVector = await this.generateEmbedding(query);

      const results: SearchResult[] = [];

      for (const [id, document] of this.documents) {
        // Apply filters
        if (filterByType && !filterByType.includes(document.metadata.fileType || '')) {
          continue;
        }

        if (filterByLanguage && !filterByLanguage.includes(document.metadata.language || '')) {
          continue;
        }

        // Calculate similarity
        const similarity = this.cosineSimilarity(queryVector, document.vector);

        if (similarity >= threshold) {
          results.push({
            document: includeMetadata ? document : { ...document, metadata: {} as any },
            similarity,
            relevanceScore: similarity
          });
        }
      }

      // Sort by similarity (descending) and limit results
      return results
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);

    } catch (error) {
      console.error('Vector search failed:', error);
      return [];
    }
  }

  /**
   * Get document by ID
   */
  getDocument(id: string): VectorDocument | undefined {
    return this.documents.get(id);
  }

  /**
   * Remove document from database
   */
  async removeDocument(id: string): Promise<boolean> {
    try {
      const removed = this.documents.delete(id);
      if (removed) {
        await this.removeVectorFromStorage(id);
        console.log(`Removed document from vector database: ${id}`);
      }
      return removed;
    } catch (error) {
      console.error(`Failed to remove document ${id}:`, error);
      return false;
    }
  }

  /**
   * Get all document IDs
   */
  getAllDocumentIds(): string[] {
    return Array.from(this.documents.keys());
  }

  /**
   * Get database statistics
   */
  getStats(): {
    totalDocuments: number;
    totalSize: number;
    averageVectorSize: number;
    languages: string[];
    fileTypes: string[];
  } {
    const docs = Array.from(this.documents.values());

    return {
      totalDocuments: docs.length,
      totalSize: docs.reduce((sum, doc) => sum + doc.metadata.size, 0),
      averageVectorSize: docs.length > 0 ? docs[0].vector.length : 0,
      languages: [...new Set(docs.map(doc => doc.metadata.language).filter(Boolean))],
      fileTypes: [...new Set(docs.map(doc => doc.metadata.fileType).filter(Boolean))]
    };
  }

  /**
   * Clear all documents
   */
  async clear(): Promise<void> {
    try {
      this.documents.clear();
      await this.clearVectorStorage();
      console.log('Vector database cleared');
    } catch (error) {
      console.error('Failed to clear vector database:', error);
      throw error;
    }
  }

  /**
   * Generate embedding vector for text content
   * Using simple TF-IDF approach for now
   */
  private async generateEmbedding(text: string): Promise<number[]> {
    // Simple tokenization and TF-IDF-like embedding
    const tokens = this.tokenize(text);
    const vocabulary = this.buildVocabulary(tokens);

    // Create a fixed-size vector (256 dimensions)
    const vectorSize = 256;
    const vector = new Array(vectorSize).fill(0);

    // Simple hash-based embedding
    for (const token of tokens) {
      const hash = this.simpleHash(token) % vectorSize;
      vector[hash] += 1;
    }

    // Normalize vector
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    if (magnitude > 0) {
      for (let i = 0; i < vector.length; i++) {
        vector[i] /= magnitude;
      }
    }

    return vector;
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private cosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error('Vectors must have the same length');
    }

    let dotProduct = 0;
    let magnitudeA = 0;
    let magnitudeB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      magnitudeA += vectorA[i] * vectorA[i];
      magnitudeB += vectorB[i] * vectorB[i];
    }

    magnitudeA = Math.sqrt(magnitudeA);
    magnitudeB = Math.sqrt(magnitudeB);

    if (magnitudeA === 0 || magnitudeB === 0) {
      return 0;
    }

    return dotProduct / (magnitudeA * magnitudeB);
  }

  /**
   * Simple tokenization
   */
  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 2);
  }

  /**
   * Build vocabulary from tokens
   */
  private buildVocabulary(tokens: string[]): Set<string> {
    return new Set(tokens);
  }

  /**
   * Simple hash function for string
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Load vectors from persistent storage
   */
  private async loadVectorsFromStorage(): Promise<void> {
    try {
      // Implementation depends on storage backend
      // For now, we'll use a simple approach
      console.log('Loading vectors from storage...');
      // TODO: Implement actual storage loading
    } catch (error) {
      console.error('Failed to load vectors from storage:', error);
    }
  }

  /**
   * Save vector to persistent storage
   */
  private async saveVectorToStorage(document: VectorDocument): Promise<void> {
    try {
      // Implementation depends on storage backend
      console.log(`Saving vector to storage: ${document.id}`);
      // TODO: Implement actual storage saving
    } catch (error) {
      console.error('Failed to save vector to storage:', error);
    }
  }

  /**
   * Remove vector from persistent storage
   */
  private async removeVectorFromStorage(id: string): Promise<void> {
    try {
      console.log(`Removing vector from storage: ${id}`);
      // TODO: Implement actual storage removal
    } catch (error) {
      console.error('Failed to remove vector from storage:', error);
    }
  }

  /**
   * Clear all vectors from persistent storage
   */
  private async clearVectorStorage(): Promise<void> {
    try {
      console.log('Clearing vector storage...');
      // TODO: Implement actual storage clearing
    } catch (error) {
      console.error('Failed to clear vector storage:', error);
    }
  }
}

// Singleton instance
let basicVectorDatabase: BasicVectorDatabase | null = null;

export function getBasicVectorDatabase(): BasicVectorDatabase {
  if (!basicVectorDatabase) {
    basicVectorDatabase = new BasicVectorDatabase();
  }
  return basicVectorDatabase;
}

export function shutdownBasicVectorDatabase(): void {
  if (basicVectorDatabase) {
    basicVectorDatabase.clear();
    basicVectorDatabase = null;
  }
}
