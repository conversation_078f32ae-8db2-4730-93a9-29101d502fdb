// components/background/git-integration.ts
import { TerminalIntegration, CommandResult } from './terminal-integration';
import { ContextHistory, HistoryEntry } from './context-history';
import { KnowledgeGraph, ImpactAnalysis } from './knowledge-graph';
import { getConfigStoreBrowser } from './config-store-browser';

export interface GitRepository {
  id: string;
  path: string;
  name: string;
  remoteUrl?: string;
  currentBranch: string;
  isClean: boolean;
  lastCommit?: GitCommit;
  branches: GitBranch[];
  remotes: GitRemote[];
  status: GitStatus;
  config: GitConfig;
}

export interface GitCommit {
  hash: string;
  shortHash: string;
  author: string;
  email: string;
  date: number;
  message: string;
  files: GitFileChange[];
  parentHashes: string[];
  tags: string[];
  branch: string;
  stats: {
    insertions: number;
    deletions: number;
    filesChanged: number;
  };
}

export interface GitBranch {
  name: string;
  isRemote: boolean;
  isCurrent: boolean;
  lastCommit: string;
  upstream?: string;
  ahead: number;
  behind: number;
  description?: string;
}

export interface GitRemote {
  name: string;
  url: string;
  type: 'fetch' | 'push';
}

export interface GitStatus {
  staged: GitFileChange[];
  unstaged: GitFileChange[];
  untracked: string[];
  conflicted: GitFileChange[];
  ahead: number;
  behind: number;
  isClean: boolean;
}

export interface GitFileChange {
  path: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed' | 'copied' | 'unmerged' | 'untracked';
  oldPath?: string; // For renamed files
  insertions: number;
  deletions: number;
  binary: boolean;
  staged: boolean;
}

export interface GitConfig {
  user: {
    name: string;
    email: string;
  };
  core: {
    editor: string;
    autocrlf: boolean;
    safecrlf: boolean;
  };
  merge: {
    tool: string;
    conflictstyle: string;
  };
  push: {
    default: string;
  };
  pull: {
    rebase: boolean;
  };
}

export interface CommitOptions {
  message: string;
  description?: string;
  files?: string[]; // Specific files to commit, if not provided commits all staged
  author?: {
    name: string;
    email: string;
  };
  amend?: boolean;
  signoff?: boolean;
  noVerify?: boolean;
  allowEmpty?: boolean;
  agentId?: string;
  generateMessage?: boolean; // Auto-generate commit message based on changes
}

export interface BranchOptions {
  name: string;
  startPoint?: string; // Branch or commit to start from
  track?: boolean; // Track remote branch
  force?: boolean;
  checkout?: boolean; // Checkout after creation
  description?: string;
}

export interface MergeOptions {
  branch: string;
  strategy?: 'recursive' | 'ours' | 'theirs' | 'octopus' | 'resolve' | 'subtree';
  noCommit?: boolean;
  noFastForward?: boolean;
  squash?: boolean;
  message?: string;
  agentId?: string;
}

export interface ConflictResolution {
  filePath: string;
  resolution: 'ours' | 'theirs' | 'manual' | 'auto';
  resolvedContent?: string;
  strategy?: string;
  confidence: number; // 0-1, how confident we are in the resolution
  reasoning: string;
}

export interface GitOperation {
  id: string;
  type: 'commit' | 'branch' | 'merge' | 'rebase' | 'push' | 'pull' | 'clone' | 'fetch' | 'reset' | 'revert';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  agentId?: string;
  startTime: number;
  endTime?: number;
  repository: string;
  command: string;
  args: string[];
  result?: CommandResult;
  impactAnalysis?: ImpactAnalysis;
  historyEntry?: string; // ID of related history entry
  metadata: {
    description: string;
    affectedFiles: string[];
    expectedOutcome: string;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    tags: string[];
  };
}

export interface GitStats {
  totalOperations: number;
  operationsByType: Record<string, number>;
  operationsByAgent: Record<string, number>;
  successRate: number;
  averageOperationTime: number;
  totalCommits: number;
  totalBranches: number;
  conflictsResolved: number;
  autoCommitsGenerated: number;
  impactAnalysesPerformed: number;
}

export interface GitIntegrationConfig {
  autoCommitGeneration: boolean;
  commitMessageTemplate: string;
  branchNamingConvention: string;
  mergeStrategy: string;
  conflictResolutionStrategy: 'auto' | 'manual' | 'agent';
  enableImpactAnalysis: boolean;
  enableHistoryTracking: boolean;
  maxOperationTimeout: number; // milliseconds
  defaultRemote: string;
  signCommits: boolean;
  enforceConventionalCommits: boolean;
  autoFetch: boolean;
  autoFetchInterval: number; // milliseconds
}

export class GitIntegration {
  private terminalIntegration: TerminalIntegration;
  private contextHistory: ContextHistory;
  private knowledgeGraph: KnowledgeGraph;
  private configStore: any;

  private repositories: Map<string, GitRepository> = new Map();
  private operations: Map<string, GitOperation> = new Map();
  private operationHistory: GitOperation[] = [];

  private config: GitIntegrationConfig = {
    autoCommitGeneration: true,
    commitMessageTemplate: '{type}({scope}): {description}',
    branchNamingConvention: 'feature/{agent-id}/{description}',
    mergeStrategy: 'recursive',
    conflictResolutionStrategy: 'auto',
    enableImpactAnalysis: true,
    enableHistoryTracking: true,
    maxOperationTimeout: 300000, // 5 minutes
    defaultRemote: 'origin',
    signCommits: false,
    enforceConventionalCommits: true,
    autoFetch: true,
    autoFetchInterval: 300000 // 5 minutes
  };

  private stats: GitStats = {
    totalOperations: 0,
    operationsByType: {},
    operationsByAgent: {},
    successRate: 0,
    averageOperationTime: 0,
    totalCommits: 0,
    totalBranches: 0,
    conflictsResolved: 0,
    autoCommitsGenerated: 0,
    impactAnalysesPerformed: 0
  };

  private autoFetchTimer: NodeJS.Timeout | null = null;

  constructor(private projectId: string) {
    this.terminalIntegration = new TerminalIntegration();
    this.contextHistory = new ContextHistory(projectId);
    this.knowledgeGraph = new KnowledgeGraph(projectId);
    this.configStore = getConfigStoreBrowser();

    this.startAutoFetch();
  }

  /**
   * Initialize the git integration
   */
  async initialize(): Promise<void> {
    try {
      await this.terminalIntegration.initialize();
      await this.contextHistory.initialize();
      await this.knowledgeGraph.initialize();
      await this.loadConfiguration();
      await this.discoverRepositories();

      console.log('Git integration initialized');
    } catch (error) {
      console.error('Failed to initialize git integration:', error);
      throw error;
    }
  }

  /**
   * Discover git repositories in the project
   */
  async discoverRepositories(): Promise<GitRepository[]> {
    try {
      // Check if current directory is a git repository
      const isGitRepo = await this.isGitRepository('.');

      if (isGitRepo) {
        const repo = await this.loadRepository('.');
        this.repositories.set(repo.id, repo);
        return [repo];
      }

      // TODO: Search for git repositories in subdirectories
      return [];
    } catch (error) {
      console.error('Error discovering repositories:', error);
      return [];
    }
  }

  /**
   * Check if a directory is a git repository
   */
  async isGitRepository(path: string): Promise<boolean> {
    try {
      const result = await this.executeGitCommand(['rev-parse', '--git-dir'], path);
      return result.success;
    } catch (error) {
      return false;
    }
  }

  /**
   * Load repository information
   */
  async loadRepository(path: string): Promise<GitRepository> {
    const repoId = this.generateRepositoryId(path);

    // Get basic repository info
    const [currentBranch, remoteUrl, status, branches, remotes, config] = await Promise.all([
      this.getCurrentBranch(path),
      this.getRemoteUrl(path),
      this.getStatus(path),
      this.getBranches(path),
      this.getRemotes(path),
      this.getConfig(path)
    ]);

    const repository: GitRepository = {
      id: repoId,
      path,
      name: path.split('/').pop() || 'unknown',
      remoteUrl,
      currentBranch,
      isClean: status.isClean,
      branches,
      remotes,
      status,
      config
    };

    // Get last commit if available
    try {
      repository.lastCommit = await this.getLastCommit(path);
    } catch (error) {
      console.warn('Could not get last commit:', error);
    }

    return repository;
  }

  /**
   * Create an automated commit with generated message
   */
  async createAutoCommit(repositoryPath: string, options: Partial<CommitOptions> = {}): Promise<string> {
    const operation = await this.startOperation('commit', repositoryPath, 'Auto-generated commit');

    try {
      // Get current status
      const status = await this.getStatus(repositoryPath);

      if (status.isClean) {
        throw new Error('No changes to commit');
      }

      // Generate commit message if requested
      let commitMessage = options.message;
      if (options.generateMessage || !commitMessage) {
        commitMessage = await this.generateCommitMessage(repositoryPath, status);
      }

      // Perform impact analysis
      let impactAnalysis: ImpactAnalysis | undefined;
      if (this.config.enableImpactAnalysis) {
        impactAnalysis = await this.analyzeChangeImpact(repositoryPath, status);
        operation.impactAnalysis = impactAnalysis;
      }

      // Stage files if not already staged
      if (status.staged.length === 0 && status.unstaged.length > 0) {
        await this.stageFiles(repositoryPath, status.unstaged.map(f => f.path));
      }

      // Create commit
      const commitOptions: CommitOptions = {
        message: commitMessage,
        ...options
      };

      const commitHash = await this.commit(repositoryPath, commitOptions);

      // Track in history
      if (this.config.enableHistoryTracking) {
        await this.trackCommitInHistory(repositoryPath, commitHash, commitOptions, impactAnalysis);
      }

      // Update statistics
      this.stats.totalCommits++;
      if (options.generateMessage) {
        this.stats.autoCommitsGenerated++;
      }

      await this.completeOperation(operation.id, { success: true, message: `Commit created: ${commitHash}` });

      console.log(`Auto-commit created: ${commitHash}`);
      return commitHash;

    } catch (error) {
      await this.failOperation(operation.id, error.message);
      throw error;
    }
  }

  /**
   * Create a new branch through agents
   */
  async createBranch(repositoryPath: string, options: BranchOptions): Promise<string> {
    const operation = await this.startOperation('branch', repositoryPath, `Create branch: ${options.name}`);

    try {
      // Validate branch name
      this.validateBranchName(options.name);

      // Check if branch already exists
      const branches = await this.getBranches(repositoryPath);
      if (branches.some(b => b.name === options.name)) {
        throw new Error(`Branch '${options.name}' already exists`);
      }

      // Create branch
      const args = ['checkout', '-b', options.name];
      if (options.startPoint) {
        args.push(options.startPoint);
      }

      const result = await this.executeGitCommand(args, repositoryPath);

      if (!result.success) {
        throw new Error(`Failed to create branch: ${result.stderr}`);
      }

      // Update repository info
      const repo = this.repositories.get(this.generateRepositoryId(repositoryPath));
      if (repo) {
        repo.currentBranch = options.name;
        repo.branches = await this.getBranches(repositoryPath);
      }

      // Track in history
      if (this.config.enableHistoryTracking) {
        await this.trackBranchInHistory(repositoryPath, options);
      }

      this.stats.totalBranches++;
      await this.completeOperation(operation.id, result);

      console.log(`Branch created: ${options.name}`);
      return options.name;

    } catch (error) {
      await this.failOperation(operation.id, error.message);
      throw error;
    }
  }

  /**
   * Merge branches with conflict resolution
   */
  async mergeBranch(repositoryPath: string, options: MergeOptions): Promise<string> {
    const operation = await this.startOperation('merge', repositoryPath, `Merge branch: ${options.branch}`);

    try {
      // Perform impact analysis before merge
      let impactAnalysis: ImpactAnalysis | undefined;
      if (this.config.enableImpactAnalysis) {
        impactAnalysis = await this.analyzeMergeImpact(repositoryPath, options.branch);
        operation.impactAnalysis = impactAnalysis;
      }

      // Attempt merge
      const args = ['merge'];
      if (options.strategy) {
        args.push('-s', options.strategy);
      }
      if (options.noCommit) {
        args.push('--no-commit');
      }
      if (options.noFastForward) {
        args.push('--no-ff');
      }
      if (options.squash) {
        args.push('--squash');
      }
      if (options.message) {
        args.push('-m', options.message);
      }
      args.push(options.branch);

      const result = await this.executeGitCommand(args, repositoryPath);

      // Check for conflicts
      if (!result.success && result.stderr.includes('CONFLICT')) {
        const conflicts = await this.detectConflicts(repositoryPath);
        const resolutions = await this.resolveConflicts(repositoryPath, conflicts);

        // Complete merge after conflict resolution
        if (resolutions.every(r => r.resolution !== 'manual')) {
          const commitResult = await this.executeGitCommand(['commit', '--no-edit'], repositoryPath);
          if (commitResult.success) {
            this.stats.conflictsResolved += conflicts.length;
          }
        }
      }

      // Track in history
      if (this.config.enableHistoryTracking) {
        await this.trackMergeInHistory(repositoryPath, options, impactAnalysis);
      }

      await this.completeOperation(operation.id, result);

      console.log(`Merge completed: ${options.branch}`);
      return result.stdout;

    } catch (error) {
      await this.failOperation(operation.id, error.message);
      throw error;
    }
  }

  /**
   * Analyze change impact before operations
   */
  async analyzeChangeImpact(repositoryPath: string, status: GitStatus): Promise<ImpactAnalysis> {
    try {
      // Get all affected files
      const affectedFiles = [
        ...status.staged.map(f => f.path),
        ...status.unstaged.map(f => f.path)
      ];

      // Use knowledge graph to analyze impact
      const impactAnalysis = await this.knowledgeGraph.analyzeImpact(affectedFiles);

      this.stats.impactAnalysesPerformed++;

      return impactAnalysis;
    } catch (error) {
      console.error('Error analyzing change impact:', error);

      // Return minimal impact analysis
      return {
        affectedNodes: [],
        affectedEdges: [],
        impactScore: 0.5,
        riskLevel: 'medium',
        recommendations: ['Manual review recommended'],
        cascadeEffects: []
      };
    }
  }

  /**
   * Generate commit message based on changes
   */
  async generateCommitMessage(repositoryPath: string, status: GitStatus): Promise<string> {
    try {
      const changes = [...status.staged, ...status.unstaged];

      // Analyze change patterns
      const patterns = this.analyzeChangePatterns(changes);

      // Generate message based on conventional commits
      if (this.config.enforceConventionalCommits) {
        return this.generateConventionalCommitMessage(patterns);
      }

      // Generate descriptive message
      return this.generateDescriptiveCommitMessage(patterns);

    } catch (error) {
      console.error('Error generating commit message:', error);
      return 'Update files';
    }
  }

  /**
   * Resolve merge conflicts automatically
   */
  async resolveConflicts(repositoryPath: string, conflicts: string[]): Promise<ConflictResolution[]> {
    const resolutions: ConflictResolution[] = [];

    for (const filePath of conflicts) {
      try {
        const resolution = await this.resolveFileConflict(repositoryPath, filePath);
        resolutions.push(resolution);

        if (resolution.resolution === 'auto' && resolution.resolvedContent) {
          // Write resolved content
          await this.writeResolvedContent(repositoryPath, filePath, resolution.resolvedContent);

          // Stage the resolved file
          await this.executeGitCommand(['add', filePath], repositoryPath);
        }
      } catch (error) {
        console.error(`Error resolving conflict in ${filePath}:`, error);
        resolutions.push({
          filePath,
          resolution: 'manual',
          confidence: 0,
          reasoning: `Automatic resolution failed: ${error.message}`
        });
      }
    }

    return resolutions;
  }

  /**
   * Get repository status
   */
  async getStatus(repositoryPath: string): Promise<GitStatus> {
    const result = await this.executeGitCommand(['status', '--porcelain=v1'], repositoryPath);

    if (!result.success) {
      throw new Error(`Failed to get status: ${result.stderr}`);
    }

    const staged: GitFileChange[] = [];
    const unstaged: GitFileChange[] = [];
    const untracked: string[] = [];
    const conflicted: GitFileChange[] = [];

    const lines = result.stdout.split('\n').filter(line => line.trim());

    for (const line of lines) {
      const statusCode = line.substring(0, 2);
      const filePath = line.substring(3);

      const change: GitFileChange = {
        path: filePath,
        status: this.parseFileStatus(statusCode),
        insertions: 0,
        deletions: 0,
        binary: false,
        staged: statusCode[0] !== ' ' && statusCode[0] !== '?'
      };

      if (statusCode.includes('U') || statusCode.includes('A') && statusCode.includes('A')) {
        conflicted.push(change);
      } else if (statusCode[0] === '?') {
        untracked.push(filePath);
      } else if (change.staged) {
        staged.push(change);
      } else {
        unstaged.push(change);
      }
    }

    // Get ahead/behind info
    const { ahead, behind } = await this.getAheadBehind(repositoryPath);

    return {
      staged,
      unstaged,
      untracked,
      conflicted,
      ahead,
      behind,
      isClean: staged.length === 0 && unstaged.length === 0 && untracked.length === 0
    };
  }

  /**
   * Get current branch
   */
  async getCurrentBranch(repositoryPath: string): Promise<string> {
    const result = await this.executeGitCommand(['branch', '--show-current'], repositoryPath);

    if (!result.success) {
      throw new Error(`Failed to get current branch: ${result.stderr}`);
    }

    return result.stdout.trim();
  }

  /**
   * Get all branches
   */
  async getBranches(repositoryPath: string): Promise<GitBranch[]> {
    const result = await this.executeGitCommand(['branch', '-a', '-v'], repositoryPath);

    if (!result.success) {
      throw new Error(`Failed to get branches: ${result.stderr}`);
    }

    const branches: GitBranch[] = [];
    const lines = result.stdout.split('\n').filter(line => line.trim());

    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;

      const isCurrent = trimmed.startsWith('*');
      const isRemote = trimmed.includes('remotes/');

      // Parse branch info
      const parts = trimmed.replace('*', '').trim().split(/\s+/);
      const name = parts[0].replace('remotes/', '');
      const lastCommit = parts[1] || '';

      branches.push({
        name,
        isRemote,
        isCurrent,
        lastCommit,
        ahead: 0,
        behind: 0
      });
    }

    return branches;
  }

  /**
   * Get repository statistics
   */
  getStats(): GitStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Get repository by path
   */
  getRepository(path: string): GitRepository | undefined {
    const repoId = this.generateRepositoryId(path);
    return this.repositories.get(repoId);
  }

  /**
   * Get all repositories
   */
  getRepositories(): GitRepository[] {
    return Array.from(this.repositories.values());
  }

  /**
   * Update configuration
   */
  async updateConfig(newConfig: Partial<GitIntegrationConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();

    // Restart auto-fetch if interval changed
    if (newConfig.autoFetchInterval || newConfig.autoFetch !== undefined) {
      this.stopAutoFetch();
      this.startAutoFetch();
    }

    console.log('Git integration configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): GitIntegrationConfig {
    return { ...this.config };
  }

  // Private implementation methods

  /**
   * Execute git command through terminal integration
   */
  private async executeGitCommand(args: string[], workingDirectory: string): Promise<CommandResult> {
    return await this.terminalIntegration.executeInSession(
      await this.getOrCreateGitSession(),
      'git',
      args
    );
  }

  /**
   * Get or create a git session
   */
  private async getOrCreateGitSession(): Promise<string> {
    // For simplicity, create a new session each time
    // In production, you might want to reuse sessions
    return await this.terminalIntegration.createSession('git-integration', {
      shell: 'bash',
      workingDirectory: process.cwd()
    });
  }

  /**
   * Start a git operation
   */
  private async startOperation(type: GitOperation['type'], repository: string, description: string): Promise<GitOperation> {
    const operationId = this.generateOperationId();

    const operation: GitOperation = {
      id: operationId,
      type,
      status: 'pending',
      startTime: Date.now(),
      repository,
      command: 'git',
      args: [],
      metadata: {
        description,
        affectedFiles: [],
        expectedOutcome: description,
        riskLevel: 'low',
        tags: ['git-operation']
      }
    };

    this.operations.set(operationId, operation);
    this.stats.totalOperations++;
    this.stats.operationsByType[type] = (this.stats.operationsByType[type] || 0) + 1;

    return operation;
  }

  /**
   * Complete a git operation
   */
  private async completeOperation(operationId: string, result: CommandResult): Promise<void> {
    const operation = this.operations.get(operationId);
    if (!operation) return;

    operation.status = 'completed';
    operation.endTime = Date.now();
    operation.result = result;

    this.operationHistory.push(operation);
  }

  /**
   * Fail a git operation
   */
  private async failOperation(operationId: string, error: string): Promise<void> {
    const operation = this.operations.get(operationId);
    if (!operation) return;

    operation.status = 'failed';
    operation.endTime = Date.now();
    operation.result = {
      commandId: '',
      processId: '',
      success: false,
      exitCode: 1,
      stdout: '',
      stderr: error,
      combinedOutput: error,
      duration: (operation.endTime || Date.now()) - operation.startTime,
      resourceUsage: { cpuTime: 0, memoryPeak: 0, diskReads: 0, diskWrites: 0 },
      metadata: {
        startTime: operation.startTime,
        endTime: operation.endTime || Date.now(),
        workingDirectory: operation.repository,
        environment: {},
        errorMessage: error
      }
    };

    this.operationHistory.push(operation);
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `git-op-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate repository ID from path
   */
  private generateRepositoryId(path: string): string {
    return `repo-${path.replace(/[^a-zA-Z0-9]/g, '-')}`;
  }

  /**
   * Validate branch name
   */
  private validateBranchName(name: string): void {
    if (!name || name.trim().length === 0) {
      throw new Error('Branch name cannot be empty');
    }

    // Git branch name restrictions
    const invalidChars = /[~^:?*[\\\s]/;
    if (invalidChars.test(name)) {
      throw new Error('Branch name contains invalid characters');
    }

    if (name.startsWith('-') || name.endsWith('.') || name.includes('..')) {
      throw new Error('Invalid branch name format');
    }
  }

  /**
   * Parse file status from git status output
   */
  private parseFileStatus(statusCode: string): GitFileChange['status'] {
    const code = statusCode[0] || statusCode[1];

    switch (code) {
      case 'A': return 'added';
      case 'M': return 'modified';
      case 'D': return 'deleted';
      case 'R': return 'renamed';
      case 'C': return 'copied';
      case 'U': return 'unmerged';
      case '?': return 'untracked';
      default: return 'modified';
    }
  }

  /**
   * Get ahead/behind information
   */
  private async getAheadBehind(repositoryPath: string): Promise<{ ahead: number; behind: number }> {
    try {
      const result = await this.executeGitCommand(['rev-list', '--left-right', '--count', 'HEAD...@{upstream}'], repositoryPath);

      if (result.success) {
        const [ahead, behind] = result.stdout.trim().split('\t').map(Number);
        return { ahead: ahead || 0, behind: behind || 0 };
      }
    } catch (error) {
      // Ignore errors (no upstream, etc.)
    }

    return { ahead: 0, behind: 0 };
  }

  /**
   * Get remote URL
   */
  private async getRemoteUrl(repositoryPath: string): Promise<string | undefined> {
    try {
      const result = await this.executeGitCommand(['remote', 'get-url', this.config.defaultRemote], repositoryPath);
      return result.success ? result.stdout.trim() : undefined;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Get remotes
   */
  private async getRemotes(repositoryPath: string): Promise<GitRemote[]> {
    try {
      const result = await this.executeGitCommand(['remote', '-v'], repositoryPath);

      if (!result.success) return [];

      const remotes: GitRemote[] = [];
      const lines = result.stdout.split('\n').filter(line => line.trim());

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 3) {
          remotes.push({
            name: parts[0],
            url: parts[1],
            type: parts[2].includes('fetch') ? 'fetch' : 'push'
          });
        }
      }

      return remotes;
    } catch (error) {
      return [];
    }
  }

  /**
   * Get git configuration
   */
  private async getConfig(repositoryPath: string): Promise<GitConfig> {
    const defaultConfig: GitConfig = {
      user: { name: '', email: '' },
      core: { editor: 'vim', autocrlf: false, safecrlf: false },
      merge: { tool: '', conflictstyle: 'merge' },
      push: { default: 'simple' },
      pull: { rebase: false }
    };

    try {
      const [userName, userEmail] = await Promise.all([
        this.getConfigValue(repositoryPath, 'user.name'),
        this.getConfigValue(repositoryPath, 'user.email')
      ]);

      return {
        ...defaultConfig,
        user: {
          name: userName || '',
          email: userEmail || ''
        }
      };
    } catch (error) {
      return defaultConfig;
    }
  }

  /**
   * Get specific config value
   */
  private async getConfigValue(repositoryPath: string, key: string): Promise<string | undefined> {
    try {
      const result = await this.executeGitCommand(['config', '--get', key], repositoryPath);
      return result.success ? result.stdout.trim() : undefined;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Get last commit
   */
  private async getLastCommit(repositoryPath: string): Promise<GitCommit | undefined> {
    try {
      const result = await this.executeGitCommand(['log', '-1', '--pretty=format:%H|%h|%an|%ae|%ct|%s'], repositoryPath);

      if (!result.success) return undefined;

      const parts = result.stdout.trim().split('|');
      if (parts.length < 6) return undefined;

      return {
        hash: parts[0],
        shortHash: parts[1],
        author: parts[2],
        email: parts[3],
        date: parseInt(parts[4]) * 1000,
        message: parts[5],
        files: [],
        parentHashes: [],
        tags: [],
        branch: '',
        stats: { insertions: 0, deletions: 0, filesChanged: 0 }
      };
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Stage files
   */
  private async stageFiles(repositoryPath: string, files: string[]): Promise<void> {
    if (files.length === 0) return;

    const result = await this.executeGitCommand(['add', ...files], repositoryPath);

    if (!result.success) {
      throw new Error(`Failed to stage files: ${result.stderr}`);
    }
  }

  /**
   * Commit changes
   */
  private async commit(repositoryPath: string, options: CommitOptions): Promise<string> {
    const args = ['commit', '-m', options.message];

    if (options.description) {
      args.push('-m', options.description);
    }

    if (options.amend) {
      args.push('--amend');
    }

    if (options.signoff) {
      args.push('--signoff');
    }

    if (options.noVerify) {
      args.push('--no-verify');
    }

    if (options.allowEmpty) {
      args.push('--allow-empty');
    }

    const result = await this.executeGitCommand(args, repositoryPath);

    if (!result.success) {
      throw new Error(`Failed to commit: ${result.stderr}`);
    }

    // Extract commit hash from output
    const hashMatch = result.stdout.match(/\[[\w\s]+ ([a-f0-9]+)\]/);
    return hashMatch ? hashMatch[1] : 'unknown';
  }

  /**
   * Analyze change patterns for commit message generation
   */
  private analyzeChangePatterns(changes: GitFileChange[]): any {
    const patterns = {
      addedFiles: changes.filter(c => c.status === 'added').length,
      modifiedFiles: changes.filter(c => c.status === 'modified').length,
      deletedFiles: changes.filter(c => c.status === 'deleted').length,
      renamedFiles: changes.filter(c => c.status === 'renamed').length,
      fileTypes: new Set(changes.map(c => c.path.split('.').pop()?.toLowerCase())),
      directories: new Set(changes.map(c => c.path.split('/')[0])),
      totalChanges: changes.length
    };

    return patterns;
  }

  /**
   * Generate conventional commit message
   */
  private generateConventionalCommitMessage(patterns: any): string {
    let type = 'chore';
    let scope = '';
    let description = '';

    // Determine type based on patterns
    if (patterns.addedFiles > 0 && patterns.modifiedFiles === 0) {
      type = 'feat';
      description = `add ${patterns.addedFiles} new file${patterns.addedFiles > 1 ? 's' : ''}`;
    } else if (patterns.deletedFiles > 0) {
      type = 'refactor';
      description = `remove ${patterns.deletedFiles} file${patterns.deletedFiles > 1 ? 's' : ''}`;
    } else if (patterns.modifiedFiles > 0) {
      type = 'fix';
      description = `update ${patterns.modifiedFiles} file${patterns.modifiedFiles > 1 ? 's' : ''}`;
    }

    // Determine scope
    if (patterns.directories.size === 1) {
      scope = Array.from(patterns.directories)[0];
    } else if (patterns.fileTypes.size === 1) {
      scope = Array.from(patterns.fileTypes)[0];
    }

    // Format message
    const scopeStr = scope ? `(${scope})` : '';
    return `${type}${scopeStr}: ${description}`;
  }

  /**
   * Generate descriptive commit message
   */
  private generateDescriptiveCommitMessage(patterns: any): string {
    const parts: string[] = [];

    if (patterns.addedFiles > 0) {
      parts.push(`Add ${patterns.addedFiles} file${patterns.addedFiles > 1 ? 's' : ''}`);
    }
    if (patterns.modifiedFiles > 0) {
      parts.push(`Update ${patterns.modifiedFiles} file${patterns.modifiedFiles > 1 ? 's' : ''}`);
    }
    if (patterns.deletedFiles > 0) {
      parts.push(`Delete ${patterns.deletedFiles} file${patterns.deletedFiles > 1 ? 's' : ''}`);
    }
    if (patterns.renamedFiles > 0) {
      parts.push(`Rename ${patterns.renamedFiles} file${patterns.renamedFiles > 1 ? 's' : ''}`);
    }

    return parts.join(', ') || 'Update files';
  }

  /**
   * Detect merge conflicts
   */
  private async detectConflicts(repositoryPath: string): Promise<string[]> {
    try {
      const result = await this.executeGitCommand(['diff', '--name-only', '--diff-filter=U'], repositoryPath);

      if (result.success) {
        return result.stdout.split('\n').filter(line => line.trim());
      }
    } catch (error) {
      console.error('Error detecting conflicts:', error);
    }

    return [];
  }

  /**
   * Resolve individual file conflict
   */
  private async resolveFileConflict(repositoryPath: string, filePath: string): Promise<ConflictResolution> {
    // This is a simplified conflict resolution
    // In a real implementation, you would use more sophisticated strategies

    try {
      // Read the conflicted file
      const result = await this.executeGitCommand(['show', `:${filePath}`], repositoryPath);

      if (result.success) {
        // For now, just use a simple strategy
        return {
          filePath,
          resolution: 'manual',
          confidence: 0.5,
          reasoning: 'Automatic resolution not implemented - manual review required'
        };
      }
    } catch (error) {
      // Ignore errors
    }

    return {
      filePath,
      resolution: 'manual',
      confidence: 0,
      reasoning: 'Could not analyze conflict - manual resolution required'
    };
  }

  /**
   * Write resolved content to file
   */
  private async writeResolvedContent(repositoryPath: string, filePath: string, content: string): Promise<void> {
    // This would need to be implemented with actual file system operations
    // For now, just log the action
    console.log(`Would write resolved content to ${filePath}`);
  }

  /**
   * Analyze merge impact
   */
  private async analyzeMergeImpact(repositoryPath: string, branch: string): Promise<ImpactAnalysis> {
    try {
      // Get files that would be affected by the merge
      const result = await this.executeGitCommand(['diff', '--name-only', `HEAD...${branch}`], repositoryPath);

      if (result.success) {
        const affectedFiles = result.stdout.split('\n').filter(line => line.trim());
        return await this.knowledgeGraph.analyzeImpact(affectedFiles);
      }
    } catch (error) {
      console.error('Error analyzing merge impact:', error);
    }

    // Return minimal impact analysis
    return {
      affectedNodes: [],
      affectedEdges: [],
      impactScore: 0.5,
      riskLevel: 'medium',
      recommendations: ['Manual review recommended before merge'],
      cascadeEffects: []
    };
  }

  /**
   * Track commit in history
   */
  private async trackCommitInHistory(
    repositoryPath: string,
    commitHash: string,
    options: CommitOptions,
    impactAnalysis?: ImpactAnalysis
  ): Promise<void> {
    try {
      const historyEntry: Omit<HistoryEntry, 'id' | 'timestamp' | 'projectId'> = {
        type: 'change',
        title: `Commit: ${options.message}`,
        description: options.description || options.message,
        rationale: 'Code changes committed to version control',
        impact: impactAnalysis?.riskLevel || 'low',
        scope: 'file',
        category: 'technical',
        author: options.agentId || 'git-integration',
        metadata: {
          affectedFiles: [],
          affectedComponents: [],
          relatedTerms: [],
          dependencies: [],
          commit: commitHash,
          tags: ['git', 'commit'],
          priority: 'normal',
          status: 'completed'
        },
        context: {
          codeChanges: []
        },
        relationships: {
          childIds: [],
          relatedIds: []
        }
      };

      await this.contextHistory.addEntry(historyEntry);
    } catch (error) {
      console.error('Error tracking commit in history:', error);
    }
  }

  /**
   * Track branch creation in history
   */
  private async trackBranchInHistory(repositoryPath: string, options: BranchOptions): Promise<void> {
    try {
      const historyEntry: Omit<HistoryEntry, 'id' | 'timestamp' | 'projectId'> = {
        type: 'milestone',
        title: `Branch created: ${options.name}`,
        description: options.description || `New branch created: ${options.name}`,
        rationale: 'Branch created for feature development or bug fix',
        impact: 'low',
        scope: 'project',
        category: 'technical',
        author: 'git-integration',
        metadata: {
          affectedFiles: [],
          affectedComponents: [],
          relatedTerms: [],
          dependencies: [],
          branch: options.name,
          tags: ['git', 'branch'],
          priority: 'normal',
          status: 'completed'
        },
        context: {},
        relationships: {
          childIds: [],
          relatedIds: []
        }
      };

      await this.contextHistory.addEntry(historyEntry);
    } catch (error) {
      console.error('Error tracking branch in history:', error);
    }
  }

  /**
   * Track merge in history
   */
  private async trackMergeInHistory(
    repositoryPath: string,
    options: MergeOptions,
    impactAnalysis?: ImpactAnalysis
  ): Promise<void> {
    try {
      const historyEntry: Omit<HistoryEntry, 'id' | 'timestamp' | 'projectId'> = {
        type: 'milestone',
        title: `Merge: ${options.branch}`,
        description: options.message || `Merged branch ${options.branch}`,
        rationale: 'Branch merged to integrate changes',
        impact: impactAnalysis?.riskLevel || 'medium',
        scope: 'project',
        category: 'technical',
        author: options.agentId || 'git-integration',
        metadata: {
          affectedFiles: [],
          affectedComponents: [],
          relatedTerms: [],
          dependencies: [],
          branch: options.branch,
          tags: ['git', 'merge'],
          priority: 'normal',
          status: 'completed'
        },
        context: {},
        relationships: {
          childIds: [],
          relatedIds: []
        }
      };

      await this.contextHistory.addEntry(historyEntry);
    } catch (error) {
      console.error('Error tracking merge in history:', error);
    }
  }

  /**
   * Update statistics
   */
  private updateStats(): void {
    const completedOps = this.operationHistory.filter(op => op.status === 'completed');
    const failedOps = this.operationHistory.filter(op => op.status === 'failed');

    this.stats.successRate = this.stats.totalOperations > 0
      ? completedOps.length / this.stats.totalOperations
      : 0;

    const durations = completedOps
      .filter(op => op.endTime)
      .map(op => op.endTime! - op.startTime);

    this.stats.averageOperationTime = durations.length > 0
      ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length
      : 0;

    // Update operation counts by agent
    for (const op of this.operationHistory) {
      if (op.agentId) {
        this.stats.operationsByAgent[op.agentId] =
          (this.stats.operationsByAgent[op.agentId] || 0) + 1;
      }
    }
  }

  /**
   * Start auto-fetch process
   */
  private startAutoFetch(): void {
    if (!this.config.autoFetch) return;

    if (this.autoFetchTimer) {
      clearInterval(this.autoFetchTimer);
    }

    this.autoFetchTimer = setInterval(() => {
      this.performAutoFetch().catch(error => {
        console.error('Error during auto-fetch:', error);
      });
    }, this.config.autoFetchInterval);
  }

  /**
   * Stop auto-fetch process
   */
  private stopAutoFetch(): void {
    if (this.autoFetchTimer) {
      clearInterval(this.autoFetchTimer);
      this.autoFetchTimer = null;
    }
  }

  /**
   * Perform automatic fetch
   */
  private async performAutoFetch(): Promise<void> {
    try {
      for (const repo of this.repositories.values()) {
        if (repo.remoteUrl) {
          await this.executeGitCommand(['fetch', this.config.defaultRemote], repo.path);
        }
      }
    } catch (error) {
      console.error('Error during auto-fetch:', error);
    }
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const stored = await this.configStore.getGlobalSetting('gitIntegration.config');
      if (stored) {
        this.config = { ...this.config, ...stored };
      }
    } catch (error) {
      console.error('Failed to load git integration configuration:', error);
    }
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    try {
      await this.configStore.setGlobalSetting('gitIntegration.config', this.config);
    } catch (error) {
      console.error('Failed to save git integration configuration:', error);
    }
  }

  /**
   * Shutdown the git integration
   */
  async shutdown(): Promise<void> {
    try {
      this.stopAutoFetch();

      // Cancel any running operations
      for (const operation of this.operations.values()) {
        if (operation.status === 'running') {
          operation.status = 'cancelled';
        }
      }

      // Clear all data
      this.repositories.clear();
      this.operations.clear();
      this.operationHistory = [];

      console.log('Git integration shut down');
    } catch (error) {
      console.error('Error during git integration shutdown:', error);
    }
  }
}

// Global instances per project
const globalGitIntegrations: Map<string, GitIntegration> = new Map();

/**
 * Get the git integration instance for a project
 */
export function getGitIntegration(projectId: string): GitIntegration {
  if (!globalGitIntegrations.has(projectId)) {
    globalGitIntegrations.set(projectId, new GitIntegration(projectId));
  }
  return globalGitIntegrations.get(projectId)!;
}
