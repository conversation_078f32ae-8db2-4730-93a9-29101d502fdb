// components/background/context-history.ts
import { KnowledgeGraph, GraphNode, GraphEdge } from './knowledge-graph';
import { ProjectDictionary, DictionaryTerm } from './project-dictionary';
import { getConfigStoreBrowser } from './config-store-browser';

export interface HistoryEntry {
  id: string;
  type: 'decision' | 'change' | 'milestone' | 'refactor' | 'bug_fix' | 'feature' | 'architecture' | 'dependency' | 'configuration';
  title: string;
  description: string;
  rationale: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  scope: 'file' | 'component' | 'module' | 'system' | 'project';
  category: 'technical' | 'business' | 'performance' | 'security' | 'usability' | 'maintenance';
  author: string;
  timestamp: number;
  projectId: string;
  metadata: {
    affectedFiles: string[];
    affectedComponents: string[];
    relatedTerms: string[];
    dependencies: string[];
    version?: string;
    branch?: string;
    commit?: string;
    pullRequest?: string;
    issue?: string;
    estimatedEffort?: number; // hours
    actualEffort?: number; // hours
    tags: string[];
    priority: 'low' | 'normal' | 'high' | 'urgent';
    status: 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'deferred';
  };
  context: {
    beforeState?: any;
    afterState?: any;
    codeChanges?: CodeChange[];
    configChanges?: ConfigChange[];
    dependencyChanges?: DependencyChange[];
    testResults?: TestResult[];
    performanceMetrics?: PerformanceMetric[];
  };
  relationships: {
    parentId?: string; // Parent decision/change
    childIds: string[]; // Child decisions/changes
    relatedIds: string[]; // Related decisions/changes
    supersededBy?: string; // If this decision was superseded
    supersedes?: string[]; // Decisions this supersedes
  };
}

export interface CodeChange {
  filePath: string;
  language: string;
  changeType: 'added' | 'modified' | 'deleted' | 'renamed' | 'moved';
  linesAdded: number;
  linesRemoved: number;
  linesModified: number;
  complexity: number;
  functions: {
    name: string;
    action: 'added' | 'modified' | 'removed';
    complexity: number;
  }[];
  classes: {
    name: string;
    action: 'added' | 'modified' | 'removed';
    methods: number;
    properties: number;
  }[];
}

export interface ConfigChange {
  configType: 'package.json' | 'tsconfig.json' | 'webpack.config.js' | 'env' | 'settings' | 'other';
  filePath: string;
  changeType: 'added' | 'modified' | 'removed';
  changes: {
    key: string;
    oldValue?: any;
    newValue?: any;
    action: 'added' | 'modified' | 'removed';
  }[];
}

export interface DependencyChange {
  packageName: string;
  changeType: 'added' | 'updated' | 'removed';
  oldVersion?: string;
  newVersion?: string;
  dependencyType: 'production' | 'development' | 'peer' | 'optional';
  reason: string;
  impact: 'low' | 'medium' | 'high';
}

export interface TestResult {
  testSuite: string;
  testFile: string;
  passed: number;
  failed: number;
  skipped: number;
  coverage: number;
  duration: number;
  errors: string[];
}

export interface PerformanceMetric {
  metric: string;
  value: number;
  unit: string;
  baseline?: number;
  improvement?: number; // percentage
  timestamp: number;
}

export interface HistoryQuery {
  types?: string[];
  categories?: string[];
  scopes?: string[];
  impacts?: string[];
  authors?: string[];
  dateRange?: {
    start: number;
    end: number;
  };
  affectedFiles?: string[];
  affectedComponents?: string[];
  tags?: string[];
  status?: string[];
  searchText?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'timestamp' | 'impact' | 'scope' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export interface HistoryStats {
  totalEntries: number;
  entriesByType: Record<string, number>;
  entriesByCategory: Record<string, number>;
  entriesByScope: Record<string, number>;
  entriesByImpact: Record<string, number>;
  entriesByAuthor: Record<string, number>;
  averageEffort: number;
  totalEffort: number;
  completionRate: number;
  recentActivity: number; // entries in last 30 days
  topAffectedFiles: { file: string; count: number }[];
  topAffectedComponents: { component: string; count: number }[];
  decisionTrends: { date: string; count: number }[];
}

export interface HistoryConfig {
  maxEntries: number;
  autoCleanup: boolean;
  cleanupInterval: number; // milliseconds
  retentionPeriod: number; // milliseconds
  enableVersioning: boolean;
  enableMetrics: boolean;
  enableRelationships: boolean;
  compressionEnabled: boolean;
}

export class ContextHistory {
  private entries: Map<string, HistoryEntry> = new Map();
  private entryIndex: Map<string, Set<string>> = new Map(); // type/category -> entry IDs
  private fileIndex: Map<string, Set<string>> = new Map(); // file -> entry IDs
  private componentIndex: Map<string, Set<string>> = new Map(); // component -> entry IDs
  private authorIndex: Map<string, Set<string>> = new Map(); // author -> entry IDs
  private tagIndex: Map<string, Set<string>> = new Map(); // tag -> entry IDs

  private knowledgeGraph: KnowledgeGraph;
  private projectDictionary: ProjectDictionary;
  private configStore: any;

  private config: HistoryConfig = {
    maxEntries: 10000,
    autoCleanup: true,
    cleanupInterval: 24 * 60 * 60 * 1000, // 24 hours
    retentionPeriod: 365 * 24 * 60 * 60 * 1000, // 1 year
    enableVersioning: true,
    enableMetrics: true,
    enableRelationships: true,
    compressionEnabled: true
  };

  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(private projectId: string) {
    this.knowledgeGraph = new KnowledgeGraph(projectId);
    this.projectDictionary = new ProjectDictionary(projectId);
    this.configStore = getConfigStoreBrowser();

    this.startCleanupProcess();
  }

  /**
   * Initialize the context history
   */
  async initialize(): Promise<void> {
    try {
      await this.knowledgeGraph.initialize();
      await this.loadConfiguration();
      await this.loadHistoryFromStorage();

      console.log('Context history initialized');
    } catch (error) {
      console.error('Failed to initialize context history:', error);
      throw error;
    }
  }

  /**
   * Add a new history entry
   */
  async addEntry(entry: Omit<HistoryEntry, 'id' | 'timestamp' | 'projectId'>): Promise<string> {
    const entryId = this.generateEntryId();
    const now = Date.now();

    const fullEntry: HistoryEntry = {
      ...entry,
      id: entryId,
      timestamp: now,
      projectId: this.projectId
    };

    // Validate entry
    this.validateEntry(fullEntry);

    // Check capacity
    if (this.entries.size >= this.config.maxEntries) {
      await this.cleanupOldEntries();
    }

    // Store entry
    this.entries.set(entryId, fullEntry);

    // Update indexes
    this.updateIndexes(fullEntry);

    // Create relationships with knowledge graph
    if (this.config.enableRelationships) {
      await this.createKnowledgeGraphRelationships(fullEntry);
    }

    // Persist to storage
    await this.saveEntryToStorage(fullEntry);

    console.log(`Added history entry: ${fullEntry.title} (${fullEntry.type})`);
    return entryId;
  }

  /**
   * Update an existing history entry
   */
  async updateEntry(entryId: string, updates: Partial<HistoryEntry>): Promise<boolean> {
    const entry = this.entries.get(entryId);
    if (!entry) {
      return false;
    }

    // Remove from old indexes
    this.removeFromIndexes(entry);

    // Apply updates
    const updatedEntry: HistoryEntry = {
      ...entry,
      ...updates,
      id: entryId, // Ensure ID doesn't change
      projectId: this.projectId // Ensure project ID doesn't change
    };

    // Validate updated entry
    this.validateEntry(updatedEntry);

    // Store updated entry
    this.entries.set(entryId, updatedEntry);

    // Update indexes
    this.updateIndexes(updatedEntry);

    // Update relationships
    if (this.config.enableRelationships) {
      await this.updateKnowledgeGraphRelationships(updatedEntry);
    }

    // Persist to storage
    await this.saveEntryToStorage(updatedEntry);

    console.log(`Updated history entry: ${updatedEntry.title} (${entryId})`);
    return true;
  }

  /**
   * Get history entry by ID
   */
  getEntry(entryId: string): HistoryEntry | undefined {
    return this.entries.get(entryId);
  }

  /**
   * Search history entries
   */
  searchEntries(query: HistoryQuery): HistoryEntry[] {
    let candidates = Array.from(this.entries.values());

    // Filter by types
    if (query.types && query.types.length > 0) {
      candidates = candidates.filter(entry => query.types!.includes(entry.type));
    }

    // Filter by categories
    if (query.categories && query.categories.length > 0) {
      candidates = candidates.filter(entry => query.categories!.includes(entry.category));
    }

    // Filter by scopes
    if (query.scopes && query.scopes.length > 0) {
      candidates = candidates.filter(entry => query.scopes!.includes(entry.scope));
    }

    // Filter by impacts
    if (query.impacts && query.impacts.length > 0) {
      candidates = candidates.filter(entry => query.impacts!.includes(entry.impact));
    }

    // Filter by authors
    if (query.authors && query.authors.length > 0) {
      candidates = candidates.filter(entry => query.authors!.includes(entry.author));
    }

    // Filter by date range
    if (query.dateRange) {
      candidates = candidates.filter(entry =>
        entry.timestamp >= query.dateRange!.start &&
        entry.timestamp <= query.dateRange!.end
      );
    }

    // Filter by affected files
    if (query.affectedFiles && query.affectedFiles.length > 0) {
      candidates = candidates.filter(entry =>
        query.affectedFiles!.some(file =>
          entry.metadata.affectedFiles.includes(file)
        )
      );
    }

    // Filter by affected components
    if (query.affectedComponents && query.affectedComponents.length > 0) {
      candidates = candidates.filter(entry =>
        query.affectedComponents!.some(component =>
          entry.metadata.affectedComponents.includes(component)
        )
      );
    }

    // Filter by tags
    if (query.tags && query.tags.length > 0) {
      candidates = candidates.filter(entry =>
        query.tags!.some(tag => entry.metadata.tags.includes(tag))
      );
    }

    // Filter by status
    if (query.status && query.status.length > 0) {
      candidates = candidates.filter(entry => query.status!.includes(entry.metadata.status));
    }

    // Text search
    if (query.searchText && query.searchText.trim().length > 0) {
      const searchLower = query.searchText.toLowerCase();
      candidates = candidates.filter(entry =>
        entry.title.toLowerCase().includes(searchLower) ||
        entry.description.toLowerCase().includes(searchLower) ||
        entry.rationale.toLowerCase().includes(searchLower)
      );
    }

    // Sort results
    const sortBy = query.sortBy || 'timestamp';
    const sortOrder = query.sortOrder || 'desc';

    candidates.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'timestamp':
          comparison = a.timestamp - b.timestamp;
          break;
        case 'impact':
          const impactOrder = { low: 1, medium: 2, high: 3, critical: 4 };
          comparison = impactOrder[a.impact] - impactOrder[b.impact];
          break;
        case 'scope':
          const scopeOrder = { file: 1, component: 2, module: 3, system: 4, project: 5 };
          comparison = scopeOrder[a.scope] - scopeOrder[b.scope];
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    // Apply pagination
    const offset = query.offset || 0;
    const limit = query.limit || candidates.length;

    return candidates.slice(offset, offset + limit);
  }

  /**
   * Get related entries for a given entry
   */
  getRelatedEntries(entryId: string): HistoryEntry[] {
    const entry = this.entries.get(entryId);
    if (!entry) return [];

    const relatedIds = new Set<string>();

    // Add direct relationships
    if (entry.relationships.parentId) {
      relatedIds.add(entry.relationships.parentId);
    }

    entry.relationships.childIds.forEach(id => relatedIds.add(id));
    entry.relationships.relatedIds.forEach(id => relatedIds.add(id));

    if (entry.relationships.supersededBy) {
      relatedIds.add(entry.relationships.supersededBy);
    }

    entry.relationships.supersedes?.forEach(id => relatedIds.add(id));

    // Find entries affecting the same files/components
    const sameFileEntries = this.findEntriesByFiles(entry.metadata.affectedFiles);
    const sameComponentEntries = this.findEntriesByComponents(entry.metadata.affectedComponents);

    sameFileEntries.forEach(e => relatedIds.add(e.id));
    sameComponentEntries.forEach(e => relatedIds.add(e.id));

    // Remove the original entry
    relatedIds.delete(entryId);

    // Convert to entries and sort by relevance
    const relatedEntries = Array.from(relatedIds)
      .map(id => this.entries.get(id))
      .filter(entry => entry !== undefined) as HistoryEntry[];

    return relatedEntries.sort((a, b) => {
      // Sort by timestamp (most recent first)
      return b.timestamp - a.timestamp;
    });
  }

  /**
   * Get project evolution timeline
   */
  getEvolutionTimeline(timeRange?: { start: number; end: number }): HistoryEntry[] {
    let entries = Array.from(this.entries.values());

    // Filter by time range if provided
    if (timeRange) {
      entries = entries.filter(entry =>
        entry.timestamp >= timeRange.start && entry.timestamp <= timeRange.end
      );
    }

    // Sort by timestamp
    return entries.sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * Get architectural changes over time
   */
  getArchitecturalChanges(): HistoryEntry[] {
    return this.searchEntries({
      types: ['architecture', 'refactor', 'milestone'],
      categories: ['technical'],
      scopes: ['system', 'project', 'module'],
      sortBy: 'timestamp',
      sortOrder: 'desc'
    });
  }

  /**
   * Get decision rationale for a specific component or file
   */
  getDecisionHistory(target: string, targetType: 'file' | 'component'): HistoryEntry[] {
    const query: HistoryQuery = {
      types: ['decision', 'architecture', 'change'],
      sortBy: 'timestamp',
      sortOrder: 'desc'
    };

    if (targetType === 'file') {
      query.affectedFiles = [target];
    } else {
      query.affectedComponents = [target];
    }

    return this.searchEntries(query);
  }

  /**
   * Get statistics about the project history
   */
  getStats(): HistoryStats {
    const entries = Array.from(this.entries.values());
    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);

    // Count by type
    const entriesByType: Record<string, number> = {};
    const entriesByCategory: Record<string, number> = {};
    const entriesByScope: Record<string, number> = {};
    const entriesByImpact: Record<string, number> = {};
    const entriesByAuthor: Record<string, number> = {};

    // File and component tracking
    const fileCount: Record<string, number> = {};
    const componentCount: Record<string, number> = {};

    // Effort tracking
    let totalEffort = 0;
    let effortCount = 0;
    let completedCount = 0;

    // Recent activity
    let recentActivity = 0;

    for (const entry of entries) {
      // Count by type
      entriesByType[entry.type] = (entriesByType[entry.type] || 0) + 1;
      entriesByCategory[entry.category] = (entriesByCategory[entry.category] || 0) + 1;
      entriesByScope[entry.scope] = (entriesByScope[entry.scope] || 0) + 1;
      entriesByImpact[entry.impact] = (entriesByImpact[entry.impact] || 0) + 1;
      entriesByAuthor[entry.author] = (entriesByAuthor[entry.author] || 0) + 1;

      // Count affected files and components
      entry.metadata.affectedFiles.forEach(file => {
        fileCount[file] = (fileCount[file] || 0) + 1;
      });

      entry.metadata.affectedComponents.forEach(component => {
        componentCount[component] = (componentCount[component] || 0) + 1;
      });

      // Track effort
      if (entry.metadata.actualEffort) {
        totalEffort += entry.metadata.actualEffort;
        effortCount++;
      }

      // Track completion
      if (entry.metadata.status === 'completed') {
        completedCount++;
      }

      // Track recent activity
      if (entry.timestamp >= thirtyDaysAgo) {
        recentActivity++;
      }
    }

    // Calculate averages
    const averageEffort = effortCount > 0 ? totalEffort / effortCount : 0;
    const completionRate = entries.length > 0 ? completedCount / entries.length : 0;

    // Top affected files and components
    const topAffectedFiles = Object.entries(fileCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([file, count]) => ({ file, count }));

    const topAffectedComponents = Object.entries(componentCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([component, count]) => ({ component, count }));

    // Decision trends (last 12 months)
    const decisionTrends = this.calculateDecisionTrends(entries);

    return {
      totalEntries: entries.length,
      entriesByType,
      entriesByCategory,
      entriesByScope,
      entriesByImpact,
      entriesByAuthor,
      averageEffort,
      totalEffort,
      completionRate,
      recentActivity,
      topAffectedFiles,
      topAffectedComponents,
      decisionTrends
    };
  }

  /**
   * Export history data
   */
  exportHistory(): {
    entries: HistoryEntry[];
    stats: HistoryStats;
    config: HistoryConfig;
  } {
    return {
      entries: Array.from(this.entries.values()),
      stats: this.getStats(),
      config: this.config
    };
  }

  /**
   * Import history data
   */
  async importHistory(data: { entries: HistoryEntry[] }): Promise<void> {
    for (const entry of data.entries) {
      // Validate entry belongs to this project
      if (entry.projectId !== this.projectId) {
        console.warn(`Skipping entry ${entry.id}: wrong project ID`);
        continue;
      }

      this.entries.set(entry.id, entry);
      this.updateIndexes(entry);
    }

    await this.saveHistoryToStorage();
    console.log(`Imported ${data.entries.length} history entries`);
  }

  /**
   * Update configuration
   */
  async updateConfig(newConfig: Partial<HistoryConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfiguration();

    // Restart cleanup process if interval changed
    if (newConfig.cleanupInterval || newConfig.autoCleanup !== undefined) {
      this.stopCleanupProcess();
      this.startCleanupProcess();
    }

    console.log('Context history configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): HistoryConfig {
    return { ...this.config };
  }

  // Private implementation methods

  /**
   * Find entries by affected files
   */
  private findEntriesByFiles(files: string[]): HistoryEntry[] {
    const entryIds = new Set<string>();

    for (const file of files) {
      const fileEntries = this.fileIndex.get(file) || new Set();
      fileEntries.forEach(id => entryIds.add(id));
    }

    return Array.from(entryIds)
      .map(id => this.entries.get(id))
      .filter(entry => entry !== undefined) as HistoryEntry[];
  }

  /**
   * Find entries by affected components
   */
  private findEntriesByComponents(components: string[]): HistoryEntry[] {
    const entryIds = new Set<string>();

    for (const component of components) {
      const componentEntries = this.componentIndex.get(component) || new Set();
      componentEntries.forEach(id => entryIds.add(id));
    }

    return Array.from(entryIds)
      .map(id => this.entries.get(id))
      .filter(entry => entry !== undefined) as HistoryEntry[];
  }

  /**
   * Calculate decision trends over time
   */
  private calculateDecisionTrends(entries: HistoryEntry[]): { date: string; count: number }[] {
    const now = Date.now();
    const oneYear = 365 * 24 * 60 * 60 * 1000;
    const oneMonth = 30 * 24 * 60 * 60 * 1000;

    const trends: { date: string; count: number }[] = [];

    for (let i = 11; i >= 0; i--) {
      const monthStart = now - (i * oneMonth);
      const monthEnd = now - ((i - 1) * oneMonth);

      const monthEntries = entries.filter(entry =>
        entry.timestamp >= monthStart &&
        entry.timestamp < monthEnd &&
        (entry.type === 'decision' || entry.type === 'architecture')
      );

      const date = new Date(monthStart).toISOString().slice(0, 7); // YYYY-MM
      trends.push({ date, count: monthEntries.length });
    }

    return trends;
  }

  /**
   * Update indexes for an entry
   */
  private updateIndexes(entry: HistoryEntry): void {
    // Type index
    this.addToIndex(this.entryIndex, entry.type, entry.id);
    this.addToIndex(this.entryIndex, entry.category, entry.id);
    this.addToIndex(this.entryIndex, entry.scope, entry.id);
    this.addToIndex(this.entryIndex, entry.impact, entry.id);

    // File index
    entry.metadata.affectedFiles.forEach(file => {
      this.addToIndex(this.fileIndex, file, entry.id);
    });

    // Component index
    entry.metadata.affectedComponents.forEach(component => {
      this.addToIndex(this.componentIndex, component, entry.id);
    });

    // Author index
    this.addToIndex(this.authorIndex, entry.author, entry.id);

    // Tag index
    entry.metadata.tags.forEach(tag => {
      this.addToIndex(this.tagIndex, tag, entry.id);
    });
  }

  /**
   * Remove entry from indexes
   */
  private removeFromIndexes(entry: HistoryEntry): void {
    // Type index
    this.removeFromIndex(this.entryIndex, entry.type, entry.id);
    this.removeFromIndex(this.entryIndex, entry.category, entry.id);
    this.removeFromIndex(this.entryIndex, entry.scope, entry.id);
    this.removeFromIndex(this.entryIndex, entry.impact, entry.id);

    // File index
    entry.metadata.affectedFiles.forEach(file => {
      this.removeFromIndex(this.fileIndex, file, entry.id);
    });

    // Component index
    entry.metadata.affectedComponents.forEach(component => {
      this.removeFromIndex(this.componentIndex, component, entry.id);
    });

    // Author index
    this.removeFromIndex(this.authorIndex, entry.author, entry.id);

    // Tag index
    entry.metadata.tags.forEach(tag => {
      this.removeFromIndex(this.tagIndex, tag, entry.id);
    });
  }

  /**
   * Add entry to index
   */
  private addToIndex(index: Map<string, Set<string>>, key: string, entryId: string): void {
    if (!index.has(key)) {
      index.set(key, new Set());
    }
    index.get(key)!.add(entryId);
  }

  /**
   * Remove entry from index
   */
  private removeFromIndex(index: Map<string, Set<string>>, key: string, entryId: string): void {
    const set = index.get(key);
    if (set) {
      set.delete(entryId);
      if (set.size === 0) {
        index.delete(key);
      }
    }
  }

  /**
   * Create relationships with knowledge graph
   */
  private async createKnowledgeGraphRelationships(entry: HistoryEntry): Promise<void> {
    try {
      // Create nodes for affected files and components
      for (const filePath of entry.metadata.affectedFiles) {
        const existingNodes = this.knowledgeGraph.findNodes({
          nodeNames: [filePath],
          nodeTypes: ['file']
        });

        if (existingNodes.length === 0) {
          await this.knowledgeGraph.addNode({
            type: 'file',
            name: filePath,
            filePath,
            language: this.detectLanguage(filePath),
            metadata: {
              lastModified: entry.timestamp
            },
            tags: ['history-tracked'],
            weight: 0.5
          });
        }
      }

      // Create relationships between entry and affected components
      for (const component of entry.metadata.affectedComponents) {
        const componentNodes = this.knowledgeGraph.findNodes({
          nodeNames: [component]
        });

        for (const node of componentNodes) {
          // Create a relationship indicating this entry affects this component
          await this.knowledgeGraph.addEdge({
            sourceId: `history-${entry.id}`,
            targetId: node.id,
            type: 'references',
            weight: this.calculateRelationshipWeight(entry),
            metadata: {
              context: `History entry: ${entry.title}`,
              direction: 'unidirectional',
              confidence: 0.8,
              lastSeen: entry.timestamp
            },
            tags: ['history-relationship']
          });
        }
      }
    } catch (error) {
      console.error('Failed to create knowledge graph relationships:', error);
    }
  }

  /**
   * Update relationships with knowledge graph
   */
  private async updateKnowledgeGraphRelationships(entry: HistoryEntry): Promise<void> {
    // For updates, we'll recreate the relationships
    await this.createKnowledgeGraphRelationships(entry);
  }

  /**
   * Calculate relationship weight based on entry impact and scope
   */
  private calculateRelationshipWeight(entry: HistoryEntry): number {
    let weight = 0.5; // Base weight

    // Adjust based on impact
    switch (entry.impact) {
      case 'critical': weight += 0.4; break;
      case 'high': weight += 0.3; break;
      case 'medium': weight += 0.2; break;
      case 'low': weight += 0.1; break;
    }

    // Adjust based on scope
    switch (entry.scope) {
      case 'project': weight += 0.3; break;
      case 'system': weight += 0.2; break;
      case 'module': weight += 0.1; break;
      case 'component': weight += 0.05; break;
      case 'file': weight += 0.02; break;
    }

    return Math.min(weight, 1.0);
  }

  /**
   * Detect programming language from file path
   */
  private detectLanguage(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();

    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
      'sql': 'sql'
    };

    return languageMap[extension || ''] || 'unknown';
  }

  /**
   * Validate history entry
   */
  private validateEntry(entry: HistoryEntry): void {
    if (!entry.title || entry.title.trim().length === 0) {
      throw new Error('Entry title is required');
    }

    if (!entry.description || entry.description.trim().length === 0) {
      throw new Error('Entry description is required');
    }

    if (!entry.author || entry.author.trim().length === 0) {
      throw new Error('Entry author is required');
    }

    if (!entry.projectId || entry.projectId.trim().length === 0) {
      throw new Error('Entry project ID is required');
    }

    // Validate enums
    const validTypes = ['decision', 'change', 'milestone', 'refactor', 'bug_fix', 'feature', 'architecture', 'dependency', 'configuration'];
    if (!validTypes.includes(entry.type)) {
      throw new Error(`Invalid entry type: ${entry.type}`);
    }

    const validImpacts = ['low', 'medium', 'high', 'critical'];
    if (!validImpacts.includes(entry.impact)) {
      throw new Error(`Invalid entry impact: ${entry.impact}`);
    }

    const validScopes = ['file', 'component', 'module', 'system', 'project'];
    if (!validScopes.includes(entry.scope)) {
      throw new Error(`Invalid entry scope: ${entry.scope}`);
    }

    const validCategories = ['technical', 'business', 'performance', 'security', 'usability', 'maintenance'];
    if (!validCategories.includes(entry.category)) {
      throw new Error(`Invalid entry category: ${entry.category}`);
    }
  }

  /**
   * Generate unique entry ID
   */
  private generateEntryId(): string {
    return `history-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup old entries when at capacity
   */
  private async cleanupOldEntries(): Promise<void> {
    const entries = Array.from(this.entries.values());

    // Sort by timestamp (oldest first)
    entries.sort((a, b) => a.timestamp - b.timestamp);

    // Remove oldest 10% of entries
    const toRemove = Math.floor(entries.length * 0.1);

    for (let i = 0; i < toRemove; i++) {
      await this.removeEntry(entries[i].id);
    }
  }

  /**
   * Remove an entry
   */
  private async removeEntry(entryId: string): Promise<boolean> {
    const entry = this.entries.get(entryId);
    if (!entry) return false;

    // Remove from indexes
    this.removeFromIndexes(entry);

    // Remove entry
    this.entries.delete(entryId);

    // Persist changes
    await this.saveHistoryToStorage();

    return true;
  }

  /**
   * Start cleanup process
   */
  private startCleanupProcess(): void {
    if (!this.config.autoCleanup) return;

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.performCleanup().catch(error => {
        console.error('Error during context history cleanup:', error);
      });
    }, this.config.cleanupInterval);
  }

  /**
   * Stop cleanup process
   */
  private stopCleanupProcess(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Perform periodic cleanup
   */
  private async performCleanup(): Promise<void> {
    try {
      const now = Date.now();
      const expiredEntries: string[] = [];

      // Find expired entries
      for (const [entryId, entry] of this.entries) {
        const age = now - entry.timestamp;
        if (age > this.config.retentionPeriod) {
          expiredEntries.push(entryId);
        }
      }

      // Remove expired entries
      for (const entryId of expiredEntries) {
        await this.removeEntry(entryId);
      }

      if (expiredEntries.length > 0) {
        console.log(`Cleaned up ${expiredEntries.length} expired history entries`);
      }
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }

  /**
   * Load configuration from storage
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const stored = await this.configStore.getGlobalSetting('contextHistory.config');
      if (stored) {
        this.config = { ...this.config, ...stored };
      }
    } catch (error) {
      console.error('Failed to load context history configuration:', error);
    }
  }

  /**
   * Save configuration to storage
   */
  private async saveConfiguration(): Promise<void> {
    try {
      await this.configStore.setGlobalSetting('contextHistory.config', this.config);
    } catch (error) {
      console.error('Failed to save context history configuration:', error);
    }
  }

  /**
   * Load history from storage
   */
  private async loadHistoryFromStorage(): Promise<void> {
    try {
      const storedEntries = await this.configStore.getGlobalSetting(`contextHistory.${this.projectId}.entries`);
      if (storedEntries && Array.isArray(storedEntries)) {
        for (const entryData of storedEntries) {
          const entry = entryData as HistoryEntry;
          this.entries.set(entry.id, entry);
          this.updateIndexes(entry);
        }
      }

      console.log(`Loaded context history: ${this.entries.size} entries`);
    } catch (error) {
      console.error('Failed to load context history from storage:', error);
    }
  }

  /**
   * Save history to storage
   */
  private async saveHistoryToStorage(): Promise<void> {
    try {
      const entryArray = Array.from(this.entries.values());
      await this.configStore.setGlobalSetting(`contextHistory.${this.projectId}.entries`, entryArray);
      console.log('Context history saved to storage');
    } catch (error) {
      console.error('Failed to save context history to storage:', error);
    }
  }

  /**
   * Save entry to storage
   */
  private async saveEntryToStorage(entry: HistoryEntry): Promise<void> {
    try {
      // For individual entry saves, we'll just trigger a full save
      // In a production system, this could be optimized
      await this.saveHistoryToStorage();
    } catch (error) {
      console.error(`Failed to save entry ${entry.id} to storage:`, error);
    }
  }

  /**
   * Shutdown the context history
   */
  async shutdown(): Promise<void> {
    try {
      this.stopCleanupProcess();
      await this.saveHistoryToStorage();

      // Clear all data
      this.entries.clear();
      this.entryIndex.clear();
      this.fileIndex.clear();
      this.componentIndex.clear();
      this.authorIndex.clear();
      this.tagIndex.clear();

      console.log('Context history shut down');
    } catch (error) {
      console.error('Error during context history shutdown:', error);
    }
  }
}

// Global instances per project
const globalContextHistories: Map<string, ContextHistory> = new Map();

/**
 * Get the context history instance for a project
 */
export function getContextHistory(projectId: string): ContextHistory {
  if (!globalContextHistories.has(projectId)) {
    globalContextHistories.set(projectId, new ContextHistory(projectId));
  }
  return globalContextHistories.get(projectId)!;
}
