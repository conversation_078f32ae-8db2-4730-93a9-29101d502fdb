// components/background/semantic-search.ts
import { BasicVectorDatabase, SearchResult, VectorSearchOptions } from './vector-database';
import { CodeIndexer } from './code-indexer';

export interface SemanticSearchQuery {
  query: string;
  context?: string;
  language?: string;
  fileType?: string;
  maxResults?: number;
  minSimilarity?: number;
}

export interface CodeContext {
  id: string;
  filePath: string;
  content: string;
  language: string;
  relevanceScore: number;
  similarity: number;
  metadata: {
    startLine?: number;
    endLine?: number;
    chunkIndex?: number;
    totalChunks?: number;
    fileSize: number;
    timestamp: number;
  };
}

export interface SearchSuggestion {
  query: string;
  description: string;
  category: 'function' | 'class' | 'variable' | 'import' | 'comment' | 'general';
}

export class SemanticSearchService {
  private vectorDb: BasicVectorDatabase;
  private indexer: CodeIndexer;
  private searchHistory: string[] = [];
  private maxHistorySize = 100;

  constructor() {
    this.vectorDb = new BasicVectorDatabase();
    this.indexer = new CodeIndexer(this.vectorDb);
  }

  /**
   * Initialize the semantic search service
   */
  async initialize(): Promise<void> {
    try {
      await this.vectorDb.initialize();
      console.log('Semantic search service initialized');
    } catch (error) {
      console.error('Failed to initialize semantic search service:', error);
      throw error;
    }
  }

  /**
   * Search for code context using semantic similarity
   */
  async searchCode(query: SemanticSearchQuery): Promise<CodeContext[]> {
    try {
      // Add to search history
      this.addToSearchHistory(query.query);

      // Prepare search options
      const searchOptions: VectorSearchOptions = {
        limit: query.maxResults || 10,
        threshold: query.minSimilarity || 0.1,
        includeMetadata: true
      };

      // Apply filters
      if (query.language) {
        searchOptions.filterByLanguage = [query.language];
      }
      
      if (query.fileType) {
        searchOptions.filterByType = [query.fileType];
      }

      // Perform vector search
      const results = await this.vectorDb.search(query.query, searchOptions);

      // Convert to CodeContext format
      const codeContexts = results.map(result => this.convertToCodeContext(result));

      // Apply additional ranking based on context
      const rankedResults = this.rankResults(codeContexts, query);

      console.log(`Semantic search found ${rankedResults.length} results for: "${query.query}"`);
      return rankedResults;

    } catch (error) {
      console.error('Semantic search failed:', error);
      return [];
    }
  }

  /**
   * Get search suggestions based on indexed content
   */
  async getSearchSuggestions(partialQuery: string): Promise<SearchSuggestion[]> {
    const suggestions: SearchSuggestion[] = [];

    // Add common programming concepts
    const commonSuggestions = [
      { query: 'function definition', description: 'Find function declarations and definitions', category: 'function' as const },
      { query: 'class implementation', description: 'Find class definitions and implementations', category: 'class' as const },
      { query: 'error handling', description: 'Find error handling patterns', category: 'general' as const },
      { query: 'API endpoints', description: 'Find API route definitions', category: 'general' as const },
      { query: 'database queries', description: 'Find database operations', category: 'general' as const },
      { query: 'component props', description: 'Find React component prop definitions', category: 'general' as const },
      { query: 'type definitions', description: 'Find TypeScript type definitions', category: 'general' as const },
      { query: 'import statements', description: 'Find module imports', category: 'import' as const }
    ];

    // Filter suggestions based on partial query
    const filtered = commonSuggestions.filter(suggestion =>
      suggestion.query.toLowerCase().includes(partialQuery.toLowerCase()) ||
      suggestion.description.toLowerCase().includes(partialQuery.toLowerCase())
    );

    suggestions.push(...filtered);

    // Add suggestions from search history
    const historySuggestions = this.searchHistory
      .filter(query => query.toLowerCase().includes(partialQuery.toLowerCase()))
      .slice(0, 5)
      .map(query => ({
        query,
        description: `Previous search: ${query}`,
        category: 'general' as const
      }));

    suggestions.push(...historySuggestions);

    return suggestions.slice(0, 10);
  }

  /**
   * Index a project for semantic search
   */
  async indexProject(projectPath: string): Promise<void> {
    try {
      console.log(`Starting semantic indexing of project: ${projectPath}`);
      const progress = await this.indexer.indexProject(projectPath);
      
      if (progress.errors.length > 0) {
        console.warn(`Indexing completed with ${progress.errors.length} errors:`, progress.errors);
      }
      
      console.log(`Semantic indexing complete: ${progress.processedFiles}/${progress.totalFiles} files processed`);
    } catch (error) {
      console.error('Failed to index project for semantic search:', error);
      throw error;
    }
  }

  /**
   * Get indexing progress
   */
  getIndexingProgress() {
    return this.indexer.getProgress();
  }

  /**
   * Check if indexing is in progress
   */
  isIndexing(): boolean {
    return this.indexer.isIndexingInProgress();
  }

  /**
   * Get database statistics
   */
  getStats() {
    return this.vectorDb.getStats();
  }

  /**
   * Clear the search index
   */
  async clearIndex(): Promise<void> {
    try {
      await this.vectorDb.clear();
      this.searchHistory = [];
      console.log('Semantic search index cleared');
    } catch (error) {
      console.error('Failed to clear semantic search index:', error);
      throw error;
    }
  }

  /**
   * Get search history
   */
  getSearchHistory(): string[] {
    return [...this.searchHistory];
  }

  /**
   * Clear search history
   */
  clearSearchHistory(): void {
    this.searchHistory = [];
  }

  /**
   * Find similar code patterns
   */
  async findSimilarCode(codeSnippet: string, maxResults: number = 5): Promise<CodeContext[]> {
    return this.searchCode({
      query: codeSnippet,
      maxResults,
      minSimilarity: 0.3
    });
  }

  /**
   * Search for specific programming constructs
   */
  async searchByConstruct(construct: 'function' | 'class' | 'interface' | 'type', name?: string): Promise<CodeContext[]> {
    let query = construct;
    if (name) {
      query += ` ${name}`;
    }

    return this.searchCode({
      query,
      maxResults: 20,
      minSimilarity: 0.2
    });
  }

  /**
   * Convert search result to code context
   */
  private convertToCodeContext(result: SearchResult): CodeContext {
    const doc = result.document;
    const metadata = doc.metadata;

    // Extract chunk information from document ID
    const isChunked = doc.id.includes('#chunk-');
    let chunkIndex: number | undefined;
    let totalChunks: number | undefined;
    let filePath = doc.id;

    if (isChunked) {
      const parts = doc.id.split('#chunk-');
      filePath = parts[0];
      chunkIndex = parseInt(parts[1]);
      // totalChunks would need to be stored in metadata or calculated
    }

    return {
      id: doc.id,
      filePath: filePath,
      content: doc.content,
      language: metadata.language || 'unknown',
      relevanceScore: result.relevanceScore,
      similarity: result.similarity,
      metadata: {
        startLine: undefined, // Would be stored in document metadata
        endLine: undefined,   // Would be stored in document metadata
        chunkIndex,
        totalChunks,
        fileSize: metadata.size,
        timestamp: metadata.timestamp
      }
    };
  }

  /**
   * Rank search results based on additional context
   */
  private rankResults(results: CodeContext[], query: SemanticSearchQuery): CodeContext[] {
    return results.sort((a, b) => {
      let scoreA = a.similarity;
      let scoreB = b.similarity;

      // Boost results that match the preferred language
      if (query.language) {
        if (a.language === query.language) scoreA += 0.1;
        if (b.language === query.language) scoreB += 0.1;
      }

      // Boost more recent files
      const ageFactorA = Math.max(0, 1 - (Date.now() - a.metadata.timestamp) / (1000 * 60 * 60 * 24 * 30)); // 30 days
      const ageFactorB = Math.max(0, 1 - (Date.now() - b.metadata.timestamp) / (1000 * 60 * 60 * 24 * 30));
      
      scoreA += ageFactorA * 0.05;
      scoreB += ageFactorB * 0.05;

      // Boost smaller files (often more focused)
      if (a.metadata.fileSize < 10000) scoreA += 0.02;
      if (b.metadata.fileSize < 10000) scoreB += 0.02;

      return scoreB - scoreA;
    });
  }

  /**
   * Add query to search history
   */
  private addToSearchHistory(query: string): void {
    // Remove if already exists
    const index = this.searchHistory.indexOf(query);
    if (index > -1) {
      this.searchHistory.splice(index, 1);
    }

    // Add to beginning
    this.searchHistory.unshift(query);

    // Limit history size
    if (this.searchHistory.length > this.maxHistorySize) {
      this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
    }
  }
}
