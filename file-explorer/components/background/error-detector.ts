// components/background/error-detector.ts

import { getMonacoIntegration, EditorEvent, MonacoEditorInstance } from './monaco-integration';
import { getSyntaxAnalyzer, SyntaxIssue } from './syntax-analyzer';
import { BasicVectorDatabase } from './vector-database';

export interface ErrorDetectionResult {
  id: string;
  editorId: string;
  filePath: string;
  timestamp: number;
  errors: DetectedError[];
  warnings: DetectedWarning[];
  suggestions: ErrorSuggestion[];
  autoFixApplied: boolean;
  analysisTime: number;
}

export interface DetectedError {
  id: string;
  type: 'syntax' | 'semantic' | 'type' | 'runtime' | 'style' | 'performance';
  severity: 'error' | 'warning' | 'info' | 'hint';
  message: string;
  line: number;
  column: number;
  endLine?: number;
  endColumn?: number;
  code?: string;
  source: string;
  rule?: string;
  fixable: boolean;
  confidence: number;
}

export interface DetectedWarning {
  id: string;
  type: 'deprecated' | 'unused' | 'inefficient' | 'accessibility' | 'security' | 'maintainability';
  message: string;
  line: number;
  column: number;
  endLine?: number;
  endColumn?: number;
  severity: number;
  fixable: boolean;
  suggestion?: string;
}

export interface ErrorSuggestion {
  id: string;
  errorId: string;
  type: 'quick_fix' | 'refactor' | 'import' | 'replace' | 'remove' | 'add';
  title: string;
  description: string;
  replacement?: string;
  line: number;
  column: number;
  endLine?: number;
  endColumn?: number;
  confidence: number;
  agentGenerated: boolean;
}

export interface ErrorDetectorConfig {
  enableRealTimeDetection: boolean;
  enableAutoFix: boolean;
  enableSemanticAnalysis: boolean;
  enableTypeChecking: boolean;
  enableStyleChecking: boolean;
  enablePerformanceAnalysis: boolean;
  autoFixConfidenceThreshold: number;
  maxSuggestionsPerError: number;
  detectionDelay: number;
}

export class ErrorDetector {
  private monacoIntegration = getMonacoIntegration();
  private syntaxAnalyzer: any; // Will be initialized with vector database
  private vectorDb: BasicVectorDatabase;
  private detectionResults: Map<string, ErrorDetectionResult> = new Map();
  private detectionTimers: Map<string, NodeJS.Timeout> = new Map();
  private config: ErrorDetectorConfig = {
    enableRealTimeDetection: true,
    enableAutoFix: false,
    enableSemanticAnalysis: true,
    enableTypeChecking: true,
    enableStyleChecking: true,
    enablePerformanceAnalysis: false,
    autoFixConfidenceThreshold: 0.9,
    maxSuggestionsPerError: 3,
    detectionDelay: 1000 // 1 second delay after typing stops
  };

  constructor(vectorDb: BasicVectorDatabase) {
    this.vectorDb = vectorDb;
    this.syntaxAnalyzer = getSyntaxAnalyzer(vectorDb);
    this.setupEventListeners();
  }

  /**
   * Configure the error detector
   */
  configure(config: Partial<ErrorDetectorConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Detect errors in a specific editor
   */
  async detectErrors(editorId: string): Promise<ErrorDetectionResult | null> {
    const editor = this.monacoIntegration.getEditor(editorId);
    if (!editor) {
      console.warn(`Editor not found: ${editorId}`);
      return null;
    }

    const startTime = Date.now();

    try {
      const result = await this.performErrorDetection(editor);
      result.analysisTime = Date.now() - startTime;

      this.detectionResults.set(editorId, result);

      // Apply auto-fixes if enabled and confidence is high enough
      if (this.config.enableAutoFix) {
        await this.applyAutoFixes(result);
      }

      return result;
    } catch (error) {
      console.error(`Error detecting errors in editor ${editorId}:`, error);
      return null;
    }
  }

  /**
   * Get cached detection result
   */
  getDetectionResult(editorId: string): ErrorDetectionResult | null {
    return this.detectionResults.get(editorId) || null;
  }

  /**
   * Get all detection results
   */
  getAllDetectionResults(): ErrorDetectionResult[] {
    return Array.from(this.detectionResults.values());
  }

  /**
   * Clear detection results
   */
  clearDetectionResults(editorId?: string): void {
    if (editorId) {
      this.detectionResults.delete(editorId);
    } else {
      this.detectionResults.clear();
    }
  }

  /**
   * Apply a specific error suggestion
   */
  async applySuggestion(editorId: string, suggestionId: string): Promise<boolean> {
    const result = this.detectionResults.get(editorId);
    if (!result) {
      return false;
    }

    const suggestion = result.suggestions.find(s => s.id === suggestionId);
    if (!suggestion || !suggestion.replacement) {
      return false;
    }

    try {
      const success = await this.monacoIntegration.replaceText(
        editorId,
        suggestion.line,
        suggestion.column,
        suggestion.endLine || suggestion.line,
        suggestion.endColumn || suggestion.column,
        suggestion.replacement,
        'error-detector'
      );

      if (success) {
        console.log(`Applied suggestion: ${suggestion.title}`);
        // Re-run detection after applying fix
        setTimeout(() => this.detectErrors(editorId), 500);
      }

      return success;
    } catch (error) {
      console.error(`Error applying suggestion ${suggestionId}:`, error);
      return false;
    }
  }

  /**
   * Generate error suggestions using AI/patterns
   */
  async generateSuggestions(error: DetectedError, content: string, language: string): Promise<ErrorSuggestion[]> {
    const suggestions: ErrorSuggestion[] = [];

    try {
      // Generate suggestions based on error type
      switch (error.type) {
        case 'syntax':
          suggestions.push(...await this.generateSyntaxSuggestions(error, content, language));
          break;
        case 'type':
          suggestions.push(...await this.generateTypeSuggestions(error, content, language));
          break;
        case 'semantic':
          suggestions.push(...await this.generateSemanticSuggestions(error, content, language));
          break;
        case 'style':
          suggestions.push(...await this.generateStyleSuggestions(error, content, language));
          break;
        case 'performance':
          suggestions.push(...await this.generatePerformanceSuggestions(error, content, language));
          break;
      }

      // Limit suggestions per error
      return suggestions
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, this.config.maxSuggestionsPerError);

    } catch (error) {
      console.error('Error generating suggestions:', error);
      return [];
    }
  }

  /**
   * Check if Monaco editor has built-in error markers
   */
  getMonacoErrors(editorId: string): DetectedError[] {
    const editor = this.monacoIntegration.getEditor(editorId);
    if (!editor || !editor.model) {
      return [];
    }

    const errors: DetectedError[] = [];

    try {
      // Get Monaco's built-in markers
      const markers = (window as any).monaco?.editor?.getModelMarkers({ resource: editor.model.uri });

      if (markers) {
        markers.forEach((marker: any, index: number) => {
          errors.push({
            id: `monaco-${index}-${Date.now()}`,
            type: this.mapMonacoSeverityToType(marker.severity),
            severity: this.mapMonacoSeverity(marker.severity),
            message: marker.message,
            line: marker.startLineNumber,
            column: marker.startColumn,
            endLine: marker.endLineNumber,
            endColumn: marker.endColumn,
            code: marker.code?.value || marker.code,
            source: marker.source || 'monaco',
            rule: marker.code?.target,
            fixable: false,
            confidence: 1.0
          });
        });
      }
    } catch (error) {
      console.warn('Error getting Monaco markers:', error);
    }

    return errors;
  }

  /**
   * Detect common JavaScript/TypeScript errors
   */
  detectLanguageSpecificErrors(content: string, language: string): DetectedError[] {
    const errors: DetectedError[] = [];
    const lines = content.split('\n');

    // Common error patterns
    const errorPatterns = {
      javascript: [
        {
          pattern: /console\.log\(/g,
          type: 'style' as const,
          severity: 'warning' as const,
          message: 'Console.log statement found - consider removing for production',
          fixable: true
        },
        {
          pattern: /var\s+\w+/g,
          type: 'style' as const,
          severity: 'info' as const,
          message: 'Use const or let instead of var',
          fixable: true
        },
        {
          pattern: /==\s*(?!==)/g,
          type: 'style' as const,
          severity: 'warning' as const,
          message: 'Use === instead of == for strict equality',
          fixable: true
        }
      ],
      typescript: [
        {
          pattern: /:\s*any\b/g,
          type: 'type' as const,
          severity: 'warning' as const,
          message: 'Avoid using "any" type - use specific types instead',
          fixable: false
        },
        {
          pattern: /@ts-ignore/g,
          type: 'type' as const,
          severity: 'warning' as const,
          message: 'Avoid @ts-ignore - fix the underlying type issue',
          fixable: false
        }
      ]
    };

    const patterns = errorPatterns[language as keyof typeof errorPatterns] || [];

    lines.forEach((line, lineIndex) => {
      patterns.forEach((pattern, patternIndex) => {
        let match;
        const regex = new RegExp(pattern.pattern.source, pattern.pattern.flags);

        while ((match = regex.exec(line)) !== null) {
          errors.push({
            id: `pattern-${lineIndex}-${patternIndex}-${Date.now()}`,
            type: pattern.type,
            severity: pattern.severity,
            message: pattern.message,
            line: lineIndex + 1,
            column: match.index + 1,
            endLine: lineIndex + 1,
            endColumn: match.index + match[0].length + 1,
            source: 'error-detector',
            rule: `pattern-${patternIndex}`,
            fixable: pattern.fixable,
            confidence: 0.8
          });
        }
      });
    });

    return errors;
  }

  private setupEventListeners(): void {
    this.monacoIntegration.onEditorEvent((event: EditorEvent) => {
      if (event.type === 'content_changed' && this.config.enableRealTimeDetection) {
        this.scheduleErrorDetection(event.editorId);
      }
    });
  }

  private scheduleErrorDetection(editorId: string): void {
    // Clear existing timer
    const existingTimer = this.detectionTimers.get(editorId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Schedule new detection
    const timer = setTimeout(() => {
      this.detectErrors(editorId);
      this.detectionTimers.delete(editorId);
    }, this.config.detectionDelay);

    this.detectionTimers.set(editorId, timer);
  }

  private async performErrorDetection(editor: MonacoEditorInstance): Promise<ErrorDetectionResult> {
    const { id: editorId, filePath, language, content } = editor;

    const result: ErrorDetectionResult = {
      id: `detection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      editorId,
      filePath,
      timestamp: Date.now(),
      errors: [],
      warnings: [],
      suggestions: [],
      autoFixApplied: false,
      analysisTime: 0
    };

    try {
      // Get Monaco's built-in errors
      const monacoErrors = this.getMonacoErrors(editorId);
      result.errors.push(...monacoErrors);

      // Detect language-specific errors
      if (this.config.enableStyleChecking) {
        const languageErrors = this.detectLanguageSpecificErrors(content, language);
        result.errors.push(...languageErrors);
      }

      // Get syntax analysis results for additional insights
      if (this.config.enableSemanticAnalysis) {
        const syntaxResult = await this.syntaxAnalyzer.analyzeEditor(editorId);
        if (syntaxResult) {
          // Convert syntax issues to errors
          syntaxResult.issues.forEach((issue: SyntaxIssue) => {
            result.errors.push({
              id: issue.id,
              type: 'semantic',
              severity: issue.type,
              message: issue.message,
              line: issue.line,
              column: issue.column,
              endLine: issue.endLine,
              endColumn: issue.endColumn,
              code: issue.code,
              source: issue.source,
              fixable: issue.fixable,
              confidence: 0.7
            });
          });
        }
      }

      // Generate suggestions for each error
      for (const error of result.errors) {
        if (error.fixable) {
          const suggestions = await this.generateSuggestions(error, content, language);
          result.suggestions.push(...suggestions);
        }
      }

      // Convert low-severity errors to warnings
      result.warnings = result.errors
        .filter(error => error.severity === 'warning' || error.severity === 'info')
        .map(error => ({
          id: error.id,
          type: error.type as any,
          message: error.message,
          line: error.line,
          column: error.column,
          endLine: error.endLine,
          endColumn: error.endColumn,
          severity: error.severity === 'warning' ? 2 : 1,
          fixable: error.fixable,
          suggestion: result.suggestions.find(s => s.errorId === error.id)?.description
        }));

      // Remove warnings from errors array
      result.errors = result.errors.filter(error =>
        error.severity === 'error' || error.severity === 'hint'
      );

      console.log(`Error detection completed for ${filePath}: ${result.errors.length} errors, ${result.warnings.length} warnings`);
      return result;

    } catch (error) {
      console.error(`Error during error detection for ${filePath}:`, error);
      return result;
    }
  }

  private async applyAutoFixes(result: ErrorDetectionResult): Promise<void> {
    const highConfidenceSuggestions = result.suggestions.filter(
      suggestion => suggestion.confidence >= this.config.autoFixConfidenceThreshold
    );

    for (const suggestion of highConfidenceSuggestions) {
      try {
        const success = await this.applySuggestion(result.editorId, suggestion.id);
        if (success) {
          result.autoFixApplied = true;
          console.log(`Auto-applied fix: ${suggestion.title}`);
        }
      } catch (error) {
        console.error(`Error applying auto-fix for suggestion ${suggestion.id}:`, error);
      }
    }
  }

  private mapMonacoSeverityToType(severity: number): DetectedError['type'] {
    // Monaco severity: 1=Hint, 2=Info, 4=Warning, 8=Error
    switch (severity) {
      case 8: return 'syntax';
      case 4: return 'style';
      case 2: return 'semantic';
      case 1: return 'style';
      default: return 'syntax';
    }
  }

  private mapMonacoSeverity(severity: number): DetectedError['severity'] {
    // Monaco severity: 1=Hint, 2=Info, 4=Warning, 8=Error
    switch (severity) {
      case 8: return 'error';
      case 4: return 'warning';
      case 2: return 'info';
      case 1: return 'hint';
      default: return 'error';
    }
  }

  private async generateSyntaxSuggestions(error: DetectedError, content: string, language: string): Promise<ErrorSuggestion[]> {
    const suggestions: ErrorSuggestion[] = [];

    // Common syntax error fixes
    const syntaxFixes = {
      'Missing semicolon': {
        replacement: ';',
        confidence: 0.9,
        description: 'Add missing semicolon'
      },
      'Missing closing brace': {
        replacement: '}',
        confidence: 0.8,
        description: 'Add missing closing brace'
      },
      'Missing opening brace': {
        replacement: '{',
        confidence: 0.8,
        description: 'Add missing opening brace'
      }
    };

    for (const [pattern, fix] of Object.entries(syntaxFixes)) {
      if (error.message.toLowerCase().includes(pattern.toLowerCase())) {
        suggestions.push({
          id: `syntax-fix-${Date.now()}`,
          errorId: error.id,
          type: 'quick_fix',
          title: fix.description,
          description: `Fix: ${fix.description}`,
          replacement: fix.replacement,
          line: error.line,
          column: error.column,
          confidence: fix.confidence,
          agentGenerated: false
        });
      }
    }

    return suggestions;
  }

  private async generateTypeSuggestions(error: DetectedError, content: string, language: string): Promise<ErrorSuggestion[]> {
    const suggestions: ErrorSuggestion[] = [];

    if (language === 'typescript') {
      // TypeScript-specific suggestions
      if (error.message.includes('any')) {
        suggestions.push({
          id: `type-fix-${Date.now()}`,
          errorId: error.id,
          type: 'replace',
          title: 'Replace any with specific type',
          description: 'Use a more specific type instead of any',
          replacement: 'unknown', // Conservative replacement
          line: error.line,
          column: error.column,
          confidence: 0.6,
          agentGenerated: true
        });
      }

      if (error.message.includes('undefined')) {
        suggestions.push({
          id: `type-fix-${Date.now()}`,
          errorId: error.id,
          type: 'add',
          title: 'Add null check',
          description: 'Add null/undefined check before usage',
          replacement: 'if (value !== undefined && value !== null) {\n  // Your code here\n}',
          line: error.line,
          column: error.column,
          confidence: 0.7,
          agentGenerated: true
        });
      }
    }

    return suggestions;
  }

  private async generateSemanticSuggestions(error: DetectedError, content: string, language: string): Promise<ErrorSuggestion[]> {
    const suggestions: ErrorSuggestion[] = [];

    try {
      // Use vector database to find similar code patterns that might help
      const errorContext = this.extractErrorContext(content, error.line, 3);
      const similarPatterns = await this.vectorDb.search(errorContext, {
        limit: 5,
        threshold: 0.5,
        filters: { language }
      });

      for (const pattern of similarPatterns) {
        if (pattern.similarity > 0.7) {
          suggestions.push({
            id: `semantic-fix-${Date.now()}`,
            errorId: error.id,
            type: 'refactor',
            title: 'Similar pattern found',
            description: `Consider using pattern from ${pattern.document.metadata?.filePath || 'similar code'}`,
            line: error.line,
            column: error.column,
            confidence: pattern.similarity * 0.8,
            agentGenerated: true
          });
        }
      }
    } catch (err) {
      console.warn('Error generating semantic suggestions:', err);
    }

    return suggestions;
  }

  private async generateStyleSuggestions(error: DetectedError, content: string, language: string): Promise<ErrorSuggestion[]> {
    const suggestions: ErrorSuggestion[] = [];

    // Common style fixes
    if (error.message.includes('console.log')) {
      suggestions.push({
        id: `style-fix-${Date.now()}`,
        errorId: error.id,
        type: 'remove',
        title: 'Remove console.log',
        description: 'Remove console.log statement for production code',
        replacement: '',
        line: error.line,
        column: error.column,
        confidence: 0.9,
        agentGenerated: false
      });
    }

    if (error.message.includes('var ')) {
      suggestions.push({
        id: `style-fix-${Date.now()}`,
        errorId: error.id,
        type: 'replace',
        title: 'Replace var with const/let',
        description: 'Use const or let instead of var for better scoping',
        replacement: 'const',
        line: error.line,
        column: error.column,
        confidence: 0.8,
        agentGenerated: false
      });
    }

    if (error.message.includes('==')) {
      suggestions.push({
        id: `style-fix-${Date.now()}`,
        errorId: error.id,
        type: 'replace',
        title: 'Use strict equality',
        description: 'Use === instead of == for strict equality comparison',
        replacement: '===',
        line: error.line,
        column: error.column,
        confidence: 0.9,
        agentGenerated: false
      });
    }

    return suggestions;
  }

  private async generatePerformanceSuggestions(error: DetectedError, content: string, language: string): Promise<ErrorSuggestion[]> {
    const suggestions: ErrorSuggestion[] = [];

    // Performance-related suggestions would be more complex
    // This is a simplified implementation
    if (error.message.includes('loop') || error.message.includes('iteration')) {
      suggestions.push({
        id: `perf-fix-${Date.now()}`,
        errorId: error.id,
        type: 'refactor',
        title: 'Optimize loop performance',
        description: 'Consider optimizing loop for better performance',
        line: error.line,
        column: error.column,
        confidence: 0.6,
        agentGenerated: true
      });
    }

    return suggestions;
  }

  private extractErrorContext(content: string, line: number, contextLines: number): string {
    const lines = content.split('\n');
    const startLine = Math.max(0, line - contextLines - 1);
    const endLine = Math.min(lines.length, line + contextLines);

    return lines.slice(startLine, endLine).join('\n');
  }
}

// Singleton instance
let errorDetector: ErrorDetector | null = null;

export function getErrorDetector(vectorDb?: BasicVectorDatabase): ErrorDetector {
  if (!errorDetector) {
    if (!vectorDb) {
      throw new Error('Vector database is required to initialize ErrorDetector');
    }
    errorDetector = new ErrorDetector(vectorDb);
  }
  return errorDetector;
}

export function shutdownErrorDetector(): void {
  if (errorDetector) {
    errorDetector.clearDetectionResults();
    errorDetector = null;
  }
}
