// components/background/coordination-protocols.ts

export interface WorkflowStep {
  id: string;
  name: string;
  type: 'task' | 'decision' | 'parallel' | 'sequential' | 'conditional';
  agentType?: string;
  requiredCapabilities: string[];
  dependencies: string[]; // step IDs that must complete first
  payload: any;
  timeout?: number; // in milliseconds
  retryPolicy?: RetryPolicy;
  rollbackAction?: RollbackAction;
  metadata?: Record<string, any>;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  createdBy: string;
  assignedAgents: Map<string, string>; // stepId -> agentId
  results: Map<string, any>; // stepId -> result
  errors: Map<string, string>; // stepId -> error
  currentStep?: string;
  rollbackSteps: string[]; // steps that need rollback if workflow fails
  metadata?: Record<string, any>;
}

export interface RetryPolicy {
  maxRetries: number;
  backoffStrategy: 'linear' | 'exponential' | 'fixed';
  baseDelay: number; // in milliseconds
  maxDelay: number; // in milliseconds
}

export interface RollbackAction {
  type: 'compensate' | 'undo' | 'notify' | 'custom';
  agentType?: string;
  payload?: any;
  timeout?: number;
}

export interface ResourceLock {
  id: string;
  resourceId: string;
  resourceType: string;
  lockType: 'read' | 'write' | 'exclusive';
  ownerId: string; // agent or workflow ID
  ownerType: 'agent' | 'workflow';
  acquiredAt: number;
  expiresAt?: number;
  metadata?: Record<string, any>;
}

export interface ConflictResolution {
  id: string;
  type: 'resource_contention' | 'agent_collision' | 'workflow_deadlock' | 'priority_conflict';
  conflictingParties: string[]; // agent or workflow IDs
  resourceInvolved?: string;
  resolutionStrategy: 'priority_based' | 'first_come_first_serve' | 'round_robin' | 'custom';
  resolvedAt?: number;
  resolution?: string;
  metadata?: Record<string, any>;
}

export interface CoordinationEvent {
  type: 'workflow_started' | 'workflow_completed' | 'workflow_failed' | 'step_completed' | 'step_failed' | 'conflict_detected' | 'conflict_resolved' | 'resource_locked' | 'resource_released';
  workflowId?: string;
  stepId?: string;
  agentId?: string;
  resourceId?: string;
  timestamp: number;
  data?: any;
}

export type CoordinationEventHandler = (event: CoordinationEvent) => void;

export interface CoordinationStats {
  activeWorkflows: number;
  completedWorkflows: number;
  failedWorkflows: number;
  totalStepsExecuted: number;
  averageWorkflowDuration: number;
  activeResourceLocks: number;
  resolvedConflicts: number;
  pendingConflicts: number;
}

export class CoordinationProtocols {
  private workflows: Map<string, Workflow> = new Map();
  private resourceLocks: Map<string, ResourceLock> = new Map();
  private activeConflicts: Map<string, ConflictResolution> = new Map();
  private eventHandlers: Map<string, CoordinationEventHandler[]> = new Map();
  private workflowHistory: Workflow[] = [];
  private maxHistorySize = 1000;
  private processingInterval: NodeJS.Timeout | null = null;
  private lockCleanupInterval: NodeJS.Timeout | null = null;
  private stats: CoordinationStats = {
    activeWorkflows: 0,
    completedWorkflows: 0,
    failedWorkflows: 0,
    totalStepsExecuted: 0,
    averageWorkflowDuration: 0,
    activeResourceLocks: 0,
    resolvedConflicts: 0,
    pendingConflicts: 0
  };

  constructor() {
    this.startProcessing();
    this.startLockCleanup();
  }

  /**
   * Create and start a new workflow
   */
  async createWorkflow(workflow: Omit<Workflow, 'id' | 'status' | 'createdAt' | 'assignedAgents' | 'results' | 'errors'>): Promise<string> {
    const workflowId = this.generateWorkflowId();

    const fullWorkflow: Workflow = {
      ...workflow,
      id: workflowId,
      status: 'pending',
      createdAt: Date.now(),
      assignedAgents: new Map(),
      results: new Map(),
      errors: new Map(),
      rollbackSteps: []
    };

    // Validate workflow
    this.validateWorkflow(fullWorkflow);

    // Store workflow
    this.workflows.set(workflowId, fullWorkflow);
    this.updateStats();

    // Emit workflow started event
    this.emitEvent({
      type: 'workflow_started',
      workflowId,
      timestamp: Date.now(),
      data: fullWorkflow
    });

    console.log(`Workflow created: ${fullWorkflow.name} (${workflowId})`);
    return workflowId;
  }

  /**
   * Start workflow execution
   */
  async startWorkflow(workflowId: string): Promise<boolean> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow || workflow.status !== 'pending') {
      return false;
    }

    workflow.status = 'running';
    workflow.startedAt = Date.now();

    // Find initial steps (no dependencies)
    const initialSteps = workflow.steps.filter(step => step.dependencies.length === 0);

    if (initialSteps.length === 0) {
      workflow.status = 'failed';
      workflow.errors.set('workflow', 'No initial steps found');
      return false;
    }

    // Start initial steps
    for (const step of initialSteps) {
      await this.executeStep(workflowId, step.id);
    }

    this.updateStats();
    return true;
  }

  /**
   * Pause workflow execution
   */
  async pauseWorkflow(workflowId: string): Promise<boolean> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow || workflow.status !== 'running') {
      return false;
    }

    workflow.status = 'paused';
    this.updateStats();

    console.log(`Workflow paused: ${workflowId}`);
    return true;
  }

  /**
   * Resume workflow execution
   */
  async resumeWorkflow(workflowId: string): Promise<boolean> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow || workflow.status !== 'paused') {
      return false;
    }

    workflow.status = 'running';
    this.updateStats();

    console.log(`Workflow resumed: ${workflowId}`);
    return true;
  }

  /**
   * Cancel workflow execution
   */
  async cancelWorkflow(workflowId: string, reason?: string): Promise<boolean> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      return false;
    }

    workflow.status = 'cancelled';
    workflow.completedAt = Date.now();

    if (reason) {
      workflow.errors.set('workflow', reason);
    }

    // Release all locks held by this workflow
    await this.releaseWorkflowLocks(workflowId);

    // Perform rollback if needed
    await this.performRollback(workflowId);

    this.addToHistory(workflow);
    this.updateStats();

    console.log(`Workflow cancelled: ${workflowId} - ${reason || 'No reason provided'}`);
    return true;
  }

  /**
   * Acquire resource lock
   */
  async acquireResourceLock(
    resourceId: string,
    resourceType: string,
    lockType: ResourceLock['lockType'],
    ownerId: string,
    ownerType: ResourceLock['ownerType'],
    timeout?: number
  ): Promise<string | null> {
    const lockId = this.generateLockId();
    const now = Date.now();

    // Check for existing locks
    const existingLock = this.findConflictingLock(resourceId, lockType);
    if (existingLock) {
      // Handle conflict
      const conflictId = await this.handleResourceConflict(resourceId, ownerId, existingLock.ownerId);
      if (!conflictId) {
        return null; // Could not resolve conflict
      }
    }

    const lock: ResourceLock = {
      id: lockId,
      resourceId,
      resourceType,
      lockType,
      ownerId,
      ownerType,
      acquiredAt: now,
      expiresAt: timeout ? now + timeout : undefined
    };

    this.resourceLocks.set(lockId, lock);
    this.updateStats();

    // Emit resource locked event
    this.emitEvent({
      type: 'resource_locked',
      resourceId,
      timestamp: now,
      data: lock
    });

    console.log(`Resource lock acquired: ${resourceId} by ${ownerId}`);
    return lockId;
  }

  /**
   * Release resource lock
   */
  async releaseResourceLock(lockId: string): Promise<boolean> {
    const lock = this.resourceLocks.get(lockId);
    if (!lock) {
      return false;
    }

    this.resourceLocks.delete(lockId);
    this.updateStats();

    // Emit resource released event
    this.emitEvent({
      type: 'resource_released',
      resourceId: lock.resourceId,
      timestamp: Date.now(),
      data: lock
    });

    console.log(`Resource lock released: ${lock.resourceId}`);
    return true;
  }

  /**
   * Get workflow by ID
   */
  getWorkflow(workflowId: string): Workflow | undefined {
    return this.workflows.get(workflowId);
  }

  /**
   * Get all active workflows
   */
  getActiveWorkflows(): Workflow[] {
    return Array.from(this.workflows.values()).filter(w =>
      w.status === 'running' || w.status === 'pending' || w.status === 'paused'
    );
  }

  /**
   * Get workflow history
   */
  getWorkflowHistory(limit?: number): Workflow[] {
    const history = [...this.workflowHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Get coordination statistics
   */
  getStats(): CoordinationStats {
    return { ...this.stats };
  }

  /**
   * Get active resource locks
   */
  getActiveResourceLocks(): ResourceLock[] {
    return Array.from(this.resourceLocks.values());
  }

  /**
   * Get active conflicts
   */
  getActiveConflicts(): ConflictResolution[] {
    return Array.from(this.activeConflicts.values());
  }

  /**
   * Subscribe to coordination events
   */
  addEventListener(eventType: CoordinationEvent['type'], handler: CoordinationEventHandler): string {
    const handlerId = `handler_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }

    this.eventHandlers.get(eventType)!.push(handler);
    return handlerId;
  }

  /**
   * Unsubscribe from coordination events
   */
  removeEventListener(eventType: CoordinationEvent['type'], handler: CoordinationEventHandler): boolean {
    const handlers = this.eventHandlers.get(eventType);
    if (!handlers) return false;

    const index = handlers.indexOf(handler);
    if (index === -1) return false;

    handlers.splice(index, 1);
    return true;
  }

  /**
   * Shutdown coordination protocols
   */
  shutdown(): void {
    // Clear processing intervals
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    if (this.lockCleanupInterval) {
      clearInterval(this.lockCleanupInterval);
      this.lockCleanupInterval = null;
    }

    // Cancel all active workflows
    for (const workflow of this.workflows.values()) {
      if (workflow.status === 'running' || workflow.status === 'pending') {
        this.cancelWorkflow(workflow.id, 'System shutdown');
      }
    }

    // Release all locks
    this.resourceLocks.clear();

    // Clear all data
    this.workflows.clear();
    this.activeConflicts.clear();
    this.eventHandlers.clear();
    this.workflowHistory = [];

    console.log('Coordination protocols shutdown');
  }

  // Private implementation methods
  private generateWorkflowId(): string {
    return `workflow-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateLockId(): string {
    return `lock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateConflictId(): string {
    return `conflict-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private validateWorkflow(workflow: Workflow): void {
    if (!workflow.name || workflow.name.trim() === '') {
      throw new Error('Workflow name is required');
    }

    if (!workflow.steps || workflow.steps.length === 0) {
      throw new Error('Workflow must have at least one step');
    }

    // Validate step dependencies
    const stepIds = new Set(workflow.steps.map(s => s.id));
    for (const step of workflow.steps) {
      for (const depId of step.dependencies) {
        if (!stepIds.has(depId)) {
          throw new Error(`Step ${step.id} has invalid dependency: ${depId}`);
        }
      }
    }

    // Check for circular dependencies
    if (this.hasCircularDependencies(workflow.steps)) {
      throw new Error('Workflow has circular dependencies');
    }
  }

  private hasCircularDependencies(steps: WorkflowStep[]): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (stepId: string): boolean => {
      if (recursionStack.has(stepId)) {
        return true; // Circular dependency found
      }

      if (visited.has(stepId)) {
        return false; // Already processed
      }

      visited.add(stepId);
      recursionStack.add(stepId);

      const step = steps.find(s => s.id === stepId);
      if (step) {
        for (const depId of step.dependencies) {
          if (hasCycle(depId)) {
            return true;
          }
        }
      }

      recursionStack.delete(stepId);
      return false;
    };

    for (const step of steps) {
      if (hasCycle(step.id)) {
        return true;
      }
    }

    return false;
  }

  private async executeStep(workflowId: string, stepId: string): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return;

    const step = workflow.steps.find(s => s.id === stepId);
    if (!step) return;

    try {
      // Check if all dependencies are completed
      const dependenciesCompleted = step.dependencies.every(depId =>
        workflow.results.has(depId)
      );

      if (!dependenciesCompleted) {
        console.log(`Step ${stepId} dependencies not yet completed`);
        return;
      }

      workflow.currentStep = stepId;

      // Execute step based on type
      let result: any;
      switch (step.type) {
        case 'task':
          result = await this.executeTaskStep(workflow, step);
          break;
        case 'decision':
          result = await this.executeDecisionStep(workflow, step);
          break;
        case 'parallel':
          result = await this.executeParallelStep(workflow, step);
          break;
        case 'sequential':
          result = await this.executeSequentialStep(workflow, step);
          break;
        case 'conditional':
          result = await this.executeConditionalStep(workflow, step);
          break;
        default:
          throw new Error(`Unknown step type: ${step.type}`);
      }

      // Store result
      workflow.results.set(stepId, result);

      // Add to rollback steps if needed
      if (step.rollbackAction) {
        workflow.rollbackSteps.push(stepId);
      }

      // Emit step completed event
      this.emitEvent({
        type: 'step_completed',
        workflowId,
        stepId,
        timestamp: Date.now(),
        data: { result }
      });

      // Check if workflow is complete
      await this.checkWorkflowCompletion(workflowId);

      // Execute next steps
      await this.executeNextSteps(workflowId, stepId);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      workflow.errors.set(stepId, errorMessage);

      // Emit step failed event
      this.emitEvent({
        type: 'step_failed',
        workflowId,
        stepId,
        timestamp: Date.now(),
        data: { error: errorMessage }
      });

      // Handle step failure
      await this.handleStepFailure(workflowId, stepId, errorMessage);
    }
  }

  private async executeTaskStep(workflow: Workflow, step: WorkflowStep): Promise<any> {
    // This would integrate with the Task Queue to execute the actual task
    // For now, return a mock result
    console.log(`Executing task step: ${step.name}`);
    return { success: true, message: `Task ${step.name} completed` };
  }

  private async executeDecisionStep(workflow: Workflow, step: WorkflowStep): Promise<any> {
    // Decision logic based on previous step results
    console.log(`Executing decision step: ${step.name}`);
    return { decision: 'continue', reason: 'All conditions met' };
  }

  private async executeParallelStep(workflow: Workflow, step: WorkflowStep): Promise<any> {
    // Execute multiple sub-steps in parallel
    console.log(`Executing parallel step: ${step.name}`);
    return { parallelResults: [], completedCount: 0 };
  }

  private async executeSequentialStep(workflow: Workflow, step: WorkflowStep): Promise<any> {
    // Execute multiple sub-steps sequentially
    console.log(`Executing sequential step: ${step.name}`);
    return { sequentialResults: [], currentIndex: 0 };
  }

  private async executeConditionalStep(workflow: Workflow, step: WorkflowStep): Promise<any> {
    // Conditional execution based on previous results
    console.log(`Executing conditional step: ${step.name}`);
    return { conditionMet: true, executedBranch: 'main' };
  }

  private async checkWorkflowCompletion(workflowId: string): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return;

    // Check if all steps are completed
    const allStepsCompleted = workflow.steps.every(step =>
      workflow.results.has(step.id) || workflow.errors.has(step.id)
    );

    if (allStepsCompleted) {
      const hasErrors = workflow.steps.some(step => workflow.errors.has(step.id));

      if (hasErrors) {
        workflow.status = 'failed';
        await this.performRollback(workflowId);

        this.emitEvent({
          type: 'workflow_failed',
          workflowId,
          timestamp: Date.now(),
          data: { errors: Array.from(workflow.errors.entries()) }
        });
      } else {
        workflow.status = 'completed';

        this.emitEvent({
          type: 'workflow_completed',
          workflowId,
          timestamp: Date.now(),
          data: { results: Array.from(workflow.results.entries()) }
        });
      }

      workflow.completedAt = Date.now();
      await this.releaseWorkflowLocks(workflowId);
      this.addToHistory(workflow);
      this.updateStats();
    }
  }

  private async executeNextSteps(workflowId: string, completedStepId: string): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow || workflow.status !== 'running') return;

    // Find steps that can now be executed (dependencies satisfied)
    const readySteps = workflow.steps.filter(step => {
      // Skip if already completed or failed
      if (workflow.results.has(step.id) || workflow.errors.has(step.id)) {
        return false;
      }

      // Check if all dependencies are completed
      return step.dependencies.every(depId => workflow.results.has(depId));
    });

    // Execute ready steps
    for (const step of readySteps) {
      await this.executeStep(workflowId, step.id);
    }
  }

  private async handleStepFailure(workflowId: string, stepId: string, error: string): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return;

    const step = workflow.steps.find(s => s.id === stepId);
    if (!step) return;

    // Check retry policy
    if (step.retryPolicy) {
      const retryCount = (step.metadata?.retryCount || 0) + 1;

      if (retryCount <= step.retryPolicy.maxRetries) {
        // Schedule retry
        step.metadata = { ...step.metadata, retryCount };

        const delay = this.calculateRetryDelay(step.retryPolicy, retryCount);
        setTimeout(() => {
          this.executeStep(workflowId, stepId);
        }, delay);

        console.log(`Retrying step ${stepId} in ${delay}ms (attempt ${retryCount})`);
        return;
      }
    }

    // No more retries, fail the workflow
    workflow.status = 'failed';
    workflow.completedAt = Date.now();

    await this.performRollback(workflowId);
    await this.releaseWorkflowLocks(workflowId);

    this.addToHistory(workflow);
    this.updateStats();
  }

  private calculateRetryDelay(retryPolicy: RetryPolicy, attempt: number): number {
    switch (retryPolicy.backoffStrategy) {
      case 'linear':
        return Math.min(retryPolicy.baseDelay * attempt, retryPolicy.maxDelay);
      case 'exponential':
        return Math.min(retryPolicy.baseDelay * Math.pow(2, attempt - 1), retryPolicy.maxDelay);
      case 'fixed':
      default:
        return retryPolicy.baseDelay;
    }
  }

  private findConflictingLock(resourceId: string, lockType: ResourceLock['lockType']): ResourceLock | null {
    for (const lock of this.resourceLocks.values()) {
      if (lock.resourceId === resourceId) {
        // Check for conflicts based on lock types
        if (lockType === 'exclusive' || lock.lockType === 'exclusive') {
          return lock; // Exclusive locks conflict with everything
        }
        if (lockType === 'write' || lock.lockType === 'write') {
          return lock; // Write locks conflict with read and write
        }
        // Read locks don't conflict with other read locks
      }
    }
    return null;
  }

  private async handleResourceConflict(resourceId: string, requesterId: string, currentOwnerId: string): Promise<string | null> {
    const conflictId = this.generateConflictId();

    const conflict: ConflictResolution = {
      id: conflictId,
      type: 'resource_contention',
      conflictingParties: [requesterId, currentOwnerId],
      resourceInvolved: resourceId,
      resolutionStrategy: 'priority_based' // Default strategy
    };

    this.activeConflicts.set(conflictId, conflict);
    this.updateStats();

    // Emit conflict detected event
    this.emitEvent({
      type: 'conflict_detected',
      resourceId,
      timestamp: Date.now(),
      data: conflict
    });

    // Resolve conflict based on strategy
    const resolved = await this.resolveConflict(conflict);

    if (resolved) {
      conflict.resolvedAt = Date.now();
      conflict.resolution = `Resolved in favor of ${requesterId}`;

      this.activeConflicts.delete(conflictId);
      this.updateStats();

      // Emit conflict resolved event
      this.emitEvent({
        type: 'conflict_resolved',
        resourceId,
        timestamp: Date.now(),
        data: conflict
      });

      return conflictId;
    }

    return null;
  }

  private async resolveConflict(conflict: ConflictResolution): Promise<boolean> {
    // Simple resolution strategy - could be enhanced with more sophisticated logic
    switch (conflict.resolutionStrategy) {
      case 'priority_based':
        // For now, always resolve in favor of the requester
        return true;
      case 'first_come_first_serve':
        // Current owner keeps the lock
        return false;
      case 'round_robin':
        // Alternate between parties
        return Math.random() > 0.5;
      default:
        return false;
    }
  }

  private async releaseWorkflowLocks(workflowId: string): Promise<void> {
    const locksToRelease = Array.from(this.resourceLocks.values())
      .filter(lock => lock.ownerId === workflowId && lock.ownerType === 'workflow');

    for (const lock of locksToRelease) {
      await this.releaseResourceLock(lock.id);
    }
  }

  private async performRollback(workflowId: string): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return;

    // Execute rollback actions in reverse order
    for (let i = workflow.rollbackSteps.length - 1; i >= 0; i--) {
      const stepId = workflow.rollbackSteps[i];
      const step = workflow.steps.find(s => s.id === stepId);

      if (step?.rollbackAction) {
        try {
          await this.executeRollbackAction(step.rollbackAction, workflow, step);
          console.log(`Rollback completed for step: ${stepId}`);
        } catch (error) {
          console.error(`Rollback failed for step ${stepId}:`, error);
        }
      }
    }
  }

  private async executeRollbackAction(action: RollbackAction, workflow: Workflow, step: WorkflowStep): Promise<void> {
    switch (action.type) {
      case 'compensate':
        // Execute compensating action
        console.log(`Executing compensating action for step: ${step.id}`);
        break;
      case 'undo':
        // Undo the step's changes
        console.log(`Undoing changes for step: ${step.id}`);
        break;
      case 'notify':
        // Send notification about rollback
        console.log(`Sending rollback notification for step: ${step.id}`);
        break;
      case 'custom':
        // Execute custom rollback logic
        console.log(`Executing custom rollback for step: ${step.id}`);
        break;
    }
  }

  private addToHistory(workflow: Workflow): void {
    this.workflowHistory.push({ ...workflow });

    // Maintain history size limit
    if (this.workflowHistory.length > this.maxHistorySize) {
      this.workflowHistory = this.workflowHistory.slice(-this.maxHistorySize);
    }
  }

  private updateStats(): void {
    const workflows = Array.from(this.workflows.values());

    this.stats.activeWorkflows = workflows.filter(w =>
      w.status === 'running' || w.status === 'pending' || w.status === 'paused'
    ).length;

    this.stats.completedWorkflows = workflows.filter(w => w.status === 'completed').length +
                                   this.workflowHistory.filter(w => w.status === 'completed').length;

    this.stats.failedWorkflows = workflows.filter(w => w.status === 'failed').length +
                                this.workflowHistory.filter(w => w.status === 'failed').length;

    // Calculate total steps executed
    this.stats.totalStepsExecuted = [...workflows, ...this.workflowHistory]
      .reduce((total, w) => total + w.results.size, 0);

    // Calculate average workflow duration
    const completedWorkflows = [...workflows, ...this.workflowHistory]
      .filter(w => w.status === 'completed' && w.startedAt && w.completedAt);

    if (completedWorkflows.length > 0) {
      this.stats.averageWorkflowDuration = completedWorkflows
        .reduce((sum, w) => sum + (w.completedAt! - w.startedAt!), 0) / completedWorkflows.length;
    }

    this.stats.activeResourceLocks = this.resourceLocks.size;
    this.stats.pendingConflicts = this.activeConflicts.size;
  }

  private emitEvent(event: CoordinationEvent): void {
    const handlers = this.eventHandlers.get(event.type);
    if (!handlers) return;

    handlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error(`Error in coordination event handler for ${event.type}:`, error);
      }
    });
  }

  private startProcessing(): void {
    // Process workflows every 500ms
    this.processingInterval = setInterval(() => {
      this.processWorkflows();
    }, 500);
  }

  private startLockCleanup(): void {
    // Clean up expired locks every 30 seconds
    this.lockCleanupInterval = setInterval(() => {
      this.cleanupExpiredLocks();
    }, 30000);
  }

  private async processWorkflows(): Promise<void> {
    const activeWorkflows = this.getActiveWorkflows();

    for (const workflow of activeWorkflows) {
      if (workflow.status === 'running') {
        // Check for ready steps
        const readySteps = workflow.steps.filter(step => {
          if (workflow.results.has(step.id) || workflow.errors.has(step.id)) {
            return false;
          }
          return step.dependencies.every(depId => workflow.results.has(depId));
        });

        // Execute ready steps
        for (const step of readySteps) {
          await this.executeStep(workflow.id, step.id);
        }
      }
    }
  }

  private cleanupExpiredLocks(): void {
    const now = Date.now();
    const expiredLocks = Array.from(this.resourceLocks.values())
      .filter(lock => lock.expiresAt && lock.expiresAt <= now);

    for (const lock of expiredLocks) {
      this.releaseResourceLock(lock.id);
      console.log(`Expired lock cleaned up: ${lock.resourceId}`);
    }
  }
}

// Global coordination protocols instance
export const globalCoordinationProtocols = new CoordinationProtocols();
