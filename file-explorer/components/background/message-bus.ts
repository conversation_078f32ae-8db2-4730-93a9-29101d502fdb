// components/background/message-bus.ts

export interface AgentMessage {
  id: string;
  type: string;
  senderId: string;
  receiverId?: string; // undefined for broadcast
  payload: any;
  timestamp: number;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  correlationId?: string; // for request-response patterns
  replyTo?: string; // for response routing
}

export interface MessageHandler {
  (message: AgentMessage): Promise<void> | void;
}

export interface MessageSubscription {
  id: string;
  agentId: string;
  messageType: string;
  handler: MessageHandler;
  filter?: (message: AgentMessage) => boolean;
}

export interface MessageBusStats {
  totalMessages: number;
  messagesPerSecond: number;
  activeSubscriptions: number;
  queuedMessages: number;
  errorCount: number;
}

export class MessageBus {
  private subscriptions: Map<string, MessageSubscription[]> = new Map();
  private messageQueue: AgentMessage[] = [];
  private isProcessing = false;
  private stats: MessageBusStats = {
    totalMessages: 0,
    messagesPerSecond: 0,
    activeSubscriptions: 0,
    queuedMessages: 0,
    errorCount: 0
  };
  private messageHistory: AgentMessage[] = [];
  private maxHistorySize = 1000;
  private processingInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startProcessing();
  }

  /**
   * Subscribe to messages of a specific type
   */
  subscribe(
    agentId: string,
    messageType: string,
    handler: MessageHandler,
    filter?: (message: AgentMessage) => boolean
  ): string {
    const subscriptionId = `${agentId}-${messageType}-${Date.now()}`;
    
    const subscription: MessageSubscription = {
      id: subscriptionId,
      agentId,
      messageType,
      handler,
      filter
    };

    if (!this.subscriptions.has(messageType)) {
      this.subscriptions.set(messageType, []);
    }
    
    this.subscriptions.get(messageType)!.push(subscription);
    this.updateStats();
    
    console.log(`Agent ${agentId} subscribed to ${messageType} messages`);
    return subscriptionId;
  }

  /**
   * Unsubscribe from messages
   */
  unsubscribe(subscriptionId: string): boolean {
    for (const [messageType, subscriptions] of this.subscriptions) {
      const index = subscriptions.findIndex(sub => sub.id === subscriptionId);
      if (index > -1) {
        subscriptions.splice(index, 1);
        if (subscriptions.length === 0) {
          this.subscriptions.delete(messageType);
        }
        this.updateStats();
        console.log(`Unsubscribed: ${subscriptionId}`);
        return true;
      }
    }
    return false;
  }

  /**
   * Publish a message to the bus
   */
  async publish(message: Omit<AgentMessage, 'id' | 'timestamp'>): Promise<void> {
    const fullMessage: AgentMessage = {
      ...message,
      id: this.generateMessageId(),
      timestamp: Date.now()
    };

    // Add to queue for processing
    this.messageQueue.push(fullMessage);
    this.stats.queuedMessages = this.messageQueue.length;
    
    // Add to history
    this.addToHistory(fullMessage);
    
    console.log(`Message published: ${fullMessage.type} from ${fullMessage.senderId}`);
  }

  /**
   * Send a direct message to a specific agent
   */
  async sendDirect(
    senderId: string,
    receiverId: string,
    messageType: string,
    payload: any,
    priority: AgentMessage['priority'] = 'normal'
  ): Promise<void> {
    await this.publish({
      type: messageType,
      senderId,
      receiverId,
      payload,
      priority
    });
  }

  /**
   * Broadcast a message to all subscribers
   */
  async broadcast(
    senderId: string,
    messageType: string,
    payload: any,
    priority: AgentMessage['priority'] = 'normal'
  ): Promise<void> {
    await this.publish({
      type: messageType,
      senderId,
      payload,
      priority
    });
  }

  /**
   * Send a request and wait for a response
   */
  async request(
    senderId: string,
    receiverId: string,
    messageType: string,
    payload: any,
    timeout: number = 5000
  ): Promise<any> {
    const correlationId = this.generateMessageId();
    const responseType = `${messageType}_response`;
    
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.unsubscribe(subscriptionId);
        reject(new Error(`Request timeout: ${messageType}`));
      }, timeout);

      const subscriptionId = this.subscribe(
        senderId,
        responseType,
        (message) => {
          if (message.correlationId === correlationId) {
            clearTimeout(timeoutId);
            this.unsubscribe(subscriptionId);
            resolve(message.payload);
          }
        }
      );

      this.publish({
        type: messageType,
        senderId,
        receiverId,
        payload,
        priority: 'normal',
        correlationId,
        replyTo: responseType
      });
    });
  }

  /**
   * Reply to a message
   */
  async reply(originalMessage: AgentMessage, senderId: string, payload: any): Promise<void> {
    if (!originalMessage.replyTo || !originalMessage.correlationId) {
      throw new Error('Cannot reply to message without replyTo or correlationId');
    }

    await this.publish({
      type: originalMessage.replyTo,
      senderId,
      receiverId: originalMessage.senderId,
      payload,
      priority: originalMessage.priority,
      correlationId: originalMessage.correlationId
    });
  }

  /**
   * Get message bus statistics
   */
  getStats(): MessageBusStats {
    return { ...this.stats };
  }

  /**
   * Get message history
   */
  getMessageHistory(limit?: number): AgentMessage[] {
    const history = [...this.messageHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Get active subscriptions for an agent
   */
  getAgentSubscriptions(agentId: string): MessageSubscription[] {
    const subscriptions: MessageSubscription[] = [];
    for (const subs of this.subscriptions.values()) {
      subscriptions.push(...subs.filter(sub => sub.agentId === agentId));
    }
    return subscriptions;
  }

  /**
   * Clear message history
   */
  clearHistory(): void {
    this.messageHistory = [];
  }

  /**
   * Shutdown the message bus
   */
  shutdown(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    this.subscriptions.clear();
    this.messageQueue = [];
    this.messageHistory = [];
    console.log('Message bus shutdown');
  }

  /**
   * Start message processing
   */
  private startProcessing(): void {
    this.processingInterval = setInterval(() => {
      this.processMessages();
    }, 10); // Process every 10ms
  }

  /**
   * Process queued messages
   */
  private async processMessages(): Promise<void> {
    if (this.isProcessing || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // Sort by priority
      this.messageQueue.sort((a, b) => {
        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

      const message = this.messageQueue.shift()!;
      await this.deliverMessage(message);
      
      this.stats.totalMessages++;
      this.stats.queuedMessages = this.messageQueue.length;
      
    } catch (error) {
      console.error('Error processing message:', error);
      this.stats.errorCount++;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Deliver a message to subscribers
   */
  private async deliverMessage(message: AgentMessage): Promise<void> {
    const subscriptions = this.subscriptions.get(message.type) || [];
    
    for (const subscription of subscriptions) {
      try {
        // Check if message is for specific receiver
        if (message.receiverId && subscription.agentId !== message.receiverId) {
          continue;
        }

        // Apply filter if present
        if (subscription.filter && !subscription.filter(message)) {
          continue;
        }

        // Deliver message
        await subscription.handler(message);
        
      } catch (error) {
        console.error(`Error delivering message to ${subscription.agentId}:`, error);
        this.stats.errorCount++;
      }
    }
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add message to history
   */
  private addToHistory(message: AgentMessage): void {
    this.messageHistory.push(message);
    if (this.messageHistory.length > this.maxHistorySize) {
      this.messageHistory = this.messageHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Update statistics
   */
  private updateStats(): void {
    this.stats.activeSubscriptions = Array.from(this.subscriptions.values())
      .reduce((total, subs) => total + subs.length, 0);
  }
}

// Global message bus instance
let globalMessageBus: MessageBus | null = null;

export function getMessageBus(): MessageBus {
  if (!globalMessageBus) {
    globalMessageBus = new MessageBus();
  }
  return globalMessageBus;
}

export function shutdownMessageBus(): void {
  if (globalMessageBus) {
    globalMessageBus.shutdown();
    globalMessageBus = null;
  }
}
