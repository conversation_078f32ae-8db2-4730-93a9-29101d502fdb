// components/background/default-configs.ts
import { NamingConventions, CodeArchitecture, StyleGuide } from './config-store';

export const DEFAULT_NAMING_CONVENTIONS: NamingConventions = {
  files: {
    caseStyle: 'kebab-case',
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    patterns: {
      component: '*.tsx',
      hook: 'use-*.ts',
      util: '*.util.ts',
      type: '*.types.ts',
      test: '*.test.ts',
      story: '*.stories.tsx'
    }
  },
  variables: {
    caseStyle: 'camelCase',
    constants: 'UPPER_SNAKE_CASE',
    private: '_'
  },
  functions: {
    caseStyle: 'camelCase',
    async: 'async',
    handlers: 'handle'
  },
  classes: {
    caseStyle: 'PascalCase',
    interfaces: 'I',
    abstract: 'Abstract',
    types: 'Type'
  }
};

export const DEFAULT_CODE_ARCHITECTURE: CodeArchitecture = {
  patterns: [
    'MVC',
    'Repository',
    'Observer',
    'Factory',
    'Singleton',
    'Strategy'
  ],
  structure: {
    srcDir: 'src',
    testDir: '__tests__',
    docsDir: 'docs',
    configDir: 'config'
  },
  dependencies: {
    allowed: [
      'react',
      'react-dom',
      'next',
      'typescript',
      'tailwindcss',
      'lucide-react',
      '@radix-ui/*'
    ],
    forbidden: [
      'jquery',
      'lodash',
      'moment'
    ],
    internal: {
      'components': ['lib', 'types', 'hooks'],
      'pages': ['components', 'lib', 'types'],
      'lib': ['types'],
      'hooks': ['lib', 'types']
    }
  },
  rules: {
    maxFileSize: 500, // lines
    maxFunctionLength: 50, // lines
    maxClassSize: 300, // lines
    cyclomaticComplexity: 10
  }
};

export const DEFAULT_STYLE_GUIDE: StyleGuide = {
  formatting: {
    indentSize: 2,
    indentType: 'spaces',
    lineLength: 100,
    trailingCommas: true,
    semicolons: true
  },
  imports: {
    sortOrder: [
      'react',
      'next',
      'external',
      'internal',
      'relative'
    ],
    groupSeparation: true,
    aliasPatterns: {
      '@/components': './components',
      '@/lib': './lib',
      '@/types': './types',
      '@/hooks': './hooks'
    }
  },
  comments: {
    requireJSDoc: true,
    headerTemplate: `/**
 * @fileoverview {description}
 * <AUTHOR>
 * @created {date}
 */`,
    todoFormat: '// TODO: {description} - {author} ({date})'
  }
};

export const TYPESCRIPT_NAMING_CONVENTIONS: NamingConventions = {
  ...DEFAULT_NAMING_CONVENTIONS,
  files: {
    ...DEFAULT_NAMING_CONVENTIONS.files,
    caseStyle: 'kebab-case',
    extensions: ['.ts', '.tsx'],
    patterns: {
      component: '*.component.tsx',
      service: '*.service.ts',
      model: '*.model.ts',
      interface: '*.interface.ts',
      type: '*.type.ts',
      enum: '*.enum.ts',
      constant: '*.constant.ts',
      util: '*.util.ts',
      test: '*.spec.ts',
      e2e: '*.e2e.ts'
    }
  },
  classes: {
    ...DEFAULT_NAMING_CONVENTIONS.classes,
    interfaces: 'I',
    abstract: 'Abstract',
    types: 'T'
  }
};

export const REACT_CODE_ARCHITECTURE: CodeArchitecture = {
  ...DEFAULT_CODE_ARCHITECTURE,
  patterns: [
    'Component Composition',
    'Render Props',
    'Higher-Order Components',
    'Custom Hooks',
    'Context Provider',
    'Compound Components'
  ],
  structure: {
    srcDir: 'src',
    testDir: '__tests__',
    docsDir: 'docs',
    configDir: 'config'
  },
  dependencies: {
    allowed: [
      'react',
      'react-dom',
      'next',
      'typescript',
      'tailwindcss',
      'lucide-react',
      '@radix-ui/*',
      'framer-motion',
      'react-hook-form',
      'zod'
    ],
    forbidden: [
      'jquery',
      'lodash',
      'moment',
      'class-transformer'
    ],
    internal: {
      'components': ['hooks', 'lib', 'types', 'context'],
      'pages': ['components', 'hooks', 'lib', 'types'],
      'hooks': ['lib', 'types', 'context'],
      'lib': ['types'],
      'context': ['types'],
      'types': []
    }
  },
  rules: {
    maxFileSize: 300,
    maxFunctionLength: 30,
    maxClassSize: 200,
    cyclomaticComplexity: 8
  }
};

export const NEXTJS_STYLE_GUIDE: StyleGuide = {
  ...DEFAULT_STYLE_GUIDE,
  imports: {
    sortOrder: [
      'react',
      'next',
      'external',
      '@/',
      'relative'
    ],
    groupSeparation: true,
    aliasPatterns: {
      '@/app': './app',
      '@/components': './components',
      '@/lib': './lib',
      '@/types': './types',
      '@/hooks': './hooks',
      '@/styles': './styles',
      '@/public': './public'
    }
  },
  comments: {
    requireJSDoc: true,
    headerTemplate: `/**
 * @fileoverview {description}
 * @module {module}
 * <AUTHOR>
 * @created {date}
 * @updated {updated}
 */`,
    todoFormat: '// TODO: {description} - {author} ({date})'
  }
};

export const PROJECT_TEMPLATES = {
  'react-typescript': {
    name: 'React TypeScript',
    namingConventions: TYPESCRIPT_NAMING_CONVENTIONS,
    codeArchitecture: REACT_CODE_ARCHITECTURE,
    styleGuide: NEXTJS_STYLE_GUIDE
  },
  'nextjs-typescript': {
    name: 'Next.js TypeScript',
    namingConventions: TYPESCRIPT_NAMING_CONVENTIONS,
    codeArchitecture: REACT_CODE_ARCHITECTURE,
    styleGuide: NEXTJS_STYLE_GUIDE
  },
  'node-typescript': {
    name: 'Node.js TypeScript',
    namingConventions: {
      ...TYPESCRIPT_NAMING_CONVENTIONS,
      files: {
        ...TYPESCRIPT_NAMING_CONVENTIONS.files,
        patterns: {
          controller: '*.controller.ts',
          service: '*.service.ts',
          model: '*.model.ts',
          middleware: '*.middleware.ts',
          route: '*.route.ts',
          util: '*.util.ts',
          test: '*.spec.ts'
        }
      }
    },
    codeArchitecture: {
      ...DEFAULT_CODE_ARCHITECTURE,
      patterns: ['MVC', 'Repository', 'Service Layer', 'Middleware', 'Factory'],
      structure: {
        srcDir: 'src',
        testDir: 'test',
        docsDir: 'docs',
        configDir: 'config'
      }
    },
    styleGuide: DEFAULT_STYLE_GUIDE
  },
  'vanilla-javascript': {
    name: 'Vanilla JavaScript',
    namingConventions: {
      ...DEFAULT_NAMING_CONVENTIONS,
      files: {
        ...DEFAULT_NAMING_CONVENTIONS.files,
        extensions: ['.js', '.mjs'],
        patterns: {
          module: '*.module.js',
          util: '*.util.js',
          test: '*.test.js'
        }
      }
    },
    codeArchitecture: DEFAULT_CODE_ARCHITECTURE,
    styleGuide: {
      ...DEFAULT_STYLE_GUIDE,
      formatting: {
        ...DEFAULT_STYLE_GUIDE.formatting,
        semicolons: false
      }
    }
  }
};

export function getProjectTemplate(templateName: keyof typeof PROJECT_TEMPLATES) {
  return PROJECT_TEMPLATES[templateName] || PROJECT_TEMPLATES['react-typescript'];
}

export function createDefaultProjectConfig(projectPath: string, templateName?: keyof typeof PROJECT_TEMPLATES) {
  const template = getProjectTemplate(templateName || 'react-typescript');
  const projectName = projectPath.split('/').pop() || 'Untitled Project';
  
  return {
    id: `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: projectName,
    path: projectPath,
    settings: {
      template: templateName || 'react-typescript',
      autoSave: true,
      autoFormat: true,
      linting: true,
      typeChecking: true
    },
    namingConventions: template.namingConventions,
    codeArchitecture: template.codeArchitecture,
    styleGuide: template.styleGuide
  };
}
