// components/background/file-system-monitor.ts

export interface FileSystemEvent {
  id: string;
  type: 'created' | 'modified' | 'deleted' | 'moved' | 'renamed';
  path: string;
  oldPath?: string; // for move/rename events
  timestamp: number;
  size?: number;
  isDirectory: boolean;
  metadata?: Record<string, any>;
  agentId?: string; // if change was made by an agent
}

export interface WatchTarget {
  id: string;
  path: string;
  recursive: boolean;
  includePatterns: string[];
  excludePatterns: string[];
  agentId?: string;
  createdAt: number;
  lastEventAt?: number;
}

export interface FileSystemStats {
  totalEvents: number;
  eventsByType: Record<string, number>;
  watchedPaths: number;
  activeWatchers: number;
  lastEventTime?: number;
  eventsPerSecond: number;
  totalBytesChanged: number;
}

export type FileSystemEventHandler = (event: FileSystemEvent) => void;

export class FileSystemMonitor {
  private watchers: Map<string, WatchTarget> = new Map();
  private eventHistory: FileSystemEvent[] = new Map();
  private maxHistorySize = 1000;
  private eventHandlers: FileSystemEventHandler[] = [];
  private stats: FileSystemStats = {
    totalEvents: 0,
    eventsByType: {},
    watchedPaths: 0,
    activeWatchers: 0,
    eventsPerSecond: 0,
    totalBytesChanged: 0
  };
  private eventRateTracker: number[] = []; // timestamps for rate calculation
  private isMonitoring = false;
  private pollingInterval?: NodeJS.Timeout;
  private fileStates: Map<string, { size: number; modified: number; hash?: string }> = new Map();

  /**
   * Start monitoring file system changes
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    
    // Start polling for changes (fallback for environments without native file watching)
    this.startPolling();
    
    console.log('File system monitoring started');
  }

  /**
   * Stop monitoring file system changes
   */
  async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    // Stop polling
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = undefined;
    }

    // Clear all watchers
    this.watchers.clear();
    this.updateStats();
    
    console.log('File system monitoring stopped');
  }

  /**
   * Add a path to watch for changes
   */
  async watchPath(
    path: string,
    options?: {
      recursive?: boolean;
      includePatterns?: string[];
      excludePatterns?: string[];
      agentId?: string;
    }
  ): Promise<string> {
    const watchId = this.generateWatchId();
    
    const watchTarget: WatchTarget = {
      id: watchId,
      path,
      recursive: options?.recursive ?? true,
      includePatterns: options?.includePatterns ?? ['*'],
      excludePatterns: options?.excludePatterns ?? [
        'node_modules/**',
        '.git/**',
        '.next/**',
        'dist/**',
        'build/**',
        '*.log',
        '.DS_Store',
        'Thumbs.db'
      ],
      agentId: options?.agentId,
      createdAt: Date.now()
    };

    this.watchers.set(watchId, watchTarget);
    
    // Initialize file states for this path
    await this.initializeFileStates(path, watchTarget.recursive);
    
    this.updateStats();
    
    console.log(`Watching path: ${path} (${watchId})`);
    return watchId;
  }

  /**
   * Stop watching a specific path
   */
  async unwatchPath(watchId: string): Promise<boolean> {
    const watchTarget = this.watchers.get(watchId);
    if (!watchTarget) {
      return false;
    }

    this.watchers.delete(watchId);
    this.updateStats();
    
    console.log(`Stopped watching: ${watchTarget.path} (${watchId})`);
    return true;
  }

  /**
   * Add event handler
   */
  onFileSystemEvent(handler: FileSystemEventHandler): void {
    this.eventHandlers.push(handler);
  }

  /**
   * Remove event handler
   */
  offFileSystemEvent(handler: FileSystemEventHandler): void {
    const index = this.eventHandlers.indexOf(handler);
    if (index > -1) {
      this.eventHandlers.splice(index, 1);
    }
  }

  /**
   * Get watched paths
   */
  getWatchedPaths(): WatchTarget[] {
    return Array.from(this.watchers.values());
  }

  /**
   * Get event history
   */
  getEventHistory(limit?: number): FileSystemEvent[] {
    const history = [...this.eventHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Get monitoring statistics
   */
  getStats(): FileSystemStats {
    return { ...this.stats };
  }

  /**
   * Clear event history
   */
  clearHistory(): void {
    this.eventHistory = [];
    this.updateStats();
  }

  /**
   * Check if a path is being watched
   */
  isPathWatched(path: string): boolean {
    for (const watchTarget of this.watchers.values()) {
      if (this.pathMatches(path, watchTarget)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Manually trigger a file system scan
   */
  async scanForChanges(): Promise<FileSystemEvent[]> {
    const events: FileSystemEvent[] = [];
    
    for (const watchTarget of this.watchers.values()) {
      const pathEvents = await this.scanPath(watchTarget);
      events.push(...pathEvents);
    }
    
    return events;
  }

  /**
   * Shutdown file system monitor
   */
  shutdown(): void {
    this.stopMonitoring();
    this.eventHandlers = [];
    this.eventHistory = [];
    this.fileStates.clear();
    console.log('File system monitor shutdown');
  }

  // Private implementation methods
  private generateWatchId(): string {
    return `watch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private startPolling(): void {
    // Poll for changes every 2 seconds
    this.pollingInterval = setInterval(async () => {
      if (this.isMonitoring) {
        await this.scanForChanges();
      }
    }, 2000);
  }

  private async initializeFileStates(rootPath: string, recursive: boolean): Promise<void> {
    try {
      // This would integrate with the actual file system
      // For browser environment, we'll use the existing Electron API
      if (typeof window !== 'undefined' && window.electronAPI) {
        await this.initializeElectronFileStates(rootPath, recursive);
      }
    } catch (error) {
      console.error(`Failed to initialize file states for ${rootPath}:`, error);
    }
  }

  private async initializeElectronFileStates(rootPath: string, recursive: boolean): Promise<void> {
    try {
      const result = await window.electronAPI.readDirectory(rootPath);
      if (!result.success) {
        console.error(`Failed to read directory ${rootPath}:`, result.error);
        return;
      }

      for (const item of result.items) {
        if (!item.isDirectory) {
          this.fileStates.set(item.path, {
            size: item.size || 0,
            modified: item.modified ? new Date(item.modified).getTime() : Date.now()
          });
        } else if (recursive) {
          await this.initializeElectronFileStates(item.path, recursive);
        }
      }
    } catch (error) {
      console.error(`Error initializing file states:`, error);
    }
  }

  private async scanPath(watchTarget: WatchTarget): Promise<FileSystemEvent[]> {
    const events: FileSystemEvent[] = [];
    
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const pathEvents = await this.scanElectronPath(watchTarget);
        events.push(...pathEvents);
      }
    } catch (error) {
      console.error(`Error scanning path ${watchTarget.path}:`, error);
    }
    
    return events;
  }

  private async scanElectronPath(watchTarget: WatchTarget): Promise<FileSystemEvent[]> {
    const events: FileSystemEvent[] = [];
    
    try {
      const result = await window.electronAPI.readDirectory(watchTarget.path);
      if (!result.success) {
        return events;
      }

      for (const item of result.items) {
        if (!this.pathMatches(item.path, watchTarget)) {
          continue;
        }

        if (!item.isDirectory) {
          const currentState = {
            size: item.size || 0,
            modified: item.modified ? new Date(item.modified).getTime() : Date.now()
          };

          const previousState = this.fileStates.get(item.path);
          
          if (!previousState) {
            // New file
            const event = this.createEvent('created', item.path, false, currentState.size, watchTarget.agentId);
            events.push(event);
            this.fileStates.set(item.path, currentState);
          } else if (
            currentState.size !== previousState.size ||
            currentState.modified !== previousState.modified
          ) {
            // Modified file
            const event = this.createEvent('modified', item.path, false, currentState.size, watchTarget.agentId);
            events.push(event);
            this.fileStates.set(item.path, currentState);
          }
        } else if (watchTarget.recursive) {
          // Recursively scan subdirectories
          const subWatchTarget: WatchTarget = {
            ...watchTarget,
            path: item.path
          };
          const subEvents = await this.scanElectronPath(subWatchTarget);
          events.push(...subEvents);
        }
      }

      // Check for deleted files
      for (const [filePath, _] of this.fileStates.entries()) {
        if (filePath.startsWith(watchTarget.path) && 
            !result.items.some(item => item.path === filePath)) {
          const event = this.createEvent('deleted', filePath, false, 0, watchTarget.agentId);
          events.push(event);
          this.fileStates.delete(filePath);
        }
      }

    } catch (error) {
      console.error(`Error scanning Electron path:`, error);
    }

    // Process events
    for (const event of events) {
      this.processEvent(event);
    }
    
    return events;
  }

  private createEvent(
    type: FileSystemEvent['type'],
    path: string,
    isDirectory: boolean,
    size?: number,
    agentId?: string
  ): FileSystemEvent {
    return {
      id: this.generateEventId(),
      type,
      path,
      timestamp: Date.now(),
      size,
      isDirectory,
      agentId,
      metadata: {
        watcherCount: this.watchers.size,
        monitoringActive: this.isMonitoring
      }
    };
  }

  private processEvent(event: FileSystemEvent): void {
    // Add to history
    this.eventHistory.push(event);
    
    // Maintain history size limit
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }

    // Update statistics
    this.updateEventStats(event);

    // Notify handlers
    for (const handler of this.eventHandlers) {
      try {
        handler(event);
      } catch (error) {
        console.error('Error in file system event handler:', error);
      }
    }

    console.log(`File system event: ${event.type} - ${event.path}`);
  }

  private pathMatches(path: string, watchTarget: WatchTarget): boolean {
    // Check if path is under the watched path
    if (!path.startsWith(watchTarget.path)) {
      return false;
    }

    // Check exclude patterns
    for (const pattern of watchTarget.excludePatterns) {
      if (this.matchesPattern(path, pattern)) {
        return false;
      }
    }

    // Check include patterns
    if (watchTarget.includePatterns.length > 0) {
      return watchTarget.includePatterns.some(pattern => this.matchesPattern(path, pattern));
    }

    return true;
  }

  private matchesPattern(path: string, pattern: string): boolean {
    // Simple pattern matching (could be enhanced with proper glob matching)
    if (pattern === '*') return true;
    if (pattern.endsWith('/**')) {
      const prefix = pattern.slice(0, -3);
      return path.includes(prefix);
    }
    if (pattern.startsWith('*.')) {
      const extension = pattern.slice(1);
      return path.endsWith(extension);
    }
    return path.includes(pattern);
  }

  private updateEventStats(event: FileSystemEvent): void {
    this.stats.totalEvents++;
    this.stats.eventsByType[event.type] = (this.stats.eventsByType[event.type] || 0) + 1;
    this.stats.lastEventTime = event.timestamp;
    
    if (event.size) {
      this.stats.totalBytesChanged += event.size;
    }

    // Update events per second calculation
    const now = Date.now();
    this.eventRateTracker.push(now);
    
    // Keep only events from the last second
    this.eventRateTracker = this.eventRateTracker.filter(timestamp => now - timestamp < 1000);
    this.stats.eventsPerSecond = this.eventRateTracker.length;
  }

  private updateStats(): void {
    this.stats.watchedPaths = this.watchers.size;
    this.stats.activeWatchers = Array.from(this.watchers.values()).length;
  }
}

// Global file system monitor instance
export const globalFileSystemMonitor = new FileSystemMonitor();
