// components/background/agent-registry.ts

export interface AgentRegistration {
  id: string;
  name: string;
  type: 'orchestrator' | 'implementation' | 'specialized' | 'middleware';
  capabilities: string[];
  status: 'online' | 'offline' | 'busy' | 'idle' | 'error';
  version: string;
  endpoint?: string; // For remote agents
  metadata: Record<string, any>;
  registeredAt: number;
  lastHeartbeat: number;
  heartbeatInterval: number; // in milliseconds
  maxConcurrentTasks: number;
  currentLoad: number; // 0-100 percentage
  healthScore: number; // 0-100 percentage
  tags: string[];
}

export interface AgentHealthMetrics {
  agentId: string;
  tokensUsed: number;
  tasksCompleted: number;
  tasksFailedCount: number;
  averageResponseTime: number;
  errorRate: number; // 0-1
  successRate: number; // 0-1
  uptime: number; // in milliseconds
  lastErrorTime?: number;
  lastSuccessTime?: number;
}

export interface ServiceDiscoveryQuery {
  capabilities?: string[];
  type?: AgentRegistration['type'];
  status?: AgentRegistration['status'][];
  tags?: string[];
  minHealthScore?: number;
  maxLoad?: number;
  excludeAgents?: string[];
  sortBy?: 'health' | 'load' | 'responseTime' | 'successRate' | 'lastActive';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
}

export interface AgentRegistryStats {
  totalAgents: number;
  onlineAgents: number;
  busyAgents: number;
  idleAgents: number;
  errorAgents: number;
  averageHealthScore: number;
  averageLoad: number;
  totalCapabilities: number;
  agentsByType: Record<string, number>;
}

export interface AgentEvent {
  type: 'registered' | 'unregistered' | 'status_changed' | 'health_updated' | 'heartbeat_missed';
  agentId: string;
  timestamp: number;
  data?: any;
}

export type AgentEventHandler = (event: AgentEvent) => void;

export class AgentRegistry {
  private agents: Map<string, AgentRegistration> = new Map();
  private healthMetrics: Map<string, AgentHealthMetrics> = new Map();
  private eventHandlers: Map<string, AgentEventHandler[]> = new Map();
  private heartbeatTimers: Map<string, NodeJS.Timeout> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;
  private stats: AgentRegistryStats = {
    totalAgents: 0,
    onlineAgents: 0,
    busyAgents: 0,
    idleAgents: 0,
    errorAgents: 0,
    averageHealthScore: 0,
    averageLoad: 0,
    totalCapabilities: 0,
    agentsByType: {}
  };

  constructor() {
    this.startCleanupProcess();
  }

  /**
   * Register a new agent
   */
  async registerAgent(registration: Omit<AgentRegistration, 'registeredAt' | 'lastHeartbeat'>): Promise<void> {
    const now = Date.now();

    const fullRegistration: AgentRegistration = {
      ...registration,
      registeredAt: now,
      lastHeartbeat: now
    };

    // Validate registration
    this.validateRegistration(fullRegistration);

    // Store agent registration
    this.agents.set(registration.id, fullRegistration);

    // Initialize health metrics
    this.healthMetrics.set(registration.id, {
      agentId: registration.id,
      tokensUsed: 0,
      tasksCompleted: 0,
      tasksFailedCount: 0,
      averageResponseTime: 0,
      errorRate: 0,
      successRate: 1,
      uptime: 0,
      lastSuccessTime: now
    });

    // Set up heartbeat monitoring
    this.setupHeartbeatMonitoring(registration.id, registration.heartbeatInterval);

    // Update statistics
    this.updateStats();

    // Emit registration event
    this.emitEvent({
      type: 'registered',
      agentId: registration.id,
      timestamp: now,
      data: fullRegistration
    });

    console.log(`Agent registered: ${registration.id} (${registration.name})`);
  }

  /**
   * Unregister an agent
   */
  async unregisterAgent(agentId: string): Promise<boolean> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      return false;
    }

    // Clean up heartbeat monitoring
    this.clearHeartbeatMonitoring(agentId);

    // Remove from registry
    this.agents.delete(agentId);
    this.healthMetrics.delete(agentId);

    // Update statistics
    this.updateStats();

    // Emit unregistration event
    this.emitEvent({
      type: 'unregistered',
      agentId,
      timestamp: Date.now(),
      data: agent
    });

    console.log(`Agent unregistered: ${agentId}`);
    return true;
  }

  /**
   * Update agent status
   */
  async updateAgentStatus(agentId: string, status: AgentRegistration['status']): Promise<boolean> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      return false;
    }

    const oldStatus = agent.status;
    agent.status = status;
    agent.lastHeartbeat = Date.now();

    // Update statistics
    this.updateStats();

    // Emit status change event
    if (oldStatus !== status) {
      this.emitEvent({
        type: 'status_changed',
        agentId,
        timestamp: Date.now(),
        data: { oldStatus, newStatus: status }
      });
    }

    return true;
  }

  /**
   * Update agent health metrics
   */
  async updateAgentHealth(agentId: string, metrics: Partial<AgentHealthMetrics>): Promise<boolean> {
    const existingMetrics = this.healthMetrics.get(agentId);
    if (!existingMetrics) {
      return false;
    }

    // Update metrics
    Object.assign(existingMetrics, metrics);

    // Calculate health score
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.healthScore = this.calculateHealthScore(existingMetrics);
    }

    // Update statistics
    this.updateStats();

    // Emit health update event
    this.emitEvent({
      type: 'health_updated',
      agentId,
      timestamp: Date.now(),
      data: existingMetrics
    });

    return true;
  }

  /**
   * Record agent heartbeat
   */
  async heartbeat(agentId: string, metadata?: Record<string, any>): Promise<boolean> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      return false;
    }

    agent.lastHeartbeat = Date.now();

    if (metadata) {
      Object.assign(agent.metadata, metadata);
    }

    // Update uptime in health metrics
    const healthMetrics = this.healthMetrics.get(agentId);
    if (healthMetrics) {
      healthMetrics.uptime = agent.lastHeartbeat - agent.registeredAt;
    }

    return true;
  }

  /**
   * Discover agents based on query criteria
   */
  discoverAgents(query: ServiceDiscoveryQuery = {}): AgentRegistration[] {
    let agents = Array.from(this.agents.values());

    // Apply filters
    if (query.capabilities && query.capabilities.length > 0) {
      agents = agents.filter(agent =>
        query.capabilities!.every(cap => agent.capabilities.includes(cap))
      );
    }

    if (query.type) {
      agents = agents.filter(agent => agent.type === query.type);
    }

    if (query.status && query.status.length > 0) {
      agents = agents.filter(agent => query.status!.includes(agent.status));
    }

    if (query.tags && query.tags.length > 0) {
      agents = agents.filter(agent =>
        query.tags!.some(tag => agent.tags.includes(tag))
      );
    }

    if (query.minHealthScore !== undefined) {
      agents = agents.filter(agent => agent.healthScore >= query.minHealthScore!);
    }

    if (query.maxLoad !== undefined) {
      agents = agents.filter(agent => agent.currentLoad <= query.maxLoad!);
    }

    if (query.excludeAgents && query.excludeAgents.length > 0) {
      agents = agents.filter(agent => !query.excludeAgents!.includes(agent.id));
    }

    // Apply sorting
    if (query.sortBy) {
      agents.sort((a, b) => {
        let valueA: number, valueB: number;

        switch (query.sortBy) {
          case 'health':
            valueA = a.healthScore;
            valueB = b.healthScore;
            break;
          case 'load':
            valueA = a.currentLoad;
            valueB = b.currentLoad;
            break;
          case 'responseTime':
            const metricsA = this.healthMetrics.get(a.id);
            const metricsB = this.healthMetrics.get(b.id);
            valueA = metricsA?.averageResponseTime || 0;
            valueB = metricsB?.averageResponseTime || 0;
            break;
          case 'successRate':
            const healthA = this.healthMetrics.get(a.id);
            const healthB = this.healthMetrics.get(b.id);
            valueA = healthA?.successRate || 0;
            valueB = healthB?.successRate || 0;
            break;
          case 'lastActive':
            valueA = a.lastHeartbeat;
            valueB = b.lastHeartbeat;
            break;
          default:
            valueA = 0;
            valueB = 0;
        }

        const order = query.sortOrder === 'asc' ? 1 : -1;
        return (valueA - valueB) * order;
      });
    }

    // Apply limit
    if (query.limit && query.limit > 0) {
      agents = agents.slice(0, query.limit);
    }

    return agents;
  }

  /**
   * Get agent by ID
   */
  getAgent(agentId: string): AgentRegistration | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Get all registered agents
   */
  getAllAgents(): AgentRegistration[] {
    return Array.from(this.agents.values());
  }

  /**
   * Get agent health metrics
   */
  getAgentHealth(agentId: string): AgentHealthMetrics | undefined {
    return this.healthMetrics.get(agentId);
  }

  /**
   * Get registry statistics
   */
  getStats(): AgentRegistryStats {
    return { ...this.stats };
  }

  /**
   * Get all unique capabilities across all agents
   */
  getAllCapabilities(): string[] {
    const capabilities = new Set<string>();
    for (const agent of this.agents.values()) {
      agent.capabilities.forEach(cap => capabilities.add(cap));
    }
    return Array.from(capabilities).sort();
  }

  /**
   * Find best agent for a specific capability
   */
  findBestAgentForCapability(capability: string): AgentRegistration | null {
    const candidates = this.discoverAgents({
      capabilities: [capability],
      status: ['online', 'idle'],
      sortBy: 'health',
      sortOrder: 'desc',
      limit: 1
    });

    return candidates.length > 0 ? candidates[0] : null;
  }

  /**
   * Subscribe to agent events
   */
  addEventListener(eventType: AgentEvent['type'], handler: AgentEventHandler): string {
    const handlerId = `handler_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }

    this.eventHandlers.get(eventType)!.push(handler);
    return handlerId;
  }

  /**
   * Unsubscribe from agent events
   */
  removeEventListener(eventType: AgentEvent['type'], handler: AgentEventHandler): boolean {
    const handlers = this.eventHandlers.get(eventType);
    if (!handlers) return false;

    const index = handlers.indexOf(handler);
    if (index === -1) return false;

    handlers.splice(index, 1);
    return true;
  }

  /**
   * Shutdown the registry
   */
  shutdown(): void {
    // Clear all heartbeat timers
    for (const timer of this.heartbeatTimers.values()) {
      clearTimeout(timer);
    }
    this.heartbeatTimers.clear();

    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Clear all data
    this.agents.clear();
    this.healthMetrics.clear();
    this.eventHandlers.clear();

    console.log('Agent registry shutdown');
  }

  // Private implementation methods
  private validateRegistration(registration: AgentRegistration): void {
    if (!registration.id || registration.id.trim() === '') {
      throw new Error('Agent ID is required');
    }

    if (!registration.name || registration.name.trim() === '') {
      throw new Error('Agent name is required');
    }

    if (!registration.capabilities || registration.capabilities.length === 0) {
      throw new Error('Agent must have at least one capability');
    }

    if (registration.heartbeatInterval < 1000) {
      throw new Error('Heartbeat interval must be at least 1000ms');
    }

    if (this.agents.has(registration.id)) {
      throw new Error(`Agent with ID ${registration.id} is already registered`);
    }
  }

  private calculateHealthScore(metrics: AgentHealthMetrics): number {
    // Health score calculation based on multiple factors
    let score = 100;

    // Penalize high error rate (0-30 points deduction)
    score -= metrics.errorRate * 30;

    // Reward high success rate (0-20 points bonus)
    score += (metrics.successRate - 0.8) * 100; // Bonus for >80% success rate

    // Penalize slow response times (0-20 points deduction)
    if (metrics.averageResponseTime > 5000) { // >5 seconds
      score -= Math.min(20, (metrics.averageResponseTime - 5000) / 1000);
    }

    // Penalize recent errors (0-10 points deduction)
    if (metrics.lastErrorTime && Date.now() - metrics.lastErrorTime < 300000) { // Last 5 minutes
      score -= 10;
    }

    // Ensure score is within bounds
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private setupHeartbeatMonitoring(agentId: string, interval: number): void {
    // Clear existing timer if any
    this.clearHeartbeatMonitoring(agentId);

    // Set up new timer with 2x interval as timeout
    const timeoutDuration = interval * 2;

    const timer = setTimeout(() => {
      this.handleMissedHeartbeat(agentId);
    }, timeoutDuration);

    this.heartbeatTimers.set(agentId, timer);
  }

  private clearHeartbeatMonitoring(agentId: string): void {
    const timer = this.heartbeatTimers.get(agentId);
    if (timer) {
      clearTimeout(timer);
      this.heartbeatTimers.delete(agentId);
    }
  }

  private handleMissedHeartbeat(agentId: string): void {
    const agent = this.agents.get(agentId);
    if (!agent) return;

    // Mark agent as offline
    agent.status = 'offline';

    // Update health score
    const metrics = this.healthMetrics.get(agentId);
    if (metrics) {
      agent.healthScore = this.calculateHealthScore(metrics);
    }

    // Update statistics
    this.updateStats();

    // Emit missed heartbeat event
    this.emitEvent({
      type: 'heartbeat_missed',
      agentId,
      timestamp: Date.now(),
      data: { lastHeartbeat: agent.lastHeartbeat }
    });

    console.warn(`Missed heartbeat for agent: ${agentId}`);
  }

  private updateStats(): void {
    const agents = Array.from(this.agents.values());

    this.stats.totalAgents = agents.length;
    this.stats.onlineAgents = agents.filter(a => a.status === 'online').length;
    this.stats.busyAgents = agents.filter(a => a.status === 'busy').length;
    this.stats.idleAgents = agents.filter(a => a.status === 'idle').length;
    this.stats.errorAgents = agents.filter(a => a.status === 'error').length;

    // Calculate averages
    if (agents.length > 0) {
      this.stats.averageHealthScore = agents.reduce((sum, a) => sum + a.healthScore, 0) / agents.length;
      this.stats.averageLoad = agents.reduce((sum, a) => sum + a.currentLoad, 0) / agents.length;
    } else {
      this.stats.averageHealthScore = 0;
      this.stats.averageLoad = 0;
    }

    // Count unique capabilities
    const allCapabilities = new Set<string>();
    agents.forEach(agent => {
      agent.capabilities.forEach(cap => allCapabilities.add(cap));
    });
    this.stats.totalCapabilities = allCapabilities.size;

    // Count agents by type
    this.stats.agentsByType = {};
    agents.forEach(agent => {
      this.stats.agentsByType[agent.type] = (this.stats.agentsByType[agent.type] || 0) + 1;
    });
  }

  private emitEvent(event: AgentEvent): void {
    const handlers = this.eventHandlers.get(event.type);
    if (!handlers) return;

    handlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error(`Error in agent event handler for ${event.type}:`, error);
      }
    });
  }

  private startCleanupProcess(): void {
    // Run cleanup every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 5 * 60 * 1000);
  }

  private performCleanup(): void {
    const now = Date.now();
    const staleThreshold = 10 * 60 * 1000; // 10 minutes

    for (const [agentId, agent] of this.agents.entries()) {
      // Remove agents that haven't sent heartbeat in a long time
      if (now - agent.lastHeartbeat > staleThreshold) {
        console.warn(`Removing stale agent: ${agentId}`);
        this.unregisterAgent(agentId);
      }
    }
  }
}

// Global agent registry instance
export const globalAgentRegistry = new AgentRegistry();
