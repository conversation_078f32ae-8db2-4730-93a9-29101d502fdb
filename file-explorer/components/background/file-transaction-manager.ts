// components/background/file-transaction-manager.ts

import { FileOperation, FileOperationResult, globalFileOperations } from './file-operations';

export interface FileTransaction {
  id: string;
  name: string;
  description?: string;
  operations: FileOperation[];
  status: 'pending' | 'executing' | 'completed' | 'failed' | 'rolled_back';
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  agentId?: string;
  rollbackOperations: FileOperation[];
  checkpoints: TransactionCheckpoint[];
  metadata?: Record<string, any>;
  autoRollback: boolean;
  timeout?: number; // in milliseconds
}

export interface TransactionCheckpoint {
  id: string;
  transactionId: string;
  operationIndex: number;
  timestamp: number;
  backupData: Map<string, string>; // path -> content backup
  metadata?: Record<string, any>;
}

export interface TransactionResult {
  success: boolean;
  transaction: FileTransaction;
  completedOperations: FileOperationResult[];
  failedOperation?: FileOperationResult;
  error?: string;
  rollbackResult?: RollbackResult;
}

export interface RollbackResult {
  success: boolean;
  rolledBackOperations: number;
  failedRollbacks: FileOperationResult[];
  error?: string;
}

export interface TransactionStats {
  totalTransactions: number;
  completedTransactions: number;
  failedTransactions: number;
  rolledBackTransactions: number;
  averageExecutionTime: number;
  averageOperationsPerTransaction: number;
  activeTransactions: number;
  totalOperationsExecuted: number;
}

export class FileTransactionManager {
  private transactions: Map<string, FileTransaction> = new Map();
  private transactionHistory: FileTransaction[] = [];
  private maxHistorySize = 500;
  private stats: TransactionStats = {
    totalTransactions: 0,
    completedTransactions: 0,
    failedTransactions: 0,
    rolledBackTransactions: 0,
    averageExecutionTime: 0,
    averageOperationsPerTransaction: 0,
    activeTransactions: 0,
    totalOperationsExecuted: 0
  };

  /**
   * Create a new transaction
   */
  async createTransaction(
    name: string,
    operations: Omit<FileOperation, 'id' | 'timestamp' | 'status' | 'transactionId'>[],
    options?: {
      description?: string;
      agentId?: string;
      autoRollback?: boolean;
      timeout?: number;
    }
  ): Promise<string> {
    const transactionId = this.generateTransactionId();

    const transaction: FileTransaction = {
      id: transactionId,
      name,
      description: options?.description,
      operations: operations.map((op, index) => ({
        ...op,
        id: `${transactionId}-op-${index}`,
        timestamp: Date.now(),
        status: 'pending',
        transactionId
      })),
      status: 'pending',
      createdAt: Date.now(),
      agentId: options?.agentId,
      rollbackOperations: [],
      checkpoints: [],
      autoRollback: options?.autoRollback ?? true,
      timeout: options?.timeout
    };

    this.transactions.set(transactionId, transaction);
    this.updateStats();

    console.log(`Transaction created: ${name} (${transactionId}) with ${operations.length} operations`);
    return transactionId;
  }

  /**
   * Execute a transaction
   */
  async executeTransaction(transactionId: string): Promise<TransactionResult> {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      throw new Error(`Transaction not found: ${transactionId}`);
    }

    if (transaction.status !== 'pending') {
      throw new Error(`Transaction is not in pending state: ${transaction.status}`);
    }

    transaction.status = 'executing';
    transaction.startedAt = Date.now();

    const completedOperations: FileOperationResult[] = [];
    let failedOperation: FileOperationResult | undefined;

    try {
      // Set up timeout if specified
      let timeoutHandle: NodeJS.Timeout | undefined;
      if (transaction.timeout) {
        timeoutHandle = setTimeout(() => {
          throw new Error(`Transaction timeout after ${transaction.timeout}ms`);
        }, transaction.timeout);
      }

      // Execute operations sequentially
      for (let i = 0; i < transaction.operations.length; i++) {
        const operation = transaction.operations[i];

        // Create checkpoint before operation
        await this.createCheckpoint(transaction, i);

        // Execute operation
        const result = await globalFileOperations.executeOperation(operation);

        if (result.success) {
          completedOperations.push(result);
          operation.status = 'completed';
          operation.result = result.data;

          // Generate rollback operation
          const rollbackOp = this.generateRollbackOperation(operation, result);
          if (rollbackOp) {
            transaction.rollbackOperations.unshift(rollbackOp); // Add to beginning for reverse order
          }
        } else {
          failedOperation = result;
          operation.status = 'failed';
          operation.error = result.error;
          break;
        }
      }

      // Clear timeout
      if (timeoutHandle) {
        clearTimeout(timeoutHandle);
      }

      // Determine final status
      if (failedOperation) {
        transaction.status = 'failed';

        // Auto-rollback if enabled
        let rollbackResult: RollbackResult | undefined;
        if (transaction.autoRollback) {
          rollbackResult = await this.rollbackTransaction(transactionId);
        }

        this.addToHistory(transaction);
        this.updateStats();

        return {
          success: false,
          transaction,
          completedOperations,
          failedOperation,
          error: failedOperation.error,
          rollbackResult
        };
      } else {
        transaction.status = 'completed';
        transaction.completedAt = Date.now();

        this.addToHistory(transaction);
        this.updateStats();

        return {
          success: true,
          transaction,
          completedOperations
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      transaction.status = 'failed';
      transaction.completedAt = Date.now();

      // Auto-rollback if enabled
      let rollbackResult: RollbackResult | undefined;
      if (transaction.autoRollback) {
        rollbackResult = await this.rollbackTransaction(transactionId);
      }

      this.addToHistory(transaction);
      this.updateStats();

      return {
        success: false,
        transaction,
        completedOperations,
        error: errorMessage,
        rollbackResult
      };
    } finally {
      this.transactions.delete(transactionId);
    }
  }

  /**
   * Rollback a transaction
   */
  async rollbackTransaction(transactionId: string): Promise<RollbackResult> {
    const transaction = this.transactions.get(transactionId) ||
                      this.transactionHistory.find(t => t.id === transactionId);

    if (!transaction) {
      throw new Error(`Transaction not found: ${transactionId}`);
    }

    const failedRollbacks: FileOperationResult[] = [];
    let rolledBackOperations = 0;

    try {
      // Execute rollback operations in reverse order
      for (const rollbackOp of transaction.rollbackOperations) {
        try {
          const result = await globalFileOperations.executeOperation(rollbackOp);

          if (result.success) {
            rolledBackOperations++;
          } else {
            failedRollbacks.push(result);
          }
        } catch (error) {
          failedRollbacks.push({
            success: false,
            operation: rollbackOp,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      // Restore from checkpoints if rollback operations failed
      if (failedRollbacks.length > 0) {
        await this.restoreFromCheckpoints(transaction);
      }

      transaction.status = 'rolled_back';
      this.updateStats();

      console.log(`Transaction rolled back: ${transactionId} (${rolledBackOperations} operations)`);

      return {
        success: failedRollbacks.length === 0,
        rolledBackOperations,
        failedRollbacks,
        error: failedRollbacks.length > 0 ? `${failedRollbacks.length} rollback operations failed` : undefined
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      return {
        success: false,
        rolledBackOperations,
        failedRollbacks,
        error: errorMessage
      };
    }
  }

  /**
   * Get transaction by ID
   */
  getTransaction(transactionId: string): FileTransaction | undefined {
    return this.transactions.get(transactionId) ||
           this.transactionHistory.find(t => t.id === transactionId);
  }

  /**
   * Get active transactions
   */
  getActiveTransactions(): FileTransaction[] {
    return Array.from(this.transactions.values());
  }

  /**
   * Get transaction history
   */
  getTransactionHistory(limit?: number): FileTransaction[] {
    const history = [...this.transactionHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Get transaction statistics
   */
  getStats(): TransactionStats {
    return { ...this.stats };
  }

  /**
   * Clear transaction history
   */
  clearHistory(): void {
    this.transactionHistory = [];
  }

  /**
   * Shutdown transaction manager
   */
  shutdown(): void {
    // Cancel all active transactions
    for (const transaction of this.transactions.values()) {
      transaction.status = 'failed';
      transaction.completedAt = Date.now();
    }

    this.transactions.clear();
    console.log('File transaction manager shutdown');
  }

  // Private implementation methods
  private generateTransactionId(): string {
    return `tx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private async createCheckpoint(transaction: FileTransaction, operationIndex: number): Promise<void> {
    const checkpointId = `cp-${transaction.id}-${operationIndex}`;
    const operation = transaction.operations[operationIndex];
    const backupData = new Map<string, string>();

    try {
      // Create backup for operations that modify files
      if (operation.type === 'write' || operation.type === 'delete' || operation.type === 'move') {
        // Try to read current file content for backup
        try {
          const result = await globalFileOperations.readFile(operation.path);
          if (result.success && result.data) {
            backupData.set(operation.path, result.data);
          }
        } catch (error) {
          // File might not exist, which is fine for create operations
          console.log(`Could not backup file ${operation.path}: ${error}`);
        }
      }

      // For move operations, also backup the target if it exists
      if (operation.type === 'move' && operation.targetPath) {
        try {
          const result = await globalFileOperations.readFile(operation.targetPath);
          if (result.success && result.data) {
            backupData.set(operation.targetPath, result.data);
          }
        } catch (error) {
          // Target file might not exist, which is fine
          console.log(`Could not backup target file ${operation.targetPath}: ${error}`);
        }
      }

      const checkpoint: TransactionCheckpoint = {
        id: checkpointId,
        transactionId: transaction.id,
        operationIndex,
        timestamp: Date.now(),
        backupData,
        metadata: {
          operationType: operation.type,
          operationPath: operation.path,
          operationTargetPath: operation.targetPath
        }
      };

      transaction.checkpoints.push(checkpoint);
      console.log(`Checkpoint created: ${checkpointId} for operation ${operationIndex}`);

    } catch (error) {
      console.error(`Failed to create checkpoint: ${error}`);
      // Don't fail the transaction for checkpoint errors
    }
  }

  private generateRollbackOperation(operation: FileOperation, result: FileOperationResult): FileOperation | null {
    const rollbackId = `rollback-${operation.id}`;

    switch (operation.type) {
      case 'create':
        // Rollback create by deleting the file
        return {
          id: rollbackId,
          type: 'delete',
          path: operation.path,
          timestamp: Date.now(),
          status: 'pending',
          transactionId: operation.transactionId,
          agentId: operation.agentId,
          metadata: { rollbackFor: operation.id }
        };

      case 'delete':
        // Rollback delete by recreating the file (if we have backup)
        const checkpoint = this.findCheckpointForOperation(operation);
        if (checkpoint && checkpoint.backupData.has(operation.path)) {
          return {
            id: rollbackId,
            type: 'create',
            path: operation.path,
            content: checkpoint.backupData.get(operation.path),
            timestamp: Date.now(),
            status: 'pending',
            transactionId: operation.transactionId,
            agentId: operation.agentId,
            metadata: { rollbackFor: operation.id }
          };
        }
        break;

      case 'write':
        // Rollback write by restoring original content
        const writeCheckpoint = this.findCheckpointForOperation(operation);
        if (writeCheckpoint && writeCheckpoint.backupData.has(operation.path)) {
          return {
            id: rollbackId,
            type: 'write',
            path: operation.path,
            content: writeCheckpoint.backupData.get(operation.path),
            timestamp: Date.now(),
            status: 'pending',
            transactionId: operation.transactionId,
            agentId: operation.agentId,
            metadata: { rollbackFor: operation.id }
          };
        }
        break;

      case 'move':
        // Rollback move by moving back
        if (operation.targetPath) {
          return {
            id: rollbackId,
            type: 'move',
            path: operation.targetPath,
            targetPath: operation.path,
            timestamp: Date.now(),
            status: 'pending',
            transactionId: operation.transactionId,
            agentId: operation.agentId,
            metadata: { rollbackFor: operation.id }
          };
        }
        break;

      case 'copy':
        // Rollback copy by deleting the copied file
        if (operation.targetPath) {
          return {
            id: rollbackId,
            type: 'delete',
            path: operation.targetPath,
            timestamp: Date.now(),
            status: 'pending',
            transactionId: operation.transactionId,
            agentId: operation.agentId,
            metadata: { rollbackFor: operation.id }
          };
        }
        break;

      default:
        console.warn(`No rollback strategy for operation type: ${operation.type}`);
        break;
    }

    return null;
  }

  private findCheckpointForOperation(operation: FileOperation): TransactionCheckpoint | undefined {
    const transaction = this.transactions.get(operation.transactionId!) ||
                       this.transactionHistory.find(t => t.id === operation.transactionId);

    if (!transaction) return undefined;

    const operationIndex = transaction.operations.findIndex(op => op.id === operation.id);
    return transaction.checkpoints.find(cp => cp.operationIndex === operationIndex);
  }

  private async restoreFromCheckpoints(transaction: FileTransaction): Promise<void> {
    console.log(`Restoring from checkpoints for transaction: ${transaction.id}`);

    // Restore files from checkpoints in reverse order
    for (let i = transaction.checkpoints.length - 1; i >= 0; i--) {
      const checkpoint = transaction.checkpoints[i];

      try {
        for (const [filePath, content] of checkpoint.backupData.entries()) {
          await globalFileOperations.writeFile(filePath, content, undefined, 'system-rollback');
        }

        console.log(`Restored checkpoint: ${checkpoint.id}`);
      } catch (error) {
        console.error(`Failed to restore checkpoint ${checkpoint.id}: ${error}`);
      }
    }
  }

  private updateStats(): void {
    const activeTransactions = this.transactions.size;
    const allTransactions = [...this.transactions.values(), ...this.transactionHistory];

    this.stats.totalTransactions = allTransactions.length;
    this.stats.activeTransactions = activeTransactions;

    // Count by status
    this.stats.completedTransactions = allTransactions.filter(t => t.status === 'completed').length;
    this.stats.failedTransactions = allTransactions.filter(t => t.status === 'failed').length;
    this.stats.rolledBackTransactions = allTransactions.filter(t => t.status === 'rolled_back').length;

    // Calculate averages
    const completedTransactions = allTransactions.filter(t => t.completedAt && t.startedAt);
    if (completedTransactions.length > 0) {
      const totalExecutionTime = completedTransactions.reduce((sum, t) =>
        sum + (t.completedAt! - t.startedAt!), 0);
      this.stats.averageExecutionTime = totalExecutionTime / completedTransactions.length;
    }

    const totalOperations = allTransactions.reduce((sum, t) => sum + t.operations.length, 0);
    this.stats.averageOperationsPerTransaction = totalOperations / Math.max(allTransactions.length, 1);
    this.stats.totalOperationsExecuted = totalOperations;
  }

  private addToHistory(transaction: FileTransaction): void {
    // Create a deep copy for history
    const historicalTransaction: FileTransaction = {
      ...transaction,
      operations: transaction.operations.map(op => ({ ...op })),
      rollbackOperations: transaction.rollbackOperations.map(op => ({ ...op })),
      checkpoints: transaction.checkpoints.map(cp => ({
        ...cp,
        backupData: new Map(cp.backupData)
      }))
    };

    this.transactionHistory.push(historicalTransaction);

    // Maintain history size limit
    if (this.transactionHistory.length > this.maxHistorySize) {
      this.transactionHistory = this.transactionHistory.slice(-this.maxHistorySize);
    }
  }
}

// Global file transaction manager instance
export const globalFileTransactionManager = new FileTransactionManager();
