// components/background/file-sync-service.ts

import { globalFileSystemMonitor, FileSystemEvent } from './file-system-monitor';

export interface FileSyncEvent {
  id: string;
  type: 'content_changed' | 'file_opened' | 'file_closed' | 'file_saved';
  filePath: string;
  content?: string;
  timestamp: number;
  windowId?: string;
  editorId?: string;
  agentId?: string;
}

export interface OpenFile {
  path: string;
  content: string;
  lastModified: number;
  openWindows: Set<string>;
  isDirty: boolean;
  lastSyncTime: number;
}

export type FileSyncEventHandler = (event: FileSyncEvent) => void;

export class FileSyncService {
  private openFiles: Map<string, OpenFile> = new Map();
  private eventHandlers: FileSyncEventHandler[] = [];
  private isInitialized = false;
  private syncInterval?: NodeJS.Timeout;
  private windowId: string;

  constructor() {
    this.windowId = this.generateWindowId();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    // Set up file system monitoring
    globalFileSystemMonitor.onFileSystemEvent(this.handleFileSystemEvent.bind(this));

    // Set up IPC event listeners for cross-window synchronization
    this.setupIPCListeners();

    // Start periodic sync check
    this.startSyncInterval();

    this.isInitialized = true;
    console.log('FileSyncService initialized');
  }

  /**
   * Register a file as opened in this window
   */
  async openFile(filePath: string, content: string, editorId?: string): Promise<void> {
    const existingFile = this.openFiles.get(filePath);

    if (existingFile) {
      // File is already open, add this window to the set
      existingFile.openWindows.add(this.windowId);
    } else {
      // New file being opened
      const openFile: OpenFile = {
        path: filePath,
        content,
        lastModified: Date.now(),
        openWindows: new Set([this.windowId]),
        isDirty: false,
        lastSyncTime: Date.now()
      };
      this.openFiles.set(filePath, openFile);
    }

    // Broadcast file opened event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'file_opened',
      filePath,
      content,
      timestamp: Date.now(),
      windowId: this.windowId,
      editorId
    });

    console.log(`File opened: ${filePath} in window ${this.windowId}`);
  }

  /**
   * Update file content (when user types)
   */
  async updateFileContent(filePath: string, content: string, editorId?: string): Promise<void> {
    const openFile = this.openFiles.get(filePath);
    if (!openFile) {
      console.warn(`Attempted to update content for unopened file: ${filePath}`);
      return;
    }

    // Update local state
    openFile.content = content;
    openFile.isDirty = true;
    openFile.lastModified = Date.now();

    // Broadcast content change event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'content_changed',
      filePath,
      content,
      timestamp: Date.now(),
      windowId: this.windowId,
      editorId
    });
  }

  /**
   * Mark file as saved
   */
  async saveFile(filePath: string, content: string, editorId?: string): Promise<void> {
    const openFile = this.openFiles.get(filePath);
    if (openFile) {
      openFile.content = content;
      openFile.isDirty = false;
      openFile.lastModified = Date.now();
      openFile.lastSyncTime = Date.now();
    }

    // Broadcast file saved event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'file_saved',
      filePath,
      content,
      timestamp: Date.now(),
      windowId: this.windowId,
      editorId
    });

    console.log(`File saved: ${filePath} from window ${this.windowId}`);
  }

  /**
   * Close file in this window
   */
  async closeFile(filePath: string, editorId?: string): Promise<void> {
    const openFile = this.openFiles.get(filePath);
    if (!openFile) return;

    // Remove this window from the set
    openFile.openWindows.delete(this.windowId);

    // If no windows have the file open, remove it from tracking
    if (openFile.openWindows.size === 0) {
      this.openFiles.delete(filePath);
    }

    // Broadcast file closed event
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'file_closed',
      filePath,
      timestamp: Date.now(),
      windowId: this.windowId,
      editorId
    });

    console.log(`File closed: ${filePath} in window ${this.windowId}`);
  }

  /**
   * Get current content of a file
   */
  getFileContent(filePath: string): string | undefined {
    return this.openFiles.get(filePath)?.content;
  }

  /**
   * Check if file is dirty (has unsaved changes)
   */
  isFileDirty(filePath: string): boolean {
    return this.openFiles.get(filePath)?.isDirty ?? false;
  }

  /**
   * Get list of open files
   */
  getOpenFiles(): string[] {
    return Array.from(this.openFiles.keys());
  }

  /**
   * Add event handler
   */
  onFileSyncEvent(handler: FileSyncEventHandler): void {
    this.eventHandlers.push(handler);
  }

  /**
   * Remove event handler
   */
  offFileSyncEvent(handler: FileSyncEventHandler): void {
    const index = this.eventHandlers.indexOf(handler);
    if (index > -1) {
      this.eventHandlers.splice(index, 1);
    }
  }

  /**
   * Shutdown the service
   */
  shutdown(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    this.openFiles.clear();
    this.eventHandlers = [];
    this.isInitialized = false;
    console.log('FileSyncService shutdown');
  }

  // Private methods
  private generateEventId(): string {
    return `sync-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateWindowId(): string {
    return `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private handleFileSystemEvent(event: FileSystemEvent): void {
    // Handle external file changes (from disk)
    if (event.type === 'modified' && this.openFiles.has(event.path)) {
      console.log(`External file change detected: ${event.path}`);

      // Reload file content from disk and sync
      this.reloadFileFromDisk(event.path);
    }
  }

  private async reloadFileFromDisk(filePath: string): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.readFile(filePath);
        if (result.success && result.content) {
          const openFile = this.openFiles.get(filePath);
          if (openFile && !openFile.isDirty) {
            // Only update if file is not dirty (no unsaved changes)
            openFile.content = result.content;
            openFile.lastModified = Date.now();
            openFile.lastSyncTime = Date.now();

            // Broadcast the change to other windows
            this.broadcastEvent({
              id: this.generateEventId(),
              type: 'content_changed',
              filePath,
              content: result.content,
              timestamp: Date.now(),
              windowId: 'disk'
            });
          }
        }
      }
    } catch (error) {
      console.error(`Failed to reload file from disk: ${filePath}`, error);
    }
  }

  private setupIPCListeners(): void {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
      // Listen for file sync events from other windows
      window.electronAPI.ipc.on('file-sync-event', (event: FileSyncEvent) => {
        if (event.windowId !== this.windowId) {
          this.handleRemoteFileSyncEvent(event);
        }
      });
    }
  }

  private handleRemoteFileSyncEvent(event: FileSyncEvent): void {
    // Handle file sync events from other windows
    const openFile = this.openFiles.get(event.filePath);

    if (event.type === 'content_changed' && openFile && !openFile.isDirty) {
      // Update content if we don't have unsaved changes
      openFile.content = event.content || '';
      openFile.lastModified = event.timestamp;
      openFile.lastSyncTime = Date.now();
      console.log(`FileSyncService: Updated content for ${event.filePath} (not dirty)`);
    } else if (event.type === 'content_changed' && openFile && openFile.isDirty) {
      console.log(`FileSyncService: Ignoring content update for ${event.filePath} (file is dirty)`);
    }

    if (event.type === 'file_saved' && openFile) {
      // Reset dirty state when file is saved in another window
      openFile.content = event.content || '';
      openFile.isDirty = false;
      openFile.lastModified = event.timestamp;
      openFile.lastSyncTime = Date.now();
      console.log(`FileSyncService: File saved, reset dirty state for ${event.filePath}`);
    }

    // Notify local handlers
    this.eventHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error in file sync event handler:', error);
      }
    });
  }

  private broadcastEvent(event: FileSyncEvent): void {
    // Broadcast to other windows via IPC
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
      window.electronAPI.ipc.send('file-sync-event', event);
    }

    // Notify local handlers
    this.eventHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error in file sync event handler:', error);
      }
    });
  }

  private startSyncInterval(): void {
    // Check for sync conflicts every 5 seconds
    this.syncInterval = setInterval(() => {
      this.checkSyncConflicts();
    }, 5000);
  }

  private checkSyncConflicts(): void {
    // Check for files that might be out of sync
    for (const [filePath, openFile] of this.openFiles.entries()) {
      const timeSinceLastSync = Date.now() - openFile.lastSyncTime;

      // If file hasn't been synced in a while and is dirty, log a warning
      if (timeSinceLastSync > 30000 && openFile.isDirty) {
        console.warn(`File may be out of sync: ${filePath} (${timeSinceLastSync}ms since last sync)`);
      }
    }
  }
}

// Global file sync service instance
export const globalFileSyncService = new FileSyncService();
