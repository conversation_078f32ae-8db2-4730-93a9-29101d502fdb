"use client"

import { useState, useRef, useEffect, ReactNode } from "react"
import { cn } from "@/lib/utils"

interface ResizableLeftPanelProps {
  children: ReactNode
  width: number
  onWidthChange: (width: number) => void
  minWidth?: number
  maxWidth?: number
  className?: string
}

export default function ResizableLeftPanel({
  children,
  width,
  onWidthChange,
  minWidth = 300,
  maxWidth = 800,
  className
}: ResizableLeftPanelProps) {
  const [isResizing, setIsResizing] = useState(false)
  const [startX, setStartX] = useState(0)
  const [startWidth, setStartWidth] = useState(0)

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsResizing(true)
    setStartX(e.clientX)
    setStartWidth(width)
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return
    
    // For left panel, we add the delta (resize from right side)
    const deltaX = e.clientX - startX
    const newWidth = startWidth + deltaX
    const clampedWidth = Math.max(minWidth, Math.min(maxWidth, newWidth))
    
    onWidthChange(clampedWidth)
  }

  const handleMouseUp = () => {
    setIsResizing(false)
  }

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    } else {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isResizing, startX, startWidth, width, minWidth, maxWidth, onWidthChange])

  return (
    <div
      className={cn(
        "relative border-r border-editor-border bg-editor-sidebar-bg transition-all duration-300 ease-in-out",
        isResizing && "transition-none",
        className
      )}
      style={{ width: `${width}px` }}
    >
      {/* Panel content */}
      <div className="h-full overflow-hidden">
        {children}
      </div>

      {/* Resize handle on the right side */}
      <div
        className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize hover:bg-editor-highlight/50 transition-colors group z-10"
        onMouseDown={handleMouseDown}
      >
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-editor-highlight/30 rounded-l opacity-0 group-hover:opacity-100 transition-opacity" />
      </div>
    </div>
  )
}
