"use client"

import * as React from "react"
import { LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"

export interface IconProps extends React.SVGProps<SVGSVGElement> {
  icon: LucideIcon
  size?: "xs" | "sm" | "md" | "lg" | "xl"
  variant?: "default" | "activity-bar" | "sidebar" | "file"
}

const iconSizeClasses = {
  xs: "h-3.5 w-3.5",
  sm: "h-4 w-4", 
  md: "h-5 w-5",
  lg: "h-6 w-6",
  xl: "h-8 w-8"
}

const iconVariantClasses = {
  default: "stroke-current fill-none stroke-2",
  "activity-bar": "activity-bar-icon",
  sidebar: "sidebar-icon", 
  file: "file-icon"
}

const Icon = React.forwardRef<SVGSVGElement, IconProps>(
  ({ icon: IconComponent, size = "md", variant = "default", className, ...props }, ref) => {
    const sizeClass = iconSizeClasses[size]
    const variantClass = iconVariantClasses[variant]
    
    return (
      <IconComponent
        ref={ref}
        className={cn(
          "stroke-current fill-none stroke-[1.5] stroke-linecap-round stroke-linejoin-round",
          sizeClass,
          variantClass,
          className
        )}
        {...props}
      />
    )
  }
)
Icon.displayName = "Icon"

export { Icon }
