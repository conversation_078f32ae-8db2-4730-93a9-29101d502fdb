"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { CardType } from "./board-context"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Available agents for assignment
const AVAILABLE_AGENTS = [
  { id: 'micromanager', name: '🤖 Micromanager', type: 'orchestrator' },
  { id: 'intern', name: '1️⃣ Intern', type: 'implementation' },
  { id: 'junior', name: '2️⃣ Junior', type: 'implementation' },
  { id: 'midlevel', name: '3️⃣ MidLevel', type: 'implementation' },
  { id: 'senior', name: '4️⃣ Senior', type: 'implementation' },
  { id: 'researcher', name: '📘 Researcher', type: 'specialized' },
  { id: 'architect', name: '🏗️ Architect', type: 'specialized' },
  { id: 'designer', name: '🎨 Designer', type: 'specialized' },
  { id: 'tester', name: '🧪 Tester', type: 'specialized' }
];

interface CreateCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateCard: (card: any) => void
  cardTypes: CardType[]
}

export function CreateCardDialog({
  open,
  onOpenChange,
  onCreateCard,
  cardTypes,
}: CreateCardDialogProps) {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [priority, setPriority] = useState<string>("")
  const [projectId, setProjectId] = useState("")
  const [tags, setTags] = useState("")
  const [assignedAgentId, setAssignedAgentId] = useState<string>("unassigned")  // ✅ New state for agent assignment
  const [shellCommand, setShellCommand] = useState("")  // ✅ Task 93: Shell command state

  useEffect(() => {
    if (open && cardTypes.length > 0) {
      setPriority(cardTypes[0].id);
    } else if (open) {
      setPriority(""); // Fallback if no card types
    }
  }, [open, cardTypes]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    const newCard = {
      title,
      description,
      priority,
      projectId,
      tags: tags.split(",").map(tag => tag.trim()).filter(Boolean),
      progress: 0,
      labels: [],
      agentAssignments: [],
      assignedAgentId: assignedAgentId === "unassigned" ? null : assignedAgentId,  // ✅ Include agent assignment
      shellCommand: shellCommand.trim() || undefined,  // ✅ Task 93: Include shell command
      dependencies: [],
      resourceMetrics: {
        tokenUsage: 0,
        cpuTime: 0,
        memoryUsage: 0,
      },
      taskHistory: [],
    }

    onCreateCard(newCard)
    resetForm()
    onOpenChange(false)
  }

  const resetForm = () => {
    setTitle("")
    setDescription("")
    if (cardTypes.length > 0) {
      setPriority(cardTypes[0].id);
    } else {
      setPriority("");
    }
    setProjectId("")
    setTags("")
    setAssignedAgentId("unassigned")  // ✅ Reset agent assignment
    setShellCommand("")  // ✅ Task 93: Reset shell command
  }

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      if (!isOpen) resetForm();
      onOpenChange(isOpen);
    }}>
      <DialogContent className="max-w-[500px] w-[90vw] max-h-[90vh] bg-background rounded-lg shadow-lg p-6">
        <DialogHeader>
          <DialogTitle>Create New Card</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="title" className="text-right text-sm">
                Title
              </label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="description" className="text-right text-sm">
                Description
              </label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="priority" className="text-right text-sm">
                Priority
              </label>
              <Select
                value={priority}
                onValueChange={(value) => setPriority(value)}
                disabled={cardTypes.length === 0}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {cardTypes.length > 0 ? (
                    cardTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>No priorities configured</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="projectId" className="text-right text-sm">
                Project ID
              </label>
              <Input
                id="projectId"
                value={projectId}
                onChange={(e) => setProjectId(e.target.value)}
                className="col-span-3"
                placeholder="e.g. TASK-123"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="tags" className="text-right text-sm">
                Tags
              </label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                className="col-span-3"
                placeholder="Comma-separated tags"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="assignedAgent" className="text-right text-sm">
                Assign Agent
              </label>
              <Select
                value={assignedAgentId}
                onValueChange={(value) => setAssignedAgentId(value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select an agent (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unassigned">None (Unassigned)</SelectItem>
                  {AVAILABLE_AGENTS.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id}>
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="shellCommand" className="text-right text-sm">
                Shell Command
              </label>
              <Input
                id="shellCommand"
                value={shellCommand}
                onChange={(e) => setShellCommand(e.target.value)}
                className="col-span-3"
                placeholder="e.g., ls -la, npm test, echo hello"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => { resetForm(); onOpenChange(false); }}>
              Cancel
            </Button>
            <Button type="submit">Create Card</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}