// components/kanban/lib/kanban-events.ts
// Event system for Kanban card operations and agent assignments

export interface KanbanAgentAssignmentEvent {
  cardId: string;
  agentId: string;
  cardTitle: string;
  columnId: string;
  boardId: string;
  timestamp: number;
}

export interface KanbanAgentUnassignmentEvent {
  cardId: string;
  previousAgentId: string;
  cardTitle: string;
  columnId: string;
  boardId: string;
  timestamp: number;
}

export interface KanbanCardStatusChangeEvent {
  cardId: string;
  oldColumnId: string;
  newColumnId: string;
  cardTitle: string;
  assignedAgentId: string | null;
  boardId: string;
  timestamp: number;
}

// ✅ Task 76: New event interfaces for task execution feedback
export interface KanbanTaskCompletedEvent {
  cardId: string;
  agentId: string;
  result: any; // AgentExecutionResult
  taskId?: string;
  boardId: string;
  timestamp: number;
}

export interface KanbanTaskFailedEvent {
  cardId: string;
  agentId: string;
  reason: string;
  result: any; // AgentExecutionResult
  taskId?: string;
  boardId: string;
  timestamp: number;
}

export interface KanbanTaskRetriedEvent {
  cardId: string;
  agentId: string;
  retryCount: number;
  reason: string;
  boardId: string;
  timestamp: number;
}

export interface KanbanTaskEscalatedEvent {
  cardId: string;
  fromAgentId: string;
  toAgentId: string;
  escalationLevel: number;
  reason: string;
  boardId: string;
  timestamp: number;
}

// Event type definitions
export type KanbanEventType =
  | 'agentAssigned'
  | 'agentUnassigned'
  | 'cardStatusChanged'
  | 'cardCreated'
  | 'cardUpdated'
  | 'cardDeleted'
  | 'taskCompleted'
  | 'taskFailed'
  | 'taskRetried'
  | 'taskEscalated';

export type KanbanEventData =
  | KanbanAgentAssignmentEvent
  | KanbanAgentUnassignmentEvent
  | KanbanCardStatusChangeEvent
  | KanbanTaskCompletedEvent
  | KanbanTaskFailedEvent
  | KanbanTaskRetriedEvent
  | KanbanTaskEscalatedEvent;

// Event listener type
export type KanbanEventListener<T = KanbanEventData> = (data: T) => void | Promise<void>;

/**
 * Kanban Events Manager
 * Handles event emission and subscription for Kanban operations
 */
class KanbanEventsManager {
  private listeners: Map<KanbanEventType, Set<KanbanEventListener>> = new Map();
  private eventHistory: Array<{ type: KanbanEventType; data: KanbanEventData; timestamp: number }> = [];
  private maxHistorySize = 100;

  /**
   * Subscribe to a specific event type
   */
  on<T extends KanbanEventData>(eventType: KanbanEventType, listener: KanbanEventListener<T>): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }

    const eventListeners = this.listeners.get(eventType)!;
    eventListeners.add(listener as KanbanEventListener);

    console.log(`🎯 KanbanEvents: Registered listener for '${eventType}' (${eventListeners.size} total)`);

    // Return unsubscribe function
    return () => {
      eventListeners.delete(listener as KanbanEventListener);
      console.log(`🎯 KanbanEvents: Unregistered listener for '${eventType}' (${eventListeners.size} remaining)`);
    };
  }

  /**
   * Emit an event to all registered listeners
   */
  emit(eventType: KanbanEventType, data: KanbanEventData): void {
    const timestamp = Date.now();

    // Add to history
    this.eventHistory.push({ type: eventType, data, timestamp });
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }

    console.log(`🎯 KanbanEvents: Emitting '${eventType}' event:`, data);

    // Notify listeners
    const eventListeners = this.listeners.get(eventType);
    if (eventListeners && eventListeners.size > 0) {
      eventListeners.forEach(listener => {
        try {
          const result = listener(data);
          // Handle async listeners
          if (result instanceof Promise) {
            result.catch(error => {
              console.error(`🎯 KanbanEvents: Error in async listener for '${eventType}':`, error);
            });
          }
        } catch (error) {
          console.error(`🎯 KanbanEvents: Error in listener for '${eventType}':`, error);
        }
      });
    } else {
      console.warn(`🎯 KanbanEvents: No listeners registered for '${eventType}'`);
    }
  }

  /**
   * Get recent event history
   */
  getEventHistory(limit = 10): Array<{ type: KanbanEventType; data: KanbanEventData; timestamp: number }> {
    return this.eventHistory.slice(-limit).reverse();
  }

  /**
   * Get events for a specific card
   */
  getCardEvents(cardId: string, limit = 10): Array<{ type: KanbanEventType; data: KanbanEventData; timestamp: number }> {
    return this.eventHistory
      .filter(event => (event.data as any).cardId === cardId)
      .slice(-limit)
      .reverse();
  }

  /**
   * Clear event history
   */
  clearHistory(): void {
    this.eventHistory = [];
    console.log('🎯 KanbanEvents: Event history cleared');
  }

  /**
   * Remove all listeners for a specific event type
   */
  removeAllListeners(eventType?: KanbanEventType): void {
    if (eventType) {
      this.listeners.delete(eventType);
      console.log(`🎯 KanbanEvents: Removed all listeners for '${eventType}'`);
    } else {
      this.listeners.clear();
      console.log('🎯 KanbanEvents: Removed all listeners for all events');
    }
  }

  /**
   * Get listener count for an event type
   */
  getListenerCount(eventType: KanbanEventType): number {
    return this.listeners.get(eventType)?.size || 0;
  }

  /**
   * Check if there are any listeners for an event type
   */
  hasListeners(eventType: KanbanEventType): boolean {
    return this.getListenerCount(eventType) > 0;
  }
}

// Create singleton instance
export const kanbanEvents = new KanbanEventsManager();

// Helper functions for common event emissions
export const emitAgentAssigned = (cardId: string, agentId: string, cardTitle: string, columnId: string, boardId = 'main') => {
  kanbanEvents.emit('agentAssigned', {
    cardId,
    agentId,
    cardTitle,
    columnId,
    boardId,
    timestamp: Date.now()
  });
};

export const emitAgentUnassigned = (cardId: string, previousAgentId: string, cardTitle: string, columnId: string, boardId = 'main') => {
  kanbanEvents.emit('agentUnassigned', {
    cardId,
    previousAgentId,
    cardTitle,
    columnId,
    boardId,
    timestamp: Date.now()
  });
};

export const emitCardStatusChanged = (cardId: string, oldColumnId: string, newColumnId: string, cardTitle: string, assignedAgentId: string | null, boardId = 'main') => {
  kanbanEvents.emit('cardStatusChanged', {
    cardId,
    oldColumnId,
    newColumnId,
    cardTitle,
    assignedAgentId,
    boardId,
    timestamp: Date.now()
  });
};
