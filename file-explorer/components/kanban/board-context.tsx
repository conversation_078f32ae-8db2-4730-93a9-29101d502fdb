"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect, useCallback } from "react" // Added useCallback
import { useToast } from "@/hooks/use-toast"
import { useTheme } from "next-themes"
import { boardIPCBridge } from './lib/board-ipc-bridge'; // New import
import { emitAgentAssigned, emitAgentUnassigned, emitCardStatusChanged } from "./lib/kanban-events"

// Define shared types for board data structure
export type CardType = {
  id: string
  name: string
  color: string
}

export type AgentAssignment = {
  agentId: string
  agentType: string
  assignmentTime: string
}

export type ResourceMetrics = {
  tokenUsage: number
  cpuTime: number
  memoryUsage: number
}

export type TaskHistoryItem = {
  timestamp: string
  action: string
  agentId: string
  details: string
}

export type Card = {
  id: string
  title: string
  description: string
  priority: string
  dueDate?: string
  labels: CardType[]
  assignee?: string
  attachments?: string[]
  comments?: { id: string; author: string; text: string; timestamp: string }[]
  progress: number
  columnId: string
  swimlaneId?: string
  projectId?: string
  tags?: string[]
  subtasks?: { id: string; title: string; completed: boolean }[]
  agentAssignments: AgentAssignment[]
  assignedAgentId: string | null  // ✅ New field for agent assignment
  shellCommand?: string  // ✅ Task 93: Shell command field for agent execution
  dependencies: string[]
  resourceMetrics: ResourceMetrics
  taskHistory: TaskHistoryItem[]
  storyPoints?: number
  updatedAt: string
  createdAt: string
}

export type Column = {
  id: string
  title: string
  cards: Card[]
  limit?: number
  subColumns?: { id: string; title: string }[]
  metadata?: any
}

export type Swimlane = {
  id: string
  title: string
  isExpanded: boolean
}

export type Agent = {
  id: string
  name: string
  type: string
  status: string
  currentTaskId?: string
  capabilities: string[]
  resourceUsage: {
    cpu: number
    memory: number
    tokens: number
  }
}

// Enhanced board type to store complete board data
export type BoardFull = {
  id: string
  name: string
  description?: string
  columns: Column[]
  swimlanes: Swimlane[]
  cardTypes: CardType[]
  agents: Agent[]
}

// Define the context type with comprehensive operations
type BoardContextType = {
  // boards: BoardFull[] // This will be a list of board metadata
  allBoardsMetadata: {id: string, name: string, description?:string}[]; // Renamed
  activeBoard: BoardFull | null
  setActiveBoard: (boardId: string) => void // This will fetch the board data via IPC
  addBoard: (name: string, description?: string) => Promise<BoardFull | null> // Changed to async
  updateBoard: (id: string, name: string, description?: string) => Promise<void>
  deleteBoard: (id: string) => Promise<void>

  // Column operations
  addColumn: (boardId: string, title: string) => Promise<string | null> // returns new columnId
  updateColumn: (boardId: string, column: Column) => Promise<void>
  deleteColumn: (boardId: string, columnId: string) => Promise<void>
  moveColumn: (boardId: string, dragId: string, overId: string | null) => Promise<void>

  // Swimlane operations
  addSwimlane: (boardId: string, title: string) => Promise<string | null> // returns new swimlaneId
  updateSwimlane: (boardId: string, swimlane: Swimlane) => Promise<void>
  deleteSwimlane: (boardId: string, swimlaneId: string) => Promise<void>
  toggleSwimlaneExpansion: (boardId: string, swimlaneId: string) => Promise<void>

  // Card operations
  addCardToColumn: (boardId: string, columnId: string, cardData: Omit<Card, "id" | "createdAt" | "updatedAt">) => Promise<string | null> // returns new cardId
  updateCardInColumn: (boardId: string, updatedCard: Card) => Promise<void>
  deleteCardFromColumn: (boardId: string, columnId: string, cardId: string) => Promise<void>
  moveCard: (
    boardId: string,
    cardId: string,
    sourceColumnId: string,
    destinationColumnId: string,
    destinationSwimlaneId: string,
  ) => Promise<void>

  // Card Type / Legend operations
  updateCardTypes: (boardId: string, cardTypes: CardType[]) => Promise<void>
  // Agent operations
  updateAgents: (boardId: string, agents: Agent[]) => Promise<void>
}

// Create the context with default values
const BoardContext = createContext<BoardContextType | undefined>(undefined)

// Default data constants for fallback when IPC is not available
const defaultCardTypes: CardType[] = [
  { id: "low", name: "Low", color: "#22c55e" },
  { id: "medium", name: "Medium", color: "#facc15" },
  { id: "high", name: "High", color: "#ef4444" },
]

const defaultColumns: Column[] = [
  { id: "column-1", title: "Backlog", cards: [] },
  { id: "column-2", title: "Ready", cards: [] },
  { id: "column-3", title: "In Development", cards: [] },
  { id: "column-4", title: "In Review", cards: [] },
  { id: "column-5", title: "Testing / QA", cards: [] },
  { id: "column-6", title: "Done", cards: [] },
]

const defaultSwimlanes: Swimlane[] = [
  { id: "swimlane-1", title: "Default Swimlane", isExpanded: true },
]

const defaultAgents: Agent[] = []

// Create initial default board
const createDefaultBoard = (): BoardFull => ({
  id: "main",
  name: "Main Development Board",
  description: "Default board for development tasks",
  columns: JSON.parse(JSON.stringify(defaultColumns)),
  swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
  cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
  agents: JSON.parse(JSON.stringify(defaultAgents)),
})

const initialFullBoards: BoardFull[] = [createDefaultBoard()]

// Provider component
export function BoardProvider({ children }: { children: React.ReactNode }) {
  const [boards, setBoardsState] = useState<BoardFull[]>([createDefaultBoard()])
  const [activeBoard, setActiveBoardState] = useState<BoardFull | null>(createDefaultBoard())
  const { toast } = useToast()
  const { setTheme } = useTheme()

  // Initialize theme only once on mount
  useEffect(() => {
    // Check if theme is already set in localStorage
    const savedTheme = localStorage.getItem("theme")
    if (savedTheme) {
      setTheme(savedTheme)

      // Also directly manipulate the DOM for immediate feedback
      if (savedTheme === "dark") {
        document.documentElement.classList.add("dark")
      } else {
        document.documentElement.classList.remove("dark")
      }
    } else {
      // Check for system preference
      if (typeof window !== "undefined") {
        const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
        setTheme(prefersDark ? "dark" : "light")

        // Also directly manipulate the DOM
        if (prefersDark) {
          document.documentElement.classList.add("dark")
        } else {
          document.documentElement.classList.remove("dark")
        }
      }
    }
    // Empty dependency array ensures this only runs once on mount
  }, [])

  // Initialize IPC listeners for state synchronization
  useEffect(() => {
    const unsubscribe = boardIPCBridge.registerEventListeners({
      onStateUpdate: (boardId: string, updatedBoard: BoardFull) => {
        console.log('Received board state update via IPC:', boardId, updatedBoard)

        // Update boards state
        setBoardsState((prevBoards) =>
          prevBoards.map((board) => (board.id === boardId ? updatedBoard : board))
        )

        // Update active board if it matches
        if (activeBoard && activeBoard.id === boardId) {
          setActiveBoardState(updatedBoard)
        }
      },
      onBoardListUpdate: (boardsMetadata) => {
        console.log('Received board list update via IPC:', boardsMetadata)
        // This could be used to update the boards list if needed
      }
    })

    return unsubscribe
  }, [activeBoard])

  // Initialize board state from main process on mount
  useEffect(() => {
    const initializeBoardState = async () => {
      try {
        // Try to get the main board state from the main process
        const mainBoardState = await boardIPCBridge.getBoardState('main')
        if (mainBoardState) {
          console.log('Loaded board state from main process:', mainBoardState)
          setBoardsState([mainBoardState])
          setActiveBoardState(mainBoardState)
        } else {
          console.log('No board state found in main process, using local default')
        }
      } catch (error) {
        console.error('Failed to load board state from main process:', error)
        console.log('Using local default board state')
      }
    }

    initializeBoardState()
  }, []) // Only run once on mount

  // Set active board by ID
  const setActiveBoard = (boardId: string) => {
    const board = boards.find((b) => b.id === boardId)
    if (board) {
      setActiveBoardState(board)
    } else {
      // Fallback if board not found
      if (boards.length > 0) setActiveBoardState(boards[0])
      else setActiveBoardState(null)
    }
  }

  // Add a new board with default structure
  const addBoard = async (name: string, description?: string): Promise<BoardFull | null> => {
    const id = name.toLowerCase().replace(/\s+/g, "-") + `-${Date.now()}`
    const newBoard: BoardFull = {
      id,
      name,
      columns: JSON.parse(JSON.stringify(defaultColumns.map((col) => ({ ...col, cards: [] })))),
      swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
      cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
      agents: JSON.parse(JSON.stringify(defaultAgents)),
      description: description || "",
    }
    setBoardsState([...boards, newBoard])
    toast({
      title: "Board created",
      description: `Board "${name}" has been created.`,
    })
    return newBoard
  }

  // Update a board
  const updateBoard = async (id: string, name: string, description?: string): Promise<void> => {
    setBoardsState(
      boards.map((board) =>
        board.id === id ? { ...board, name, description: description ?? board.description } : board,
      ),
    )
    if (activeBoard && activeBoard.id === id) {
      setActiveBoardState((prev) => (prev ? { ...prev, name, description: description ?? prev.description } : null))
    }
    toast({
      title: "Board updated",
      description: `Board has been renamed to "${name}".`,
    })
  }

  // Delete a board
  const deleteBoard = async (id: string): Promise<void> => {
    if (boards.length <= 1) {
      toast({
        title: "Cannot delete board",
        description: "You must have at least one board.",
        variant: "destructive",
      })
      return
    }
    const updated = boards.filter((board) => board.id !== id)
    setBoardsState(updated)
    if (activeBoard && activeBoard.id === id) {
      setActiveBoard(updated.length > 0 ? updated[0].id : "")
    }
    toast({
      title: "Board deleted",
      description: "The board has been deleted.",
    })
  }

  // Add a new column
  const addColumn = async (boardId: string, title: string): Promise<string | null> => {
    try {
      // Use IPC bridge to add column in main process
      const newColumn = await boardIPCBridge.addColumn(boardId, title)

      if (newColumn) {
        // Don't update local state immediately - let the IPC event listener handle it
        // This prevents duplicate updates and ensures consistency across all windows
        toast({
          title: "Column added",
          description: `Column "${newColumn.title}" has been added.`,
        })
        return newColumn.id
      } else {
        // Fallback to local state if IPC fails
        const newColumnId = `column-${Date.now()}`
        setBoardsState((prevBoards) =>
          prevBoards.map((board) => {
            if (board.id === boardId) {
              const newColumn: Column = { id: newColumnId, title, cards: [] }
              return { ...board, columns: [...board.columns, newColumn] }
            }
            return board
          }),
        )

        // Update activeBoard if it's the one being modified
        if (activeBoard && activeBoard.id === boardId) {
          setActiveBoardState((prev) => {
            if (!prev) return null
            const newColumn: Column = { id: newColumnId, title, cards: [] }
            return { ...prev, columns: [...prev.columns, newColumn] }
          })
        }

        toast({
          title: "Column added",
          description: `Column "${title}" has been added.`,
        })

        return newColumnId
      }
    } catch (error) {
      console.error('Failed to add column via IPC:', error)
      toast({
        title: "Error",
        description: "Failed to add column. Please try again.",
        variant: "destructive",
      })
      return null
    }
  }

  // Update a column
  const updateColumn = async (boardId: string, updatedColumn: Column): Promise<void> => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return {
            ...board,
            columns: board.columns.map((col) => (col.id === updatedColumn.id ? updatedColumn : col)),
          }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              columns: prev.columns.map((col) => (col.id === updatedColumn.id ? updatedColumn : col)),
            }
          : null,
      )
    }

    toast({
      title: "Column updated",
      description: `Column "${updatedColumn.title}" has been updated.`,
    })
  }

  // Delete a column
  const deleteColumn = async (boardId: string, columnId: string): Promise<void> => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return { ...board, columns: board.columns.filter((col) => col.id !== columnId) }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              columns: prev.columns.filter((col) => col.id !== columnId),
            }
          : null,
      )
    }

    toast({
      title: "Column deleted",
      description: "The column has been deleted.",
    })
  }

  // Move column
  const moveColumn = async (boardId: string, dragId: string, overId: string | null): Promise<void> => {
    // Implementation for moving columns - placeholder for now
    toast({
      title: "Column moved",
      description: "The column order has been updated.",
    })
  }

  // Add a new swimlane
  const addSwimlane = async (boardId: string, title: string): Promise<string | null> => {
    try {
      // Use IPC bridge to add swimlane in main process
      const newSwimlane = await boardIPCBridge.addSwimlane(boardId, title)

      if (newSwimlane) {
        // Don't update local state immediately - let the IPC event listener handle it
        // This prevents duplicate updates and ensures consistency across all windows
        toast({
          title: "Swimlane added",
          description: `Swimlane "${newSwimlane.title}" has been added.`,
        })
        return newSwimlane.id
      } else {
        // Fallback to local state if IPC fails
        const newSwimlaneId = `swimlane-${Date.now()}`
        setBoardsState((prevBoards) =>
          prevBoards.map((b) => {
            if (b.id === boardId) {
              const newSwimlane: Swimlane = { id: newSwimlaneId, title, isExpanded: true }
              return { ...b, swimlanes: [...b.swimlanes, newSwimlane] }
            }
            return b
          }),
        )

        if (activeBoard && activeBoard.id === boardId) {
          setActiveBoardState((prev) => {
            if (!prev) return null
            const newSwimlane: Swimlane = { id: newSwimlaneId, title, isExpanded: true }
            return { ...prev, swimlanes: [...prev.swimlanes, newSwimlane] }
          })
        }

        toast({
          title: "Swimlane added",
          description: `Swimlane "${title}" has been added.`,
        })

        return newSwimlaneId
      }
    } catch (error) {
      console.error('Failed to add swimlane via IPC:', error)
      toast({
        title: "Error",
        description: "Failed to add swimlane. Please try again.",
        variant: "destructive",
      })
      return null
    }
  }

  // Update a swimlane
  const updateSwimlane = async (boardId: string, updatedSwimlane: Swimlane): Promise<void> => {
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          return { ...b, swimlanes: b.swimlanes.map((s) => (s.id === updatedSwimlane.id ? updatedSwimlane : s)) }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              swimlanes: prev.swimlanes.map((s) => (s.id === updatedSwimlane.id ? updatedSwimlane : s)),
            }
          : null,
      )
    }

    toast({
      title: "Swimlane updated",
      description: `Swimlane "${updatedSwimlane.title}" has been updated.`,
    })
  }

  // Delete a swimlane
  const deleteSwimlane = async (boardId: string, swimlaneId: string): Promise<void> => {
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          return { ...b, swimlanes: b.swimlanes.filter((s) => s.id !== swimlaneId) }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              swimlanes: prev.swimlanes.filter((s) => s.id !== swimlaneId),
            }
          : null,
      )
    }

    toast({
      title: "Swimlane deleted",
      description: "The swimlane has been deleted.",
    })
  }

  // Toggle swimlane expansion
  const toggleSwimlaneExpansion = async (boardId: string, swimlaneId: string): Promise<void> => {
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          return {
            ...b,
            swimlanes: b.swimlanes.map((s) => (s.id === swimlaneId ? { ...s, isExpanded: !s.isExpanded } : s)),
          }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              swimlanes: prev.swimlanes.map((s) => (s.id === swimlaneId ? { ...s, isExpanded: !s.isExpanded } : s)),
            }
          : null,
      )
    }
  }

  // Add a card to a column
  const addCardToColumn = async (boardId: string, columnId: string, cardData: Omit<Card, "id" | "createdAt" | "updatedAt">): Promise<string | null> => {
    try {
      // Use IPC bridge to create card in main process
      const newCard = await boardIPCBridge.createCard(boardId, columnId, cardData)

      if (newCard) {
        // Don't update local state immediately - let the IPC event listener handle it
        // This prevents duplicate cards when multiple windows are open

        // ✅ Emit agent assignment event if card has an assigned agent
        if (newCard.assignedAgentId) {
          emitAgentAssigned(newCard.id, newCard.assignedAgentId, newCard.title, columnId, boardId);
        }

        toast({
          title: "Card added",
          description: `Card "${newCard.title}" has been added.`,
        })
        return newCard.id
      } else {
        // Fallback to local state if IPC fails
        const now = new Date().toISOString()
        const newCardId = `card-${Date.now()}`

        const fallbackCard: Card = {
          ...cardData,
          id: newCardId,
          columnId,
          createdAt: now,
          updatedAt: now,
          taskHistory: [
            ...(cardData.taskHistory || []),
            {
              timestamp: now,
              action: "created",
              agentId: "user",
              details: "Card created",
            },
          ],
        }

        setBoardsState((prevBoards) =>
          prevBoards.map((board) => {
            if (board.id === boardId) {
              return {
                ...board,
                columns: board.columns.map((col) => {
                  if (col.id === columnId) {
                    return { ...col, cards: [...col.cards, fallbackCard] }
                  }
                  return col
                }),
              }
            }
            return board
          }),
        )

        if (activeBoard && activeBoard.id === boardId) {
          setActiveBoardState((prev) => {
            if (!prev) return null
            return {
              ...prev,
              columns: prev.columns.map((col) => {
                if (col.id === columnId) {
                  return { ...col, cards: [...col.cards, fallbackCard] }
                }
                return col
              }),
            }
          })
        }

        // ✅ Emit agent assignment event if card has an assigned agent
        if (fallbackCard.assignedAgentId) {
          emitAgentAssigned(fallbackCard.id, fallbackCard.assignedAgentId, fallbackCard.title, columnId, boardId);
        }

        toast({
          title: "Card added",
          description: `Card "${fallbackCard.title}" has been added.`,
        })

        return newCardId
      }
    } catch (error) {
      console.error('Failed to add card via IPC:', error)
      toast({
        title: "Error",
        description: "Failed to add card. Please try again.",
        variant: "destructive",
      })
      return null
    }
  }

  // Update a card in a column
  const updateCardInColumn = async (boardId: string, updatedCard: Card): Promise<void> => {
    // ✅ Find the original card to check for agent assignment changes
    let originalCard: Card | undefined;
    if (activeBoard) {
      for (const column of activeBoard.columns) {
        const found = column.cards.find(c => c.id === updatedCard.id);
        if (found) {
          originalCard = found;
          break;
        }
      }
    }

    try {
      // Use IPC bridge to update card in main process
      const result = await boardIPCBridge.updateCard(boardId, { ...updatedCard, updatedAt: new Date().toISOString() })

      if (result) {
        // Don't update local state immediately - let the IPC event listener handle it
        // This prevents duplicate updates and ensures consistency across all windows

        // ✅ Emit agent assignment/unassignment events if agent changed
        const oldAgentId = originalCard?.assignedAgentId;
        const newAgentId = result.assignedAgentId;

        if (oldAgentId !== newAgentId) {
          if (oldAgentId && !newAgentId) {
            // Agent was unassigned
            emitAgentUnassigned(result.id, oldAgentId, result.title, result.columnId, boardId);
          } else if (!oldAgentId && newAgentId) {
            // Agent was assigned
            emitAgentAssigned(result.id, newAgentId, result.title, result.columnId, boardId);
          } else if (oldAgentId && newAgentId) {
            // Agent was changed
            emitAgentUnassigned(result.id, oldAgentId, result.title, result.columnId, boardId);
            emitAgentAssigned(result.id, newAgentId, result.title, result.columnId, boardId);
          }
        }

        toast({
          title: "Card updated",
          description: `Card "${result.title}" has been updated.`,
        })
      } else {
        // Fallback to local state if IPC fails
        setBoardsState((prevBoards) =>
          prevBoards.map((board) => {
            if (board.id === boardId) {
              return {
                ...board,
                columns: board.columns.map((col) => {
                  if (col.id === updatedCard.columnId) {
                    return {
                      ...col,
                      cards: col.cards.map((c) =>
                        c.id === updatedCard.id ? { ...updatedCard, updatedAt: new Date().toISOString() } : c,
                      ),
                    }
                  }
                  const cardExistsInOtherColumn = col.cards.some(c => c.id === updatedCard.id);
                  if (cardExistsInOtherColumn && col.id !== updatedCard.columnId) {
                    return {
                      ...col,
                      cards: col.cards.filter(c => c.id !== updatedCard.id)
                    }
                  }
                  return col
                }),
              }
            }
            return board
          }),
        )

        if (activeBoard && activeBoard.id === boardId) {
          setActiveBoardState((prev) => {
            if (!prev) return null
            return {
              ...prev,
              columns: prev.columns.map((col) => {
                if (col.id === updatedCard.columnId) {
                  return {
                    ...col,
                    cards: col.cards.map((c) =>
                      c.id === updatedCard.id ? { ...updatedCard, updatedAt: new Date().toISOString() } : c,
                    ),
                  }
                }
                const cardExistsInOtherColumn = col.cards.some(c => c.id === updatedCard.id);
                if (cardExistsInOtherColumn && col.id !== updatedCard.columnId) {
                  return {
                    ...col,
                    cards: col.cards.filter(c => c.id !== updatedCard.id)
                  }
                }
                return col
              }),
            }
          })
        }

        toast({
          title: "Card updated",
          description: `Card "${updatedCard.title}" has been updated.`,
        })
      }
    } catch (error) {
      console.error('Failed to update card via IPC:', error)
      toast({
        title: "Error",
        description: "Failed to update card. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Delete a card from a column
  const deleteCardFromColumn = async (boardId: string, columnId: string, cardId: string): Promise<void> => {
    try {
      // Use IPC bridge to delete card in main process
      const result = await boardIPCBridge.deleteCard(boardId, columnId, cardId)

      if (result) {
        // Don't update local state immediately - let the IPC event listener handle it
        // This prevents duplicate updates and ensures consistency across all windows
        toast({
          title: "Card deleted",
          description: "The card has been deleted.",
        })
      } else {
        // Fallback to local state if IPC fails
        setBoardsState((prevBoards) =>
          prevBoards.map((board) => {
            if (board.id === boardId) {
              return {
                ...board,
                columns: board.columns.map((col) => {
                  if (col.id === columnId) {
                    return { ...col, cards: col.cards.filter((c) => c.id !== cardId) }
                  }
                  return col
                }),
              }
            }
            return board
          }),
        )

        if (activeBoard && activeBoard.id === boardId) {
          setActiveBoardState((prev) => {
            if (!prev) return null
            return {
              ...prev,
              columns: prev.columns.map((col) => {
                if (col.id === columnId) {
                  return { ...col, cards: col.cards.filter((c) => c.id !== cardId) }
                }
                return col
              }),
            }
          })
        }

        toast({
          title: "Card deleted",
          description: "The card has been deleted.",
        })
      }
    } catch (error) {
      console.error('Failed to delete card via IPC:', error)
      toast({
        title: "Error",
        description: "Failed to delete card. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Move a card between columns
  const moveCard = async (
    boardId: string,
    cardId: string,
    sourceColumnId: string,
    destinationColumnId: string,
    destinationSwimlaneId: string,
  ): Promise<void> => {
    try {
      // Use IPC bridge to move card in main process
      const movedCard = await boardIPCBridge.moveCard(boardId, cardId, sourceColumnId, destinationColumnId, destinationSwimlaneId)

      if (movedCard) {
        // Don't update local state immediately - let the IPC event listener handle it
        // This prevents duplicate updates and ensures consistency across all windows

        // ✅ Emit card status change event
        emitCardStatusChanged(cardId, sourceColumnId, destinationColumnId, movedCard.title, movedCard.assignedAgentId, boardId);

        toast({
          title: "Card moved",
          description: "The card has been moved.",
        })
      } else {
        // Fallback to local state if IPC fails
        setBoardsState((prevBoards) =>
          prevBoards.map((board) => {
            if (board.id === boardId) {
              let cardToMove: Card | undefined

              // Remove card from source column
              const columnsWithoutCard = board.columns.map((col) => {
                if (col.id === sourceColumnId) {
                  const cardIndex = col.cards.findIndex((c) => c.id === cardId)
                  if (cardIndex > -1) {
                    cardToMove = col.cards[cardIndex]
                    return {
                      ...col,
                      cards: col.cards.filter((c) => c.id !== cardId),
                    }
                  }
                }
                return col
              })

              if (!cardToMove) return board

              // Update card with new swimlaneId and columnId
              const updatedCard = {
                ...cardToMove,
                swimlaneId: destinationSwimlaneId,
                columnId: destinationColumnId,
                updatedAt: new Date().toISOString(),
                taskHistory: [
                  ...cardToMove.taskHistory,
                  {
                    timestamp: new Date().toISOString(),
                    action: "moved",
                    agentId: "user",
                    details: `Moved from ${sourceColumnId} to ${destinationColumnId}`,
                  },
                ],
              }

              // Add card to destination column
              return {
                ...board,
                columns: columnsWithoutCard.map((col) => {
                  if (col.id === destinationColumnId) {
                    return {
                      ...col,
                      cards: [...col.cards, updatedCard],
                    }
                  }
                  return col
                }),
              }
            }
            return board
          }),
        )

        // Update activeBoard if it's the one being modified
        if (activeBoard && activeBoard.id === boardId) {
          setActiveBoardState((prevActiveBoard) => {
            if (!prevActiveBoard) return null

            let cardToMove: Card | undefined

            // Remove card from source column
            const columnsWithoutCard = prevActiveBoard.columns.map((col) => {
              if (col.id === sourceColumnId) {
                const cardIndex = col.cards.findIndex((c) => c.id === cardId)
                if (cardIndex > -1) {
                  cardToMove = col.cards[cardIndex]
                  return {
                    ...col,
                    cards: col.cards.filter((c) => c.id !== cardId),
                  }
                }
              }
              return col
            })

            if (!cardToMove) return prevActiveBoard

            // Update card with new swimlaneId and columnId
            const updatedCard = {
              ...cardToMove,
              swimlaneId: destinationSwimlaneId,
              columnId: destinationColumnId,
              updatedAt: new Date().toISOString(),
              taskHistory: [
                ...cardToMove.taskHistory,
                {
                  timestamp: new Date().toISOString(),
                  action: "moved",
                  agentId: "user",
                  details: `Moved from ${sourceColumnId} to ${destinationColumnId}`,
                },
              ],
            }

            // Add card to destination column
            return {
              ...prevActiveBoard,
              columns: columnsWithoutCard.map((col) => {
                if (col.id === destinationColumnId) {
                  return {
                    ...col,
                    cards: [...col.cards, updatedCard],
                  }
                }
                return col
              }),
            }
          })
        }

        // ✅ Emit card status change event for fallback case
        if (cardToMove) {
          emitCardStatusChanged(cardId, sourceColumnId, destinationColumnId, cardToMove.title, cardToMove.assignedAgentId, boardId);
        }

        toast({
          title: "Card moved",
          description: "The card has been moved.",
        })
      }
    } catch (error) {
      console.error('Failed to move card via IPC:', error)
      toast({
        title: "Error",
        description: "Failed to move card. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Update card types (legend)
  const updateCardTypes = async (boardId: string, newCardTypes: CardType[]): Promise<void> => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => (board.id === boardId ? { ...board, cardTypes: newCardTypes } : board)),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => (prev ? { ...prev, cardTypes: newCardTypes } : null))
    }

    toast({
      title: "Legend updated",
      description: "The card types have been updated.",
    })
  }

  // Update agents
  const updateAgents = async (boardId: string, newAgents: Agent[]): Promise<void> => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => (board.id === boardId ? { ...board, agents: newAgents } : board)),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => (prev ? { ...prev, agents: newAgents } : null))
    }

    toast({
      title: "Agents updated",
      description: "The agents have been updated.",
    })
  }

  return (
    <BoardContext.Provider
      value={{
        allBoardsMetadata: boards.map(board => ({ id: board.id, name: board.name, description: board.description })),
        activeBoard,
        setActiveBoard,
        addBoard,
        updateBoard,
        deleteBoard,
        addColumn,
        updateColumn,
        deleteColumn,
        moveColumn,
        addSwimlane,
        updateSwimlane,
        deleteSwimlane,
        toggleSwimlaneExpansion,
        addCardToColumn,
        updateCardInColumn,
        deleteCardFromColumn,
        moveCard,
        updateCardTypes,
        updateAgents,
      }}
    >
      {children}
    </BoardContext.Provider>
  )
}

// Custom hook to use the board context
export function useBoard() {
  const context = useContext(BoardContext)
  if (context === undefined) {
    throw new Error("useBoard must be used within a BoardProvider")
  }
  return context
}