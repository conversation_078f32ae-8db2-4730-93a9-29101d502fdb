"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect, useRef, useCallback } from "react"
import { useBoard, Card, Column, Swimlane, CardType, Agent as BoardAgentType } from "./board-context"
import { useToast } from "@/hooks/use-toast"
import { CompleteAgentManager, AgentStatus, TaskAssignment, AgentMessage } from "../agents/agent-manager-complete"
import { BoardAgentAPI } from "./lib/board-agent-api"

interface IBoardAgentService {
  createTaskCard(task: any, agentId: string): Promise<any>;
  moveCardToColumn(cardId: string, columnId: string, agentId: string): Promise<any>;
  addCardDependency(cardId: string, dependencyCardId: string, agentId: string): Promise<any>;
  updateCardProgress(cardId: string, progress: number, agentId: string): Promise<any>;
}

type AgentBoardControllerContextType = {
  isAgentRunning: boolean
  startAgent: () => void
  stopAgent: () => void
  agentLogs: string[]
  clearLogs: () => void
  agentStatus: "idle" | "running" | "paused" | "error"
}

const AgentBoardControllerContext = createContext<AgentBoardControllerContextType | undefined>(undefined)

export function AgentBoardControllerProvider({ children }: { children: React.ReactNode }) {
  const [isAgentRunning, setIsAgentRunning] = useState(false)
  const [agentLogs, setAgentLogs] = useState<string[]>([])
  const [agentStatus, setAgentStatus] = useState<"idle" | "running" | "paused" | "error">("idle")
  const { activeBoard, addCardToColumn, updateCardInColumn, updateAgents, columns, swimlanes, moveCard } = useBoard()
  const { toast } = useToast()

  const agentManagerRef = useRef<CompleteAgentManager | null>(null);
  const boardAgentAPIRef = useRef<BoardAgentAPI | null>(null);

  useEffect(() => {
    if (!activeBoard) {
        console.warn("No active board, Agent Manager and Board Agent API cannot be initialized.");
        setIsAgentRunning(false);
        setAgentStatus("idle");
        if (agentManagerRef.current) {
          agentManagerRef.current.shutdown();
          agentManagerRef.current = null;
        }
        return;
    }

    const boardContextAdapter = {
      addCard: async (newCard: Card): Promise<string | null> => {
        if (!activeBoard || !newCard.columnId) {
          console.error("Attempted to add card without activeBoard or columnId in adapter.");
          return null;
        }
        const { id, createdAt, updatedAt, ...cardData } = newCard;
        return await addCardToColumn(activeBoard.id, newCard.columnId, cardData);
      },
      updateCard: async (updatedCard: Card): Promise<void> => {
        if (!activeBoard) {
          console.error("Attempted to update card without activeBoard in adapter.");
          return;
        }
        return await updateCardInColumn(activeBoard.id, updatedCard);
      },
      moveCard: async (cardId: string, sourceColId: string, destColId: string, destSwimlaneId: string): Promise<void> => {
        if (!activeBoard) {
            console.error("Attempted to move card without activeBoard in adapter.");
            return;
        }
        await moveCard(activeBoard.id, cardId, sourceColId, destColId, destSwimlaneId);
      },
      get columns() { return activeBoard.columns; },
      get swimlanes() { return activeBoard.swimlanes; },
    };

    const currentBoardAgentAPI = new BoardAgentAPI(boardContextAdapter);
    boardAgentAPIRef.current = currentBoardAgentAPI;

    const boardServiceForAgentManager: IBoardAgentService = {
        createTaskCard: async (task: any, agentId: string) => {
            return await currentBoardAgentAPI.createTaskCard(task, agentId);
        },
        moveCardToColumn: async (cardId: string, columnId: string, agentId: string) => {
            return await currentBoardAgentAPI.moveCardToColumn(cardId, columnId, agentId);
        },
        addCardDependency: async (cardId: string, dependencyCardId: string, agentId: string) => {
            return await currentBoardAgentAPI.addCardDependency(cardId, dependencyCardId, agentId);
        },
        updateCardProgress: async (cardId: string, progress: number, agentId: string) => {
            return await currentBoardAgentAPI.updateCardProgress(cardId, progress, agentId);
        }
    };

    if (!agentManagerRef.current) {
      const manager = new CompleteAgentManager(boardServiceForAgentManager);
      agentManagerRef.current = manager;

      manager.onMessage((message: AgentMessage) => {
        const timestamp = new Date(message.timestamp).toLocaleTimeString();
        setAgentLogs((prev) => [...prev, `[${timestamp}] [${message.agentId}] ${message.message}`]);
        const updatedStatuses = manager.getAllAgentStatuses();
        const mappedAgents: BoardAgentType[] = updatedStatuses.map(s => ({
          id: s.id,
          name: s.name,
          type: s.type,
          status: s.status,
          currentTaskId: s.currentTask,
          capabilities: s.capabilities,
          resourceUsage: {
            cpu: s.healthScore,
            memory: s.tokensUsed / 1000,
            tokens: s.tokensUsed,
          },
        }));
        updateAgents(activeBoard.id, mappedAgents);
      });
    }

    const interval = setInterval(() => {
      if (agentManagerRef.current && activeBoard) {
        const updatedStatuses = agentManagerRef.current.getAllAgentStatuses();
        const mappedAgents: BoardAgentType[] = updatedStatuses.map(s => ({
          id: s.id,
          name: s.name,
          type: s.type,
          status: s.status,
          currentTaskId: s.currentTask,
          capabilities: s.capabilities,
          resourceUsage: {
            cpu: s.healthScore,
            memory: s.tokensUsed / 1000,
            tokens: s.tokensUsed,
          },
        }));
        updateAgents(activeBoard.id, mappedAgents);

        const micromanagerStatus = updatedStatuses.find(a => a.id === 'micromanager')?.status;
        if (micromanagerStatus === 'busy') setIsAgentRunning(true);
        else if (micromanagerStatus === 'error') setIsAgentRunning(true);
        else setIsAgentRunning(false);

        if (micromanagerStatus === 'busy') setAgentStatus('running');
        else if (micromanagerStatus === 'error') setAgentStatus('error');
        else setAgentStatus('idle');
      }
    }, 5000);

    return () => {
      if (agentManagerRef.current) {
        agentManagerRef.current.offMessage(() => {});
      }
      clearInterval(interval);
    };
  }, [activeBoard, addCardToColumn, updateCardInColumn, updateAgents, columns, swimlanes, moveCard]);

  const startAgent = async () => {
    if (!activeBoard || !agentManagerRef.current) {
      toast({
        title: "Error",
        description: "Agent system not fully initialized or no active board.",
        variant: "destructive",
      })
      return
    }

    setIsAgentRunning(true)
    setAgentStatus("running")

    const timestamp = new Date().toISOString()
    setAgentLogs((prev) => [...prev, `[${timestamp}] Agent system started`])

    toast({
      title: "Agent system started",
      description: "The AI agent system is now running and awaiting tasks.",
    })

    try {
        const initialTask = "Develop a simple user authentication module with sign-up and login functionality. Include database schema, API endpoints, and a basic frontend component.";
        setAgentLogs((prev) => [...prev, `[${new Date().toISOString()}] User task submitted: "${initialTask}"`]);
        const newTaskId = await agentManagerRef.current.submitTask(initialTask, undefined, 'urgent', { originalTaskId: `user-task-${Date.now()}` });
        setAgentLogs((prev) => [...prev, `[${new Date().toISOString()}] Task submitted to Micromanager with ID: ${newTaskId}.`]);
    } catch (error: any) {
        console.error("Error submitting initial task to agent manager:", error);
        setAgentLogs((prev) => [...prev, `[${new Date().toISOString()}] Error submitting task: ${error.message || String(error)}`]);
        setAgentStatus("error");
        toast({
            title: "Agent Error",
            description: `Failed to submit initial task: ${error.message || String(error)}`,
            variant: "destructive",
        });
        setIsAgentRunning(false);
    }
  }

  const stopAgent = () => {
    if (agentManagerRef.current) {
      agentManagerRef.current.shutdown();
    }
    setIsAgentRunning(false);
    setAgentStatus("idle");
    const timestamp = new Date().toISOString();
    setAgentLogs((prev) => [...prev, `[${timestamp}] Agent system stopped`]);
    toast({
      title: "Agent system stopped",
      description: "The AI agent system has been stopped.",
    });
  };

  const clearLogs = () => {
    setAgentLogs([])
  }

  return (
    <AgentBoardControllerContext.Provider
      value={{
        isAgentRunning,
        startAgent,
        stopAgent,
        agentLogs,
        clearLogs,
        agentStatus,
      }}
    >
      {children}
    </AgentBoardControllerContext.Provider>
  )
}

export function useAgentBoardController() {
  const context = useContext(AgentBoardControllerContext)
  if (context === undefined) {
    throw new Error("useAgentBoardController must be used within an AgentBoardControllerProvider")
  }
  return context
}