"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { CardType } from "./board-context"
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog"
import { Trash2 } from "lucide-react"

interface LegendEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  cardTypes: CardType[]
  onSave: (cardTypes: CardType[]) => void
}

export function LegendEditDialog({
  open,
  onOpenChange,
  cardTypes,
  onSave,
}: LegendEditDialogProps) {
  const [types, setTypes] = useState<CardType[]>([])
  const [newTypeName, setNewTypeName] = useState("")
  const [newTypeColor, setNewTypeColor] = useState("#888888")

  useEffect(() => {
    setTypes([...cardTypes])
  }, [cardTypes, open])

  const handleAddType = () => {
    if (!newTypeName) return
    
    const newType: CardType = {
      id: newTypeName.toLowerCase().replace(/\s+/g, "-"),
      name: newTypeName,
      color: newTypeColor,
    }
    
    setTypes([...types, newType])
    setNewTypeName("")
    setNewTypeColor("#888888")
  }

  const handleRemoveType = (id: string) => {
    setTypes(types.filter(type => type.id !== id))
  }

  const handleUpdateType = (id: string, field: keyof CardType, value: string) => {
    setTypes(types.map(type => 
      type.id === id ? { ...type, [field]: value } : type
    ))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(types)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Legend</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <h3 className="text-sm font-medium">Current Types</h3>
            <div className="space-y-2">
              {types.map((type) => (
                <div key={type.id} className="flex items-center gap-2">
                  <Input
                    type="color"
                    value={type.color}
                    onChange={(e) => handleUpdateType(type.id, "color", e.target.value)}
                    className="w-12 h-8 p-1"
                  />
                  <Input
                    value={type.name}
                    onChange={(e) => handleUpdateType(type.id, "name", e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => handleRemoveType(type.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            <div className="border-t border-border pt-4 mt-2">
              <h3 className="text-sm font-medium mb-2">Add New Type</h3>
              <div className="flex items-center gap-2">
                <Input
                  type="color"
                  value={newTypeColor}
                  onChange={(e) => setNewTypeColor(e.target.value)}
                  className="w-12 h-8 p-1"
                />
                <Input
                  value={newTypeName}
                  onChange={(e) => setNewTypeName(e.target.value)}
                  className="flex-1"
                  placeholder="Type name"
                />
                <Button type="button" onClick={handleAddType}>
                  Add
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}