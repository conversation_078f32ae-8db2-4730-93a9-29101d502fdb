"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useBoard } from "./board-context"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  <PERSON>alogTitle,
  <PERSON>alogFooter,
} from "@/components/ui/dialog"

interface BoardSettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  boardId: string
}

export function BoardSettingsDialog({
  open,
  onOpenChange,
  boardId,
}: BoardSettingsDialogProps) {
  const { allBoardsMetadata, activeBoard, updateBoard } = useBoard()

  // Find board metadata or use activeBoard if it matches
  const boardMetadata = allBoardsMetadata.find(b => b.id === boardId)
  const boardToEdit = activeBoard && activeBoard.id === boardId ? activeBoard : boardMetadata

  const [name, setName] = useState(boardToEdit?.name || "")
  const [description, setDescription] = useState(boardToEdit?.description || "")

  useEffect(() => {
    if (open) {
      // Use activeBoard if it matches the boardId, otherwise use metadata
      const currentBoardToEdit = activeBoard && activeBoard.id === boardId ? activeBoard : boardMetadata
      if (currentBoardToEdit) {
        setName(currentBoardToEdit.name);
        setDescription(currentBoardToEdit.description || "");
      } else {
        setName("");
        setDescription("");
      }
    }
  }, [open, boardId, allBoardsMetadata, activeBoard, boardMetadata]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (boardToEdit && boardToEdit.id) {
        updateBoard(boardToEdit.id, name, description)
    }
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Board Settings{boardToEdit ? `: ${boardToEdit.name}` : ''}</DialogTitle>
        </DialogHeader>
        {boardToEdit && boardToEdit.id ? (
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="name" className="text-right text-sm">
                  Board Name
                </label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="description" className="text-right text-sm">
                  Description
                </label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="col-span-3"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </DialogFooter>
          </form>
        ) : (
          <div className="py-4 text-center text-muted-foreground">
            Board not found or an error occurred.
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}