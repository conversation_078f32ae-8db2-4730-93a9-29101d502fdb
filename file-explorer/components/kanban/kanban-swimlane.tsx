"use client"

import { useDroppable } from "@dnd-kit/core"
import { Card, Column, Swimlane, CardType, Agent } from "./board-context"
import { KanbanCard } from "./kanban-card"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronRight, MoreHorizontal, Plus } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

const SwimlaneColumn = ({
  column,
  swimlaneId,
  cards,
  onAddCard,
  cardTypes,
  isBoardOver,
  onCardUpdate,
  onDeleteCard,
}: {
  column: Column
  swimlaneId: string
  cards: Card[]
  onAddCard: (columnId: string) => void
  cardTypes: CardType[]
  isBoardOver?: boolean
  onCardUpdate: (card: Card) => void
  onDeleteCard: (cardId: string) => void
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `${swimlaneId}-${column.id}`,
    data: {
      type: "swimlane-column",
      columnId: column.id,
      swimlaneId: swimlaneId,
    },
  })

  const isActiveDropTarget = isOver || isBoardOver;

  return (
    <div
      ref={setNodeRef}
      className={`w-72 shrink-0 border rounded-md p-2 flex flex-col ${
        isActiveDropTarget ? "ring-2 ring-primary border-primary" : "border-border"
      }`}
    >
      <div className="font-medium mb-2 flex justify-between items-center">
        <span>{column.title}</span>
        <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => onAddCard(column.id)}>
            <Plus className="h-3.5 w-3.5" />
        </Button>
      </div>
      <ScrollArea className="flex-1">
        <div className="space-y-2 min-h-[50px]">
          {cards.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              Drop cards here
            </div>
          ) : (
            cards.map((card) => (
              <KanbanCard
                key={card.id}
                card={card}
                cardTypes={cardTypes}
                columnId={column.id}
                onCardUpdate={onCardUpdate}
                onDeleteCard={onDeleteCard}
              />
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

interface KanbanSwimlaneProps {
  swimlane: Swimlane
  columns: Column[]
  onAddCard: (columnId: string) => void
  onCardUpdate: (card: Card) => void
  onDeleteCard: (columnId: string, cardId: string) => void
  onToggleExpansion: () => void
  onEditSwimlane: () => void
  cardTypes: CardType[]
  agents: Agent[]
  boardActiveColumnId?: string | null;
  boardActiveSwimlaneId?: string | null;
}

export function KanbanSwimlane({
  swimlane,
  columns,
  onAddCard,
  onCardUpdate,
  onDeleteCard,
  onToggleExpansion,
  onEditSwimlane,
  cardTypes,
  agents,
  boardActiveColumnId,
  boardActiveSwimlaneId,
}: KanbanSwimlaneProps) {

  const getCardsForColumnInSwimlane = (columnId: string): Card[] => {
    const column = columns.find(c => c.id === columnId);
    return column?.cards.filter(card => card.swimlaneId === swimlane.id) || [];
  }

  return (
    <div className="border border-border rounded-md overflow-hidden">
      <div className="flex items-center justify-between p-2 bg-muted/50">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={onToggleExpansion}>
            {swimlane.isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
          <div className="font-medium">{swimlane.title}</div>
        </div>
        <Button variant="ghost" size="icon" className="h-7 w-7" onClick={onEditSwimlane}>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </div>

      {swimlane.isExpanded && (
        <div className="p-2">
          <div className="flex gap-4 overflow-x-auto pb-2">
            {columns.map((column) => (
              <SwimlaneColumn
                key={column.id}
                column={column}
                swimlaneId={swimlane.id}
                cards={getCardsForColumnInSwimlane(column.id)}
                onAddCard={onAddCard}
                cardTypes={cardTypes}
                isBoardOver={boardActiveColumnId === column.id && boardActiveSwimlaneId === swimlane.id}
                onCardUpdate={onCardUpdate}
                onDeleteCard={(cardId) => onDeleteCard(column.id, cardId)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}