"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { CalendarIcon, Link, PlusCircle, Trash2 } from "lucide-react"
import { format, formatDistanceToNow } from "date-fns"
import { Card, CardType as BoardCardType } from "./board-context"
import { Checkbox } from "@/components/ui/checkbox"
import TaskLogViewer from "./TaskLogViewer"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Available agents for assignment
const AVAILABLE_AGENTS = [
  { id: 'micromanager', name: '🤖 Micromanager', type: 'orchestrator' },
  { id: 'intern', name: '1️⃣ Intern', type: 'implementation' },
  { id: 'junior', name: '2️⃣ Junior', type: 'implementation' },
  { id: 'midlevel', name: '3️⃣ MidLevel', type: 'implementation' },
  { id: 'senior', name: '4️⃣ Senior', type: 'implementation' },
  { id: 'researcher', name: '📘 Researcher', type: 'specialized' },
  { id: 'architect', name: '🏗️ Architect', type: 'specialized' },
  { id: 'designer', name: '🎨 Designer', type: 'specialized' },
  { id: 'tester', name: '🧪 Tester', type: 'specialized' }
];

interface Subtask {
  id: string
  title: string
  completed: boolean
}

interface CardDetailViewProps {
  card: Card
  cardTypes: BoardCardType[]
  onClose: () => void
  onUpdate?: (updatedCard: Card) => void
  onDelete?: () => void
}

export function CardDetailView({ card, cardTypes, onClose, onUpdate, onDelete }: CardDetailViewProps) {
  const [title, setTitle] = useState(card.title)
  const [description, setDescription] = useState(card.description || "")
  const [priority, setPriority] = useState<string>(card.priority || (cardTypes.length > 0 ? cardTypes[0].id : ""))
  const [date, setDate] = useState<Date | undefined>(card.dueDate ? new Date(card.dueDate) : undefined)
  const [progress, setProgress] = useState(card.progress || 0)
  const [dependencies, setDependencies] = useState<string[]>(card.dependencies || [])
  const [tags, setTags] = useState<string[]>(card.tags || [])
  const [newTag, setNewTag] = useState("")
  const [activeTab, setActiveTab] = useState("details")
  const [shellCommand, setShellCommand] = useState(card.shellCommand || "")  // ✅ Task 93: Shell command state
  const [subtasks, setSubtasks] = useState<Subtask[]>(card.subtasks || [])
  const [newSubtask, setNewSubtask] = useState("")
  const [assignedAgentId, setAssignedAgentId] = useState<string>(card.assignedAgentId || "unassigned")  // ✅ New state for agent assignment

  useEffect(() => {
    // Ensure priority is a valid ID from cardTypes, or default if not
    if (cardTypes.length > 0) {
      const isValidPriority = cardTypes.some(ct => ct.id === card.priority);
      if (!isValidPriority) {
        setPriority(cardTypes[0].id);
      } else {
        setPriority(card.priority);
      }
    } else {
      setPriority(""); // No types available
    }
  }, [card.priority, cardTypes]);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return format(date, "PPP p")
    } catch (error) {
      return dateString
    }
  }

  const handleToggleSubtask = (subtaskId: string) => {
    setSubtasks(
      subtasks.map((st) => (st.id === subtaskId ? { ...st, completed: !st.completed } : st))
    )
  }

  const handleAddSubtask = () => {
    if (newSubtask.trim()) {
      setSubtasks([
        ...subtasks,
        {
          id: `subtask-${Date.now()}`,
          title: newSubtask.trim(),
          completed: false,
        },
      ])
      setNewSubtask("")
    }
  }

  const handleDeleteSubtask = (subtaskId: string) => {
    setSubtasks(subtasks.filter((st) => st.id !== subtaskId))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!title.trim()) return

    // Create a new card object with updated values
    const updatedCard: Card = {
      ...card,
      title,
      description,
      priority,
      dueDate: date?.toISOString(),
      progress,
      dependencies,
      tags,
      subtasks,
      assignedAgentId: assignedAgentId === "unassigned" ? null : assignedAgentId,  // ✅ Include agent assignment
      shellCommand: shellCommand.trim() || undefined,  // ✅ Task 93: Include shell command
      updatedAt: new Date().toISOString(),
    }

    if (onUpdate) {
      onUpdate(updatedCard)
    }

    onClose()
  }

  const handleDelete = () => {
    if (window.confirm("Are you sure you want to delete this card?")) {
      if (typeof onDelete === "function") {
        onDelete()
      }
      onClose()
    }
  }

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove))
  }

  return (
    <Dialog open={true} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Card Details</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="dependencies">Dependencies</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="logs">Task Logs</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4 pt-4">
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter card title"
                    required
                  />
                </div>

                {card.projectId && (
                  <div className="grid gap-2">
                    <Label htmlFor="projectId">Project ID</Label>
                    <Input id="projectId" value={card.projectId} disabled className="bg-muted" />
                  </div>
                )}

                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter card description"
                    className="min-h-[100px]"
                  />
                </div>

                <div className="grid gap-2">
                  <Label>Priority</Label>
                  <Select
                    value={priority}
                    onValueChange={(value) => setPriority(value)}
                    disabled={cardTypes.length === 0}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      {cardTypes.length > 0 ? (
                        cardTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="" disabled>No priorities configured</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label>Assigned Agent</Label>
                  <Select
                    value={assignedAgentId}
                    onValueChange={(value) => setAssignedAgentId(value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select an agent (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unassigned">None (Unassigned)</SelectItem>
                      {AVAILABLE_AGENTS.map((agent) => (
                        <SelectItem key={agent.id} value={agent.id}>
                          {agent.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="shellCommand">Shell Command</Label>
                  <Input
                    id="shellCommand"
                    value={shellCommand}
                    onChange={(e) => setShellCommand(e.target.value)}
                    placeholder="e.g., ls -la, npm test, echo hello"
                  />
                  <p className="text-xs text-muted-foreground">
                    Optional shell command that will be executed automatically when an agent is assigned to this card.
                  </p>
                </div>

                <div className="grid gap-2">
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-1 text-muted-foreground hover:text-foreground"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault()
                          handleAddTag()
                        }
                      }}
                    />
                    <Button type="button" onClick={handleAddTag} size="sm">
                      Add
                    </Button>
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label>Due Date</Label>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        className="flex-1 justify-start text-left font-normal"
                        onClick={(e) => {
                          e.preventDefault()
                          // Toggle date picker visibility
                          const datePicker = document.getElementById(`date-picker-${card.id}`)
                          if (datePicker) {
                            datePicker.style.display = datePicker.style.display === "none" ? "block" : "none"
                          }
                        }}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {date ? format(date, "PPP") : "Select a date"}
                      </Button>

                      {date && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.preventDefault()
                            setDate(undefined)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div
                      id={`date-picker-${card.id}`}
                      className="border rounded-md p-2 bg-background"
                      style={{ display: "none" }}
                    >
                      <Calendar
                        mode="single"
                        selected={date}
                        onSelect={(newDate) => {
                          setDate(newDate)
                          // Hide calendar after selection
                          const datePicker = document.getElementById(`date-picker-${card.id}`)
                          if (datePicker) {
                            datePicker.style.display = "none"
                          }
                        }}
                        initialFocus
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-2">
                  <div className="flex justify-between">
                    <Label>Progress: {progress}%</Label>
                  </div>
                  <Slider
                    value={[progress]}
                    min={0}
                    max={100}
                    step={5}
                    onValueChange={(value) => setProgress(value[0])}
                  />
                  <Progress value={progress} className="h-2" />
                </div>

                <div className="grid gap-2 mt-4">
                  <Label>Subtasks</Label>
                  <div className="space-y-2">
                    {subtasks.map((subtask) => (
                      <div key={subtask.id} className="flex items-center gap-2">
                        <Checkbox
                          id={subtask.id}
                          checked={subtask.completed}
                          onCheckedChange={() => handleToggleSubtask(subtask.id)}
                        />
                        <Label
                          htmlFor={subtask.id}
                          className={`flex-1 text-sm ${subtask.completed ? "line-through text-muted-foreground" : ""}`}
                        >
                          {subtask.title}
                        </Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => handleDeleteSubtask(subtask.id)}
                        >
                          <Trash2 className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                        </Button>
                      </div>
                    ))}

                    <div className="flex gap-2 mt-2">
                      <Input
                        value={newSubtask}
                        onChange={(e) => setNewSubtask(e.target.value)}
                        placeholder="Add a subtask"
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault()
                            handleAddSubtask()
                          }
                        }}
                      />
                      <Button type="button" onClick={handleAddSubtask} size="sm">
                        <PlusCircle className="h-4 w-4 mr-1" />
                        Add
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-between mt-6">
                <Button type="button" variant="destructive" onClick={handleDelete}>
                  Delete Card
                </Button>
                <div className="flex gap-2">
                  <Button type="button" variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button type="submit">Save Changes</Button>
                </div>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="dependencies" className="space-y-4 pt-4">
            <div className="grid gap-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Task Dependencies</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Select tasks that must be completed before this task can be started
                </p>

                {/* Simplified dependency selector */}
                <div className="border rounded-md p-4">
                  <p className="text-sm text-muted-foreground">
                    Dependencies can be managed here. In a full implementation, you would see a list of available cards
                    to select as dependencies.
                  </p>
                </div>
              </div>

              {dependencies.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">Current Dependencies</h4>
                  <div className="space-y-2">
                    {dependencies.map((depId) => (
                      <div key={depId} className="p-3 border rounded-md">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center gap-2">
                              <h5 className="font-medium">{`Card ${depId}`}</h5>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDependencies(dependencies.filter((id) => id !== depId))}
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end mt-6">
              <Button onClick={onClose}>Close</Button>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-4 pt-4">
            <div>
              <h3 className="text-lg font-medium mb-4">Task History</h3>

              {!card.taskHistory || card.taskHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground border rounded-md">
                  No history available for this task
                </div>
              ) : (
                <div className="space-y-4">
                  {card.taskHistory.map((historyItem, index) => (
                    <div key={index} className="relative pl-6 pb-4">
                      {/* Timeline connector */}
                      {index < card.taskHistory.length - 1 && (
                        <div className="absolute left-[0.6rem] top-3 w-0.5 h-full -ml-px bg-muted-foreground/20" />
                      )}

                      {/* Timeline dot */}
                      <div className="absolute left-0 top-1.5 w-3 h-3 rounded-full bg-primary" />

                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{historyItem.action.replace(/_/g, " ")}</span>
                          <Badge variant="outline" className="text-xs">
                            {historyItem.agentId}
                          </Badge>
                        </div>
                        <span className="text-xs text-muted-foreground">{formatDate(historyItem.timestamp)}</span>
                        <p className="mt-1 text-sm">{historyItem.details}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          {/* ✅ Task 84: Task Logs Tab */}
          <TabsContent value="logs" className="space-y-4 pt-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Agent Execution Logs</h3>
                <Badge variant="outline" className="text-xs">
                  Real-time
                </Badge>
              </div>

              <div className="text-sm text-muted-foreground">
                View real-time terminal output and execution logs from agents working on this task.
              </div>

              {/* ✅ Task 84: TaskLogViewer component */}
              <TaskLogViewer
                taskId={card.id}
                cardId={card.id}
                className="w-full"
                maxHeight="500px"
              />
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}