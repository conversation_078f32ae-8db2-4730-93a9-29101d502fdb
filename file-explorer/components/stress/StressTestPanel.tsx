// components/stress/StressTestPanel.tsx

"use client"

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/hooks/use-toast'
import {
  AlertTriangle,
  Play,
  Square,
  BarChart3,
  Clock,
  Zap,
  AlertCircle,
  CheckCircle2,
  Activity,
  RefreshCw,
  Target,
  Users,
  Timer,
  Cpu,
  MemoryStick,
  TestTube,
  Loader2,
  Trash2,
  Terminal,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

import { StressTestRunner, type StressTestConfig, type StressTestResult } from "../../systems/stress/StressTestRunner"
import { CompleteAgentManager } from "@/components/agents/agent-manager-complete"
import { SettingsManager } from "@/components/settings/settings-manager"
import { getAnalyticsService } from "@/services/analytics-service"

interface StressTestPanelProps {
  testModeEnabled: boolean
  agentManager: CompleteAgentManager
}

// Available agents in the system
const AVAILABLE_AGENTS = [
  { id: 'micromanager', name: '🤖 Micromanager', type: 'orchestrator' },
  { id: 'intern', name: '1️⃣ Intern', type: 'implementation' },
  { id: 'junior', name: '2️⃣ Junior', type: 'implementation' },
  { id: 'midlevel', name: '3️⃣ MidLevel', type: 'implementation' },
  { id: 'senior', name: '4️⃣ Senior', type: 'implementation' },
  { id: 'researcher', name: '📘 Researcher', type: 'specialized' },
  { id: 'architect', name: '🏗️ Architect', type: 'specialized' },
  { id: 'designer', name: '🎨 Designer', type: 'specialized' },
  { id: 'tester', name: '🧪 Tester', type: 'specialized' }
];

// Custom hook for stress testing functionality
const useStressTest = (agentManager: CompleteAgentManager, settingsManager: SettingsManager) => {
  const [isRunning, setIsRunning] = useState(false);
  const [currentResult, setCurrentResult] = useState<StressTestResult | null>(null);
  const [liveOutput, setLiveOutput] = useState<string[]>([]);
  const [progress, setProgress] = useState(0);
  const stressRunnerRef = useRef<StressTestRunner | null>(null);
  const analyticsService = getAnalyticsService();

  // Initialize stress runner
  useEffect(() => {
    stressRunnerRef.current = new StressTestRunner(agentManager, settingsManager);
  }, [agentManager, settingsManager]);

  const addLiveOutput = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLiveOutput(prev => [...prev.slice(-49), `[${timestamp}] ${message}`]);
  }, []);

  const runTest = useCallback(async (config: StressTestConfig) => {
    if (!stressRunnerRef.current || isRunning) return;

    setIsRunning(true);
    setProgress(0);
    setCurrentResult(null);
    setLiveOutput([]);

    try {
      addLiveOutput(`🧪 Starting stress test with ${config.agents.length} agents`);
      addLiveOutput(`📋 Config: ${config.taskType} tasks, ${config.duration}s duration, ${config.concurrency} concurrency`);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + (100 / config.duration), 95));
      }, 1000);

      const result = await stressRunnerRef.current.runTest(config);

      clearInterval(progressInterval);
      setProgress(100);

      addLiveOutput(`✅ Test completed: ${result.metrics.totalTasks} tasks, ${((result.metrics.successfulTasks / result.metrics.totalTasks) * 100).toFixed(1)}% success rate`);

      // Report to analytics service
      analyticsService.reportStressTestResults(result);
      setCurrentResult(result);

      return result;
    } catch (error) {
      addLiveOutput(`❌ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    } finally {
      setIsRunning(false);
      setProgress(0);
    }
  }, [isRunning, addLiveOutput, analyticsService]);

  const clearResults = useCallback(() => {
    setCurrentResult(null);
    setLiveOutput([]);
    setProgress(0);
  }, []);

  return {
    isRunning,
    currentResult,
    liveOutput,
    progress,
    runTest,
    clearResults
  };
};

export default function StressTestPanel({ testModeEnabled, agentManager }: StressTestPanelProps) {
  const { toast } = useToast();
  const [settingsManager] = useState(() => new SettingsManager());

  // Use the custom hook for stress testing functionality
  const {
    isRunning,
    currentResult,
    liveOutput,
    progress,
    runTest,
    clearResults
  } = useStressTest(agentManager, settingsManager);

  // Form configuration state
  const [selectedAgents, setSelectedAgents] = useState<string[]>(['micromanager', 'senior']);
  const [taskType, setTaskType] = useState<'simple' | 'complex'>('simple');
  const [duration, setDuration] = useState(30);
  const [concurrency, setConcurrency] = useState(3);
  const [maxTasks, setMaxTasks] = useState<number | undefined>(undefined);

  // System settings
  const [systemSettings, setSystemSettings] = useState(settingsManager.getSystemSettings());

  // Development mode check
  if (process.env.NODE_ENV === 'production') {
    return null; // Don't render in production
  }

  if (!testModeEnabled) {
    return (
      <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950 dark:border-yellow-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-700 dark:text-yellow-300">
            <AlertTriangle className="h-5 w-5" />
            Stress Testing Disabled
          </CardTitle>
          <CardDescription className="text-yellow-600 dark:text-yellow-400">
            Enable test mode in System settings to access stress testing features.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const handleAgentToggle = (agentId: string, checked: boolean) => {
    if (checked) {
      setSelectedAgents(prev => [...prev, agentId]);
    } else {
      setSelectedAgents(prev => prev.filter(id => id !== agentId));
    }
  };

  const validateConfig = (): string | null => {
    if (selectedAgents.length === 0) {
      return 'Please select at least one agent';
    }
    if (concurrency > systemSettings.maxConcurrentTasks) {
      return `Concurrency (${concurrency}) cannot exceed system limit (${systemSettings.maxConcurrentTasks})`;
    }
    if (duration <= 0) {
      return 'Duration must be greater than 0';
    }
    if (concurrency <= 0) {
      return 'Concurrency must be greater than 0';
    }
    return null;
  };

  const handleRunTest = async () => {
    const validationError = validateConfig();
    if (validationError) {
      toast({
        title: "Configuration Error",
        description: validationError,
        variant: "destructive",
      });
      return;
    }

    try {
      const config: StressTestConfig = {
        agents: selectedAgents,
        taskType,
        duration,
        concurrency,
        maxTasks
      };

      await runTest(config);

      toast({
        title: "Stress Test Completed",
        description: `Test completed successfully`,
        variant: "default",
      });

    } catch (error) {
      console.error('Stress test failed:', error);
      toast({
        title: "Stress Test Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    }
  };

  const handleClearResults = () => {
    clearResults();
    toast({
      title: "Results Cleared",
      description: "Test results and output have been cleared",
      variant: "default",
    });
  };

  const formatDuration = (ms: number): string => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <TestTube className="h-6 w-6" />
            Stress Test Runner
          </h2>
          <p className="text-muted-foreground">
            Configure and execute stress tests to validate Agent System performance
          </p>
        </div>
        <Badge variant="outline" className="text-xs">
          Development Mode Only
        </Badge>
      </div>

      {/* Configuration Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Target className="h-5 w-5" />
            Test Configuration
          </CardTitle>
          <CardDescription>
            Configure stress test parameters and select agents to test
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Agent Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Select Agents to Test</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {AVAILABLE_AGENTS.map((agent) => (
                <div key={agent.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={agent.id}
                    checked={selectedAgents.includes(agent.id)}
                    onCheckedChange={(checked) => handleAgentToggle(agent.id, checked as boolean)}
                  />
                  <Label htmlFor={agent.id} className="text-sm cursor-pointer">
                    {agent.name}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-muted-foreground">
              Selected: {selectedAgents.length} agent{selectedAgents.length !== 1 ? 's' : ''}
            </p>
          </div>

          {/* Test Parameters */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="taskType">Task Type</Label>
              <Select value={taskType} onValueChange={(value: 'simple' | 'complex') => setTaskType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="simple">Simple Tasks</SelectItem>
                  <SelectItem value="complex">Complex Tasks</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration">Duration (seconds)</Label>
              <Input
                id="duration"
                type="number"
                value={duration}
                onChange={(e) => setDuration(Number(e.target.value))}
                min={5}
                max={300}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="concurrency">Concurrency</Label>
              <Input
                id="concurrency"
                type="number"
                value={concurrency}
                onChange={(e) => setConcurrency(Number(e.target.value))}
                min={1}
                max={systemSettings.maxConcurrentTasks}
              />
              <p className="text-xs text-muted-foreground">
                Max: {systemSettings.maxConcurrentTasks}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxTasks">Max Tasks (optional)</Label>
              <Input
                id="maxTasks"
                type="number"
                value={maxTasks || ''}
                onChange={(e) => setMaxTasks(e.target.value ? Number(e.target.value) : undefined)}
                min={1}
                max={1000}
                placeholder="Auto"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3">
            <Button
              onClick={handleRunTest}
              disabled={isRunning || selectedAgents.length === 0}
              className="flex items-center gap-2"
            >
              {isRunning ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Running Test...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  Run Stress Test
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={handleClearResults}
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Clear Results
            </Button>
          </div>

          {/* Progress Bar */}
          {isRunning && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Test Progress</span>
                <span>{progress.toFixed(0)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Live Output Console */}
      {(isRunning || liveOutput.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Terminal className="h-5 w-5" />
              Live Test Output
            </CardTitle>
            <CardDescription>
              Real-time console output from stress test execution
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-48 w-full rounded border bg-muted/30 p-4">
              <pre className="text-xs font-mono">
                {liveOutput.length === 0 ? (
                  <span className="text-muted-foreground">No output yet...</span>
                ) : (
                  liveOutput.join('\n')
                )}
              </pre>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Test Results Summary */}
      {currentResult && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Test Results Summary
            </CardTitle>
            <CardDescription>
              Performance metrics and analysis from the latest stress test
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
              {/* Tasks Executed */}
              <div className="text-center p-4 bg-accent/30 rounded">
                <div className="text-2xl font-bold">{currentResult.metrics.totalTasks}</div>
                <div className="text-xs text-muted-foreground">Tasks Executed</div>
              </div>

              {/* Average Latency */}
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded">
                <div className="text-2xl font-bold text-blue-600">
                  {currentResult.metrics.averageResponseTime.toFixed(0)}ms
                </div>
                <div className="text-xs text-muted-foreground">Average Latency</div>
              </div>

              {/* Failed Tasks */}
              <div className="text-center p-4 bg-red-50 dark:bg-red-950 rounded">
                <div className="text-2xl font-bold text-red-600">
                  {currentResult.metrics.failedTasks}
                </div>
                <div className="text-xs text-muted-foreground">Failed Tasks</div>
              </div>

              {/* Token Usage */}
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-950 rounded">
                <div className="text-2xl font-bold text-purple-600">
                  {formatNumber(currentResult.metrics.tokenUsage.total)}
                </div>
                <div className="text-xs text-muted-foreground">Token Usage</div>
              </div>

              {/* Throughput */}
              <div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded">
                <div className="text-2xl font-bold text-green-600">
                  {currentResult.metrics.throughput.toFixed(2)}
                </div>
                <div className="text-xs text-muted-foreground">Tasks/sec</div>
              </div>
            </div>

            <Separator className="my-4" />

            {/* Detailed Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Performance Details
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Success Rate:</span>
                    <span className={`font-medium ${((currentResult.metrics.successfulTasks / currentResult.metrics.totalTasks) * 100) >= 90 ? 'text-green-600' : 'text-yellow-600'}`}>
                      {((currentResult.metrics.successfulTasks / currentResult.metrics.totalTasks) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Min Response Time:</span>
                    <span className="font-medium">{currentResult.metrics.minResponseTime.toFixed(0)}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Max Response Time:</span>
                    <span className="font-medium">{currentResult.metrics.maxResponseTime.toFixed(0)}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Timeout Tasks:</span>
                    <span className="font-medium text-yellow-600">{currentResult.metrics.timeoutTasks}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  System Health
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>System Health Score:</span>
                    <span className="font-medium">{currentResult.systemMetrics.systemHealthScore.toFixed(1)}/100</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Concurrency Respected:</span>
                    <span className={`font-medium ${currentResult.systemMetrics.concurrencyLimitRespected ? 'text-green-600' : 'text-red-600'}`}>
                      {currentResult.systemMetrics.concurrencyLimitRespected ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Max Concurrent Used:</span>
                    <span className="font-medium">{currentResult.systemMetrics.maxConcurrentTasksUsed}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Test Duration:</span>
                    <span className="font-medium">{formatDuration(currentResult.duration)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Errors and Warnings */}
            {(currentResult.errors.length > 0 || currentResult.warnings.length > 0) && (
              <>
                <Separator className="my-4" />
                <div className="space-y-3">
                  {currentResult.errors.length > 0 && (
                    <div>
                      <h4 className="font-medium flex items-center gap-2 text-red-600">
                        <AlertCircle className="h-4 w-4" />
                        Errors ({currentResult.errors.length})
                      </h4>
                      <ScrollArea className="h-24 mt-2">
                        <div className="space-y-1">
                          {currentResult.errors.map((error, index) => (
                            <div key={index} className="text-xs p-2 bg-red-50 dark:bg-red-950 rounded">
                              <div className="font-medium">{error.type}: {error.message}</div>
                              {error.taskId && <div className="text-muted-foreground">Task: {error.taskId}</div>}
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </div>
                  )}

                  {currentResult.warnings.length > 0 && (
                    <div>
                      <h4 className="font-medium flex items-center gap-2 text-yellow-600">
                        <AlertTriangle className="h-4 w-4" />
                        Warnings ({currentResult.warnings.length})
                      </h4>
                      <div className="space-y-1 mt-2">
                        {currentResult.warnings.map((warning, index) => (
                          <div key={index} className="text-xs p-2 bg-yellow-50 dark:bg-yellow-950 rounded">
                            {warning}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}