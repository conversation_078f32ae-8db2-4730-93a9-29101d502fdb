"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON><PERSON>, Send, <PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

type Message = {
  id: number
  content: string
  sender: "user" | "ai"
  timestamp: Date
}

export default function AiChatPanel({ onClose }: { onClose: () => void }) {
  const [input, setInput] = useState("")
  const [activeTab, setActiveTab] = useState("chat")
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      content: "Hello! I'm your AI coding assistant. How can I help you today?",
      sender: "ai",
      timestamp: new Date(),
    },
  ])
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const handleSendMessage = () => {
    if (!input.trim()) return

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      content: input,
      sender: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")

    // Simulate AI response
    setTimeout(() => {
      const aiMessage: Message = {
        id: messages.length + 2,
        content: "I'm here to help with your coding questions. Could you provide more details about what you need?",
        sender: "ai",
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, aiMessage])
    }, 1000)
  }

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  return (
    <div className="flex flex-col h-full bg-background text-foreground">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-editor-border">
        <div className="flex items-center">
          <Sparkles className="h-4 w-4 text-editor-highlight mr-2" />
          <span className="font-medium text-sm">AI Assistant</span>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-muted-foreground hover:text-foreground"
          onClick={onClose}
        >
          <X className="h-3 w-3" />
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="bg-background border-b border-editor-border p-0 h-9 rounded-none">
          <TabsTrigger
            value="chat"
            className="flex-1 h-9 rounded-none data-[state=active]:bg-accent data-[state=active]:shadow-none"
          >
            Chat
          </TabsTrigger>
          <TabsTrigger
            value="context"
            className="flex-1 h-9 rounded-none data-[state=active]:bg-accent data-[state=active]:shadow-none"
          >
            Context
          </TabsTrigger>
          <TabsTrigger
            value="history"
            className="flex-1 h-9 rounded-none data-[state=active]:bg-accent data-[state=active]:shadow-none"
          >
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="flex-1 flex flex-col p-0 m-0">
          {/* Messages */}
          <ScrollArea className="flex-1 p-3">
            <div className="space-y-3">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "p-2 rounded-lg max-w-[85%]",
                    message.sender === "user"
                      ? "bg-editor-highlight text-editor-highlight-fg ml-auto"
                      : "bg-accent text-accent-foreground",
                  )}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                  </p>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Input */}
          <div className="p-3 border-t border-editor-border">
            <div className="flex items-center">
              <Input
                placeholder="Ask a question..."
                className="bg-background border-input text-sm"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSendMessage()
                  }
                }}
              />
              <Button
                size="icon"
                className="ml-2 bg-editor-highlight text-editor-highlight-fg hover:bg-editor-highlight/90"
                onClick={handleSendMessage}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="context" className="flex-1 p-3 m-0">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Active Files</h3>
              <div className="bg-accent p-2 rounded-md text-xs">
                <p className="text-muted-foreground">No active files</p>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">Project Structure</h3>
              <div className="bg-accent p-2 rounded-md text-xs">
                <p className="text-muted-foreground">No project structure available</p>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">Dependencies</h3>
              <div className="bg-accent p-2 rounded-md text-xs">
                <p className="text-muted-foreground">No dependencies found</p>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="history" className="flex-1 p-3 m-0">
          <div className="flex items-center justify-center h-full">
            <p className="text-muted-foreground">No chat history available</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
