"use client"

import { useRef, useEffect, useState } from "react"
import dynamic from "next/dynamic"
import { useTheme } from "next-themes"
import { FileSystemItem } from "./file-sidebar"
import { Button } from "@/components/ui/button"
import { Save, Undo, Redo, Search, Replace, Zap, AlertCircle, CheckCircle, Brain, Code2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useEditorActions } from "./editor/editor-action-provider"

// Dynamically import Monaco Editor to prevent SSR issues
const Editor = dynamic(() => import("@monaco-editor/react"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full">
      <div className="flex flex-col items-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
        <p className="text-sm text-muted-foreground">Loading editor...</p>
      </div>
    </div>
  ),
})

interface MonacoEditorProps {
  file: FileSystemItem | null
  onContentChange?: (content: string) => void
  readOnly?: boolean
}

// Simple fallback text editor component
const FallbackEditor = ({
  content,
  onChange,
  readOnly = false
}: {
  content: string
  onChange: (value: string) => void
  readOnly?: boolean
}) => {
  return (
    <Textarea
      value={content}
      onChange={(e) => onChange(e.target.value)}
      readOnly={readOnly}
      className="w-full h-full resize-none font-mono text-sm border-0 focus-visible:ring-0"
      placeholder="Type your code here..."
    />
  )
}

// Map file extensions to Monaco languages
const getMonacoLanguage = (file: FileSystemItem | null): string => {
  if (!file) return "plaintext";

  console.log("Getting language for file:", file.name, "type:", file.type);

  // Extract extension from file name or type
  let extension: string | undefined;

  if (file.name && file.name.includes('.')) {
    extension = file.name.split('.').pop()?.toLowerCase();
    console.log("Extracted extension from name:", extension);
  } else if (typeof file.type === 'string' && file.type !== 'folder' && file.type !== 'file') {
    extension = file.type.toLowerCase();
    console.log("Using type as extension:", extension);
  }

  if (!extension) {
    console.log("No extension found, using plaintext");
    return "plaintext";
  }

  // Map of file extensions to Monaco language identifiers
  const languageMap: Record<string, string> = {
    js: "javascript",
    jsx: "javascript",
    ts: "typescript",
    tsx: "typescript",
    html: "html",
    htm: "html",
    css: "css",
    scss: "scss",
    sass: "scss",
    less: "less",
    json: "json",
    xml: "xml",
    yaml: "yaml",
    yml: "yaml",
    md: "markdown",
    py: "python",
    java: "java",
    c: "c",
    cpp: "cpp",
    cs: "csharp",
    php: "php",
    rb: "ruby",
    go: "go",
    rs: "rust",
    swift: "swift",
    kt: "kotlin",
    dart: "dart",
    sql: "sql",
    sh: "shell",
    bash: "shell",
    ps1: "powershell",
    dockerfile: "dockerfile",
    vue: "vue",
    svelte: "svelte",
    graphql: "graphql",
    r: "r",
    scala: "scala",
    clj: "clojure",
    fs: "fsharp",
    vb: "vb",
    lua: "lua",
    perl: "perl",
    ini: "ini",
    toml: "toml",
    txt: "plaintext",
  }

  const language = languageMap[extension] || "plaintext";
  console.log("Selected language:", language);
  return language;
}

export default function MonacoEditor({ file, onContentChange, readOnly = false }: MonacoEditorProps) {
  const editorRef = useRef<any>(null)
  const { theme } = useTheme()
  const { toast } = useToast()
  const [content, setContent] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [editorError, setEditorError] = useState<string | null>(null)
  const [useMonaco, setUseMonaco] = useState(true)

  // Use shared action state
  const {
    actionState,
    updateFileState,
    updateEditorState,
    triggerSave,
    executeSave,
    triggerUndo,
    triggerRedo,
    triggerFormat,
    triggerSearch,
    triggerAIAssist,
    setFormattingState,
    setSearchState,
    updateAIState
  } = useEditorActions()

  // Monaco integration state
  const [editorId, setEditorId] = useState<string | null>(null)
  const [monacoIntegration, setMonacoIntegration] = useState<any>(null)
  const [syntaxAnalyzer, setSyntaxAnalyzer] = useState<any>(null)
  const [errorDetector, setErrorDetector] = useState<any>(null)
  const [codeCompletionEnhancer, setCodeCompletionEnhancer] = useState<any>(null)
  const [analysisResults, setAnalysisResults] = useState<any>(null)
  const [errorResults, setErrorResults] = useState<any>(null)
  const [enableAdvancedFeatures, setEnableAdvancedFeatures] = useState(false)

  // File sync state
  const [fileSyncInitialized, setFileSyncInitialized] = useState(false)
  const [isUpdatingFromSync, setIsUpdatingFromSync] = useState(false)
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const syncUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Update content when file changes
  useEffect(() => {
    console.log("File changed:", file);

    // Reset editor state when file changes
    setEditorError(null);

    if (!file) {
      console.log("No file selected");
      // Update action state for no file
      updateFileState(null, null, false);
      return;
    }

    if (file.content !== undefined) {
      console.log("File has content, length:", file.content.length);
      setContent(file.content);
      // Update action state for loaded file
      updateFileState(file.path || null, file.name, false);
    } else if (file.path && window.electronAPI) {
      // If file has path but no content, try to load it
      console.log("File has path but no content, loading from disk:", file.path);
      setIsLoading(true);

      window.electronAPI.readFile(file.path)
        .then(result => {
          if (result.success) {
            console.log("Successfully loaded file content from disk");
            setContent(result.content);
            // Update the file object with content
            file.content = result.content;
            // Update action state for loaded file
            updateFileState(file.path || null, file.name, false);
          } else {
            console.error("Failed to load file content:", result.error);
            setContent("");
            updateFileState(file.path || null, file.name, false);
            toast({
              title: "Error loading file",
              description: result.error || "Failed to load file content",
              variant: "destructive",
            });
          }
        })
        .catch(error => {
          console.error("Error reading file:", error);
          setContent("");
          updateFileState(file.path || null, file.name, false);
          toast({
            title: "Error loading file",
            description: "An unexpected error occurred while loading the file",
            variant: "destructive",
          });
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      // If file has no content and no path, set empty string
      console.log("File has no content and no path, setting empty string");
      setContent("");
      updateFileState(file.path || null, file.name, false);
    }
  }, [file, toast, updateFileState])

  // Initialize file sync service
  useEffect(() => {
    if (!fileSyncInitialized && file?.path) {
      const initializeFileSync = async () => {
        try {
          // Dynamic import to avoid SSR issues
          const { globalFileSyncService } = await import('@/components/background/file-sync-service');

          // Register file as opened
          await globalFileSyncService.openFile(file.path!, content, editorId || undefined);

          // Set up event handler for real-time sync
          const handleFileSyncEvent = (event: any) => {
            if (event.filePath === file.path && event.windowId !== globalFileSyncService['windowId']) {
              // Update content from other windows only if we don't have unsaved changes
              if (event.type === 'content_changed' && event.content !== undefined) {
                console.log('Received sync event from window:', event.windowId, 'content length:', event.content.length);

                // Check if we have unsaved changes before applying sync
                if (actionState.isDirty) {
                  console.log('Ignoring sync event - file has unsaved changes');
                  return;
                }

                // Update React state immediately
                setContent(event.content);

                // Apply debounced editor update to prevent rapid changes
                if (editorRef.current) {
                  debouncedApplySyncUpdate(editorRef.current, event.content);
                }
              }

              // Handle file saved events to reset dirty state
              if (event.type === 'file_saved') {
                console.log('File saved in other window, updating local state');
                if (file) {
                  file.content = event.content;
                  // The action state will be updated by the action state service
                }
              }
            }
          };

          globalFileSyncService.onFileSyncEvent(handleFileSyncEvent);
          setFileSyncInitialized(true);

          console.log('File sync initialized for:', file.path);

          // Cleanup function
          return () => {
            globalFileSyncService.offFileSyncEvent(handleFileSyncEvent);
            if (file?.path) {
              globalFileSyncService.closeFile(file.path, editorId || undefined);
            }
          };
        } catch (error) {
          console.error('Failed to initialize file sync:', error);
        }
      };

      initializeFileSync();
    }
  }, [file?.path, editorId, fileSyncInitialized]);

  // Apply sync update with cursor preservation
  const applySyncUpdate = (editor: any, incomingContent: string) => {
    try {
      // Get current editor content
      const currentContent = editor.getValue();

      // Only update if content has actually changed
      if (currentContent === incomingContent) {
        console.log('Sync content identical to current content, skipping update');
        setIsUpdatingFromSync(false);
        return;
      }

      // Check if we have unsaved changes - if so, don't overwrite
      if (actionState.isDirty) {
        console.log('Skipping sync update - file has unsaved changes');
        setIsUpdatingFromSync(false);
        return;
      }

      console.log('Applying sync update with cursor preservation');

      // Save current cursor position and selection
      const currentPosition = editor.getPosition();
      const currentSelection = editor.getSelection();

      // Use executeEdits to preserve undo/redo stack and cursor position
      const model = editor.getModel();
      if (model) {
        const fullRange = model.getFullModelRange();

        // Apply the edit operation
        editor.executeEdits('sync-update', [
          {
            range: fullRange,
            text: incomingContent,
            forceMoveMarkers: true
          }
        ]);

        // Restore cursor position if it's still valid
        if (currentPosition && currentSelection) {
          const newModel = editor.getModel();
          const lineCount = newModel.getLineCount();
          const lastLineLength = newModel.getLineLength(lineCount);

          // Ensure position is within bounds
          const validPosition = {
            lineNumber: Math.min(currentPosition.lineNumber, lineCount),
            column: currentPosition.lineNumber <= lineCount
              ? Math.min(currentPosition.column, newModel.getLineLength(currentPosition.lineNumber) + 1)
              : lastLineLength + 1
          };

          // Restore position
          editor.setPosition(validPosition);

          // If there was a selection, try to restore it
          if (currentSelection.startLineNumber !== currentSelection.endLineNumber ||
              currentSelection.startColumn !== currentSelection.endColumn) {
            const validSelection = {
              startLineNumber: Math.min(currentSelection.startLineNumber, lineCount),
              startColumn: currentSelection.startLineNumber <= lineCount
                ? Math.min(currentSelection.startColumn, newModel.getLineLength(currentSelection.startLineNumber) + 1)
                : lastLineLength + 1,
              endLineNumber: Math.min(currentSelection.endLineNumber, lineCount),
              endColumn: currentSelection.endLineNumber <= lineCount
                ? Math.min(currentSelection.endColumn, newModel.getLineLength(currentSelection.endLineNumber) + 1)
                : lastLineLength + 1
            };
            editor.setSelection(validSelection);
          }
        }
      }

      console.log('Sync update applied successfully');
    } catch (error) {
      console.error('Error applying sync update:', error);
      // Fallback to setValue if executeEdits fails
      editor.setValue(incomingContent);
    } finally {
      // Always reset the sync flag
      setTimeout(() => setIsUpdatingFromSync(false), 0);
    }
  };

  // Debounced version of applySyncUpdate to prevent rapid updates
  const debouncedApplySyncUpdate = (editor: any, incomingContent: string) => {
    // Clear existing timeout
    if (syncUpdateTimeoutRef.current) {
      clearTimeout(syncUpdateTimeoutRef.current);
    }

    // Set flag to prevent local changes from syncing
    setIsUpdatingFromSync(true);

    // Apply update after short delay
    syncUpdateTimeoutRef.current = setTimeout(() => {
      applySyncUpdate(editor, incomingContent);
    }, 50); // 50ms debounce for sync updates
  };

  const handleEditorWillMount = (monaco: any) => {
    console.log("Monaco beforeMount called")

    // Configure Monaco editor themes
    monaco.editor.defineTheme('custom-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#0f0f0f',
        'editor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editorLineNumber.activeForeground': '#c6c6c6',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41',
      }
    })

    monaco.editor.defineTheme('custom-light', {
      base: 'vs',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#ffffff',
        'editor.foreground': '#000000',
        'editorLineNumber.foreground': '#237893',
        'editorLineNumber.activeForeground': '#0b216f',
        'editor.selectionBackground': '#add6ff',
        'editor.inactiveSelectionBackground': '#e5ebf1',
      }
    })
  }

  const handleEditorDidMount = (editor: any, monaco: any) => {
    console.log("Monaco editor mounted successfully")
    editorRef.current = editor
    setEditorError(null)

    // Set theme based on current theme
    const currentTheme = theme === 'dark' ? 'custom-dark' : 'custom-light'
    monaco.editor.setTheme(currentTheme)

    // Initialize Monaco integration if not already done
    initializeMonacoIntegration(editor, monaco)

    // Configure editor options
    editor.updateOptions({
      fontSize: 14,
      fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
      lineNumbers: 'on',
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      tabSize: 2,
      insertSpaces: true,
      wordWrap: 'on',
      bracketPairColorization: { enabled: true },
      guides: {
        bracketPairs: true,
        indentation: true,
      },
      suggest: {
        showKeywords: true,
        showSnippets: true,
      },
      quickSuggestions: {
        other: true,
        comments: true,
        strings: true,
      },
    })

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave()
    })

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
      editor.getAction('actions.find').run()
    })

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyH, () => {
      editor.getAction('editor.action.startFindReplaceAction').run()
    })
  }

  // Initialize Monaco integration with advanced features
  const initializeMonacoIntegration = async (editor: any, monaco: any) => {
    try {
      // Only initialize in browser environment
      if (typeof window === 'undefined') {
        return;
      }

      console.log("Initializing Monaco integration...");

      // Dynamic imports to avoid SSR issues
      const { getMonacoIntegration } = await import('@/components/background/monaco-integration');
      const { getSyntaxAnalyzer } = await import('@/components/background/syntax-analyzer');
      const { getErrorDetector } = await import('@/components/background/error-detector');
      const { getCodeCompletionEnhancer } = await import('@/components/background/code-completion-enhancer');
      const { getBasicVectorDatabase } = await import('@/components/background/vector-database');

      // Initialize vector database
      const vectorDb = getBasicVectorDatabase();
      await vectorDb.initialize();

      // Initialize Monaco integration
      const integration = getMonacoIntegration();
      const analyzer = getSyntaxAnalyzer(vectorDb);
      const detector = getErrorDetector(vectorDb);
      const enhancer = getCodeCompletionEnhancer(vectorDb);

      // Register the editor
      const language = getMonacoLanguage(file);
      const newEditorId = integration.registerEditor(
        editor,
        file?.path || file?.name || 'untitled',
        language,
        content
      );

      // Register completion provider
      enhancer.registerCompletionProvider(language, newEditorId);

      // Set up event listeners for analysis updates
      integration.onEditorEvent((event: any) => {
        if (event.editorId === newEditorId) {
          // Update analysis results when content changes
          if (event.type === 'content_changed') {
            updateAnalysisResults(newEditorId, analyzer, detector);
          }
        }
      });

      // Store references
      setEditorId(newEditorId);
      setMonacoIntegration(integration);
      setSyntaxAnalyzer(analyzer);
      setErrorDetector(detector);
      setCodeCompletionEnhancer(enhancer);
      setEnableAdvancedFeatures(true);

      // Initial analysis
      await updateAnalysisResults(newEditorId, analyzer, detector);

      console.log("Monaco integration initialized successfully");

      toast({
        title: "Advanced Features Enabled",
        description: "AI-powered code analysis and completion are now active",
      });

    } catch (error) {
      console.error("Error initializing Monaco integration:", error);
      setEnableAdvancedFeatures(false);

      toast({
        title: "Advanced Features Unavailable",
        description: "Basic editor functionality is still available",
        variant: "default",
      });
    }
  };

  // Update analysis results
  const updateAnalysisResults = async (editorId: string, analyzer: any, detector: any) => {
    try {
      // Run syntax analysis
      const syntaxResult = await analyzer.analyzeEditor(editorId);
      setAnalysisResults(syntaxResult);

      // Run error detection
      const errorResult = await detector.detectErrors(editorId);
      setErrorResults(errorResult);

    } catch (error) {
      console.error("Error updating analysis results:", error);
    }
  };

  // Toggle advanced features
  const toggleAdvancedFeatures = () => {
    const newState = !enableAdvancedFeatures;
    setEnableAdvancedFeatures(newState);

    // Update shared action state
    updateAIState(newState, actionState.aiSuggestionsCount);
    triggerAIAssist();

    if (newState && editorRef.current) {
      // Re-initialize if enabling
      const monaco = (window as any).monaco;
      if (monaco) {
        initializeMonacoIntegration(editorRef.current, monaco);
      }
    }
  };

  // Apply error suggestion
  const applyErrorSuggestion = async (suggestionId: string) => {
    if (!errorDetector || !editorId) return;

    try {
      const success = await errorDetector.applySuggestion(editorId, suggestionId);
      if (success) {
        toast({
          title: "Fix Applied",
          description: "Error suggestion has been applied successfully",
        });
      }
    } catch (error) {
      console.error("Error applying suggestion:", error);
      toast({
        title: "Fix Failed",
        description: "Could not apply the suggested fix",
        variant: "destructive",
      });
    }
  };

  const handleMonacoError = (error: any) => {
    console.error("Monaco Editor initialization failed:", error)

    // Try to get more detailed error information
    let errorMessage = "Monaco Editor failed to load. Using fallback text editor.";
    if (error && error.message) {
      errorMessage += ` Error: ${error.message}`;
      console.error("Detailed error:", error.message);
    }

    // Check if it's a CSP error
    if (error && error.message && error.message.includes("Content Security Policy")) {
      errorMessage = "Monaco Editor failed to load due to Content Security Policy restrictions. Using fallback text editor.";
      console.error("CSP Error detected. Check your Electron CSP settings.");
    }

    setEditorError(errorMessage);
    setUseMonaco(false);

    // Monaco loader reinitialization is handled by the dynamic import

    toast({
      title: "Editor Notice",
      description: "Advanced editor features unavailable. Using basic text editor.",
      variant: "default",
    })
  }

  const handleContentChange = async (value: string | undefined) => {
    // Skip if this change is from sync to prevent infinite loops
    if (isUpdatingFromSync) {
      console.log('Skipping content change - updating from sync');
      return;
    }

    const newContent = value || ""
    console.log('Content changed by user:', newContent.length, 'characters');
    setContent(newContent)

    // Calculate dirty state based on comparison with original file content
    const isDirty = newContent !== (file?.content || "")
    onContentChange?.(newContent)

    // Only update dirty state if it's actually changing to prevent unnecessary updates
    if (file && (actionState.isDirty !== isDirty)) {
      console.log('Updating dirty state:', isDirty);
      updateFileState(file.path || null, file.name, isDirty);
    }

    // Update editor state if Monaco editor is available
    if (editorRef.current) {
      const editor = editorRef.current;
      const model = editor.getModel();
      if (model) {
        const canUndo = model.canUndo();
        const canRedo = model.canRedo();
        const selection = editor.getSelection();
        const hasSelection = selection && !selection.isEmpty();
        const position = editor.getPosition();

        updateEditorState(
          canUndo,
          canRedo,
          hasSelection,
          position || { line: 1, column: 1 },
          hasSelection ? selection : undefined
        );
      }
    }

    // Debounce file sync to prevent rapid updates
    if (file?.path && fileSyncInitialized) {
      // Clear existing timeout
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }

      // Set new timeout for sync
      syncTimeoutRef.current = setTimeout(async () => {
        try {
          const { globalFileSyncService } = await import('@/components/background/file-sync-service');
          await globalFileSyncService.updateFileContent(file.path!, newContent, editorId || undefined);
        } catch (error) {
          console.error('Failed to sync content change:', error);
        }
      }, 300); // 300ms debounce
    }
  }

  const handleSave = async () => {
    if (!file?.path || !actionState.isDirty) return

    setIsLoading(true)
    try {
      // Use the shared executeSave method for consistent behavior
      const success = await executeSave(file.path, content);

      if (success) {
        // Update file object to reflect saved content
        file.content = content;

        // Notify file sync service of save
        if (fileSyncInitialized) {
          try {
            const { globalFileSyncService } = await import('@/components/background/file-sync-service');
            await globalFileSyncService.saveFile(file.path, content, editorId || undefined);
          } catch (error) {
            console.error('Failed to sync file save:', error);
          }
        }

        console.log('File saved successfully, content updated:', file.path);
        toast({
          title: "File saved",
          description: `${file.name} has been saved successfully.`,
        })
      } else {
        toast({
          title: "Save failed",
          description: "Failed to save file",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error saving file:', error)
      toast({
        title: "Save failed",
        description: "An unexpected error occurred while saving the file",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleUndo = () => {
    editorRef.current?.trigger('keyboard', 'undo', null)
    triggerUndo()
  }

  const handleRedo = () => {
    editorRef.current?.trigger('keyboard', 'redo', null)
    triggerRedo()
  }

  const handleFind = () => {
    editorRef.current?.getAction('actions.find').run()
    setSearchState(true)
    triggerSearch()
  }

  const handleReplace = () => {
    editorRef.current?.getAction('editor.action.startFindReplaceAction').run()
    setSearchState(true)
    triggerSearch()
  }

  // Update theme when it changes
  useEffect(() => {
    if (editorRef.current) {
      const monaco = (window as any).monaco
      if (monaco) {
        const currentTheme = theme === 'dark' ? 'custom-dark' : 'custom-light'
        monaco.editor.setTheme(currentTheme)
      }
    }
  }, [theme])

  // Listen for action events from other windows
  useEffect(() => {
    const handleActionEvent = async (event: any) => {
      if (event.type === 'action_triggered' && event.actionType === 'save' && !event.actionData?.success) {
        // Save action triggered from another window - execute save if we have the file
        if (file?.path && actionState.isDirty && content) {
          console.log('Executing save triggered from other window');
          await handleSave();
        }
      }
    };

    // Set up action event listener through the action state service
    const setupActionListener = async () => {
      try {
        const { globalEditorActionStateService } = await import('./editor/editor-action-state');
        globalEditorActionStateService.onActionStateEvent(handleActionEvent);

        return () => {
          globalEditorActionStateService.offActionStateEvent(handleActionEvent);
        };
      } catch (error) {
        console.error('Failed to set up action event listener:', error);
      }
    };

    let cleanup: (() => void) | undefined;
    setupActionListener().then(cleanupFn => {
      cleanup = cleanupFn;
    });

    return () => {
      if (cleanup) cleanup();
    };
  }, [file?.path, actionState.canSave, content]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
      if (syncUpdateTimeoutRef.current) {
        clearTimeout(syncUpdateTimeoutRef.current);
      }
    };
  }, [])

  if (!file) {
    return (
      <div className="h-full flex items-center justify-center text-muted-foreground">
        <div className="text-center">
          <p>No file selected</p>
          <p className="text-sm mt-2">Select a file from the sidebar to start editing</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Editor toolbar */}
      <div className="flex items-center justify-between p-2 border-b border-border bg-background">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{file.name}</span>
          {actionState.isDirty && (
            <span className="text-xs text-muted-foreground">• Modified</span>
          )}
          {actionState.aiContextEnabled && actionState.aiAssistantAvailable && (
            <Badge variant="secondary" className="text-xs">
              <Brain className="h-3 w-3 mr-1" />
              AI Enhanced
            </Badge>
          )}
          {analysisResults && (
            <Badge variant="outline" className="text-xs">
              <Code2 className="h-3 w-3 mr-1" />
              Complexity: {analysisResults.complexity}
            </Badge>
          )}
          {errorResults && errorResults.errors.length > 0 && (
            <Badge variant="destructive" className="text-xs">
              <AlertCircle className="h-3 w-3 mr-1" />
              {errorResults.errors.length} errors
            </Badge>
          )}
          {errorResults && errorResults.warnings.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              <AlertCircle className="h-3 w-3 mr-1" />
              {errorResults.warnings.length} warnings
            </Badge>
          )}
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleUndo}
            disabled={!actionState.canUndo}
            title="Undo (Ctrl+Z)"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRedo}
            disabled={!actionState.canRedo}
            title="Redo (Ctrl+Y)"
          >
            <Redo className="h-4 w-4" />
          </Button>
          <Button
            variant={actionState.isSearchOpen ? "default" : "ghost"}
            size="sm"
            onClick={handleFind}
            title="Find (Ctrl+F)"
          >
            <Search className="h-4 w-4" />
          </Button>
          <Button
            variant={actionState.isSearchOpen ? "default" : "ghost"}
            size="sm"
            onClick={handleReplace}
            title="Replace (Ctrl+H)"
          >
            <Replace className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSave}
            disabled={!actionState.isDirty || !file?.path || isLoading || readOnly}
            title="Save (Ctrl+S)"
          >
            <Save className="h-4 w-4" />
          </Button>
          {actionState.aiAssistantAvailable && (
            <Button
              variant={actionState.aiContextEnabled ? "default" : "ghost"}
              size="sm"
              onClick={toggleAdvancedFeatures}
              title={actionState.aiContextEnabled ? "Disable AI Features" : "Enable AI Features"}
            >
              <Zap className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
              <p className="text-sm text-muted-foreground">Loading file content...</p>
            </div>
          </div>
        ) : editorError && !useMonaco ? (
          <div className="h-full">
            <FallbackEditor
              content={content}
              onChange={handleContentChange}
              readOnly={readOnly}
            />
          </div>
        ) : (
          <Editor
            height="100%"
            language={getMonacoLanguage(file)}
            value={content}
            onChange={handleContentChange}
            beforeMount={handleEditorWillMount}
            onMount={handleEditorDidMount}
            theme={theme === 'dark' ? 'custom-dark' : 'custom-light'}
            options={{
              readOnly: readOnly || isLoading,
              automaticLayout: true,
              minimap: { enabled: true },
              scrollBeyondLastLine: false,
              fontSize: 14,
              fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
              // Disable features that might cause issues
              quickSuggestions: false,
              parameterHints: { enabled: false },
              suggestOnTriggerCharacters: false,
              acceptSuggestionOnEnter: "off",
              tabCompletion: "off",
              wordBasedSuggestions: "off",
              // Improve performance
              renderWhitespace: "none",
              renderControlCharacters: false,
              renderIndentGuides: false,
              renderLineHighlight: "none",
              folding: false,
            }}
            loading={
              <div className="flex items-center justify-center h-full">
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
                  <p className="text-sm text-muted-foreground">Initializing editor...</p>
                </div>
              </div>
            }
            onError={handleMonacoError}
          />
        )}
      </div>
    </div>
  )
}