// components/agents/openai-model-selector.tsx
import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getOpenAIModelMetadata, getOpenAIModelSeries, hasOpenAIModelMetadata } from './openai-models';
import { ModelRegistryService } from './model-registry-service';
import { PricingDisplay } from './pricing-display';

interface OpenAIModelSelectorProps {
  value: string;
  onChange: (value: string) => void;
  apiKey: string;
  availableModels: string[];
  isLoadingModels: boolean;
  onRefreshModels: () => void;
  showCustomInput?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export const OpenAIModelSelector: React.FC<OpenAIModelSelectorProps> = ({
  value,
  onChange,
  apiKey,
  availableModels,
  isLoadingModels,
  onRefreshModels,
  showCustomInput = true,
  disabled = false,
  placeholder = "Select an OpenAI model"
}) => {
  const [showCustom, setShowCustom] = useState(false);
  const [customModel, setCustomModel] = useState('');

  const selectedModelMetadata = getOpenAIModelMetadata(value);

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === 'custom') {
      setShowCustom(true);
    } else {
      setShowCustom(false);
      onChange(selectedValue);
    }
  };

  const handleCustomSubmit = () => {
    if (customModel.trim()) {
      onChange(customModel.trim());
      setShowCustom(false);
      setCustomModel('');
    }
  };

  const handleCustomCancel = () => {
    setShowCustom(false);
    setCustomModel('');
  };

  const getModelBadgeVariant = (modelId: string) => {
    if (modelId.startsWith('gpt-4o')) {
      return 'default'; // GPT-4o series
    }
    if (modelId.startsWith('gpt-4')) {
      return 'secondary'; // GPT-4 series
    }
    if (modelId.startsWith('gpt-3.5')) {
      return 'outline'; // GPT-3.5 series
    }
    return 'secondary'; // Other models
  };

  // Group models by series for better organization
  const groupedModels = {
    'gpt-4o': availableModels.filter(m => m.startsWith('gpt-4o')),
    'gpt-4': availableModels.filter(m => m.startsWith('gpt-4') && !m.startsWith('gpt-4o')),
    'gpt-3.5': availableModels.filter(m => m.startsWith('gpt-3.5')),
    'other': availableModels.filter(m => !m.startsWith('gpt-4') && !m.startsWith('gpt-3.5'))
  };

  const isElectronAPIAvailable = () => {
    return typeof window !== 'undefined' && window.electronAPI?.llm?.fetchModels !== undefined;
  };

  // ✅ Step 6: UI feedback for fallback detection
  const isFallbackActive = availableModels.length <= 5 && !isLoadingModels;
  const isElectronAvailable = isElectronAPIAvailable();

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <Label>Model</Label>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onRefreshModels}
            disabled={isLoadingModels || !apiKey || !isElectronAPIAvailable()}
            className="h-6 px-2 text-xs"
          >
            {isLoadingModels ? (
              <div className="animate-spin h-3 w-3 border border-gray-300 border-t-blue-600 rounded-full" />
            ) : (
              'Refresh'
            )}
          </Button>
          {!isElectronAvailable && (
            <span className="text-xs text-orange-600 dark:text-orange-400">
              Electron required
            </span>
          )}
          {isFallbackActive && (
            <span className="text-xs text-yellow-600 dark:text-yellow-400">
              ⚠️ Limited models (fallback)
            </span>
          )}
        </div>
      </div>

      {/* Custom Model Input */}
      {showCustom ? (
        <div className="space-y-2">
          <Input
            value={customModel}
            onChange={(e) => setCustomModel(e.target.value)}
            placeholder="Enter custom OpenAI model ID"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleCustomSubmit();
              } else if (e.key === 'Escape') {
                handleCustomCancel();
              }
            }}
            autoFocus
          />
          <div className="flex gap-2">
            <Button size="sm" onClick={handleCustomSubmit} disabled={!customModel.trim()}>
              Use Model
            </Button>
            <Button size="sm" variant="outline" onClick={handleCustomCancel}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        /* Model Selection Dropdown */
        <Select
          value={value}
          onValueChange={handleSelectChange}
          disabled={disabled || isLoadingModels}
        >
          <SelectTrigger>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {/* GPT-4o Series */}
            {groupedModels['gpt-4o'].length > 0 && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b">
                  GPT-4o Series (Latest)
                </div>
                {groupedModels['gpt-4o'].map((modelId) => (
                  <SelectItem key={modelId} value={modelId}>
                    <div className="flex items-center gap-2">
                      <span>{getOpenAIModelMetadata(modelId)?.label || modelId}</span>
                      {hasOpenAIModelMetadata(modelId) ? (
                        <Badge variant="default" className="text-xs">Verified</Badge>
                      ) : (
                        <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">Not verified</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </>
            )}

            {/* GPT-4 Series */}
            {groupedModels['gpt-4'].length > 0 && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                  GPT-4 Series
                </div>
                {groupedModels['gpt-4'].map((modelId) => (
                  <SelectItem key={modelId} value={modelId}>
                    <div className="flex items-center gap-2">
                      <span>{getOpenAIModelMetadata(modelId)?.label || modelId}</span>
                      {hasOpenAIModelMetadata(modelId) ? (
                        <Badge variant="secondary" className="text-xs">Verified</Badge>
                      ) : (
                        <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">Not verified</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </>
            )}

            {/* GPT-3.5 Series */}
            {groupedModels['gpt-3.5'].length > 0 && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                  GPT-3.5 Series
                </div>
                {groupedModels['gpt-3.5'].map((modelId) => (
                  <SelectItem key={modelId} value={modelId}>
                    <div className="flex items-center gap-2">
                      <span>{getOpenAIModelMetadata(modelId)?.label || modelId}</span>
                      {hasOpenAIModelMetadata(modelId) ? (
                        <Badge variant="outline" className="text-xs">Verified</Badge>
                      ) : (
                        <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">Not verified</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </>
            )}

            {/* Other Models */}
            {groupedModels['other'].length > 0 && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                  Other Models
                </div>
                {groupedModels['other'].map((modelId) => (
                  <SelectItem key={modelId} value={modelId}>
                    <div className="flex items-center gap-2">
                      <span>{getOpenAIModelMetadata(modelId)?.label || modelId}</span>
                      {hasOpenAIModelMetadata(modelId) ? (
                        <Badge variant="outline" className="text-xs">Verified</Badge>
                      ) : (
                        <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">Not verified</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </>
            )}

            {/* Custom Model Option */}
            {showCustomInput && (
              <>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b mt-2">
                  Custom Model
                </div>
                <SelectItem value="custom">
                  <div className="flex items-center gap-2">
                    <span>Custom Model ID...</span>
                    <Badge variant="outline" className="text-xs">Manual</Badge>
                  </div>
                </SelectItem>
              </>
            )}
          </SelectContent>
        </Select>
      )}

      {/* ✅ Transparent Error Logging for Fallback Detection */}
      {isFallbackActive && (
        <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
          <div className="flex items-start gap-2">
            <span className="text-yellow-600 dark:text-yellow-400 text-sm">⚠️</span>
            <div className="text-sm text-yellow-800 dark:text-yellow-200">
              <p className="font-medium">Limited OpenAI models shown (fallback mode)</p>
              <p className="text-xs mt-1">
                Showing only {availableModels.length} static models instead of 68+ from OpenAI API.
                {!isElectronAvailable
                  ? ' ⚠️ Electron API unavailable – falling back to static OpenAI models'
                  : ' Check API key and network connectivity.'
                }
              </p>
              {!isElectronAvailable && (
                <p className="text-xs mt-1 text-yellow-700 dark:text-yellow-300">
                  Running in web browser mode. For full model access, use the Electron desktop application.
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Model Metadata Display */}
      {selectedModelMetadata && !showCustom && (
        <div className="p-3 bg-muted rounded-lg space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant={getModelBadgeVariant(selectedModelMetadata.id)}>
              {getOpenAIModelSeries(selectedModelMetadata.id)}
            </Badge>
            <span className="text-sm font-medium">{selectedModelMetadata.label}</span>
          </div>

          {selectedModelMetadata.description && (
            <p className="text-sm text-muted-foreground">
              {selectedModelMetadata.description}
            </p>
          )}

          <div className="space-y-2">
            {selectedModelMetadata.contextSize && (
              <div className="text-xs">
                <span className="font-medium">Context:</span> {selectedModelMetadata.contextSize.toLocaleString()} tokens
              </div>
            )}
            <PricingDisplay pricing={selectedModelMetadata.pricing} />
          </div>

          {selectedModelMetadata.tags && selectedModelMetadata.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {selectedModelMetadata.tags.map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Unverified Model Info */}
      {!selectedModelMetadata && value && !showCustom && (
        <div className="p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-orange-600 border-orange-300">Not verified</Badge>
            <span className="text-sm font-medium">{value}</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            ... and thus no metadata
          </p>
          <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
            This model is available via API but lacks verified specifications. Pricing and capabilities may vary.
          </p>
        </div>
      )}
    </div>
  );
};

export default OpenAIModelSelector;
