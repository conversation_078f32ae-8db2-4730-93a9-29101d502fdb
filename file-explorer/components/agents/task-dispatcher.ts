// components/agents/task-dispatcher.ts
import { AgentSubtask, Agent<PERSON><PERSON> } from './task-orchestrator';
import { AgentB<PERSON>, AgentContext, AgentResponse } from './agent-base';
import { CompleteAgentManager } from './agent-manager-complete';

export interface TaskDispatchResult {
  taskId: string;
  agentId: string;
  status: 'dispatched' | 'failed' | 'agent_not_found' | 'invalid_task';
  message: string;
  timestamp: number;
}

export interface TaskExecutionCallback {
  onTaskStart?: (taskId: string, agentId: string) => void;
  onTaskProgress?: (taskId: string, agentId: string, progress: number) => void;
  onTaskComplete?: (taskId: string, agentId: string, result: AgentResponse) => void;
  onTaskError?: (taskId: string, agentId: string, error: string) => void;
}

export class TaskDispatcher {
  private static instance: TaskDispatcher;
  private agentManager: CompleteAgentManager;
  private callbacks: TaskExecutionCallback = {};
  private dispatchHistory: Map<string, TaskDispatchResult> = new Map();
  private activeDispatches: Map<string, { taskId: string; agentId: string; startTime: number }> = new Map();

  constructor(agentManager: CompleteAgentManager) {
    this.agentManager = agentManager;
  }

  public static getInstance(agentManager: CompleteAgentManager): TaskDispatcher {
    if (!TaskDispatcher.instance) {
      TaskDispatcher.instance = new TaskDispatcher(agentManager);
    }
    return TaskDispatcher.instance;
  }

  /**
   * Set callbacks for task execution events
   */
  public setCallbacks(callbacks: TaskExecutionCallback): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Dispatch a subtask to the appropriate agent for execution
   */
  public async dispatch(subtask: AgentSubtask): Promise<TaskDispatchResult> {
    const timestamp = Date.now();
    
    console.log(`TaskDispatcher: Dispatching task ${subtask.id} to agent ${subtask.agent}`);

    try {
      // Validate the subtask
      const validation = this.validateSubtask(subtask);
      if (!validation.valid) {
        const result: TaskDispatchResult = {
          taskId: subtask.id,
          agentId: subtask.agent,
          status: 'invalid_task',
          message: validation.error || 'Invalid task structure',
          timestamp
        };
        this.dispatchHistory.set(subtask.id, result);
        return result;
      }

      // Check if agent exists and is available
      const agent = this.agentManager.getAgent(subtask.agent);
      if (!agent) {
        const result: TaskDispatchResult = {
          taskId: subtask.id,
          agentId: subtask.agent,
          status: 'agent_not_found',
          message: `Agent '${subtask.agent}' not found in registry`,
          timestamp
        };
        this.dispatchHistory.set(subtask.id, result);
        return result;
      }

      // Convert AgentSubtask to AgentContext for execution
      const context: AgentContext = {
        task: subtask.description,
        files: subtask.context.files,
        codeContext: subtask.context.codeContext,
        rules: subtask.context.rules,
        dependencies: subtask.dependencies,
        metadata: {
          ...subtask.context.metadata,
          subtaskId: subtask.id,
          parentTaskId: subtask.linkedTo,
          priority: subtask.priority,
          estimatedTokens: subtask.estimatedTokens,
          requiredCapabilities: subtask.requiredCapabilities,
          taskPhase: subtask.metadata?.phase,
          originalSubtask: subtask
        }
      };

      // Track active dispatch
      this.activeDispatches.set(subtask.id, {
        taskId: subtask.id,
        agentId: subtask.agent,
        startTime: timestamp
      });

      // Notify task start
      if (this.callbacks.onTaskStart) {
        this.callbacks.onTaskStart(subtask.id, subtask.agent);
      }

      // Dispatch to agent manager for execution
      const executionTaskId = await this.agentManager.assignTask(
        subtask.agent,
        context,
        subtask.priority,
        3, // maxRetries
        subtask.metadata?.kanbanCardId
      );

      console.log(`TaskDispatcher: Task ${subtask.id} assigned to agent ${subtask.agent} with execution ID ${executionTaskId}`);

      const result: TaskDispatchResult = {
        taskId: subtask.id,
        agentId: subtask.agent,
        status: 'dispatched',
        message: `Task successfully dispatched to ${subtask.agent} (execution ID: ${executionTaskId})`,
        timestamp
      };

      this.dispatchHistory.set(subtask.id, result);
      return result;

    } catch (error) {
      console.error(`TaskDispatcher: Failed to dispatch task ${subtask.id}:`, error);
      
      const result: TaskDispatchResult = {
        taskId: subtask.id,
        agentId: subtask.agent,
        status: 'failed',
        message: `Dispatch failed: ${error instanceof Error ? error.message : String(error)}`,
        timestamp
      };

      this.dispatchHistory.set(subtask.id, result);
      
      // Notify error
      if (this.callbacks.onTaskError) {
        this.callbacks.onTaskError(subtask.id, subtask.agent, result.message);
      }

      return result;
    }
  }

  /**
   * Dispatch multiple subtasks in batch
   */
  public async dispatchBatch(subtasks: AgentSubtask[]): Promise<TaskDispatchResult[]> {
    console.log(`TaskDispatcher: Dispatching batch of ${subtasks.length} tasks`);
    
    const results: TaskDispatchResult[] = [];
    
    // Dispatch tasks in dependency order
    const sortedTasks = this.sortTasksByDependencies(subtasks);
    
    for (const subtask of sortedTasks) {
      const result = await this.dispatch(subtask);
      results.push(result);
      
      // Small delay between dispatches to prevent overwhelming
      if (subtasks.length > 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    console.log(`TaskDispatcher: Batch dispatch complete. ${results.filter(r => r.status === 'dispatched').length}/${results.length} successful`);
    return results;
  }

  /**
   * Get dispatch status for a task
   */
  public getDispatchStatus(taskId: string): TaskDispatchResult | null {
    return this.dispatchHistory.get(taskId) || null;
  }

  /**
   * Get all active dispatches
   */
  public getActiveDispatches(): Array<{ taskId: string; agentId: string; startTime: number; duration: number }> {
    const now = Date.now();
    return Array.from(this.activeDispatches.values()).map(dispatch => ({
      ...dispatch,
      duration: now - dispatch.startTime
    }));
  }

  /**
   * Get dispatch history
   */
  public getDispatchHistory(): TaskDispatchResult[] {
    return Array.from(this.dispatchHistory.values()).sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Handle task completion notification from agent manager
   */
  public notifyTaskComplete(taskId: string, agentId: string, result: AgentResponse): void {
    console.log(`TaskDispatcher: Task ${taskId} completed by agent ${agentId}`);
    
    // Remove from active dispatches
    this.activeDispatches.delete(taskId);
    
    // Notify completion callback
    if (this.callbacks.onTaskComplete) {
      this.callbacks.onTaskComplete(taskId, agentId, result);
    }
  }

  /**
   * Handle task error notification from agent manager
   */
  public notifyTaskError(taskId: string, agentId: string, error: string): void {
    console.log(`TaskDispatcher: Task ${taskId} failed on agent ${agentId}: ${error}`);
    
    // Remove from active dispatches
    this.activeDispatches.delete(taskId);
    
    // Notify error callback
    if (this.callbacks.onTaskError) {
      this.callbacks.onTaskError(taskId, agentId, error);
    }
  }

  /**
   * Validate subtask structure
   */
  private validateSubtask(subtask: AgentSubtask): { valid: boolean; error?: string } {
    if (!subtask.id) {
      return { valid: false, error: 'Task ID is required' };
    }
    
    if (!subtask.agent) {
      return { valid: false, error: 'Agent assignment is required' };
    }
    
    if (!subtask.title && !subtask.description) {
      return { valid: false, error: 'Task title or description is required' };
    }
    
    if (!subtask.context || !subtask.context.task) {
      return { valid: false, error: 'Task context is required' };
    }
    
    return { valid: true };
  }

  /**
   * Sort tasks by dependencies to ensure proper execution order
   */
  private sortTasksByDependencies(subtasks: AgentSubtask[]): AgentSubtask[] {
    const sorted: AgentSubtask[] = [];
    const remaining = new Set(subtasks);
    const completed = new Set<string>();

    while (remaining.size > 0) {
      const ready = Array.from(remaining).filter(task => 
        !task.dependencies || task.dependencies.every(dep => completed.has(dep))
      );

      if (ready.length === 0) {
        // No more tasks can be processed (circular dependency or missing dependency)
        // Add remaining tasks anyway to prevent infinite loop
        sorted.push(...Array.from(remaining));
        break;
      }

      // Add ready tasks to sorted list
      ready.forEach(task => {
        sorted.push(task);
        remaining.delete(task);
        completed.add(task.id);
      });
    }

    return sorted;
  }

  /**
   * Get dispatch statistics
   */
  public getDispatchStats(): {
    total: number;
    successful: number;
    failed: number;
    active: number;
    averageDispatchTime: number;
  } {
    const history = Array.from(this.dispatchHistory.values());
    const successful = history.filter(h => h.status === 'dispatched').length;
    const failed = history.filter(h => h.status === 'failed' || h.status === 'agent_not_found' || h.status === 'invalid_task').length;
    const active = this.activeDispatches.size;
    
    // Calculate average dispatch time for completed tasks
    const completedDispatches = Array.from(this.activeDispatches.values());
    const averageDispatchTime = completedDispatches.length > 0 
      ? completedDispatches.reduce((sum, d) => sum + (Date.now() - d.startTime), 0) / completedDispatches.length
      : 0;

    return {
      total: history.length,
      successful,
      failed,
      active,
      averageDispatchTime
    };
  }
}
