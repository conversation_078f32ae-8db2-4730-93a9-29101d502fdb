// components/agents/unified-model-service.ts
import { <PERSON><PERSON><PERSON><PERSON>, LLMProviderRegistry } from './llm-provider-registry';
import { OPENAI_MODEL_METADATA, OpenAIModelMetadata } from './openai-models';
import { ANTHROPIC_MODELS, AnthropicModel } from './anthropic-models';
import { OPENROUTER_MODEL_METADATA, OpenRouterModelMetadata } from './openrouter-models';
import { DEEPSEEK_MODEL_METADATA, DeepSeekModelMetadata } from './deepseek-models';
import { FIREWORKS_MODEL_METADATA, FireworksModelMetadata } from './fireworks-models';
import { GOOGLE_MODEL_METADATA, GoogleModelMetadata } from './google-models';

/**
 * Unified model metadata interface that combines all provider-specific interfaces
 */
export interface UnifiedModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input: number;  // per 1K tokens
    output: number; // per 1K tokens
  };
  tags?: string[];
  provider: LLMProvider;
  providerName: string;
  releaseDate?: string;
  originalProvider?: string; // For OpenRouter models that proxy other providers
}

/**
 * Service to aggregate and provide unified access to all model metadata
 */
export class UnifiedModelService {
  private static instance: UnifiedModelService;

  public static getInstance(): UnifiedModelService {
    if (!UnifiedModelService.instance) {
      UnifiedModelService.instance = new UnifiedModelService();
    }
    return UnifiedModelService.instance;
  }

  /**
   * Get all models from all providers with unified metadata
   */
  public getAllModels(): UnifiedModelMetadata[] {
    const allModels: UnifiedModelMetadata[] = [];

    // OpenAI models
    Object.values(OPENAI_MODEL_METADATA).forEach(model => {
      allModels.push(this.convertOpenAIModel(model));
    });

    // Anthropic models
    ANTHROPIC_MODELS.forEach(model => {
      allModels.push(this.convertAnthropicModel(model));
    });

    // OpenRouter models
    Object.values(OPENROUTER_MODEL_METADATA).forEach(model => {
      allModels.push(this.convertOpenRouterModel(model));
    });

    // DeepSeek models
    Object.values(DEEPSEEK_MODEL_METADATA).forEach(model => {
      allModels.push(this.convertDeepSeekModel(model));
    });

    // Fireworks models
    Object.values(FIREWORKS_MODEL_METADATA).forEach(model => {
      allModels.push(this.convertFireworksModel(model));
    });

    // Google models
    Object.values(GOOGLE_MODEL_METADATA).forEach(model => {
      allModels.push(this.convertGoogleModel(model));
    });

    return allModels;
  }

  /**
   * Get models grouped by provider
   */
  public getModelsByProvider(): Record<LLMProvider, UnifiedModelMetadata[]> {
    const allModels = this.getAllModels();
    const grouped: Record<LLMProvider, UnifiedModelMetadata[]> = {
      openai: [],
      anthropic: [],
      openrouter: [],
      azure: [],
      google: [],
      deepseek: [],
      fireworks: []
    };

    allModels.forEach(model => {
      grouped[model.provider].push(model);
    });

    return grouped;
  }

  /**
   * Get models for a specific provider
   */
  public getModelsForProvider(provider: LLMProvider): UnifiedModelMetadata[] {
    return this.getModelsByProvider()[provider] || [];
  }

  /**
   * Get model metadata by ID (searches across all providers)
   */
  public getModelById(modelId: string): UnifiedModelMetadata | null {
    const allModels = this.getAllModels();
    return allModels.find(model => model.id === modelId) || null;
  }

  /**
   * Get provider statistics
   */
  public getProviderStats(): Record<LLMProvider, { count: number; hasMetadata: boolean }> {
    const grouped = this.getModelsByProvider();
    const stats: Record<LLMProvider, { count: number; hasMetadata: boolean }> = {} as any;

    Object.entries(grouped).forEach(([provider, models]) => {
      stats[provider as LLMProvider] = {
        count: models.length,
        hasMetadata: models.length > 0
      };
    });

    return stats;
  }

  // Conversion methods for each provider
  private convertOpenAIModel(model: OpenAIModelMetadata): UnifiedModelMetadata {
    return {
      id: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      provider: 'openai',
      providerName: 'OpenAI',
      releaseDate: model.releaseDate
    };
  }

  private convertAnthropicModel(model: AnthropicModel): UnifiedModelMetadata {
    return {
      id: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextLength,
      pricing: model.costPer1kTokens,
      tags: model.capabilities,
      provider: 'anthropic',
      providerName: 'Anthropic',
      releaseDate: model.releaseDate
    };
  }

  private convertOpenRouterModel(model: OpenRouterModelMetadata): UnifiedModelMetadata {
    return {
      id: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      provider: 'openrouter',
      providerName: 'OpenRouter',
      originalProvider: model.provider
    };
  }

  private convertDeepSeekModel(model: DeepSeekModelMetadata): UnifiedModelMetadata {
    return {
      id: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      provider: 'deepseek',
      providerName: 'DeepSeek',
      releaseDate: model.releaseDate
    };
  }

  private convertFireworksModel(model: FireworksModelMetadata): UnifiedModelMetadata {
    return {
      id: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      provider: 'fireworks',
      providerName: 'Fireworks AI',
      releaseDate: model.releaseDate
    };
  }

  private convertGoogleModel(model: GoogleModelMetadata): UnifiedModelMetadata {
    return {
      id: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      provider: 'google',
      providerName: 'Google AI',
      releaseDate: model.releaseDate
    };
  }
}

// Export singleton instance
export const unifiedModelService = UnifiedModelService.getInstance();
