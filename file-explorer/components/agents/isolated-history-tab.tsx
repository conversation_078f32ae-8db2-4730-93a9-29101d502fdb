"use client"

import React, { useEffect } from 'react'
import AgentHistoryTab from '../history/AgentHistoryTab'

interface IsolatedHistoryTabProps {
  // Add any props needed for the history tab
}

/**
 * ✅ Isolated History Tab Component
 *
 * This wrapper ensures proper mounting/unmounting lifecycle for the AgentHistoryTab
 * to prevent memory leaks and background processes when tab is not active.
 */
const IsolatedHistoryTab = React.memo<IsolatedHistoryTabProps>(() => {
  useEffect(() => {
    console.log('🔄 AgentHistoryTab mounted')

    return () => {
      console.log('🧹 AgentHistoryTab unmounted - cleaning up')

      // ✅ Clear any intervals or timers that might be running
      // This prevents memory leaks when switching tabs
      const highestTimeoutId = setTimeout(() => {}, 0)
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i)
      }

      // Clear any intervals
      const highestIntervalId = setInterval(() => {}, 0)
      for (let i = 0; i < highestIntervalId; i++) {
        clearInterval(i)
      }
    }
  }, [])

  return <AgentHistoryTab />
})

IsolatedHistoryTab.displayName = 'IsolatedHistoryTab'

export default IsolatedHistoryTab
