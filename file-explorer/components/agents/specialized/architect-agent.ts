// components/agents/specialized/architect-agent.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from '../agent-base';

export class ArchitectAgent extends AgentBase {
  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return [
      'system_design',
      'component_architecture',
      'technical_strategy',
      'design_patterns',
      'scalability_planning',
      'integration_design',
      'technology_evaluation',
      'architectural_documentation'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Architect agent, responsible for high-level system design, component relationships, and technical strategy.

CORE RESPONSIBILITIES:
1. Create coherent system designs balancing technical excellence with practical implementation
2. Design clear component boundaries and interfaces
3. Choose appropriate design patterns and document decisions
4. Create hierarchical task trees with clear dependencies
5. Establish architectural validation criteria and quality standards

Focus on creating designs that are both technically sound and practically implementable.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      const architecture = await this.designArchitecture(context);

      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 800;

      return this.createSuccessResponse(
        architecture,
        tokensUsed,
        executionTime,
        ['Review implementation feasibility', 'Consider scalability requirements'],
        { taskType: 'architectural_design' }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Architecture design failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  private async designArchitecture(context: AgentContext): Promise<string> {
    // ✅ Task 82: Get shared context from Micromanager
    const taskId = context.metadata?.originalTaskId || context.metadata?.parentTaskId || `architect-${Date.now()}`;
    const chainContext = await this.getChainContext(taskId, ['plan_outline']);

    let contextualInfo = '';
    if (Object.keys(chainContext).length > 0) {
      contextualInfo = '\n\n// SHARED CONTEXT FROM PREVIOUS AGENTS:\n';
      for (const [key, value] of Object.entries(chainContext)) {
        if (typeof value === 'object' && value !== null) {
          contextualInfo += `// ${key}:\n// ${JSON.stringify(value, null, 2).replace(/\n/g, '\n// ')}\n\n`;
        } else {
          contextualInfo += `// ${key}: ${value}\n\n`;
        }
      }
      console.log(`🔍 ArchitectAgent: Using shared context from ${Object.keys(chainContext).length} previous agents`);
    }

    const architecture = `// Architectural Design
// Task: ${context.task}
${contextualInfo}
/**
 * SYSTEM ARCHITECTURE SPECIFICATION
 * ${Object.keys(chainContext).length > 0 ? 'Built upon shared context from previous agents' : 'Standalone architectural design'}
 */

## Component Architecture
- Core Components: [List main components based on task analysis]
- Integration Points: [Define interfaces considering shared context]
- Data Flow: [Describe data movement patterns]

## Design Patterns
- Applied Patterns: MVC, Repository, Observer
- Rationale: [Explain pattern choices based on requirements]

## Quality Attributes
- Scalability: Horizontal scaling support
- Performance: < 200ms response time
- Security: Authentication/Authorization layers
- Maintainability: Modular design

## Implementation Guidelines
1. Follow established patterns
2. Implement proper error handling
3. Add comprehensive logging
4. Include performance monitoring

export default architecturalSpecification;
`;

    // ✅ Task 82: Store architectural decisions for other agents
    await this.storeSharedContext(
      taskId,
      'architecture_decisions',
      {
        architecturalDesign: architecture,
        systemComponents: 'extracted_from_design',
        dataFlow: 'defined_in_architecture',
        integrationPatterns: 'specified_patterns',
        recommendations: 'architectural_recommendations'
      },
      {
        tags: ['architect', 'design', 'system_architecture'],
        parentTaskId: context.metadata?.parentTaskId,
        metadata: {
          basedOnContext: Object.keys(chainContext).length > 0,
          contextSources: Object.keys(chainContext)
        }
      }
    );
    console.log(`🔍 ArchitectAgent: Stored architectural decisions for task ${taskId}`);

    return architecture;
  }
}
