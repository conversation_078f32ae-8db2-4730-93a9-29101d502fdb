// components/agents/specialized/tester-agent.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from '../agent-base';

export class TesterAgent extends AgentBase {
  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return [
      'test_generation',
      'quality_assurance',
      'test_planning',
      'integration_testing',
      'performance_testing',
      'accessibility_testing',
      'test_automation',
      'bug_identification'
    ];
  }

  // ✅ Task 95: Implement command support checking
  public supportsCommand(command: string): boolean {
    const lowerCommand = command.toLowerCase();
    const supportedKeywords = [
      'test', 'spec', 'verify', 'validate', 'check', 'assert', 'qa',
      'quality', 'bug', 'debug', 'coverage', 'performance', 'security',
      'integration', 'unit', 'e2e', 'automation', 'mock', 'stub'
    ];

    return supportedKeywords.some(keyword => lowerCommand.includes(keyword));
  }

  public getSystemPrompt(): string {
    return `You are the Tester agent, responsible for generating comprehensive tests and ensuring code quality.

CORE RESPONSIBILITIES:
1. Generate comprehensive test suites for implementations
2. Create integration tests for multi-component features
3. Implement performance and accessibility tests
4. Design test automation strategies
5. Identify potential bugs and edge cases

Focus on creating thorough test coverage while maintaining test maintainability.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      // ✅ Task 95: Check if this agent supports the command
      const command = context.task?.trim();
      if (!command) {
        return {
          success: false,
          output: '⚠️ Empty command received.',
          status: 'failed',
          tokensUsed: 0,
          executionTime: Date.now() - startTime
        };
      }

      if (!this.supportsCommand(command)) {
        const analysis = this.analyzeCommandAndRecommendAgent(command);
        return this.createUnsupportedResponse(command, analysis.suggestions, analysis.recommendedAgent);
      }

      const tests = await this.generateTests(context);
      
      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 500;

      return this.createSuccessResponse(
        tests,
        tokensUsed,
        executionTime,
        ['Run tests in CI/CD pipeline', 'Monitor test coverage metrics'],
        { taskType: 'test_generation' }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Test generation failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  private async generateTests(context: AgentContext): Promise<string> {
    return `// Comprehensive Test Suite
// Generated for: ${context.task}

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, test, expect, beforeEach, afterEach } from 'vitest';

describe('Component Test Suite', () => {
  beforeEach(() => {
    // Setup test environment
  });

  afterEach(() => {
    // Cleanup
  });

  describe('Unit Tests', () => {
    test('renders without crashing', () => {
      // Basic rendering test
    });

    test('handles props correctly', () => {
      // Props testing
    });

    test('manages state appropriately', () => {
      // State management tests
    });
  });

  describe('Integration Tests', () => {
    test('integrates with other components', async () => {
      // Integration testing
    });

    test('handles API interactions', async () => {
      // API integration tests
    });
  });

  describe('Accessibility Tests', () => {
    test('meets WCAG guidelines', () => {
      // Accessibility testing
    });

    test('supports keyboard navigation', () => {
      // Keyboard accessibility
    });
  });

  describe('Performance Tests', () => {
    test('renders within performance budget', () => {
      // Performance testing
    });
  });
});

export default {};
`;
  }
}
