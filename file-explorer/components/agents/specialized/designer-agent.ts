// components/agents/specialized/designer-agent.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from '../agent-base';
import { AgentExecutionService, FileCreationRequest, KanbanUpdateRequest } from '../agent-execution-service';

export class DesignerAgent extends AgentBase {
  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return [
      'ui_design',
      'component_styling',
      'responsive_design',
      'accessibility',
      'brand_consistency',
      'interaction_design',
      'visual_optimization',
      'css_architecture'
    ];
  }

  // ✅ Task 95: Implement command support checking
  public supportsCommand(command: string): boolean {
    const lowerCommand = command.toLowerCase();
    const supportedKeywords = [
      'design', 'ui', 'interface', 'component', 'style', 'css', 'layout',
      'responsive', 'accessibility', 'visual', 'theme', 'color', 'font',
      'button', 'form', 'card', 'modal', 'navigation', 'dashboard',
      'mockup', 'wireframe', 'prototype', 'brand', 'logo'
    ];

    return supportedKeywords.some(keyword => lowerCommand.includes(keyword));
  }

  public getSystemPrompt(): string {
    return `You are the Designer agent, responsible for UI/UX implementation, styling, and visual coherence.

CORE RESPONSIBILITIES:
1. Create visually appealing, user-friendly interfaces
2. Maintain visual consistency with existing components
3. Follow accessibility standards (WCAG)
4. Ensure responsive behavior across devices
5. Design for reusability and extensibility

Focus on creating designs that are both visually appealing and functionally effective.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      // ✅ Task 95: Check if this agent supports the command
      const command = context.task?.trim();
      if (!command) {
        return {
          success: false,
          output: '⚠️ Empty command received.',
          status: 'failed',
          tokensUsed: 0,
          executionTime: Date.now() - startTime
        };
      }

      if (!this.supportsCommand(command)) {
        const analysis = this.analyzeCommandAndRecommendAgent(command);
        return this.createUnsupportedResponse(command, analysis.suggestions, analysis.recommendedAgent);
      }

      // Perform real design work
      const designResult = await this.performRealDesignWork(context);

      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 600;

      return this.createSuccessResponse(
        designResult.output,
        tokensUsed,
        executionTime,
        ['Ensure accessibility compliance', 'Test responsive behavior', 'Review design system consistency'],
        {
          taskType: 'ui_design',
          filesCreated: designResult.files?.length || 0,
          realWork: true
        }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Design creation failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  private async performRealDesignWork(context: AgentContext): Promise<any> {
    const executionService = AgentExecutionService.getInstance();

    // Analyze the design task
    const designAnalysis = this.analyzeDesignTask(context);

    // Generate design files based on task requirements
    const designFiles = this.generateDesignFiles(context, designAnalysis);

    // Execute real file creation and Kanban updates
    const result = await executionService.executeWork(context, this.getId(), {
      files: designFiles,
      kanban: [{
        cardId: context.metadata?.kanbanCardId,
        action: 'update',
        data: {
          progress: 50,
          tags: ['design', 'ui', designAnalysis.type],
          agentAssignments: [{
            agentId: this.getId(),
            status: 'working',
            assignmentTime: new Date().toISOString()
          }]
        }
      }]
    });

    console.log(`DesignerAgent: Real design work completed. Files: ${result.files?.length || 0}, Success: ${result.success}`);
    return result;
  }

  private analyzeDesignTask(context: AgentContext): { type: string; complexity: string; components: string[] } {
    const task = context.task.toLowerCase();

    let type = 'component';
    if (task.includes('dashboard')) type = 'dashboard';
    else if (task.includes('form')) type = 'form';
    else if (task.includes('layout')) type = 'layout';
    else if (task.includes('page')) type = 'page';

    let complexity = 'simple';
    if (task.includes('complex') || task.includes('advanced')) complexity = 'complex';
    else if (task.includes('responsive') || task.includes('interactive')) complexity = 'medium';

    const components = [];
    if (task.includes('button')) components.push('button');
    if (task.includes('input') || task.includes('form')) components.push('input');
    if (task.includes('card')) components.push('card');
    if (task.includes('modal')) components.push('modal');
    if (task.includes('navigation') || task.includes('nav')) components.push('navigation');

    return { type, complexity, components };
  }

  private generateDesignFiles(context: AgentContext, analysis: any): FileCreationRequest[] {
    const files: FileCreationRequest[] = [];
    const timestamp = Date.now();

    // Main component file
    const componentName = this.extractComponentName(context.task);
    const componentPath = `components/ui/${componentName.toLowerCase()}.tsx`;

    files.push({
      path: componentPath,
      content: this.generateComponentCode(componentName, context, analysis),
      language: 'typescript',
      openInEditor: true
    });

    // Styles file if needed
    if (analysis.complexity !== 'simple') {
      files.push({
        path: `components/ui/${componentName.toLowerCase()}.module.css`,
        content: this.generateStylesCode(componentName, analysis),
        language: 'css'
      });
    }

    // Story file for Storybook (if complex)
    if (analysis.complexity === 'complex') {
      files.push({
        path: `stories/${componentName}.stories.tsx`,
        content: this.generateStoryCode(componentName, analysis),
        language: 'typescript'
      });
    }

    return files;
  }

  private extractComponentName(task: string): string {
    // Extract component name from task description
    const words = task.split(' ');
    for (const word of words) {
      if (word.includes('component') || word.includes('button') || word.includes('form') || word.includes('card')) {
        return word.charAt(0).toUpperCase() + word.slice(1).replace(/[^a-zA-Z]/g, '');
      }
    }
    return 'DesignComponent';
  }

  private generateComponentCode(componentName: string, context: AgentContext, analysis: any): string {
    return `// ${componentName} Component
// Generated by DesignerAgent for task: ${context.task}
// Type: ${analysis.type} | Complexity: ${analysis.complexity}

import React from 'react';
import { cn } from '@/lib/utils';
${analysis.complexity !== 'simple' ? `import styles from './${componentName.toLowerCase()}.module.css';` : ''}

interface ${componentName}Props {
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children?: React.ReactNode;
  ${analysis.components.includes('button') ? 'onClick?: () => void;' : ''}
  ${analysis.components.includes('input') ? 'value?: string; onChange?: (value: string) => void;' : ''}
}

export const ${componentName}: React.FC<${componentName}Props> = ({
  className,
  variant = 'primary',
  size = 'md',
  children,
  ${analysis.components.includes('button') ? 'onClick,' : ''}
  ${analysis.components.includes('input') ? 'value, onChange,' : ''}
  ...props
}) => {
  return (
    <div
      className={cn(
        'rounded-md transition-all duration-200',
        {
          'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'primary',
          'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',
          'border border-input bg-background hover:bg-accent': variant === 'outline',
        },
        {
          'px-2 py-1 text-sm': size === 'sm',
          'px-4 py-2 text-base': size === 'md',
          'px-6 py-3 text-lg': size === 'lg',
        },
        ${analysis.complexity !== 'simple' ? `styles.${componentName.toLowerCase()},` : ''}
        className
      )}
      ${analysis.components.includes('button') ? 'onClick={onClick}' : ''}
      {...props}
    >
      ${analysis.components.includes('input') ? `
      <input
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        className="w-full bg-transparent outline-none"
      />` : 'children'}
    </div>
  );
};

export default ${componentName};
`;
  }

  private generateStylesCode(componentName: string, analysis: any): string {
    return `/* ${componentName} Styles */
/* Generated by DesignerAgent */

.${componentName.toLowerCase()} {
  /* Component-specific styles */
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.${componentName.toLowerCase()}:focus-within {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
  .${componentName.toLowerCase()} {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .${componentName.toLowerCase()} {
    padding: 0.375rem;
    font-size: 0.75rem;
  }
}

/* Animation enhancements */
.${componentName.toLowerCase()}:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.${componentName.toLowerCase()}:active {
  transform: translateY(0);
}
`;
  }

  private generateStoryCode(componentName: string, analysis: any): string {
    return `// ${componentName} Stories
// Generated by DesignerAgent

import type { Meta, StoryObj } from '@storybook/react';
import { ${componentName} } from '../components/ui/${componentName.toLowerCase()}';

const meta: Meta<typeof ${componentName}> = {
  title: 'UI/${componentName}',
  component: ${componentName},
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'outline'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: '${componentName}',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: '${componentName}',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: '${componentName}',
  },
};

export const Small: Story = {
  args: {
    size: 'sm',
    children: 'Small ${componentName}',
  },
};

export const Large: Story = {
  args: {
    size: 'lg',
    children: 'Large ${componentName}',
  },
};
`;
  }
}
