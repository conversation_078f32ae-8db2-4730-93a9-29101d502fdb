// components/agents/specialized/researcher-agent.ts
import { <PERSON><PERSON><PERSON>, AgentConfig, AgentContext, AgentResponse } from '../agent-base';

export interface ResearchFindings {
  patterns: CodePattern[];
  dependencies: DependencyMap[];
  conventions: NamingConvention[];
  apiContracts: ApiContract[];
  businessLogic: BusinessRule[];
  architecture: ArchitecturalInsight[];
  recommendations: string[];
  riskFactors: string[];
}

export interface CodePattern {
  id: string;
  name: string;
  type: 'design_pattern' | 'implementation_pattern' | 'anti_pattern';
  description: string;
  examples: CodeExample[];
  frequency: number;
  confidence: number;
  fileReferences: string[];
}

export interface CodeExample {
  file: string;
  lineStart: number;
  lineEnd: number;
  code: string;
  context: string;
}

export interface DependencyMap {
  source: string;
  target: string;
  type: 'import' | 'function_call' | 'inheritance' | 'composition' | 'data_flow';
  strength: 'weak' | 'medium' | 'strong';
  description: string;
}

export interface NamingConvention {
  category: 'function' | 'variable' | 'class' | 'interface' | 'file' | 'component';
  pattern: string;
  examples: string[];
  confidence: number;
  adherence: number; // 0-100%
}

export interface ApiContract {
  endpoint: string;
  method: string;
  parameters: { name: string; type: string; required: boolean }[];
  response: { type: string; structure: any };
  documentation: string;
  usageExamples: string[];
}

export interface BusinessRule {
  id: string;
  description: string;
  implementation: string;
  constraints: string[];
  exceptions: string[];
  relatedFiles: string[];
}

export interface ArchitecturalInsight {
  component: string;
  role: string;
  interactions: string[];
  responsibilities: string[];
  boundaries: string[];
  quality: 'good' | 'acceptable' | 'needs_improvement';
}

export class ResearcherAgent extends AgentBase {
  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return [
      'codebase_analysis',
      'pattern_recognition',
      'dependency_mapping',
      'convention_extraction',
      'api_discovery',
      'architecture_analysis',
      'business_logic_identification',
      'knowledge_synthesis',
      'risk_assessment',
      'recommendation_generation'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Researcher agent, responsible for exploring the codebase, extracting patterns, and building project understanding.

CORE RESPONSIBILITIES:
1. CODEBASE ANALYSIS:
   - Examine code to identify design patterns and implementation approaches
   - Document naming conventions and code organization
   - Map component relationships and dependencies
   - Extract API contracts and interfaces
   - Identify business logic and domain concepts
   - Create hierarchical representations of project structure
   - Generate abstract syntax trees (ASTs) for semantic understanding

2. KNOWLEDGE CONSTRUCTION:
   - Create embeddings for semantic search
   - Map relationships for knowledge graphs
   - Extract terminology for project dictionaries
   - Identify patterns for configuration stores
   - Document decisions for context history

3. CONTEXT OPTIMIZATION:
   - Create compressed representations of code components
   - Assign relevance scores to different elements
   - Build efficient indexes for quick retrieval
   - Develop summaries that balance conciseness with completeness

4. RESEARCH REPORTING:
   - Always cite specific file paths and line numbers
   - Include relevance scores for each result
   - Organize findings from highest to lowest importance
   - Highlight potential impacts of modifications
   - Identify risks and dependencies

5. PATTERN RECOGNITION:
   - Discover recurring patterns that could be abstracted
   - Identify inconsistencies or variations in similar components
   - Suggest refactoring opportunities based on detected patterns
   - Track pattern evolution over time

6. LEARNING INTEGRATION:
   - Feed discoveries into learning databases
   - Flag patterns that could benefit other projects
   - Identify successful approaches for similar problems

RESEARCH METHODOLOGY:
- Systematic exploration of codebase structure
- Statistical analysis of code patterns and conventions
- Dependency analysis and impact assessment
- Business logic extraction and documentation
- Risk identification and mitigation recommendations

Always prioritize accuracy and detailed reference information. Provide specific, concrete references over general observations.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      // Determine research scope and strategy
      const researchPlan = this.createResearchPlan(context);

      // Execute research phases
      const findings = await this.conductResearch(context, researchPlan);

      // Synthesize findings and generate recommendations
      const synthesis = this.synthesizeFindings(findings);

      // Generate research report
      const report = this.generateResearchReport(context, findings, synthesis);

      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 800; // Research overhead

      return this.createSuccessResponse(
        report,
        tokensUsed,
        executionTime,
        synthesis.recommendations,
        {
          researchType: researchPlan.type,
          scope: researchPlan.scope,
          patternsFound: findings.patterns.length,
          dependenciesFound: findings.dependencies.length,
          risksIdentified: findings.riskFactors.length,
          confidence: this.calculateOverallConfidence(findings)
        }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Research failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  private createResearchPlan(context: AgentContext): {
    type: string;
    scope: string[];
    phases: string[];
    priorities: string[];
  } {
    const task = context.task.toLowerCase();

    // Determine research type
    let type = 'general_analysis';
    if (task.includes('pattern')) type = 'pattern_analysis';
    else if (task.includes('dependency') || task.includes('relationship')) type = 'dependency_analysis';
    else if (task.includes('api') || task.includes('interface')) type = 'api_analysis';
    else if (task.includes('architecture')) type = 'architectural_analysis';
    else if (task.includes('business') || task.includes('logic')) type = 'business_logic_analysis';
    else if (task.includes('convention') || task.includes('style')) type = 'convention_analysis';

    // Define research scope
    const scope: string[] = [];
    if (context.files && context.files.length > 0) {
      scope.push(...context.files);
    } else {
      // Default scope for comprehensive analysis
      scope.push('codebase_structure', 'component_relationships', 'naming_conventions', 'patterns', 'dependencies');
    }

    // Define research phases
    const phases = [
      'structural_analysis',
      'pattern_recognition',
      'dependency_mapping',
      'convention_extraction',
      'synthesis_and_recommendations'
    ];

    // Set priorities based on research type
    const priorities: string[] = [];
    switch (type) {
      case 'pattern_analysis':
        priorities.push('design_patterns', 'code_patterns', 'anti_patterns');
        break;
      case 'dependency_analysis':
        priorities.push('component_dependencies', 'data_flow', 'coupling_analysis');
        break;
      case 'api_analysis':
        priorities.push('api_contracts', 'interface_definitions', 'usage_patterns');
        break;
      case 'architectural_analysis':
        priorities.push('component_architecture', 'system_boundaries', 'quality_assessment');
        break;
      default:
        priorities.push('comprehensive_analysis');
    }

    return { type, scope, phases, priorities };
  }

  private async conductResearch(context: AgentContext, plan: any): Promise<ResearchFindings> {
    const findings: ResearchFindings = {
      patterns: [],
      dependencies: [],
      conventions: [],
      apiContracts: [],
      businessLogic: [],
      architecture: [],
      recommendations: [],
      riskFactors: []
    };

    // Phase 1: Structural Analysis
    findings.architecture = await this.analyzeArchitecture(context);

    // Phase 2: Pattern Recognition
    findings.patterns = await this.recognizePatterns(context);

    // Phase 3: Dependency Mapping
    findings.dependencies = await this.mapDependencies(context);

    // Phase 4: Convention Extraction
    findings.conventions = await this.extractConventions(context);

    // Phase 5: API Analysis (if applicable)
    if (plan.priorities.includes('api_contracts')) {
      findings.apiContracts = await this.analyzeApiContracts(context);
    }

    // Phase 6: Business Logic Analysis
    findings.businessLogic = await this.identifyBusinessLogic(context);

    // Phase 7: Risk Assessment
    findings.riskFactors = this.assessRisks(findings);

    return findings;
  }

  private async analyzeArchitecture(context: AgentContext): Promise<ArchitecturalInsight[]> {
    // Simulate architectural analysis
    const insights: ArchitecturalInsight[] = [];

    // Example insight for a React component architecture
    if (context.codeContext?.includes('react') || context.task.includes('component')) {
      insights.push({
        component: 'UI Components',
        role: 'User Interface Layer',
        interactions: ['State Management', 'API Services', 'Event Handlers'],
        responsibilities: ['Rendering UI', 'User Interactions', 'State Display'],
        boundaries: ['No direct database access', 'No business logic'],
        quality: 'good'
      });
    }

    // Example insight for service layer
    if (context.codeContext?.includes('service') || context.task.includes('service')) {
      insights.push({
        component: 'Service Layer',
        role: 'Business Logic Layer',
        interactions: ['Data Access Layer', 'External APIs', 'UI Components'],
        responsibilities: ['Business Logic', 'Data Processing', 'API Integration'],
        boundaries: ['No UI rendering', 'No direct database queries'],
        quality: 'acceptable'
      });
    }

    return insights;
  }

  private async recognizePatterns(context: AgentContext): Promise<CodePattern[]> {
    const patterns: CodePattern[] = [];

    // Simulate pattern recognition based on context
    if (context.codeContext?.includes('useState') || context.codeContext?.includes('useEffect')) {
      patterns.push({
        id: 'react_hooks_pattern',
        name: 'React Hooks Pattern',
        type: 'implementation_pattern',
        description: 'Usage of React hooks for state management and side effects',
        examples: [{
          file: 'component.tsx',
          lineStart: 10,
          lineEnd: 15,
          code: 'const [state, setState] = useState(initialValue);',
          context: 'React functional component'
        }],
        frequency: 5,
        confidence: 0.9,
        fileReferences: ['component.tsx', 'hooks.ts']
      });
    }

    if (context.codeContext?.includes('class') && context.codeContext?.includes('constructor')) {
      patterns.push({
        id: 'class_pattern',
        name: 'Class-based Architecture',
        type: 'design_pattern',
        description: 'Object-oriented class-based implementation',
        examples: [{
          file: 'service.ts',
          lineStart: 1,
          lineEnd: 20,
          code: 'class Service { constructor() { ... } }',
          context: 'Service layer implementation'
        }],
        frequency: 3,
        confidence: 0.8,
        fileReferences: ['service.ts', 'model.ts']
      });
    }

    // Add more pattern recognition logic based on common patterns
    return patterns;
  }

  private async mapDependencies(context: AgentContext): Promise<DependencyMap[]> {
    const dependencies: DependencyMap[] = [];

    // Simulate dependency mapping
    if (context.files && context.files.length > 1) {
      // Example dependency between component and service
      dependencies.push({
        source: 'component.tsx',
        target: 'service.ts',
        type: 'import',
        strength: 'medium',
        description: 'Component imports and uses service for data operations'
      });

      dependencies.push({
        source: 'service.ts',
        target: 'api.ts',
        type: 'function_call',
        strength: 'strong',
        description: 'Service makes API calls for data fetching'
      });
    }

    return dependencies;
  }

  private async extractConventions(context: AgentContext): Promise<NamingConvention[]> {
    const conventions: NamingConvention[] = [];

    // Simulate convention extraction
    conventions.push({
      category: 'function',
      pattern: 'camelCase',
      examples: ['handleClick', 'processData', 'validateInput'],
      confidence: 0.9,
      adherence: 85
    });

    conventions.push({
      category: 'component',
      pattern: 'PascalCase',
      examples: ['UserProfile', 'DataTable', 'NavigationBar'],
      confidence: 0.95,
      adherence: 92
    });

    conventions.push({
      category: 'file',
      pattern: 'kebab-case.extension',
      examples: ['user-profile.tsx', 'data-service.ts', 'api-client.ts'],
      confidence: 0.8,
      adherence: 78
    });

    return conventions;
  }

  private async analyzeApiContracts(context: AgentContext): Promise<ApiContract[]> {
    const contracts: ApiContract[] = [];

    // Simulate API contract analysis
    if (context.codeContext?.includes('api') || context.codeContext?.includes('endpoint')) {
      contracts.push({
        endpoint: '/api/users',
        method: 'GET',
        parameters: [
          { name: 'page', type: 'number', required: false },
          { name: 'limit', type: 'number', required: false }
        ],
        response: {
          type: 'object',
          structure: {
            users: 'array',
            total: 'number',
            page: 'number'
          }
        },
        documentation: 'Retrieves paginated list of users',
        usageExamples: ['fetch("/api/users?page=1&limit=10")']
      });
    }

    return contracts;
  }

  private async identifyBusinessLogic(context: AgentContext): Promise<BusinessRule[]> {
    const rules: BusinessRule[] = [];

    // Simulate business logic identification
    if (context.codeContext?.includes('validation') || context.task.includes('business')) {
      rules.push({
        id: 'user_validation_rule',
        description: 'User email must be unique and follow valid email format',
        implementation: 'Email validation with regex and database uniqueness check',
        constraints: ['Must be valid email format', 'Must be unique in system'],
        exceptions: ['System admin emails', 'Test environment emails'],
        relatedFiles: ['user-service.ts', 'validation.ts']
      });
    }

    return rules;
  }

  private assessRisks(findings: ResearchFindings): string[] {
    const risks: string[] = [];

    // Check for architectural risks
    const poorQualityComponents = findings.architecture.filter(a => a.quality === 'needs_improvement');
    if (poorQualityComponents.length > 0) {
      risks.push(`Architecture quality issues in: ${poorQualityComponents.map(c => c.component).join(', ')}`);
    }

    // Check for pattern risks
    const antiPatterns = findings.patterns.filter(p => p.type === 'anti_pattern');
    if (antiPatterns.length > 0) {
      risks.push(`Anti-patterns detected: ${antiPatterns.map(p => p.name).join(', ')}`);
    }

    // Check for dependency risks
    const strongDependencies = findings.dependencies.filter(d => d.strength === 'strong');
    if (strongDependencies.length > findings.dependencies.length * 0.7) {
      risks.push('High coupling detected - many strong dependencies between components');
    }

    // Check for convention consistency
    const poorConventions = findings.conventions.filter(c => c.adherence < 70);
    if (poorConventions.length > 0) {
      risks.push(`Inconsistent naming conventions in: ${poorConventions.map(c => c.category).join(', ')}`);
    }

    return risks;
  }

  private synthesizeFindings(findings: ResearchFindings): {
    recommendations: string[];
    summary: string;
    priorities: string[];
  } {
    const recommendations: string[] = [];
    const priorities: string[] = [];

    // Generate recommendations based on findings
    if (findings.riskFactors.length > 0) {
      recommendations.push('Address identified risk factors to improve code quality and maintainability');
      priorities.push('risk_mitigation');
    }

    if (findings.patterns.some(p => p.type === 'anti_pattern')) {
      recommendations.push('Refactor anti-patterns to improve code quality');
      priorities.push('pattern_improvement');
    }

    if (findings.conventions.some(c => c.adherence < 80)) {
      recommendations.push('Improve naming convention consistency across the codebase');
      priorities.push('convention_standardization');
    }

    if (findings.dependencies.filter(d => d.strength === 'strong').length > 5) {
      recommendations.push('Consider refactoring to reduce tight coupling between components');
      priorities.push('dependency_optimization');
    }

    // Add general recommendations
    recommendations.push('Document identified patterns for future reference');
    recommendations.push('Create coding guidelines based on discovered conventions');
    recommendations.push('Implement automated checks for pattern compliance');

    const summary = `Research completed on ${findings.patterns.length} patterns, ${findings.dependencies.length} dependencies, and ${findings.architecture.length} architectural components. ${findings.riskFactors.length} risk factors identified.`;

    return { recommendations, summary, priorities };
  }

  private generateResearchReport(context: AgentContext, findings: ResearchFindings, synthesis: any): string {
    let report = `[RESEARCHER ANALYSIS REPORT]

TASK: ${context.task}

EXECUTIVE SUMMARY:
${synthesis.summary}

ARCHITECTURAL INSIGHTS:
${findings.architecture.map(arch => `
- Component: ${arch.component}
  Role: ${arch.role}
  Quality: ${arch.quality}
  Responsibilities: ${arch.responsibilities.join(', ')}
  Interactions: ${arch.interactions.join(', ')}
`).join('')}

CODE PATTERNS IDENTIFIED:
${findings.patterns.map(pattern => `
- ${pattern.name} (${pattern.type})
  Description: ${pattern.description}
  Frequency: ${pattern.frequency}
  Confidence: ${(pattern.confidence * 100).toFixed(1)}%
  Files: ${pattern.fileReferences.join(', ')}
`).join('')}

DEPENDENCIES MAPPED:
${findings.dependencies.map(dep => `
- ${dep.source} → ${dep.target}
  Type: ${dep.type}
  Strength: ${dep.strength}
  Description: ${dep.description}
`).join('')}

NAMING CONVENTIONS:
${findings.conventions.map(conv => `
- ${conv.category}: ${conv.pattern}
  Adherence: ${conv.adherence}%
  Examples: ${conv.examples.slice(0, 3).join(', ')}
`).join('')}

`;

    if (findings.apiContracts.length > 0) {
      report += `API CONTRACTS:
${findings.apiContracts.map(api => `
- ${api.method} ${api.endpoint}
  Parameters: ${api.parameters.map(p => `${p.name}(${p.type})`).join(', ')}
  Description: ${api.documentation}
`).join('')}

`;
    }

    if (findings.businessLogic.length > 0) {
      report += `BUSINESS RULES:
${findings.businessLogic.map(rule => `
- ${rule.description}
  Implementation: ${rule.implementation}
  Constraints: ${rule.constraints.join(', ')}
  Related Files: ${rule.relatedFiles.join(', ')}
`).join('')}

`;
    }

    if (findings.riskFactors.length > 0) {
      report += `RISK FACTORS:
${findings.riskFactors.map(risk => `- ${risk}`).join('\n')}

`;
    }

    report += `RECOMMENDATIONS:
${synthesis.recommendations.map((rec: string) => `- ${rec}`).join('\n')}

PRIORITIES:
${synthesis.priorities.map((priority: string) => `- ${priority}`).join('\n')}

CONFIDENCE METRICS:
- Overall Analysis Confidence: ${(this.calculateOverallConfidence(findings) * 100).toFixed(1)}%
- Pattern Recognition Confidence: ${findings.patterns.length > 0 ? (findings.patterns.reduce((sum, p) => sum + p.confidence, 0) / findings.patterns.length * 100).toFixed(1) : 'N/A'}%
- Convention Analysis Confidence: ${findings.conventions.length > 0 ? (findings.conventions.reduce((sum, c) => sum + c.confidence, 0) / findings.conventions.length * 100).toFixed(1) : 'N/A'}%

NEXT STEPS:
1. Address high-priority risk factors
2. Implement recommended pattern improvements
3. Standardize naming conventions
4. Document architectural decisions
5. Create automated quality checks

[END RESEARCH REPORT]`;

    return report;
  }

  private calculateOverallConfidence(findings: ResearchFindings): number {
    let totalConfidence = 0;
    let itemCount = 0;

    // Include pattern confidence
    if (findings.patterns.length > 0) {
      totalConfidence += findings.patterns.reduce((sum, p) => sum + p.confidence, 0);
      itemCount += findings.patterns.length;
    }

    // Include convention confidence
    if (findings.conventions.length > 0) {
      totalConfidence += findings.conventions.reduce((sum, c) => sum + c.confidence, 0);
      itemCount += findings.conventions.length;
    }

    // Base confidence for architectural analysis
    if (findings.architecture.length > 0) {
      totalConfidence += findings.architecture.length * 0.8; // Assume 80% confidence for architectural analysis
      itemCount += findings.architecture.length;
    }

    return itemCount > 0 ? totalConfidence / itemCount : 0.5;
  }
}