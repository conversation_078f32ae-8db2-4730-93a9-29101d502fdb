// components/agents/capability-tags.ts
// Strict whitelist schema for model capability tags
// All model metadata must use only these approved tags

/**
 * Comprehensive list of allowed capability tags for model metadata
 * This ensures consistent semantic tagging across the entire Agent System
 */
export const ALLOWED_CAPABILITY_TAGS = [
  // Performance & Speed
  'fast',
  'low-latency',
  'high-performance',
  
  // Context & Memory
  'long-context',
  'high-context',
  'extended-context',
  
  // Modality & Input Types
  'multimodal',
  'vision',
  'audio',
  'text-only',
  
  // Cognitive Capabilities
  'reasoning',
  'advanced-reasoning',
  'enhanced-reasoning',
  'logical-reasoning',
  'complex-analysis',
  
  // Domain Specialization
  'code',
  'programming',
  'debugging',
  'math',
  'science',
  'creative-writing',
  'general-purpose',
  
  // Technical Characteristics
  'instruction-tuned',
  'fine-tuned',
  'foundation-model',
  'mixture-of-experts',
  
  // Accessibility & Licensing
  'open-weight',
  'closed-weight',
  'open-source',
  'commercial',
  
  // Use Case Categories
  'chat',
  'conversational',
  'assistant',
  'completion',
  'embedding',
  
  // Quality & Reliability
  'production-ready',
  'research',
  'experimental',
  'beta',
  'preview',
  'stable',
  
  // Cost & Efficiency
  'affordable',
  'cost-effective',
  'premium',
  'enterprise',
  
  // Developer Experience
  'developer-friendly',
  'api-optimized',
  'batch-processing',
  
  // Specialized Tasks
  'summarization',
  'translation',
  'classification',
  'sentiment-analysis',
  'question-answering',
  
  // Legacy & Compatibility
  'legacy',
  'deprecated',
  'latest',
  'current'
] as const;

/**
 * Type definition for capability tags (ensures compile-time checking)
 */
export type CapabilityTag = typeof ALLOWED_CAPABILITY_TAGS[number];

/**
 * Capability tag categories for better organization
 */
export const TAG_CATEGORIES = {
  performance: ['fast', 'low-latency', 'high-performance'],
  context: ['long-context', 'high-context', 'extended-context'],
  modality: ['multimodal', 'vision', 'audio', 'text-only'],
  reasoning: ['reasoning', 'advanced-reasoning', 'enhanced-reasoning', 'logical-reasoning', 'complex-analysis'],
  domain: ['code', 'programming', 'debugging', 'math', 'science', 'creative-writing', 'general-purpose'],
  technical: ['instruction-tuned', 'fine-tuned', 'foundation-model', 'mixture-of-experts'],
  licensing: ['open-weight', 'closed-weight', 'open-source', 'commercial'],
  useCase: ['chat', 'conversational', 'assistant', 'completion', 'embedding'],
  quality: ['production-ready', 'research', 'experimental', 'beta', 'preview', 'stable'],
  cost: ['affordable', 'cost-effective', 'premium', 'enterprise'],
  developer: ['developer-friendly', 'api-optimized', 'batch-processing'],
  specialized: ['summarization', 'translation', 'classification', 'sentiment-analysis', 'question-answering'],
  lifecycle: ['legacy', 'deprecated', 'latest', 'current']
} as const;

/**
 * Validate that a tag is in the allowed list
 */
export function isValidCapabilityTag(tag: string): tag is CapabilityTag {
  return ALLOWED_CAPABILITY_TAGS.includes(tag as CapabilityTag);
}

/**
 * Validate an array of tags
 */
export function validateCapabilityTags(tags: string[]): {
  valid: boolean;
  invalidTags: string[];
  validTags: CapabilityTag[];
} {
  const invalidTags = tags.filter(tag => !isValidCapabilityTag(tag));
  const validTags = tags.filter(isValidCapabilityTag) as CapabilityTag[];
  
  return {
    valid: invalidTags.length === 0,
    invalidTags,
    validTags
  };
}

/**
 * Get tags by category
 */
export function getTagsByCategory(category: keyof typeof TAG_CATEGORIES): readonly string[] {
  return TAG_CATEGORIES[category];
}

/**
 * Get all categories for a given tag
 */
export function getCategoriesForTag(tag: string): string[] {
  const categories: string[] = [];
  
  for (const [categoryName, categoryTags] of Object.entries(TAG_CATEGORIES)) {
    if (categoryTags.includes(tag as any)) {
      categories.push(categoryName);
    }
  }
  
  return categories;
}

/**
 * Suggest similar valid tags for invalid ones (basic fuzzy matching)
 */
export function suggestValidTags(invalidTag: string): string[] {
  const suggestions: string[] = [];
  const lowerInvalid = invalidTag.toLowerCase();
  
  for (const validTag of ALLOWED_CAPABILITY_TAGS) {
    const lowerValid = validTag.toLowerCase();
    
    // Exact substring match
    if (lowerValid.includes(lowerInvalid) || lowerInvalid.includes(lowerValid)) {
      suggestions.push(validTag);
    }
    
    // Similar length and some character overlap
    if (Math.abs(lowerValid.length - lowerInvalid.length) <= 3) {
      let commonChars = 0;
      for (const char of lowerInvalid) {
        if (lowerValid.includes(char)) commonChars++;
      }
      
      if (commonChars >= Math.min(lowerInvalid.length, lowerValid.length) * 0.6) {
        suggestions.push(validTag);
      }
    }
  }
  
  return [...new Set(suggestions)].slice(0, 3); // Return top 3 unique suggestions
}

/**
 * Common tag combinations that work well together
 */
export const RECOMMENDED_TAG_COMBINATIONS = {
  'high-performance-multimodal': ['fast', 'multimodal', 'high-context'],
  'coding-specialist': ['code', 'programming', 'debugging', 'reasoning'],
  'general-assistant': ['chat', 'general-purpose', 'reasoning', 'production-ready'],
  'research-model': ['research', 'experimental', 'advanced-reasoning'],
  'production-chat': ['chat', 'production-ready', 'fast', 'stable'],
  'affordable-coding': ['code', 'affordable', 'developer-friendly'],
  'premium-reasoning': ['premium', 'advanced-reasoning', 'complex-analysis'],
  'open-source-base': ['open-weight', 'foundation-model', 'general-purpose']
} as const;

/**
 * Export total count for validation reporting
 */
export const TOTAL_ALLOWED_TAGS = ALLOWED_CAPABILITY_TAGS.length;

/**
 * Export for external validation scripts
 */
export default {
  ALLOWED_CAPABILITY_TAGS,
  TAG_CATEGORIES,
  isValidCapabilityTag,
  validateCapabilityTags,
  getTagsByCategory,
  getCategoriesForTag,
  suggestValidTags,
  RECOMMENDED_TAG_COMBINATIONS,
  TOTAL_ALLOWED_TAGS
};
