// components/agents/agent-context-memory.ts

export interface ContextMemoryEntry {
  id: string;
  agentId: string;
  taskId: string;
  contextType: 'plan_outline' | 'folder_structure' | 'component_map' | 'research_findings' | 'architecture_decisions' | 'design_specs' | 'test_results' | 'custom';
  data: Record<string, any>;
  timestamp: number;
  expiresAt?: number;
  tags: string[];
  parentTaskId?: string;
  linkedTaskIds: string[];
  version: number;
  metadata?: Record<string, any>;
}

export interface ContextQuery {
  agentId?: string;
  taskId?: string;
  parentTaskId?: string;
  contextType?: string;
  tags?: string[];
  timeRange?: { start: number; end: number };
  includeExpired?: boolean;
}

export interface ContextChain {
  taskId: string;
  agentChain: string[];
  contextFlow: ContextMemoryEntry[];
  dependencies: string[];
}

/**
 * ✅ Task 82: Agent Context Memory
 * Shared context memory system allowing agents to access shared context from prior steps
 */
export class AgentContextMemory {
  private memory = new Map<string, ContextMemoryEntry>();
  private taskChains = new Map<string, ContextChain>();
  private agentIndex = new Map<string, Set<string>>(); // agentId -> entry IDs
  private taskIndex = new Map<string, Set<string>>(); // taskId -> entry IDs
  private typeIndex = new Map<string, Set<string>>(); // contextType -> entry IDs
  private tagIndex = new Map<string, Set<string>>(); // tag -> entry IDs

  // Configuration
  private readonly config = {
    maxEntries: 5000,
    defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
    maxVersions: 10,
    compressionThreshold: 1000, // Compress entries larger than 1KB
    persistToDisk: true
  };

  constructor() {
    this.startPeriodicCleanup();
    this.loadFromStorage();
  }

  /**
   * Store context data for sharing between agents
   */
  public async storeContext(
    agentId: string,
    taskId: string,
    contextType: ContextMemoryEntry['contextType'],
    data: Record<string, any>,
    options?: {
      tags?: string[];
      parentTaskId?: string;
      linkedTaskIds?: string[];
      ttl?: number;
      metadata?: Record<string, any>;
    }
  ): Promise<string> {
    const entryId = this.generateEntryId(agentId, taskId, contextType);
    const now = Date.now();
    const ttl = options?.ttl || this.config.defaultTTL;

    // Check if entry already exists and increment version
    const existingEntry = this.memory.get(entryId);
    const version = existingEntry ? existingEntry.version + 1 : 1;

    const entry: ContextMemoryEntry = {
      id: entryId,
      agentId,
      taskId,
      contextType,
      data: this.compressData(data),
      timestamp: now,
      expiresAt: now + ttl,
      tags: options?.tags || [],
      parentTaskId: options?.parentTaskId,
      linkedTaskIds: options?.linkedTaskIds || [],
      version,
      metadata: options?.metadata
    };

    // Store the entry
    this.memory.set(entryId, entry);

    // Update indexes
    this.updateIndexes(entry);

    // Update task chain if applicable
    if (options?.parentTaskId) {
      this.updateTaskChain(taskId, agentId, entry, options.parentTaskId);
    }

    // Ensure we don't exceed memory limits
    await this.enforceMemoryLimits();

    console.log(`AgentContextMemory: Stored context for agent ${agentId}, task ${taskId}, type ${contextType} (version ${version})`);

    if (this.config.persistToDisk) {
      await this.saveToStorage();
    }

    return entryId;
  }

  /**
   * Retrieve context data by agent and task
   */
  public async getContext(agentId: string, taskId: string, contextType?: string): Promise<Record<string, any> | null> {
    const query: ContextQuery = { agentId, taskId, contextType };
    const entries = this.queryContext(query);

    if (entries.length === 0) {
      return null;
    }

    // Return the most recent entry
    const latestEntry = entries.sort((a, b) => b.timestamp - a.timestamp)[0];
    console.log(`AgentContextMemory: Retrieved context for agent ${agentId}, task ${taskId}, type ${contextType || 'any'} (version ${latestEntry.version})`);

    return this.decompressData(latestEntry.data);
  }

  /**
   * Get context from the task chain (Micromanager → Architect → Intern)
   */
  public async getChainContext(taskId: string, contextTypes?: string[]): Promise<Record<string, any>> {
    const chain = this.taskChains.get(taskId);
    if (!chain) {
      console.log(`AgentContextMemory: No task chain found for task ${taskId}`);
      return {};
    }

    const chainContext: Record<string, any> = {};

    // Collect context from all agents in the chain
    for (const entry of chain.contextFlow) {
      if (!contextTypes || contextTypes.includes(entry.contextType)) {
        const contextKey = `${entry.agentId}_${entry.contextType}`;
        chainContext[contextKey] = this.decompressData(entry.data);
      }
    }

    console.log(`AgentContextMemory: Retrieved chain context for task ${taskId} with ${Object.keys(chainContext).length} context entries`);
    return chainContext;
  }

  /**
   * Query context entries with flexible criteria
   */
  public queryContext(query: ContextQuery): ContextMemoryEntry[] {
    let candidateIds = new Set<string>();

    // Start with all entries if no specific criteria
    if (!query.agentId && !query.taskId && !query.contextType && !query.tags?.length) {
      candidateIds = new Set(this.memory.keys());
    } else {
      // Use indexes to find candidate entries
      if (query.agentId) {
        const agentEntries = this.agentIndex.get(query.agentId) || new Set();
        candidateIds = new Set(agentEntries);
      }

      if (query.taskId) {
        const taskEntries = this.taskIndex.get(query.taskId) || new Set();
        candidateIds = candidateIds.size > 0 
          ? new Set([...candidateIds].filter(id => taskEntries.has(id)))
          : taskEntries;
      }

      if (query.contextType) {
        const typeEntries = this.typeIndex.get(query.contextType) || new Set();
        candidateIds = candidateIds.size > 0
          ? new Set([...candidateIds].filter(id => typeEntries.has(id)))
          : typeEntries;
      }

      if (query.tags?.length) {
        for (const tag of query.tags) {
          const tagEntries = this.tagIndex.get(tag) || new Set();
          candidateIds = candidateIds.size > 0
            ? new Set([...candidateIds].filter(id => tagEntries.has(id)))
            : tagEntries;
        }
      }
    }

    // Filter candidates by additional criteria
    const results: ContextMemoryEntry[] = [];
    const now = Date.now();

    for (const entryId of candidateIds) {
      const entry = this.memory.get(entryId);
      if (!entry) continue;

      // Check expiration
      if (!query.includeExpired && entry.expiresAt && entry.expiresAt < now) {
        continue;
      }

      // Check time range
      if (query.timeRange) {
        if (entry.timestamp < query.timeRange.start || entry.timestamp > query.timeRange.end) {
          continue;
        }
      }

      // Check parent task
      if (query.parentTaskId && entry.parentTaskId !== query.parentTaskId) {
        continue;
      }

      results.push(entry);
    }

    return results.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Get context summary for an agent to understand what's available
   */
  public getContextSummary(agentId: string, taskId?: string): Record<string, any> {
    const query: ContextQuery = { agentId, taskId };
    const entries = this.queryContext(query);

    const summary = {
      totalEntries: entries.length,
      contextTypes: {} as Record<string, number>,
      recentEntries: [] as Array<{ type: string; timestamp: number; agentId: string }>,
      availableData: {} as Record<string, string[]>
    };

    for (const entry of entries) {
      // Count by type
      summary.contextTypes[entry.contextType] = (summary.contextTypes[entry.contextType] || 0) + 1;

      // Add to recent entries (last 10)
      if (summary.recentEntries.length < 10) {
        summary.recentEntries.push({
          type: entry.contextType,
          timestamp: entry.timestamp,
          agentId: entry.agentId
        });
      }

      // Track available data keys
      if (!summary.availableData[entry.contextType]) {
        summary.availableData[entry.contextType] = [];
      }
      summary.availableData[entry.contextType].push(...Object.keys(entry.data));
    }

    // Remove duplicates from available data
    for (const type in summary.availableData) {
      summary.availableData[type] = [...new Set(summary.availableData[type])];
    }

    return summary;
  }

  /**
   * Clear context for a specific task or agent
   */
  public async clearContext(agentId?: string, taskId?: string): Promise<number> {
    const query: ContextQuery = { agentId, taskId };
    const entries = this.queryContext(query);

    let clearedCount = 0;
    for (const entry of entries) {
      if (this.memory.delete(entry.id)) {
        this.removeFromIndexes(entry);
        clearedCount++;
      }
    }

    // Clean up task chains
    if (taskId) {
      this.taskChains.delete(taskId);
    }

    console.log(`AgentContextMemory: Cleared ${clearedCount} context entries`);

    if (this.config.persistToDisk) {
      await this.saveToStorage();
    }

    return clearedCount;
  }

  /**
   * Get memory statistics
   */
  public getStats() {
    const now = Date.now();
    const entries = Array.from(this.memory.values());
    const expiredEntries = entries.filter(e => e.expiresAt && e.expiresAt < now);

    return {
      totalEntries: entries.length,
      expiredEntries: expiredEntries.length,
      activeEntries: entries.length - expiredEntries.length,
      memoryUsage: this.calculateMemoryUsage(),
      contextTypes: this.getContextTypeStats(),
      agentDistribution: this.getAgentDistributionStats(),
      taskChains: this.taskChains.size,
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : 0,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : 0
    };
  }

  // Private helper methods
  private generateEntryId(agentId: string, taskId: string, contextType: string): string {
    return `${agentId}:${taskId}:${contextType}`;
  }

  private updateIndexes(entry: ContextMemoryEntry): void {
    // Agent index
    if (!this.agentIndex.has(entry.agentId)) {
      this.agentIndex.set(entry.agentId, new Set());
    }
    this.agentIndex.get(entry.agentId)!.add(entry.id);

    // Task index
    if (!this.taskIndex.has(entry.taskId)) {
      this.taskIndex.set(entry.taskId, new Set());
    }
    this.taskIndex.get(entry.taskId)!.add(entry.id);

    // Type index
    if (!this.typeIndex.has(entry.contextType)) {
      this.typeIndex.set(entry.contextType, new Set());
    }
    this.typeIndex.get(entry.contextType)!.add(entry.id);

    // Tag index
    for (const tag of entry.tags) {
      if (!this.tagIndex.has(tag)) {
        this.tagIndex.set(tag, new Set());
      }
      this.tagIndex.get(tag)!.add(entry.id);
    }
  }

  private removeFromIndexes(entry: ContextMemoryEntry): void {
    this.agentIndex.get(entry.agentId)?.delete(entry.id);
    this.taskIndex.get(entry.taskId)?.delete(entry.id);
    this.typeIndex.get(entry.contextType)?.delete(entry.id);
    
    for (const tag of entry.tags) {
      this.tagIndex.get(tag)?.delete(entry.id);
    }
  }

  private updateTaskChain(taskId: string, agentId: string, entry: ContextMemoryEntry, parentTaskId: string): void {
    if (!this.taskChains.has(taskId)) {
      this.taskChains.set(taskId, {
        taskId,
        agentChain: [],
        contextFlow: [],
        dependencies: []
      });
    }

    const chain = this.taskChains.get(taskId)!;
    
    // Add agent to chain if not already present
    if (!chain.agentChain.includes(agentId)) {
      chain.agentChain.push(agentId);
    }

    // Add context entry to flow
    chain.contextFlow.push(entry);

    // Add dependency
    if (!chain.dependencies.includes(parentTaskId)) {
      chain.dependencies.push(parentTaskId);
    }

    // Sort context flow by timestamp
    chain.contextFlow.sort((a, b) => a.timestamp - b.timestamp);
  }

  private compressData(data: Record<string, any>): Record<string, any> {
    // Simple compression for large data (could be enhanced with actual compression)
    const serialized = JSON.stringify(data);
    if (serialized.length > this.config.compressionThreshold) {
      // For now, just return as-is. In production, could use compression library
      console.log(`AgentContextMemory: Large data detected (${serialized.length} chars), compression recommended`);
    }
    return data;
  }

  private decompressData(data: Record<string, any>): Record<string, any> {
    // Decompress if needed (placeholder for actual decompression)
    return data;
  }

  private async enforceMemoryLimits(): Promise<void> {
    if (this.memory.size <= this.config.maxEntries) return;

    // Remove oldest entries to stay within limits
    const entries = Array.from(this.memory.values())
      .sort((a, b) => a.timestamp - b.timestamp);

    const toRemove = entries.slice(0, this.memory.size - this.config.maxEntries);
    
    for (const entry of toRemove) {
      this.memory.delete(entry.id);
      this.removeFromIndexes(entry);
    }

    console.log(`AgentContextMemory: Removed ${toRemove.length} old entries to enforce memory limits`);
  }

  private startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, 60000); // Run every minute
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    const expiredIds: string[] = [];

    for (const [id, entry] of this.memory.entries()) {
      if (entry.expiresAt && entry.expiresAt < now) {
        expiredIds.push(id);
      }
    }

    for (const id of expiredIds) {
      const entry = this.memory.get(id);
      if (entry) {
        this.memory.delete(id);
        this.removeFromIndexes(entry);
      }
    }

    if (expiredIds.length > 0) {
      console.log(`AgentContextMemory: Cleaned up ${expiredIds.length} expired entries`);
    }
  }

  private calculateMemoryUsage(): number {
    let totalSize = 0;
    for (const entry of this.memory.values()) {
      totalSize += JSON.stringify(entry).length * 2; // Rough estimate
    }
    return totalSize;
  }

  private getContextTypeStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    for (const entry of this.memory.values()) {
      stats[entry.contextType] = (stats[entry.contextType] || 0) + 1;
    }
    return stats;
  }

  private getAgentDistributionStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    for (const entry of this.memory.values()) {
      stats[entry.agentId] = (stats[entry.agentId] || 0) + 1;
    }
    return stats;
  }

  private async loadFromStorage(): Promise<void> {
    if (!this.config.persistToDisk) return;

    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('agent-context-memory');
        if (stored) {
          const data = JSON.parse(stored);
          this.deserializeMemory(data);
          console.log(`AgentContextMemory: Loaded ${this.memory.size} entries from localStorage`);
        }
      }
    } catch (error) {
      console.error('AgentContextMemory: Failed to load from storage:', error);
    }
  }

  private async saveToStorage(): Promise<void> {
    if (!this.config.persistToDisk) return;

    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const data = this.serializeMemory();
        localStorage.setItem('agent-context-memory', JSON.stringify(data));
      }
    } catch (error) {
      console.error('AgentContextMemory: Failed to save to storage:', error);
    }
  }

  private serializeMemory() {
    return {
      memory: Array.from(this.memory.entries()),
      taskChains: Array.from(this.taskChains.entries()),
      timestamp: Date.now()
    };
  }

  private deserializeMemory(data: any): void {
    if (data.memory) {
      this.memory = new Map(data.memory);
      
      // Rebuild indexes
      this.agentIndex.clear();
      this.taskIndex.clear();
      this.typeIndex.clear();
      this.tagIndex.clear();
      
      for (const entry of this.memory.values()) {
        this.updateIndexes(entry);
      }
    }
    
    if (data.taskChains) {
      this.taskChains = new Map(data.taskChains);
    }
  }
}

// Global instance
export const agentContextMemory = new AgentContextMemory();
