// components/agents/llm-response-cache.ts

export interface CachedLLMResponse {
  id: string;
  agentId: string;
  taskId: string;
  content: string;
  tokensUsed: number;
  model: string;
  provider: string;
  finishReason: string;
  responseTime: number;
  timestamp: number;
  contextHash: string; // Hash of the input context for cache key generation
  metadata?: Record<string, any>;
}

export interface LLMCacheEntry {
  response: CachedLLMResponse;
  accessCount: number;
  lastAccessed: number;
  createdAt: number;
  size: number;
}

export interface LLMCacheConfig {
  maxEntries: number;
  maxSizeBytes: number;
  defaultTTL: number; // Time to live in milliseconds
  compressionEnabled: boolean;
  persistToDisk: boolean;
}

/**
 * ✅ Task 80: LLM Response Cache
 * Persistent caching system for agent LLM outputs
 */
export class LLMResponseCache {
  private cache = new Map<string, LLMCacheEntry>();
  private accessOrder: string[] = []; // For LRU eviction
  private currentSize = 0;
  private hitCount = 0;
  private missCount = 0;

  private readonly config: LLMCacheConfig = {
    maxEntries: 1000,
    maxSizeBytes: 50 * 1024 * 1024, // 50MB
    defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
    compressionEnabled: true,
    persistToDisk: true
  };

  constructor(config?: Partial<LLMCacheConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
    this.loadFromStorage();
  }

  /**
   * Cache an LLM response
   */
  public async cacheResponse(
    agentId: string, 
    taskId: string, 
    content: string,
    tokensUsed: number = 0,
    model: string = '',
    provider: string = '',
    finishReason: string = 'stop',
    responseTime: number = 0,
    contextHash: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const responseId = this.generateResponseId(agentId, taskId);
      const now = Date.now();
      
      const cachedResponse: CachedLLMResponse = {
        id: responseId,
        agentId,
        taskId,
        content,
        tokensUsed,
        model,
        provider,
        finishReason,
        responseTime,
        timestamp: now,
        contextHash,
        metadata
      };

      const size = this.calculateSize(cachedResponse);
      
      // Ensure we have space
      await this.ensureSpace(size);

      const entry: LLMCacheEntry = {
        response: cachedResponse,
        accessCount: 0,
        lastAccessed: now,
        createdAt: now,
        size
      };

      // Remove existing entry if it exists
      if (this.cache.has(responseId)) {
        await this.remove(responseId);
      }

      this.cache.set(responseId, entry);
      this.updateAccessOrder(responseId);
      this.currentSize += size;

      console.log(`LLMResponseCache: Cached response for agent ${agentId}, task ${taskId} (${size} bytes)`);
      
      if (this.config.persistToDisk) {
        await this.saveToStorage();
      }
    } catch (error) {
      console.error('LLMResponseCache: Failed to cache response:', error);
    }
  }

  /**
   * Retrieve a cached LLM response
   */
  public async getCachedResponse(agentId: string, taskId: string): Promise<string | null> {
    try {
      const responseId = this.generateResponseId(agentId, taskId);
      const entry = this.cache.get(responseId);

      if (!entry) {
        this.missCount++;
        return null;
      }

      // Check if entry has expired
      const now = Date.now();
      const age = now - entry.createdAt;
      if (age > this.config.defaultTTL) {
        await this.remove(responseId);
        this.missCount++;
        return null;
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = now;
      this.updateAccessOrder(responseId);
      this.hitCount++;

      console.log(`LLMResponseCache: Cache hit for agent ${agentId}, task ${taskId} (accessed ${entry.accessCount} times)`);
      return entry.response.content;
    } catch (error) {
      console.error('LLMResponseCache: Failed to retrieve cached response:', error);
      this.missCount++;
      return null;
    }
  }

  /**
   * Get full cached response with metadata
   */
  public async getCachedResponseFull(agentId: string, taskId: string): Promise<CachedLLMResponse | null> {
    try {
      const responseId = this.generateResponseId(agentId, taskId);
      const entry = this.cache.get(responseId);

      if (!entry) {
        this.missCount++;
        return null;
      }

      // Check if entry has expired
      const now = Date.now();
      const age = now - entry.createdAt;
      if (age > this.config.defaultTTL) {
        await this.remove(responseId);
        this.missCount++;
        return null;
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = now;
      this.updateAccessOrder(responseId);
      this.hitCount++;

      return entry.response;
    } catch (error) {
      console.error('LLMResponseCache: Failed to retrieve full cached response:', error);
      this.missCount++;
      return null;
    }
  }

  /**
   * Check if a response is cached
   */
  public hasCachedResponse(agentId: string, taskId: string): boolean {
    const responseId = this.generateResponseId(agentId, taskId);
    const entry = this.cache.get(responseId);
    
    if (!entry) return false;
    
    // Check if expired
    const age = Date.now() - entry.createdAt;
    return age <= this.config.defaultTTL;
  }

  /**
   * Remove a cached response
   */
  public async remove(responseId: string): Promise<boolean> {
    try {
      const entry = this.cache.get(responseId);
      if (!entry) return false;

      this.cache.delete(responseId);
      this.removeFromAccessOrder(responseId);
      this.currentSize -= entry.size;

      if (this.config.persistToDisk) {
        await this.saveToStorage();
      }

      return true;
    } catch (error) {
      console.error('LLMResponseCache: Failed to remove cached response:', error);
      return false;
    }
  }

  /**
   * Clear all cached responses
   */
  public async clear(): Promise<void> {
    this.cache.clear();
    this.accessOrder = [];
    this.currentSize = 0;
    this.hitCount = 0;
    this.missCount = 0;

    if (this.config.persistToDisk) {
      await this.saveToStorage();
    }
  }

  /**
   * Get cache statistics
   */
  public getStats() {
    const totalRequests = this.hitCount + this.missCount;
    const hitRate = totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0;

    return {
      entries: this.cache.size,
      currentSizeBytes: this.currentSize,
      maxSizeBytes: this.config.maxSizeBytes,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: Math.round(hitRate * 100) / 100,
      oldestEntry: this.getOldestEntryAge(),
      newestEntry: this.getNewestEntryAge()
    };
  }

  // Private helper methods
  private generateResponseId(agentId: string, taskId: string): string {
    return `${agentId}:${taskId}`;
  }

  private calculateSize(response: CachedLLMResponse): number {
    return JSON.stringify(response).length * 2; // Rough estimate: 2 bytes per character
  }

  private async ensureSpace(requiredSize: number): Promise<void> {
    // Remove expired entries first
    await this.removeExpiredEntries();

    // If still not enough space, use LRU eviction
    while (
      (this.currentSize + requiredSize > this.config.maxSizeBytes) ||
      (this.cache.size >= this.config.maxEntries)
    ) {
      if (this.accessOrder.length === 0) break;
      
      const oldestId = this.accessOrder[0];
      await this.remove(oldestId);
    }
  }

  private async removeExpiredEntries(): Promise<void> {
    const now = Date.now();
    const expiredIds: string[] = [];

    for (const [id, entry] of this.cache.entries()) {
      const age = now - entry.createdAt;
      if (age > this.config.defaultTTL) {
        expiredIds.push(id);
      }
    }

    for (const id of expiredIds) {
      await this.remove(id);
    }
  }

  private updateAccessOrder(id: string): void {
    this.removeFromAccessOrder(id);
    this.accessOrder.push(id);
  }

  private removeFromAccessOrder(id: string): void {
    const index = this.accessOrder.indexOf(id);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  private getOldestEntryAge(): number {
    if (this.cache.size === 0) return 0;
    
    let oldest = Date.now();
    for (const entry of this.cache.values()) {
      if (entry.createdAt < oldest) {
        oldest = entry.createdAt;
      }
    }
    return Date.now() - oldest;
  }

  private getNewestEntryAge(): number {
    if (this.cache.size === 0) return 0;
    
    let newest = 0;
    for (const entry of this.cache.values()) {
      if (entry.createdAt > newest) {
        newest = entry.createdAt;
      }
    }
    return Date.now() - newest;
  }

  private async loadFromStorage(): Promise<void> {
    if (!this.config.persistToDisk) return;

    try {
      // Try to load from localStorage first
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('llm-response-cache');
        if (stored) {
          const data = JSON.parse(stored);
          this.deserializeCache(data);
          console.log(`LLMResponseCache: Loaded ${this.cache.size} entries from localStorage`);
        }
      }
    } catch (error) {
      console.error('LLMResponseCache: Failed to load from storage:', error);
    }
  }

  private async saveToStorage(): Promise<void> {
    if (!this.config.persistToDisk) return;

    try {
      // Save to localStorage
      if (typeof window !== 'undefined' && window.localStorage) {
        const data = this.serializeCache();
        localStorage.setItem('llm-response-cache', JSON.stringify(data));
      }
    } catch (error) {
      console.error('LLMResponseCache: Failed to save to storage:', error);
    }
  }

  private serializeCache() {
    return {
      entries: Array.from(this.cache.entries()),
      accessOrder: this.accessOrder,
      currentSize: this.currentSize,
      hitCount: this.hitCount,
      missCount: this.missCount,
      timestamp: Date.now()
    };
  }

  private deserializeCache(data: any): void {
    if (data.entries) {
      this.cache = new Map(data.entries);
      this.accessOrder = data.accessOrder || [];
      this.currentSize = data.currentSize || 0;
      this.hitCount = data.hitCount || 0;
      this.missCount = data.missCount || 0;
    }
  }
}

// Global instance
export const llmResponseCache = new LLMResponseCache();
