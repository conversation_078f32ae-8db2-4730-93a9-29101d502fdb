// components/agents/anthropic-models.ts
// Complete and accurate Anthropic model list based on latest known model IDs
// This ensures agents configured with Anthropic have access to all high-performance models

export interface AnthropicModel {
  id: string;
  label: string;
  description?: string;
  contextLength?: number;
  maxOutputTokens?: number; // Maximum output tokens for this model
  costPer1kTokens?: {
    input: number;
    output: number;
  };
  capabilities?: string[];
  releaseDate?: string;
}

export const ANTHROPIC_MODELS: AnthropicModel[] = [
  // Claude 4 Series (Latest Generation)
  {
    id: 'claude-opus-4-20250514',
    label: 'Claude Opus 4',
    description: 'Most powerful Claude model with exceptional reasoning and analysis capabilities',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.015,
      output: 0.075
    },
    capabilities: ['advanced-reasoning', 'complex-analysis', 'creative-writing', 'code'],
    releaseDate: '2025-05-14'
  },
  {
    id: 'claude-sonnet-4-20250514',
    label: 'Claude Sonnet 4',
    description: 'Balanced performance and speed for most use cases',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.003,
      output: 0.015
    },
    capabilities: ['general-purpose', 'code', 'complex-analysis', 'creative-writing'],
    releaseDate: '2025-05-14'
  },

  // Claude 3.7 Series (Enhanced 3.5)
  {
    id: 'claude-3-7-sonnet-20250219',
    label: 'Claude Sonnet 3.7 (Latest)',
    description: 'Enhanced version of Claude 3.5 with improved capabilities',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.003,
      output: 0.015
    },
    capabilities: ['enhanced-reasoning', 'code', 'complex-analysis', 'creative-writing'],
    releaseDate: '2025-02-19'
  },

  // Claude 3.5 Series (Current Production)
  {
    id: 'claude-3-5-haiku-20241022',
    label: 'Claude Haiku 3.5 (Latest)',
    description: 'Fastest Claude model optimized for speed and efficiency',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.00025,
      output: 0.00125
    },
    capabilities: ['fast', 'chat', 'conversational'],
    releaseDate: '2024-10-22'
  },
  {
    id: 'claude-3-5-sonnet-20241022',
    label: 'Claude Sonnet 3.5 v2 (Latest)',
    description: 'Latest version of Claude 3.5 Sonnet with improved performance',
    contextLength: 200000,
    maxOutputTokens: 8192, // Claude 3.5 Sonnet has 8192 output token limit
    costPer1kTokens: {
      input: 0.003,
      output: 0.015
    },
    capabilities: ['general-purpose', 'code', 'complex-analysis', 'creative-writing'],
    releaseDate: '2024-10-22'
  },
  {
    id: 'claude-3-5-sonnet-20240620',
    label: 'Claude Sonnet 3.5',
    description: 'Original Claude 3.5 Sonnet release',
    contextLength: 200000,
    maxOutputTokens: 8192, // Claude 3.5 Sonnet has 8192 output token limit
    costPer1kTokens: {
      input: 0.003,
      output: 0.015
    },
    capabilities: ['general-purpose', 'code', 'complex-analysis', 'creative-writing'],
    releaseDate: '2024-06-20'
  },

  // Claude 3 Series (Stable Production)
  {
    id: 'claude-3-opus-20240229',
    label: 'Claude Opus 3',
    description: 'Most powerful Claude 3 model for complex reasoning tasks',
    contextLength: 200000,
    maxOutputTokens: 4096, // Claude 3 Opus has 4096 output token limit
    costPer1kTokens: {
      input: 0.015,
      output: 0.075
    },
    capabilities: ['advanced-reasoning', 'complex-analysis', 'creative-writing'],
    releaseDate: '2024-02-29'
  },
  {
    id: 'claude-3-sonnet-20240229',
    label: 'Claude Sonnet 3',
    description: 'Balanced Claude 3 model for general use',
    contextLength: 200000,
    maxOutputTokens: 4096, // Claude 3 Sonnet has 4096 output token limit
    costPer1kTokens: {
      input: 0.003,
      output: 0.015
    },
    capabilities: ['general-purpose', 'complex-analysis', 'creative-writing'],
    releaseDate: '2024-02-29'
  },
  {
    id: 'claude-3-haiku-20240307',
    label: 'Claude Haiku 3',
    description: 'Fastest Claude 3 model for quick responses',
    contextLength: 200000,
    maxOutputTokens: 4096, // Claude Haiku has a 4096 output token limit
    costPer1kTokens: {
      input: 0.00025,
      output: 0.00125
    },
    capabilities: ['fast', 'chat'],
    releaseDate: '2024-03-07'
  }
];

// Helper functions for working with Anthropic models
export function getAnthropicModelById(id: string): AnthropicModel | undefined {
  return ANTHROPIC_MODELS.find(model => model.id === id);
}

export function getAnthropicModelOptions(): Array<{ label: string; value: string }> {
  return ANTHROPIC_MODELS.map(model => ({
    label: model.label,
    value: model.id
  }));
}

export function getLatestAnthropicModels(): AnthropicModel[] {
  // Return models from the latest generation (Claude 4 and 3.7)
  return ANTHROPIC_MODELS.filter(model =>
    model.id.includes('claude-opus-4') ||
    model.id.includes('claude-sonnet-4') ||
    model.id.includes('claude-3-7') ||
    model.id.includes('claude-3-5-sonnet-20241022') ||
    model.id.includes('claude-3-5-haiku-20241022')
  );
}

export function getAnthropicModelsByCapability(capability: string): AnthropicModel[] {
  return ANTHROPIC_MODELS.filter(model =>
    model.capabilities?.includes(capability)
  );
}

export function isValidAnthropicModel(modelId: string): boolean {
  return ANTHROPIC_MODELS.some(model => model.id === modelId);
}

export function getAnthropicModelDescription(modelId: string): string {
  const model = getAnthropicModelById(modelId);
  return model?.description || `Anthropic model: ${modelId}`;
}

export function getAnthropicModelCost(modelId: string): { input: number; output: number } | null {
  const model = getAnthropicModelById(modelId);
  return model?.costPer1kTokens || null;
}

export function getAnthropicModelMaxOutputTokens(modelId: string): number | null {
  const model = getAnthropicModelById(modelId);
  return model?.maxOutputTokens || null;
}

// Export model IDs for backward compatibility
export const ANTHROPIC_MODEL_IDS = ANTHROPIC_MODELS.map(model => model.id);

// Export simplified model map for provider registry compatibility
export const ANTHROPIC_MODEL_MAP: Record<string, string> = ANTHROPIC_MODELS.reduce((acc, model) => {
  // Create simplified keys for common usage
  const key = model.label.toLowerCase()
    .replace(/claude\s+/g, '')
    .replace(/\s+\(.+\)/g, '')
    .replace(/\s+/g, '-');
  acc[key] = model.id;
  return acc;
}, {} as Record<string, string>);

// Add direct ID mappings for exact matches
ANTHROPIC_MODELS.forEach(model => {
  ANTHROPIC_MODEL_MAP[model.id] = model.id;
});

export default ANTHROPIC_MODELS;
