// file-explorer/components/agents/task-completion-tracker.ts
// ✅ Task 85: Task completion tracker for waiting on card completion

import { kanbanEvents, KanbanCardStatusChangeEvent } from '../kanban/lib/kanban-events';

export interface TaskCompletionOptions {
  timeoutMs?: number;
  retryIntervalMs?: number;
  completedColumns?: string[];
  failedColumns?: string[];
}

export interface TaskCompletionResult {
  success: boolean;
  completedCards: string[];
  failedCards: string[];
  timedOutCards: string[];
  totalTime: number;
  error?: string;
}

/**
 * ✅ Task 85: TaskCompletionTracker
 * Waits for Kanban cards to finish execution using event-driven approach
 */
export class TaskCompletionTracker {
  private static instance: TaskCompletionTracker;
  
  // Default configuration
  private readonly defaultOptions: Required<TaskCompletionOptions> = {
    timeoutMs: 5 * 60 * 1000, // 5 minutes
    retryIntervalMs: 1000, // 1 second
    completedColumns: ['column-6'], // Done column
    failedColumns: ['column-4'] // In Review (failed) column
  };

  private constructor() {
    // Private constructor for singleton
  }

  public static getInstance(): TaskCompletionTracker {
    if (!TaskCompletionTracker.instance) {
      TaskCompletionTracker.instance = new TaskCompletionTracker();
    }
    return TaskCompletionTracker.instance;
  }

  /**
   * ✅ Task 85: Wait for cards to finish execution
   * Uses event-driven approach with timeout protection
   */
  public async waitForCardsToFinish(
    cardIds: string[], 
    options: TaskCompletionOptions = {}
  ): Promise<TaskCompletionResult> {
    const opts = { ...this.defaultOptions, ...options };
    const startTime = Date.now();
    
    console.log(`⏳ TaskCompletionTracker: Waiting for ${cardIds.length} cards to finish`, {
      cardIds,
      timeoutMs: opts.timeoutMs,
      completedColumns: opts.completedColumns,
      failedColumns: opts.failedColumns
    });

    if (cardIds.length === 0) {
      return {
        success: true,
        completedCards: [],
        failedCards: [],
        timedOutCards: [],
        totalTime: 0
      };
    }

    const result: TaskCompletionResult = {
      success: false,
      completedCards: [],
      failedCards: [],
      timedOutCards: [],
      totalTime: 0
    };

    const pendingCards = new Set(cardIds);
    let eventListener: ((data: KanbanCardStatusChangeEvent) => void) | null = null;

    return new Promise((resolve) => {
      // Set up timeout
      const timeoutId = setTimeout(() => {
        console.log(`⏰ TaskCompletionTracker: Timeout reached (${opts.timeoutMs}ms), stopping wait`);
        
        // Add remaining cards to timed out list
        result.timedOutCards = Array.from(pendingCards);
        result.success = false;
        result.error = `Timeout exceeded: ${result.timedOutCards.length} cards did not complete within ${opts.timeoutMs}ms`;
        result.totalTime = Date.now() - startTime;

        // Clean up event listener
        if (eventListener) {
          kanbanEvents.off('cardStatusChanged', eventListener);
        }

        console.log(`❌ TaskCompletionTracker: Completed with timeout`, result);
        resolve(result);
      }, opts.timeoutMs);

      // Set up event listener for card status changes
      eventListener = (data: KanbanCardStatusChangeEvent) => {
        const { cardId, newColumnId, cardTitle } = data;

        // Only process cards we're tracking
        if (!pendingCards.has(cardId)) {
          return;
        }

        console.log(`📊 TaskCompletionTracker: Card ${cardId} moved to column ${newColumnId}`);

        // Check if card completed successfully
        if (opts.completedColumns.includes(newColumnId)) {
          pendingCards.delete(cardId);
          result.completedCards.push(cardId);
          console.log(`✅ TaskCompletionTracker: Card ${cardId} (${cardTitle}) completed successfully`);
        }
        // Check if card failed
        else if (opts.failedColumns.includes(newColumnId)) {
          pendingCards.delete(cardId);
          result.failedCards.push(cardId);
          console.log(`❌ TaskCompletionTracker: Card ${cardId} (${cardTitle}) failed`);
        }

        // Check if all cards are done
        if (pendingCards.size === 0) {
          clearTimeout(timeoutId);
          kanbanEvents.off('cardStatusChanged', eventListener!);
          
          result.success = result.failedCards.length === 0;
          result.totalTime = Date.now() - startTime;
          
          console.log(`🎉 TaskCompletionTracker: All cards completed`, {
            success: result.success,
            completed: result.completedCards.length,
            failed: result.failedCards.length,
            totalTime: result.totalTime
          });
          
          resolve(result);
        }
      };

      // Register event listener
      kanbanEvents.on('cardStatusChanged', eventListener);

      // Check initial state of cards (in case some are already completed)
      this.checkInitialCardStates(cardIds, opts).then((initialResult) => {
        // Update result with initial states
        result.completedCards.push(...initialResult.completedCards);
        result.failedCards.push(...initialResult.failedCards);
        
        // Remove already completed/failed cards from pending
        initialResult.completedCards.forEach(id => pendingCards.delete(id));
        initialResult.failedCards.forEach(id => pendingCards.delete(id));

        // If all cards are already done, resolve immediately
        if (pendingCards.size === 0) {
          clearTimeout(timeoutId);
          if (eventListener) {
            kanbanEvents.off('cardStatusChanged', eventListener);
          }
          
          result.success = result.failedCards.length === 0;
          result.totalTime = Date.now() - startTime;
          
          console.log(`🚀 TaskCompletionTracker: All cards already completed`, result);
          resolve(result);
        }
      }).catch((error) => {
        console.error(`❌ TaskCompletionTracker: Error checking initial card states:`, error);
        // Continue with event-driven approach even if initial check fails
      });
    });
  }

  /**
   * Check initial state of cards to see if any are already completed/failed
   */
  private async checkInitialCardStates(
    cardIds: string[], 
    options: Required<TaskCompletionOptions>
  ): Promise<{ completedCards: string[]; failedCards: string[] }> {
    const completedCards: string[] = [];
    const failedCards: string[] = [];

    try {
      // Get current board state via Electron API
      if (typeof window !== 'undefined' && window.electronAPI) {
        const boardState = await window.electronAPI.ipc?.invoke('board:get-state', 'main');
        
        if (boardState && boardState.columns) {
          for (const column of boardState.columns) {
            for (const card of column.cards) {
              if (cardIds.includes(card.id)) {
                if (options.completedColumns.includes(column.id)) {
                  completedCards.push(card.id);
                } else if (options.failedColumns.includes(column.id)) {
                  failedCards.push(card.id);
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.error(`❌ TaskCompletionTracker: Error checking initial card states:`, error);
    }

    console.log(`🔍 TaskCompletionTracker: Initial state check - completed: ${completedCards.length}, failed: ${failedCards.length}`);
    return { completedCards, failedCards };
  }
}

// Export singleton instance
export const taskCompletionTracker = TaskCompletionTracker.getInstance();
