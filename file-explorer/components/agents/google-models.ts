// components/agents/google-models.ts

export interface GoogleModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input: number;  // per 1K tokens
    output: number; // per 1K tokens
  };
  tags?: string[];
  releaseDate?: string;
}

/**
 * Verified Google AI model metadata from official Google AI documentation
 * Only includes models with confirmed specifications
 */
export const GOOGLE_MODEL_METADATA: Record<string, GoogleModelMetadata> = {
  'gemini-pro': {
    id: 'gemini-pro',
    label: 'Gemini Pro',
    description: 'Google\'s flagship multimodal AI model for complex reasoning tasks',
    contextSize: 32768,
    pricing: {
      input: 0.000125,
      output: 0.000375
    },
    tags: ['multimodal', 'advanced-reasoning', 'vision', 'fast'],
    releaseDate: '2023-12-06'
  },
  'gemini-1.5-pro': {
    id: 'gemini-1.5-pro',
    label: 'Gemini 1.5 Pro',
    description: 'Enhanced version with extended context window and improved capabilities',
    contextSize: 1000000,
    pricing: {
      input: 0.007,
      output: 0.021
    },
    tags: ['multimodal', 'long-context', 'advanced-reasoning', 'vision', 'code'],
    releaseDate: '2024-02-15'
  },
  'gemini-1.5-flash': {
    id: 'gemini-1.5-flash',
    label: 'Gemini 1.5 Flash',
    description: 'Faster, more efficient version optimized for speed and cost',
    contextSize: 1000000,
    pricing: {
      input: 0.000075,
      output: 0.0003
    },
    tags: ['multimodal', 'fast', 'affordable', 'long-context'],
    releaseDate: '2024-05-14'
  }
};

/**
 * Get metadata for a specific Google model
 */
export function getGoogleModelMetadata(modelId: string): GoogleModelMetadata | null {
  return GOOGLE_MODEL_METADATA[modelId] || null;
}

/**
 * Check if a model has verified metadata
 */
export function hasGoogleModelMetadata(modelId: string): boolean {
  return modelId in GOOGLE_MODEL_METADATA;
}

/**
 * Get all available Google models with metadata
 */
export function getGoogleModelsWithMetadata(): GoogleModelMetadata[] {
  return Object.values(GOOGLE_MODEL_METADATA);
}

/**
 * Get model series for grouping
 */
export function getGoogleModelSeries(modelId: string): string {
  if (modelId.includes('1.5')) {
    return 'Gemini 1.5';
  }
  if (modelId.includes('gemini')) {
    return 'Gemini 1.0';
  }
  return 'Other';
}
