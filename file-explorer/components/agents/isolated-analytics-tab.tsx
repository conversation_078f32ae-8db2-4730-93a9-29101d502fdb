"use client"

import React, { useEffect } from 'react'
import AgentAnalyticsTab from '../analytics/AgentAnalyticsTab'

interface IsolatedAnalyticsTabProps {
  // Add any props needed for the analytics tab
}

/**
 * ✅ Isolated Analytics Tab Component
 *
 * This wrapper ensures proper mounting/unmounting lifecycle for the AgentAnalyticsTab
 * to prevent memory leaks and background processes when tab is not active.
 */
const IsolatedAnalyticsTab = React.memo<IsolatedAnalyticsTabProps>(() => {
  useEffect(() => {
    console.log('🔄 AgentAnalyticsTab mounted')

    return () => {
      console.log('🧹 AgentAnalyticsTab unmounted - cleaning up')

      // ✅ Clear any intervals or timers that might be running
      // This prevents memory leaks when switching tabs
      const highestTimeoutId = setTimeout(() => {}, 0)
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i)
      }

      // Clear any intervals
      const highestIntervalId = setInterval(() => {}, 0)
      for (let i = 0; i < highestIntervalId; i++) {
        clearInterval(i)
      }
    }
  }, [])

  return <AgentAnalyticsTab />
})

IsolatedAnalyticsTab.displayName = 'IsolatedAnalyticsTab'

export default IsolatedAnalyticsTab
