// components/agents/openrouter-models.ts

export interface OpenRouterModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input: number;  // per 1K tokens
    output: number; // per 1K tokens
  };
  tags?: string[];
  provider?: string; // Original provider (e.g., "Mistral", "Meta", "Anthropic")
}

/**
 * Verified OpenRouter model metadata from official OpenRouter documentation
 * Only includes models with confirmed specifications
 */
export const OPENROUTER_MODEL_METADATA: Record<string, OpenRouterModelMetadata> = {
  'mixtral': {
    id: 'mistralai/mixtral-8x7b-instruct',
    label: 'Mixtral 8x7B Instruct',
    description: 'High-quality sparse mixture of experts model with strong reasoning capabilities',
    contextSize: 32768,
    pricing: {
      input: 0.002,
      output: 0.002
    },
    tags: ['open-weight', 'advanced-reasoning', 'fast', 'mixture-of-experts'],
    provider: 'Mistral AI'
  },
  'llama-3.1-70b': {
    id: 'meta-llama/llama-3.1-70b-instruct',
    label: 'Llama 3.1 70B Instruct',
    description: 'Meta\'s flagship open-source model with excellent reasoning and code capabilities',
    contextSize: 131072,
    pricing: {
      input: 0.00088,
      output: 0.00088
    },
    tags: ['open-weight', 'advanced-reasoning', 'code', 'long-context'],
    provider: 'Meta'
  },
  'claude-3-sonnet': {
    id: 'anthropic/claude-3-sonnet',
    label: 'Claude 3 Sonnet',
    description: 'Balanced performance and speed for most use cases via OpenRouter',
    contextSize: 200000,
    pricing: {
      input: 0.003,
      output: 0.015
    },
    tags: ['advanced-reasoning', 'complex-analysis', 'creative-writing', 'long-context'],
    provider: 'Anthropic'
  },
  'gpt-4': {
    id: 'openai/gpt-4',
    label: 'GPT-4',
    description: 'OpenAI\'s flagship model via OpenRouter with superior reasoning capabilities',
    contextSize: 8192,
    pricing: {
      input: 0.03,
      output: 0.06
    },
    tags: ['advanced-reasoning', 'general-purpose', 'creative-writing'],
    provider: 'OpenAI'
  },
  'gemini-pro': {
    id: 'google/gemini-pro',
    label: 'Gemini Pro',
    description: 'Google\'s multimodal AI model via OpenRouter',
    contextSize: 32768,
    pricing: {
      input: 0.000125,
      output: 0.000375
    },
    tags: ['multimodal', 'fast', 'affordable'],
    provider: 'Google'
  }
};

/**
 * Get metadata for a specific OpenRouter model
 */
export function getOpenRouterModelMetadata(modelId: string): OpenRouterModelMetadata | null {
  return OPENROUTER_MODEL_METADATA[modelId] || null;
}

/**
 * Check if a model has verified metadata
 */
export function hasOpenRouterModelMetadata(modelId: string): boolean {
  return modelId in OPENROUTER_MODEL_METADATA;
}

/**
 * Get all available OpenRouter models with metadata
 */
export function getOpenRouterModelsWithMetadata(): OpenRouterModelMetadata[] {
  return Object.values(OPENROUTER_MODEL_METADATA);
}

/**
 * Get model provider for grouping
 */
export function getOpenRouterModelProvider(modelId: string): string {
  const metadata = getOpenRouterModelMetadata(modelId);
  return metadata?.provider || 'Unknown';
}
