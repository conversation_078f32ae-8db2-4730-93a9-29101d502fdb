// components/agents/refactor-service.tsx
// ✅ Task 66: Refactor-Aware Agent Tasks

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  RefreshCw, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Eye, 
  Code, 
  ArrowRight,
  AlertTriangle,
  Folder
} from 'lucide-react';
import { SemanticSearchService } from '@/components/background/semantic-search';
import { executionLogger } from './agent-execution-trace';

// ✅ Task 66: Refactor Operation Types
export interface RefactorOperation {
  id: string;
  type: 'rename_symbol' | 'extract_component' | 'move_function' | 'split_file' | 'update_imports';
  filePath: string;
  oldValue: string;
  newValue: string;
  lineStart?: number;
  lineEnd?: number;
  preview: string;
  dependencies?: string[]; // Files that depend on this change
}

export interface RefactorBatch {
  id: string;
  description: string;
  operations: RefactorOperation[];
  agentId: string;
  timestamp: number;
  status: 'pending' | 'previewing' | 'confirmed' | 'applied' | 'failed';
}

// ✅ Task 66: Refactor Service
class RefactorService {
  private static instance: RefactorService;
  private semanticSearch: SemanticSearchService;
  private batches: RefactorBatch[] = [];
  private listeners: ((batches: RefactorBatch[]) => void)[] = [];

  constructor() {
    this.semanticSearch = new SemanticSearchService();
  }

  static getInstance(): RefactorService {
    if (!RefactorService.instance) {
      RefactorService.instance = new RefactorService();
    }
    return RefactorService.instance;
  }

  // ✅ Task 66: Detect refactor intent from agent task
  async detectRefactorIntent(task: string): Promise<boolean> {
    const refactorPatterns = [
      /rename\s+(\w+)/i,
      /extract\s+(component|function|class)/i,
      /move\s+(\w+)\s+to/i,
      /split\s+(file|component)/i,
      /refactor\s+/i,
      /reorganize\s+/i
    ];

    return refactorPatterns.some(pattern => pattern.test(task));
  }

  // ✅ Task 66: Find all symbol occurrences using semantic search
  async findSymbolOccurrences(symbol: string, fileType?: string): Promise<{filePath: string, occurrences: number}[]> {
    try {
      executionLogger.logEvent({
        agentId: 'refactor_service',
        action: 'vector_lookup',
        details: `Finding all occurrences of symbol: ${symbol}`,
        status: 'running',
        metadata: { symbol, fileType }
      });

      const searchResults = await this.semanticSearch.searchCode({
        query: symbol,
        fileType,
        maxResults: 50,
        minSimilarity: 0.5
      });

      const occurrenceMap = new Map<string, number>();
      
      searchResults.forEach(result => {
        const count = (result.content.match(new RegExp(symbol, 'g')) || []).length;
        occurrenceMap.set(result.filePath, (occurrenceMap.get(result.filePath) || 0) + count);
      });

      const occurrences = Array.from(occurrenceMap.entries()).map(([filePath, count]) => ({
        filePath,
        occurrences: count
      }));

      executionLogger.logEvent({
        agentId: 'refactor_service',
        action: 'vector_lookup',
        details: `Found ${occurrences.length} files with symbol occurrences`,
        status: 'completed',
        metadata: { symbol, totalFiles: occurrences.length }
      });

      return occurrences;
    } catch (error) {
      executionLogger.logEvent({
        agentId: 'refactor_service',
        action: 'error',
        details: `Failed to find symbol occurrences: ${error instanceof Error ? error.message : 'Unknown error'}`,
        status: 'failed',
        metadata: { symbol, error }
      });
      return [];
    }
  }

  // ✅ Task 66: Create refactor batch
  async createRefactorBatch(
    description: string,
    agentId: string,
    operations: Omit<RefactorOperation, 'id'>[]
  ): Promise<RefactorBatch> {
    const batch: RefactorBatch = {
      id: `refactor-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      description,
      agentId,
      timestamp: Date.now(),
      status: 'pending',
      operations: operations.map((op, index) => ({
        ...op,
        id: `op-${index}-${Date.now()}`
      }))
    };

    this.batches.unshift(batch);
    this.notifyListeners();

    executionLogger.logEvent({
      agentId,
      action: 'thought',
      details: `Created refactor batch: ${description} with ${operations.length} operations`,
      status: 'completed',
      metadata: { batchId: batch.id, operationCount: operations.length }
    });

    return batch;
  }

  // ✅ Task 66: Generate rename symbol refactor
  async generateRenameRefactor(
    oldSymbol: string,
    newSymbol: string,
    agentId: string
  ): Promise<RefactorBatch> {
    const occurrences = await this.findSymbolOccurrences(oldSymbol);
    
    const operations: Omit<RefactorOperation, 'id'>[] = occurrences.map(({ filePath }) => ({
      type: 'rename_symbol',
      filePath,
      oldValue: oldSymbol,
      newValue: newSymbol,
      preview: `Rename "${oldSymbol}" to "${newSymbol}" in ${filePath}`,
      dependencies: [] // Will be populated by dependency analysis
    }));

    return this.createRefactorBatch(
      `Rename symbol "${oldSymbol}" to "${newSymbol}"`,
      agentId,
      operations
    );
  }

  // ✅ Task 66: Generate extract component refactor
  async generateExtractComponentRefactor(
    sourceFile: string,
    componentName: string,
    targetFile: string,
    agentId: string
  ): Promise<RefactorBatch> {
    const operations: Omit<RefactorOperation, 'id'>[] = [
      {
        type: 'extract_component',
        filePath: sourceFile,
        oldValue: 'selected_code_block',
        newValue: componentName,
        preview: `Extract component "${componentName}" from ${sourceFile}`,
        dependencies: [targetFile]
      },
      {
        type: 'update_imports',
        filePath: sourceFile,
        oldValue: '',
        newValue: `import { ${componentName} } from './${targetFile}';`,
        preview: `Add import for ${componentName} in ${sourceFile}`
      }
    ];

    return this.createRefactorBatch(
      `Extract component "${componentName}" to ${targetFile}`,
      agentId,
      operations
    );
  }

  // ✅ Task 66: Get all batches
  getBatches(): RefactorBatch[] {
    return [...this.batches];
  }

  // ✅ Task 66: Update batch status
  updateBatchStatus(batchId: string, status: RefactorBatch['status']): void {
    const batch = this.batches.find(b => b.id === batchId);
    if (batch) {
      batch.status = status;
      this.notifyListeners();

      executionLogger.logEvent({
        agentId: batch.agentId,
        action: status === 'applied' ? 'completion' : 'thought',
        details: `Refactor batch status updated to: ${status}`,
        status: status === 'failed' ? 'failed' : 'completed',
        metadata: { batchId, newStatus: status }
      });
    }
  }

  // ✅ Task 66: Subscribe to changes
  subscribe(listener: (batches: RefactorBatch[]) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener([...this.batches]));
  }
}

// ✅ Global refactor service instance
export const refactorService = RefactorService.getInstance();

// ✅ Task 66: Refactor Preview Component
interface RefactorPreviewProps {
  batch: RefactorBatch;
  onConfirm: (batchId: string) => void;
  onReject: (batchId: string) => void;
  className?: string;
}

export const RefactorPreview: React.FC<RefactorPreviewProps> = ({
  batch,
  onConfirm,
  onReject,
  className
}) => {
  const [selectedOperations, setSelectedOperations] = useState<Set<string>>(
    new Set(batch.operations.map(op => op.id))
  );

  const handleOperationToggle = (operationId: string, checked: boolean) => {
    const newSelected = new Set(selectedOperations);
    if (checked) {
      newSelected.add(operationId);
    } else {
      newSelected.delete(operationId);
    }
    setSelectedOperations(newSelected);
  };

  const handleConfirm = () => {
    // Only apply selected operations
    const filteredBatch = {
      ...batch,
      operations: batch.operations.filter(op => selectedOperations.has(op.id))
    };
    onConfirm(batch.id);
  };

  const getOperationIcon = (type: string) => {
    switch (type) {
      case 'rename_symbol': return <RefreshCw className="h-4 w-4 text-blue-500" />;
      case 'extract_component': return <Code className="h-4 w-4 text-green-500" />;
      case 'move_function': return <ArrowRight className="h-4 w-4 text-purple-500" />;
      case 'split_file': return <Folder className="h-4 w-4 text-orange-500" />;
      case 'update_imports': return <FileText className="h-4 w-4 text-gray-500" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Refactor Preview
            </CardTitle>
            <CardDescription>
              {batch.description} • {batch.operations.length} operations
            </CardDescription>
          </div>
          <Badge variant="outline">
            {batch.agentId}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <ScrollArea className="h-[300px] w-full border rounded-lg p-4">
          <div className="space-y-3">
            {batch.operations.map(operation => (
              <div key={operation.id} className="flex items-start gap-3 p-3 border rounded-lg">
                <Checkbox
                  checked={selectedOperations.has(operation.id)}
                  onCheckedChange={(checked) => 
                    handleOperationToggle(operation.id, checked as boolean)
                  }
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {getOperationIcon(operation.type)}
                    <span className="font-medium text-sm">{operation.type.replace('_', ' ')}</span>
                    <Badge variant="outline" className="text-xs">
                      {operation.filePath}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{operation.preview}</p>
                  {operation.dependencies && operation.dependencies.length > 0 && (
                    <div className="mt-2">
                      <span className="text-xs text-muted-foreground">Dependencies: </span>
                      {operation.dependencies.map(dep => (
                        <Badge key={dep} variant="outline" className="text-xs mr-1">
                          {dep}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <AlertTriangle className="h-4 w-4" />
            <span>{selectedOperations.size} of {batch.operations.length} operations selected</span>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onReject(batch.id)}>
              <XCircle className="h-4 w-4 mr-2" />
              Reject All
            </Button>
            <Button 
              onClick={handleConfirm}
              disabled={selectedOperations.size === 0}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Apply Selected ({selectedOperations.size})
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RefactorPreview;
