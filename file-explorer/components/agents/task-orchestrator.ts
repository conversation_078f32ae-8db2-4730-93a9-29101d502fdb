// components/agents/task-orchestrator.ts
import { AgentContext } from './agent-base';

export type AgentRole =
  | 'micromanager'
  | 'intern'
  | 'junior'
  | 'midlevel'
  | 'senior'
  | 'researcher'
  | 'architect'
  | 'designer'
  | 'tester';

export interface AgentSubtask {
  id: string;
  title: string;
  description: string;
  agent: AgentRole;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  linkedTo?: string; // parent task or card
  estimatedTokens: number;
  requiredCapabilities: string[];
  dependencies?: string[]; // IDs of other subtasks this depends on
  metadata?: Record<string, any>;
  context: Partial<AgentContext>;
}

export interface TaskDecompositionResult {
  parentTaskId: string;
  subtasks: AgentSubtask[];
  executionPlan: {
    phases: { name: string; tasks: string[]; estimatedTime: number }[];
    totalEstimatedTime: number;
    parallelizable: string[][];
  };
  metadata: {
    originalTask: string;
    complexity: 'low' | 'medium' | 'high' | 'very_high';
    taskType: string;
    decompositionStrategy: string;
  };
}

export class TaskOrchestrator {
  private static taskIdCounter = 0;
  private static lastCreatedCardIds: string[] = []; // ✅ Task 85: Track created card IDs

  /**
   * Main decomposition method - converts user input into structured subtasks
   */
  static decompose(input: string, parentTaskId?: string): TaskDecompositionResult {
    const taskId = parentTaskId || `parent-task-${Date.now()}-${++this.taskIdCounter}`;

    // Analyze the input to determine task type and complexity
    const analysis = this.analyzeTask(input);

    // Generate subtasks based on analysis
    const subtasks = this.generateSubtasks(input, analysis, taskId);

    // Create execution plan
    const executionPlan = this.createExecutionPlan(subtasks);

    return {
      parentTaskId: taskId,
      subtasks,
      executionPlan,
      metadata: {
        originalTask: input,
        complexity: analysis.complexity,
        taskType: analysis.type,
        decompositionStrategy: analysis.strategy
      }
    };
  }

  /**
   * Analyze task to determine type, complexity, and decomposition strategy
   */
  private static analyzeTask(input: string): {
    type: string;
    complexity: 'low' | 'medium' | 'high' | 'very_high';
    strategy: string;
    keywords: string[];
    domains: string[];
  } {
    const lowerInput = input.toLowerCase();
    const keywords = this.extractKeywords(lowerInput);
    const domains = this.identifyDomains(lowerInput);

    // Determine task type
    let type = 'general';
    if (keywords.some(k => ['design', 'ui', 'interface', 'wireframe', 'mockup'].includes(k))) {
      type = 'design';
    } else if (keywords.some(k => ['auth', 'login', 'signup', 'authentication', 'security'].includes(k))) {
      type = 'authentication';
    } else if (keywords.some(k => ['api', 'backend', 'server', 'database', 'endpoint'].includes(k))) {
      type = 'backend';
    } else if (keywords.some(k => ['frontend', 'component', 'react', 'vue', 'angular'].includes(k))) {
      type = 'frontend';
    } else if (keywords.some(k => ['test', 'testing', 'unit', 'integration', 'e2e'].includes(k))) {
      type = 'testing';
    }

    // Determine complexity based on multiple factors
    let complexity: 'low' | 'medium' | 'high' | 'very_high' = 'low';
    let complexityScore = 0;

    // Length and detail indicators
    if (input.length > 200) complexityScore += 2;
    else if (input.length > 100) complexityScore += 1;

    // Multiple domain indicators
    if (domains.length > 2) complexityScore += 2;
    else if (domains.length > 1) complexityScore += 1;

    // Complex keywords
    const complexKeywords = ['system', 'architecture', 'integration', 'microservice', 'scalable', 'distributed'];
    complexityScore += keywords.filter(k => complexKeywords.includes(k)).length;

    // Multiple action indicators
    const actions = keywords.filter(k => ['create', 'build', 'implement', 'design', 'develop', 'setup'].includes(k));
    if (actions.length > 3) complexityScore += 2;
    else if (actions.length > 1) complexityScore += 1;

    if (complexityScore >= 6) complexity = 'very_high';
    else if (complexityScore >= 4) complexity = 'high';
    else if (complexityScore >= 2) complexity = 'medium';

    // Determine strategy
    const strategy = complexity === 'low' ? 'single-agent' : 'multi-agent-decomposition';

    return { type, complexity, strategy, keywords, domains };
  }

  /**
   * Extract relevant keywords from input
   */
  private static extractKeywords(input: string): string[] {
    const keywords = [
      // Actions
      'create', 'build', 'implement', 'design', 'develop', 'setup', 'configure', 'deploy',
      'test', 'validate', 'optimize', 'refactor', 'migrate', 'integrate',

      // Technologies
      'react', 'vue', 'angular', 'node', 'express', 'fastapi', 'django', 'flask',
      'mongodb', 'postgresql', 'mysql', 'redis', 'docker', 'kubernetes',

      // Domains
      'frontend', 'backend', 'fullstack', 'api', 'database', 'auth', 'authentication',
      'ui', 'ux', 'design', 'component', 'service', 'microservice', 'system',

      // Features
      'login', 'signup', 'dashboard', 'admin', 'user', 'profile', 'settings',
      'search', 'filter', 'pagination', 'notification', 'email', 'payment'
    ];

    return keywords.filter(keyword => input.includes(keyword));
  }

  /**
   * Identify technical domains involved
   */
  private static identifyDomains(input: string): string[] {
    const domains = [];

    if (input.match(/frontend|ui|interface|component|react|vue|angular/)) {
      domains.push('frontend');
    }
    if (input.match(/backend|api|server|database|endpoint/)) {
      domains.push('backend');
    }
    if (input.match(/design|wireframe|mockup|prototype|ux/)) {
      domains.push('design');
    }
    if (input.match(/test|testing|unit|integration|e2e/)) {
      domains.push('testing');
    }
    if (input.match(/deploy|devops|docker|kubernetes|ci\/cd/)) {
      domains.push('devops');
    }
    if (input.match(/auth|login|signup|security|permission/)) {
      domains.push('security');
    }

    return domains;
  }

  /**
   * Generate subtasks based on analysis
   */
  private static generateSubtasks(
    input: string,
    analysis: any,
    parentTaskId: string
  ): AgentSubtask[] {
    const subtasks: AgentSubtask[] = [];

    // For simple tasks, create a single subtask
    if (analysis.complexity === 'low') {
      subtasks.push(this.createSubtask(
        `${parentTaskId}-main`,
        input,
        input,
        this.selectAgentForTask(analysis.type, 'simple'),
        'medium',
        parentTaskId,
        this.estimateTokens(input),
        this.getRequiredCapabilities(analysis.type),
        { task: input, metadata: { taskType: analysis.type, complexity: 'simple' } }
      ));
      return subtasks;
    }

    // For complex tasks, decompose into logical phases
    let taskCounter = 1;

    // Research phase for complex tasks
    if (analysis.complexity === 'high' || analysis.complexity === 'very_high') {
      subtasks.push(this.createSubtask(
        `${parentTaskId}-research-${taskCounter++}`,
        'Research and Analysis',
        `Research requirements and analyze approach for: ${input}`,
        'researcher',
        'high',
        parentTaskId,
        800,
        ['research', 'analysis', 'documentation'],
        { task: `Research and analyze: ${input}`, metadata: { phase: 'research' } }
      ));
    }

    // Architecture phase for complex systems
    if (analysis.domains.length > 1 || analysis.keywords.includes('system')) {
      subtasks.push(this.createSubtask(
        `${parentTaskId}-architecture-${taskCounter++}`,
        'System Architecture',
        `Design system architecture for: ${input}`,
        'architect',
        'high',
        parentTaskId,
        1000,
        ['system_design', 'architecture', 'planning'],
        { task: `Design architecture for: ${input}`, metadata: { phase: 'architecture' } },
        analysis.complexity === 'high' || analysis.complexity === 'very_high' ? [`${parentTaskId}-research-${taskCounter-2}`] : undefined
      ));
    }

    // Design phase for UI/UX tasks
    if (analysis.domains.includes('design') || analysis.domains.includes('frontend')) {
      subtasks.push(this.createSubtask(
        `${parentTaskId}-design-${taskCounter++}`,
        'UI/UX Design',
        `Create design and wireframes for: ${input}`,
        'designer',
        'medium',
        parentTaskId,
        600,
        ['ui_design', 'wireframing', 'prototyping'],
        { task: `Design UI for: ${input}`, metadata: { phase: 'design' } }
      ));
    }

    // Implementation phases based on domains
    if (analysis.domains.includes('backend')) {
      subtasks.push(this.createSubtask(
        `${parentTaskId}-backend-${taskCounter++}`,
        'Backend Implementation',
        `Implement backend functionality for: ${input}`,
        this.selectAgentForComplexity(analysis.complexity),
        'high',
        parentTaskId,
        1200,
        ['backend_development', 'api_design', 'database_design'],
        { task: `Implement backend for: ${input}`, metadata: { phase: 'backend' } }
      ));
    }

    if (analysis.domains.includes('frontend')) {
      subtasks.push(this.createSubtask(
        `${parentTaskId}-frontend-${taskCounter++}`,
        'Frontend Implementation',
        `Implement frontend functionality for: ${input}`,
        this.selectAgentForComplexity(analysis.complexity),
        'high',
        parentTaskId,
        1000,
        ['frontend_development', 'component_creation', 'state_management'],
        { task: `Implement frontend for: ${input}`, metadata: { phase: 'frontend' } }
      ));
    }

    // Testing phase
    if (analysis.complexity !== 'low') {
      subtasks.push(this.createSubtask(
        `${parentTaskId}-testing-${taskCounter++}`,
        'Testing and Validation',
        `Test and validate implementation for: ${input}`,
        'tester',
        'medium',
        parentTaskId,
        500,
        ['testing', 'validation', 'quality_assurance'],
        { task: `Test and validate: ${input}`, metadata: { phase: 'testing' } },
        subtasks.filter(t => t.metadata?.phase !== 'research' && t.metadata?.phase !== 'architecture').map(t => t.id)
      ));
    }

    return subtasks;
  }

  /**
   * Create a subtask object
   */
  private static createSubtask(
    id: string,
    title: string,
    description: string,
    agent: AgentRole,
    priority: 'low' | 'medium' | 'high' | 'urgent',
    linkedTo: string,
    estimatedTokens: number,
    requiredCapabilities: string[],
    context: Partial<AgentContext>,
    dependencies?: string[]
  ): AgentSubtask {
    return {
      id,
      title,
      description,
      agent,
      priority,
      linkedTo,
      estimatedTokens,
      requiredCapabilities,
      dependencies,
      context,
      metadata: {
        createdAt: Date.now(),
        ...context.metadata
      }
    };
  }

  /**
   * Select appropriate agent based on task type
   */
  private static selectAgentForTask(taskType: string, complexity: string): AgentRole {
    switch (taskType) {
      case 'design':
        return 'designer';
      case 'authentication':
      case 'backend':
        return complexity === 'simple' ? 'midlevel' : 'senior';
      case 'frontend':
        return complexity === 'simple' ? 'junior' : 'midlevel';
      case 'testing':
        return 'tester';
      default:
        return complexity === 'simple' ? 'junior' : 'midlevel';
    }
  }

  /**
   * Select agent based on complexity level
   */
  private static selectAgentForComplexity(complexity: string): AgentRole {
    switch (complexity) {
      case 'low':
        return 'intern';
      case 'medium':
        return 'junior';
      case 'high':
        return 'midlevel';
      case 'very_high':
        return 'senior';
      default:
        return 'junior';
    }
  }

  /**
   * Get required capabilities for task type
   */
  private static getRequiredCapabilities(taskType: string): string[] {
    const capabilityMap: Record<string, string[]> = {
      design: ['ui_design', 'wireframing', 'prototyping'],
      authentication: ['security', 'backend_development', 'database_design'],
      backend: ['backend_development', 'api_design', 'database_design'],
      frontend: ['frontend_development', 'component_creation', 'state_management'],
      testing: ['testing', 'validation', 'quality_assurance'],
      general: ['code_generation', 'problem_solving']
    };

    return capabilityMap[taskType] || capabilityMap.general;
  }

  /**
   * Estimate token usage for a task
   */
  private static estimateTokens(task: string): number {
    const baseTokens = Math.ceil(task.length / 4); // 4 chars per token estimate
    const complexityMultiplier = task.length > 100 ? 2 : 1.5;
    return Math.ceil(baseTokens * complexityMultiplier);
  }

  /**
   * Create execution plan with phases and dependencies
   */
  private static createExecutionPlan(subtasks: AgentSubtask[]): {
    phases: { name: string; tasks: string[]; estimatedTime: number }[];
    totalEstimatedTime: number;
    parallelizable: string[][];
  } {
    const phases: { name: string; tasks: string[]; estimatedTime: number }[] = [];
    const completed = new Set<string>();
    const remaining = new Set(subtasks.map(t => t.id));

    // Group tasks into phases based on dependencies
    while (remaining.size > 0) {
      const ready = subtasks.filter(task =>
        remaining.has(task.id) &&
        (!task.dependencies || task.dependencies.every(dep => completed.has(dep)))
      );

      if (ready.length === 0) break; // Circular dependency or error

      const phaseTime = Math.max(...ready.map(t => t.estimatedTokens / 100)); // Rough time estimate
      phases.push({
        name: `Phase ${phases.length + 1}`,
        tasks: ready.map(t => t.id),
        estimatedTime: phaseTime
      });

      ready.forEach(task => {
        completed.add(task.id);
        remaining.delete(task.id);
      });
    }

    const totalEstimatedTime = phases.reduce((sum, phase) => sum + phase.estimatedTime, 0);
    const parallelizable = phases.filter(p => p.tasks.length > 1).map(p => p.tasks);

    return { phases, totalEstimatedTime, parallelizable };
  }

  // ✅ Task 85: Methods for tracking created card IDs

  /**
   * Set the last created card IDs for completion tracking
   */
  static setLastCreatedCardIds(cardIds: string[]): void {
    this.lastCreatedCardIds = [...cardIds];
    console.log(`🎯 TaskOrchestrator: Tracking ${cardIds.length} card IDs for completion:`, cardIds);
  }

  /**
   * Get the last created card IDs for completion tracking
   */
  static getLastCreatedCardIds(): string[] {
    return [...this.lastCreatedCardIds];
  }

  /**
   * Clear the tracked card IDs
   */
  static clearLastCreatedCardIds(): void {
    this.lastCreatedCardIds = [];
  }
}
