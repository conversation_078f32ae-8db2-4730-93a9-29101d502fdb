// components/agents/pricing-display.tsx
import React from 'react';

export interface PricingData {
  input?: number;
  output?: number;
}

interface PricingDisplayProps {
  pricing?: PricingData | null;
  className?: string;
  showIcon?: boolean;
}

export const PricingDisplay: React.FC<PricingDisplayProps> = ({
  pricing,
  className = "",
  showIcon = true
}) => {
  const formatPrice = (price: number | undefined): string => {
    if (price === undefined || price === null) {
      return "Unavailable";
    }
    
    // Format price with appropriate precision
    if (price === 0) {
      return "$0.00";
    } else if (price < 0.001) {
      return `$${price.toFixed(6)}`;
    } else if (price < 0.01) {
      return `$${price.toFixed(5)}`;
    } else if (price < 0.1) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(3)}`;
    }
  };

  const inputPrice = formatPrice(pricing?.input);
  const outputPrice = formatPrice(pricing?.output);

  return (
    <div className={`text-xs ${className}`}>
      <div className="flex items-center gap-1">
        {showIcon && <span>💰</span>}
        <span className="font-medium">Pricing per 1K tokens</span>
      </div>
      <div className="mt-1">
        <span>Input: {inputPrice} | Output: {outputPrice}</span>
      </div>
    </div>
  );
};

export default PricingDisplay;
