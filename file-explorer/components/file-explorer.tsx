"use client"

import { useState } from "react"
import {
  ChevronRight,
  FileText,
  FileImage,
  FileCode,
  FileArchive,
  FileAudio,
  FileVideo,
  Folder,
  Search,
  Grid2X2,
  List,
  SortAsc,
  Plus,
  Home,
  Star,
  Download,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// File type icons mapping
const fileIcons = {
  pdf: <FileText className="h-10 w-10 text-red-500" />,
  doc: <FileText className="h-10 w-10 text-blue-500" />,
  docx: <FileText className="h-10 w-10 text-blue-500" />,
  xls: <FileText className="h-10 w-10 text-green-500" />,
  xlsx: <FileText className="h-10 w-10 text-green-500" />,
  ppt: <FileText className="h-10 w-10 text-orange-500" />,
  pptx: <FileText className="h-10 w-10 text-orange-500" />,
  jpg: <FileImage className="h-10 w-10 text-purple-500" />,
  jpeg: <FileImage className="h-10 w-10 text-purple-500" />,
  png: <FileImage className="h-10 w-10 text-purple-500" />,
  gif: <FileImage className="h-10 w-10 text-purple-500" />,
  svg: <FileImage className="h-10 w-10 text-purple-500" />,
  mp3: <FileAudio className="h-10 w-10 text-pink-500" />,
  wav: <FileAudio className="h-10 w-10 text-pink-500" />,
  mp4: <FileVideo className="h-10 w-10 text-indigo-500" />,
  mov: <FileVideo className="h-10 w-10 text-indigo-500" />,
  zip: <FileArchive className="h-10 w-10 text-yellow-500" />,
  rar: <FileArchive className="h-10 w-10 text-yellow-500" />,
  html: <FileCode className="h-10 w-10 text-cyan-500" />,
  css: <FileCode className="h-10 w-10 text-cyan-500" />,
  js: <FileCode className="h-10 w-10 text-yellow-500" />,
  jsx: <FileCode className="h-10 w-10 text-yellow-500" />,
  ts: <FileCode className="h-10 w-10 text-blue-500" />,
  tsx: <FileCode className="h-10 w-10 text-blue-500" />,
  json: <FileCode className="h-10 w-10 text-gray-500" />,
  md: <FileText className="h-10 w-10 text-gray-500" />,
  txt: <FileText className="h-10 w-10 text-gray-500" />,
}

// Empty data structure
const emptyData = {
  folders: [],
  files: [],
}

export default function FileExplorer() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [currentFolder, setCurrentFolder] = useState<string>("Home")
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    favorites: true,
    locations: true,
  })

  // Filter files based on search query
  const filteredFiles = emptyData.files.filter((file) => file.name.toLowerCase().includes(searchQuery.toLowerCase()))

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  return (
    <div className="flex h-screen bg-white">
      {/* Sidebar */}
      <div className="w-64 border-r border-gray-200 bg-gray-50 p-4 flex flex-col">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">File Explorer</h2>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search files..."
              className="pl-8 bg-white"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {/* Favorites Section */}
        <div className="mb-4">
          <div
            className="flex items-center justify-between cursor-pointer py-1 text-gray-700 hover:text-gray-900"
            onClick={() => toggleSection("favorites")}
          >
            <span className="font-medium">Favorites</span>
            <ChevronRight
              className={cn("h-4 w-4 transition-transform", expandedSections.favorites ? "rotate-90" : "")}
            />
          </div>
          {expandedSections.favorites && (
            <div className="ml-2 mt-1 space-y-1">
              <div className="flex items-center py-1 px-2 text-sm rounded-md hover:bg-gray-200 cursor-pointer">
                <Star className="h-4 w-4 mr-2 text-yellow-500" />
                <span>Starred Files</span>
              </div>
              <div className="flex items-center py-1 px-2 text-sm rounded-md hover:bg-gray-200 cursor-pointer">
                <Download className="h-4 w-4 mr-2 text-gray-500" />
                <span>Recent Downloads</span>
              </div>
            </div>
          )}
        </div>

        {/* Locations Section */}
        <div className="mb-4">
          <div
            className="flex items-center justify-between cursor-pointer py-1 text-gray-700 hover:text-gray-900"
            onClick={() => toggleSection("locations")}
          >
            <span className="font-medium">Locations</span>
            <ChevronRight
              className={cn("h-4 w-4 transition-transform", expandedSections.locations ? "rotate-90" : "")}
            />
          </div>
          {expandedSections.locations && (
            <div className="ml-2 mt-1 space-y-1">
              <div
                className={cn(
                  "flex items-center py-1 px-2 text-sm rounded-md hover:bg-gray-200 cursor-pointer",
                  currentFolder === "Home" && "bg-gray-200 font-medium",
                )}
                onClick={() => setCurrentFolder("Home")}
              >
                <Home className="h-4 w-4 mr-2 text-gray-500" />
                <span>Home</span>
              </div>
            </div>
          )}
        </div>

        <div className="mt-auto pt-4 border-t border-gray-200">
          <div className="flex items-center text-sm text-gray-500">
            <div className="flex-1">Storage</div>
            <div>0 GB / 100 GB</div>
          </div>
          <div className="w-full h-1.5 bg-gray-200 rounded-full mt-2">
            <div className="h-full bg-blue-500 rounded-full" style={{ width: "0%" }}></div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="border-b border-gray-200 p-4 flex items-center justify-between">
          <div className="flex items-center space-x-1 text-sm text-gray-600">
            <Button variant="ghost" size="sm" className="h-8">
              <Home className="h-4 w-4 mr-1" />
              <span>Home</span>
            </Button>
            {currentFolder !== "Home" && (
              <>
                <span>/</span>
                <Button variant="ghost" size="sm" className="h-8">
                  {currentFolder}
                </Button>
              </>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={cn(viewMode === "grid" && "bg-gray-100")}
                    onClick={() => setViewMode("grid")}
                  >
                    <Grid2X2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Grid view</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={cn(viewMode === "list" && "bg-gray-100")}
                    onClick={() => setViewMode("list")}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>List view</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <SortAsc className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Sort files</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <Plus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>New item</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Files and Folders */}
        <div className="flex-1 p-6 overflow-auto">
          {/* Empty state */}
          <div className="h-full flex flex-col items-center justify-center text-gray-500">
            <Folder className="h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium mb-2">No files or folders</h3>
            <p className="text-sm text-center max-w-md mb-6">
              Your files and folders will appear here. Click the + button to create a new item.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create New Item
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
