// components/adapters/taskmaster-adapter.ts
import { activeProjectService } from '../../services/active-project-service';

export interface AgentTask {
  id: string;
  title: string;
  description: string;
  assignedAgentId: string;
  dependencies: string[];
  priority: 'low' | 'medium' | 'high';
  // Additional metadata from Taskmaster
  module?: string;
  milestone?: string;
  phase?: string;
  estimatedHours?: number;
  complexity?: 'simple' | 'moderate' | 'complex';
  tags?: string[];
  acceptanceCriteria?: string[];
  subtasks?: string[];
  dueDate?: string;
  storyPoints?: number;
}

export interface TaskmasterData {
  tasks: AgentTask[];
  metadata?: {
    projectName?: string;
    version?: string;
    generatedAt?: string;
    totalTasks?: number;
    modules?: string[];
    milestones?: string[];
  };
}

export interface TaskLoadResult {
  success: boolean;
  data?: TaskmasterData;
  error?: string;
  filePath?: string;
}

export class TaskmasterAdapter {
  private static instance: TaskmasterAdapter;
  private cachedTasks: TaskmasterData | null = null;
  private lastLoadTime: number = 0;
  private readonly CACHE_DURATION = 30000; // 30 seconds

  private constructor() {}

  public static getInstance(): TaskmasterAdapter {
    if (!TaskmasterAdapter.instance) {
      TaskmasterAdapter.instance = new TaskmasterAdapter();
    }
    return TaskmasterAdapter.instance;
  }

  /**
   * ✅ Load and parse tasks.json from current project's .taskmaster directory
   */
  public async loadTasks(projectPath?: string, forceReload = false): Promise<TaskLoadResult> {
    try {
      // Use cache if available and not expired
      if (!forceReload && this.cachedTasks && (Date.now() - this.lastLoadTime) < this.CACHE_DURATION) {
        console.log('📋 TaskmasterAdapter: Using cached tasks');
        return {
          success: true,
          data: this.cachedTasks
        };
      }

      const targetProjectPath = projectPath || activeProjectService.getActiveProject()?.path;

      if (!targetProjectPath) {
        return {
          success: false,
          error: 'No active project selected. Cannot load Taskmaster tasks.'
        };
      }

      const tasksFilePath = `${targetProjectPath}/.taskmaster/tasks.json`;

      if (typeof window !== 'undefined' && window.electronAPI) {
        console.log(`📋 TaskmasterAdapter: Loading tasks from ${tasksFilePath}`);

        const tasksFile = await window.electronAPI.readFile(tasksFilePath);

        if (!tasksFile.success) {
          return {
            success: false,
            error: `Tasks file not found: ${tasksFilePath}. Run Taskmaster CLI to generate tasks first.`,
            filePath: tasksFilePath
          };
        }

        try {
          const rawData = JSON.parse(tasksFile.content);
          const validatedData = this.validateAndNormalizeTasks(rawData);

          // Cache the validated data
          this.cachedTasks = validatedData;
          this.lastLoadTime = Date.now();

          console.log(`✅ TaskmasterAdapter: Loaded ${validatedData.tasks.length} tasks successfully`);

          return {
            success: true,
            data: validatedData,
            filePath: tasksFilePath
          };
        } catch (parseError) {
          return {
            success: false,
            error: `Invalid JSON in tasks file: ${parseError instanceof Error ? parseError.message : 'Parse error'}`,
            filePath: tasksFilePath
          };
        }
      } else {
        return {
          success: false,
          error: 'File system operations not available (Electron API required)'
        };
      }
    } catch (error) {
      console.error('TaskmasterAdapter: Error loading tasks:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * ✅ Validate and normalize tasks from Taskmaster JSON
   */
  private validateAndNormalizeTasks(rawData: any): TaskmasterData {
    if (!rawData || typeof rawData !== 'object') {
      throw new Error('Invalid tasks data: must be an object');
    }

    let tasks: any[] = [];
    let metadata: any = {};

    // Handle different JSON structures from Taskmaster
    if (Array.isArray(rawData)) {
      // Simple array of tasks
      tasks = rawData;
    } else if (rawData.tasks && Array.isArray(rawData.tasks)) {
      // Object with tasks array
      tasks = rawData.tasks;
      metadata = rawData.metadata || {};
    } else if (rawData.project && rawData.project.tasks) {
      // Nested project structure
      tasks = rawData.project.tasks;
      metadata = rawData.project.metadata || rawData.metadata || {};
    } else {
      // Try to extract tasks from object values
      const possibleTasks = Object.values(rawData).filter(item =>
        typeof item === 'object' && item !== null && 'title' in item
      );
      if (possibleTasks.length > 0) {
        tasks = possibleTasks;
      } else {
        throw new Error('No valid tasks found in JSON structure');
      }
    }

    if (!Array.isArray(tasks) || tasks.length === 0) {
      throw new Error('No tasks found in Taskmaster data');
    }

    // Validate and normalize each task
    const validatedTasks: AgentTask[] = tasks.map((task, index) => {
      return this.validateAndNormalizeTask(task, index);
    });

    // Extract metadata
    const normalizedMetadata = {
      projectName: metadata.projectName || metadata.name || 'Unknown Project',
      version: metadata.version || '1.0.0',
      generatedAt: metadata.generatedAt || metadata.timestamp || new Date().toISOString(),
      totalTasks: validatedTasks.length,
      modules: this.extractUniqueValues(validatedTasks, 'module'),
      milestones: this.extractUniqueValues(validatedTasks, 'milestone')
    };

    return {
      tasks: validatedTasks,
      metadata: normalizedMetadata
    };
  }

  /**
   * ✅ Validate and normalize individual task
   */
  private validateAndNormalizeTask(task: any, index: number): AgentTask {
    if (!task || typeof task !== 'object') {
      throw new Error(`Task ${index} is not a valid object`);
    }

    // Required fields
    const id = task.id || task.taskId || `task-${Date.now()}-${index}`;
    const title = task.title || task.name || task.summary || `Task ${index + 1}`;
    const description = task.description || task.details || task.content || title;

    // Agent assignment - try multiple possible fields
    let assignedAgentId = task.assignedAgentId || task.agentId || task.agent || task.assignee;

    // Map common agent names to our agent IDs
    if (typeof assignedAgentId === 'string') {
      assignedAgentId = this.normalizeAgentId(assignedAgentId);
    } else {
      // Default assignment based on task type or complexity
      assignedAgentId = this.inferAgentFromTask(task);
    }

    // Dependencies
    const dependencies = this.normalizeDependencies(task.dependencies || task.dependsOn || task.requires || []);

    // Priority
    const priority = this.normalizePriority(task.priority || task.importance || 'medium');

    // Optional fields
    const module = task.module || task.component || task.area;
    const milestone = task.milestone || task.epic || task.release;
    const phase = task.phase || task.stage || task.status;
    const estimatedHours = this.parseNumber(task.estimatedHours || task.effort || task.hours);
    const complexity = this.normalizeComplexity(task.complexity || task.difficulty);
    const tags = this.normalizeTags(task.tags || task.labels || []);
    const acceptanceCriteria = this.normalizeStringArray(task.acceptanceCriteria || task.criteria || task.acceptance || []);
    const subtasks = this.normalizeStringArray(task.subtasks || task.steps || task.checklist || []);
    const dueDate = task.dueDate || task.deadline || task.targetDate;
    const storyPoints = this.parseNumber(task.storyPoints || task.points || task.estimate);

    return {
      id,
      title,
      description,
      assignedAgentId,
      dependencies,
      priority,
      module,
      milestone,
      phase,
      estimatedHours,
      complexity,
      tags,
      acceptanceCriteria,
      subtasks,
      dueDate,
      storyPoints
    };
  }

  /**
   * ✅ Normalize agent ID to match our agent system
   */
  private normalizeAgentId(agentName: string): string {
    const agentMap: Record<string, string> = {
      'intern': 'intern',
      'junior': 'junior',
      'mid': 'midlevel',
      'midlevel': 'midlevel',
      'senior': 'senior',
      'architect': 'architect',
      'designer': 'designer',
      'tester': 'tester',
      'researcher': 'researcher',
      'micromanager': 'micromanager',
      // Common variations
      'dev': 'midlevel',
      'developer': 'midlevel',
      'qa': 'tester',
      'test': 'tester',
      'design': 'designer',
      'ui': 'designer',
      'ux': 'designer',
      'backend': 'senior',
      'frontend': 'midlevel',
      'fullstack': 'senior'
    };

    const normalized = agentName.toLowerCase().trim();
    return agentMap[normalized] || 'midlevel'; // Default to midlevel
  }

  /**
   * ✅ Infer agent assignment from task characteristics
   */
  private inferAgentFromTask(task: any): string {
    const title = (task.title || '').toLowerCase();
    const description = (task.description || '').toLowerCase();
    const content = `${title} ${description}`;

    // Design tasks
    if (content.includes('design') || content.includes('ui') || content.includes('ux') || content.includes('mockup')) {
      return 'designer';
    }

    // Testing tasks
    if (content.includes('test') || content.includes('qa') || content.includes('bug') || content.includes('verify')) {
      return 'tester';
    }

    // Architecture tasks
    if (content.includes('architect') || content.includes('structure') || content.includes('framework') || content.includes('system')) {
      return 'architect';
    }

    // Research tasks
    if (content.includes('research') || content.includes('investigate') || content.includes('analyze') || content.includes('study')) {
      return 'researcher';
    }

    // Complex tasks go to senior
    if (content.includes('complex') || content.includes('advanced') || content.includes('optimize') || content.includes('performance')) {
      return 'senior';
    }

    // Simple tasks go to intern
    if (content.includes('simple') || content.includes('basic') || content.includes('documentation') || content.includes('setup')) {
      return 'intern';
    }

    // Default to midlevel for general development
    return 'midlevel';
  }

  /**
   * ✅ Normalize dependencies array
   */
  private normalizeDependencies(deps: any): string[] {
    if (!deps) return [];
    if (typeof deps === 'string') return [deps];
    if (Array.isArray(deps)) return deps.filter(d => typeof d === 'string');
    return [];
  }

  /**
   * ✅ Normalize priority value
   */
  private normalizePriority(priority: any): 'low' | 'medium' | 'high' {
    if (typeof priority !== 'string') return 'medium';

    const p = priority.toLowerCase();
    if (p.includes('high') || p.includes('urgent') || p.includes('critical')) return 'high';
    if (p.includes('low') || p.includes('minor')) return 'low';
    return 'medium';
  }

  /**
   * ✅ Normalize complexity value
   */
  private normalizeComplexity(complexity: any): 'simple' | 'moderate' | 'complex' | undefined {
    if (typeof complexity !== 'string') return undefined;

    const c = complexity.toLowerCase();
    if (c.includes('simple') || c.includes('easy') || c.includes('basic')) return 'simple';
    if (c.includes('complex') || c.includes('hard') || c.includes('difficult') || c.includes('advanced')) return 'complex';
    if (c.includes('moderate') || c.includes('medium') || c.includes('normal')) return 'moderate';
    return undefined;
  }

  /**
   * ✅ Normalize tags array
   */
  private normalizeTags(tags: any): string[] {
    if (!tags) return [];
    if (typeof tags === 'string') return tags.split(',').map(t => t.trim()).filter(Boolean);
    if (Array.isArray(tags)) return tags.filter(t => typeof t === 'string').map(t => t.trim()).filter(Boolean);
    return [];
  }

  /**
   * ✅ Normalize string array
   */
  private normalizeStringArray(arr: any): string[] {
    if (!arr) return [];
    if (typeof arr === 'string') return [arr];
    if (Array.isArray(arr)) return arr.filter(item => typeof item === 'string');
    return [];
  }

  /**
   * ✅ Parse number safely
   */
  private parseNumber(value: any): number | undefined {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? undefined : parsed;
    }
    return undefined;
  }

  /**
   * ✅ Extract unique values from tasks for a specific field
   */
  private extractUniqueValues(tasks: AgentTask[], field: keyof AgentTask): string[] {
    const values = tasks
      .map(task => task[field])
      .filter((value): value is string => typeof value === 'string' && value.length > 0);

    return Array.from(new Set(values)).sort();
  }

  /**
   * ✅ Get cached tasks without reloading
   */
  public getCachedTasks(): TaskmasterData | null {
    return this.cachedTasks;
  }

  /**
   * ✅ Clear cache to force reload on next request
   */
  public clearCache(): void {
    this.cachedTasks = null;
    this.lastLoadTime = 0;
  }

  /**
   * ✅ Check if tasks file exists in current project
   */
  public async hasTasksFile(projectPath?: string): Promise<boolean> {
    try {
      const targetProjectPath = projectPath || activeProjectService.getActiveProject()?.path;
      if (!targetProjectPath) return false;

      const tasksFilePath = `${targetProjectPath}/.taskmaster/tasks.json`;

      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.readFile(tasksFilePath);
        return result.success;
      }

      return false;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const taskmasterAdapter = TaskmasterAdapter.getInstance();
