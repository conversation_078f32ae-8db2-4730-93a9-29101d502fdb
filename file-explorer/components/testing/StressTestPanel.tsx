// components/testing/StressTestPanel.tsx

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  AlertTriangle, 
  Play, 
  Square, 
  Download, 
  Trash2, 
  Activity, 
  Clock, 
  Zap, 
  Brain,
  Target,
  BarChart3,
  RefreshCw,
  CheckCircle,
  XCircle,
  Loader2
} from "lucide-react"
import { getStressTester, type StressTestOptions } from "@/lib/testing/stress-tester"
import { getStressTestLogger, formatDuration, formatMemoryUsage } from "@/lib/testing/stress-log"
import { CompleteAgentManager } from "@/components/agents/agent-manager-complete"

interface StressTestPanelProps {
  testModeEnabled: boolean
  agentManager: CompleteAgentManager
}

export default function StressTestPanel({ testModeEnabled, agentManager }: StressTestPanelProps) {
  const [stressTester] = useState(() => getStressTester(agentManager, testModeEnabled))
  const [logger] = useState(() => getStressTestLogger())
  const [activeTests, setActiveTests] = useState<string[]>([])
  const [testResults, setTestResults] = useState<any[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [selectedTest, setSelectedTest] = useState<string>('concurrency')

  // Test configuration state
  const [concurrency, setConcurrency] = useState(10)
  const [taskCount, setTaskCount] = useState(20)
  const [timeoutMs, setTimeoutMs] = useState(5000)
  const [failureRate, setFailureRate] = useState(0.3)

  useEffect(() => {
    if (!testModeEnabled) return

    const interval = setInterval(() => {
      setActiveTests(stressTester.getActiveTests())
      setTestResults(stressTester.getTestResults())
    }, 1000)

    return () => clearInterval(interval)
  }, [testModeEnabled, stressTester])

  if (!testModeEnabled) {
    return (
      <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950 dark:border-yellow-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-700 dark:text-yellow-300">
            <AlertTriangle className="h-5 w-5" />
            Stress Testing Disabled
          </CardTitle>
          <CardDescription className="text-yellow-600 dark:text-yellow-400">
            Enable test mode in settings to access stress testing features.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  const runStressTest = async (testType: string) => {
    if (isRunning) return

    setIsRunning(true)
    try {
      const options: StressTestOptions = {
        testType: testType as any,
        concurrency,
        taskCount,
        timeoutMs: testType === 'timeout' ? timeoutMs : undefined,
        failureRate: testType === 'failure' ? failureRate : undefined,
        enableMetrics: true,
        enableMemoryTracking: testType === 'memory'
      }

      let testId: string
      switch (testType) {
        case 'concurrency':
          testId = await stressTester.runHighConcurrencyTest(options)
          break
        case 'timeout':
          testId = await stressTester.runTimeoutSimulation(options)
          break
        case 'failure':
          testId = await stressTester.runFailureSimulation(options)
          break
        case 'memory':
          testId = await stressTester.runMemoryStressTest(options)
          break
        default:
          throw new Error(`Unknown test type: ${testType}`)
      }

      console.log(`✅ Stress test completed: ${testId}`)
    } catch (error) {
      console.error('Stress test failed:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const exportResults = (format: 'json' | 'csv') => {
    try {
      const data = stressTester.exportResults(format)
      const blob = new Blob([data], { 
        type: format === 'json' ? 'application/json' : 'text/csv' 
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `stress-test-results-${new Date().toISOString().split('T')[0]}.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const clearLogs = () => {
    try {
      stressTester.clearTestLogs()
      setTestResults([])
    } catch (error) {
      console.error('Failed to clear logs:', error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failure':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getTestIcon = (testType: string) => {
    switch (testType) {
      case 'concurrency':
        return <Zap className="h-4 w-4" />
      case 'timeout':
        return <Clock className="h-4 w-4" />
      case 'failure':
        return <AlertTriangle className="h-4 w-4" />
      case 'memory':
        return <Brain className="h-4 w-4" />
      default:
        return <Target className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
            <Activity className="h-5 w-5" />
            Stress Testing & Load Simulation
          </CardTitle>
          <CardDescription className="text-blue-600 dark:text-blue-400">
            Test system stability under various load conditions. All tests use real concurrency with clearly labeled test behavior.
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="controls" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="controls">Test Controls</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="monitoring">Live Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="controls" className="space-y-4">
          {/* Test Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Test Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="concurrency">Concurrency</Label>
                  <Input
                    id="concurrency"
                    type="number"
                    value={concurrency}
                    onChange={(e) => setConcurrency(Number(e.target.value))}
                    min={1}
                    max={50}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="taskCount">Task Count</Label>
                  <Input
                    id="taskCount"
                    type="number"
                    value={taskCount}
                    onChange={(e) => setTaskCount(Number(e.target.value))}
                    min={1}
                    max={200}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timeoutMs">Timeout (ms)</Label>
                  <Input
                    id="timeoutMs"
                    type="number"
                    value={timeoutMs}
                    onChange={(e) => setTimeoutMs(Number(e.target.value))}
                    min={1000}
                    max={30000}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="failureRate">Failure Rate</Label>
                  <Input
                    id="failureRate"
                    type="number"
                    value={failureRate}
                    onChange={(e) => setFailureRate(Number(e.target.value))}
                    min={0}
                    max={1}
                    step={0.1}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Test Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <Button
                  onClick={() => runStressTest('concurrency')}
                  disabled={isRunning}
                  className="w-full"
                  variant="outline"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  High Concurrency
                </Button>
                <p className="text-xs text-muted-foreground mt-2">
                  Test with {concurrency} concurrent tasks
                </p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <Button
                  onClick={() => runStressTest('timeout')}
                  disabled={isRunning}
                  className="w-full"
                  variant="outline"
                >
                  <Clock className="h-4 w-4 mr-2" />
                  Timeout Simulation
                </Button>
                <p className="text-xs text-muted-foreground mt-2">
                  Test with {timeoutMs}ms timeout
                </p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <Button
                  onClick={() => runStressTest('failure')}
                  disabled={isRunning}
                  className="w-full"
                  variant="outline"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Failure Simulation
                </Button>
                <p className="text-xs text-muted-foreground mt-2">
                  Test with {(failureRate * 100).toFixed(0)}% failure rate
                </p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <Button
                  onClick={() => runStressTest('memory')}
                  disabled={isRunning}
                  className="w-full"
                  variant="outline"
                >
                  <Brain className="h-4 w-4 mr-2" />
                  Memory Stress
                </Button>
                <p className="text-xs text-muted-foreground mt-2">
                  Test memory usage patterns
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Active Tests */}
          {activeTests.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Active Tests
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {activeTests.map((testId) => (
                    <div key={testId} className="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-950 rounded">
                      <span className="text-sm font-mono">{testId}</span>
                      <Badge variant="outline" className="text-blue-600">
                        Running
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Test Results</h3>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => exportResults('json')}>
                <Download className="h-4 w-4 mr-2" />
                Export JSON
              </Button>
              <Button variant="outline" size="sm" onClick={() => exportResults('csv')}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Button variant="outline" size="sm" onClick={clearLogs}>
                <Trash2 className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>

          {/* Results List */}
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {testResults.length === 0 ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No test results yet. Run a stress test to see results here.</p>
                  </CardContent>
                </Card>
              ) : (
                testResults.map((result) => (
                  <Card key={result.testId}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            {getTestIcon(result.testType)}
                            <span className="font-medium">{result.testId}</span>
                            <Badge variant="outline" className="text-xs">
                              {result.testType}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Duration:</span>
                              <div className="font-medium">{formatDuration(result.duration || 0)}</div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Success Rate:</span>
                              <div className="font-medium">
                                {((result.successfulTasks / result.totalTasks) * 100).toFixed(1)}%
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Avg Response:</span>
                              <div className="font-medium">{result.averageResponseTime.toFixed(0)}ms</div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Tasks:</span>
                              <div className="font-medium">
                                {result.successfulTasks}/{result.totalTasks}
                              </div>
                            </div>
                          </div>
                          {result.memoryUsage && (
                            <div className="text-sm">
                              <span className="text-muted-foreground">Memory Peak:</span>
                              <span className="font-medium ml-2">
                                {formatMemoryUsage(result.memoryUsage.peak)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="text-xs text-muted-foreground">
                            {new Date(result.startTime).toLocaleTimeString()}
                          </div>
                          {result.errors.length > 0 && (
                            <Badge variant="destructive" className="text-xs mt-1">
                              {result.errors.length} errors
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Live System Monitoring</CardTitle>
              <CardDescription>
                Real-time metrics during stress testing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-accent/30 rounded">
                  <div className="text-2xl font-bold">{activeTests.length}</div>
                  <div className="text-xs text-muted-foreground">Active Tests</div>
                </div>
                <div className="text-center p-4 bg-accent/30 rounded">
                  <div className="text-2xl font-bold">{testResults.length}</div>
                  <div className="text-xs text-muted-foreground">Total Tests</div>
                </div>
                <div className="text-center p-4 bg-accent/30 rounded">
                  <div className="text-2xl font-bold">
                    {testResults.length > 0 
                      ? ((testResults.reduce((sum, r) => sum + r.successfulTasks, 0) / 
                         testResults.reduce((sum, r) => sum + r.totalTasks, 0)) * 100).toFixed(0)
                      : 0}%
                  </div>
                  <div className="text-xs text-muted-foreground">Success Rate</div>
                </div>
                <div className="text-center p-4 bg-accent/30 rounded">
                  <div className="text-2xl font-bold">
                    {testResults.length > 0 
                      ? (testResults.reduce((sum, r) => sum + r.averageResponseTime, 0) / testResults.length).toFixed(0)
                      : 0}ms
                  </div>
                  <div className="text-xs text-muted-foreground">Avg Response</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
