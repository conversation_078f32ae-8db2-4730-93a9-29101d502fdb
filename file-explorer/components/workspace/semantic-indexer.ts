// components/workspace/semantic-indexer.ts

export interface CodeChunk {
  id: string;
  filePath: string;
  content: string;
  startLine: number;
  endLine: number;
  type: 'function' | 'class' | 'interface' | 'component' | 'module' | 'other';
  name?: string;
  description?: string;
  embedding?: number[];
}

export interface SemanticIndex {
  projectPath: string;
  chunks: CodeChunk[];
  lastIndexed: number;
  version: string;
}

export class WorkspaceSemanticIndexer {
  private projectPath: string | null = null;
  private index: SemanticIndex | null = null;
  private supportedExtensions = ['.ts', '.tsx', '.js', '.jsx', '.py', '.java', '.cpp', '.c', '.cs', '.go', '.rs', '.php'];

  constructor() {
    console.log('WorkspaceSemanticIndexer initialized');
  }

  /**
   * Initialize indexer for a specific project
   */
  public async initializeProject(projectPath: string): Promise<void> {
    try {
      console.log(`Initializing semantic indexer for project: ${projectPath}`);
      this.projectPath = projectPath;
      
      // Check if we have an existing index
      const existingIndex = await this.loadExistingIndex(projectPath);
      if (existingIndex) {
        this.index = existingIndex;
        console.log(`Loaded existing index with ${existingIndex.chunks.length} chunks`);
      } else {
        // Create new index
        await this.createNewIndex(projectPath);
      }
    } catch (error) {
      console.error('Failed to initialize semantic indexer:', error);
      throw error;
    }
  }

  /**
   * Traverse project file tree and extract code chunks
   */
  public async indexProject(): Promise<void> {
    if (!this.projectPath) {
      throw new Error('Project path not set. Call initializeProject first.');
    }

    try {
      console.log('Starting project indexing...');
      const chunks: CodeChunk[] = [];
      
      // Traverse file tree
      await this.traverseDirectory(this.projectPath, chunks);
      
      // Update index
      this.index = {
        projectPath: this.projectPath,
        chunks,
        lastIndexed: Date.now(),
        version: '1.0.0'
      };

      // Save index to storage
      await this.saveIndex();
      
      console.log(`Indexing complete. Generated ${chunks.length} code chunks.`);
    } catch (error) {
      console.error('Failed to index project:', error);
      throw error;
    }
  }

  /**
   * Search for code chunks by semantic meaning
   */
  public async semanticSearch(query: string, limit: number = 10): Promise<CodeChunk[]> {
    if (!this.index || this.index.chunks.length === 0) {
      console.warn('No index available for semantic search');
      return [];
    }

    try {
      // For now, implement simple text-based search
      // In a full implementation, this would use vector embeddings
      const queryLower = query.toLowerCase();
      const results = this.index.chunks
        .filter(chunk => 
          chunk.content.toLowerCase().includes(queryLower) ||
          chunk.name?.toLowerCase().includes(queryLower) ||
          chunk.description?.toLowerCase().includes(queryLower)
        )
        .slice(0, limit);

      console.log(`Semantic search for "${query}" returned ${results.length} results`);
      return results;
    } catch (error) {
      console.error('Semantic search failed:', error);
      return [];
    }
  }

  /**
   * Get project summary
   */
  public getProjectSummary(): { totalChunks: number; fileTypes: Record<string, number>; lastIndexed: number } | null {
    if (!this.index) return null;

    const fileTypes: Record<string, number> = {};
    this.index.chunks.forEach(chunk => {
      const ext = this.getFileExtension(chunk.filePath);
      fileTypes[ext] = (fileTypes[ext] || 0) + 1;
    });

    return {
      totalChunks: this.index.chunks.length,
      fileTypes,
      lastIndexed: this.index.lastIndexed
    };
  }

  /**
   * Traverse directory recursively
   */
  private async traverseDirectory(dirPath: string, chunks: CodeChunk[]): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.readDirectory(dirPath);
        
        if (result.success && result.items) {
          for (const item of result.items) {
            const itemPath = `${dirPath}/${item.name}`;
            
            if (item.type === 'folder') {
              // Skip common directories that shouldn't be indexed
              if (this.shouldSkipDirectory(item.name)) {
                continue;
              }
              
              // Recursively traverse subdirectories
              await this.traverseDirectory(itemPath, chunks);
            } else if (item.type === 'file') {
              // Process supported file types
              if (this.isSupportedFile(item.name)) {
                await this.processFile(itemPath, chunks);
              }
            }
          }
        }
      }
    } catch (error) {
      console.warn(`Failed to traverse directory ${dirPath}:`, error);
    }
  }

  /**
   * Process individual file and extract chunks
   */
  private async processFile(filePath: string, chunks: CodeChunk[]): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.readFile(filePath);
        
        if (result.success && result.content) {
          const fileChunks = this.extractCodeChunks(filePath, result.content);
          chunks.push(...fileChunks);
        }
      }
    } catch (error) {
      console.warn(`Failed to process file ${filePath}:`, error);
    }
  }

  /**
   * Extract code chunks from file content
   */
  private extractCodeChunks(filePath: string, content: string): CodeChunk[] {
    const chunks: CodeChunk[] = [];
    const lines = content.split('\n');
    const extension = this.getFileExtension(filePath);

    // Simple chunk extraction based on file type
    if (['.ts', '.tsx', '.js', '.jsx'].includes(extension)) {
      this.extractJavaScriptChunks(filePath, lines, chunks);
    } else if (extension === '.py') {
      this.extractPythonChunks(filePath, lines, chunks);
    } else {
      // Generic chunking for other file types
      this.extractGenericChunks(filePath, lines, chunks);
    }

    return chunks;
  }

  /**
   * Extract JavaScript/TypeScript chunks
   */
  private extractJavaScriptChunks(filePath: string, lines: string[], chunks: CodeChunk[]): void {
    let currentChunk: { start: number; content: string[]; type: string; name?: string } | null = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Function declarations
      if (line.match(/^(export\s+)?(async\s+)?function\s+\w+/)) {
        if (currentChunk) {
          this.finalizeChunk(filePath, currentChunk, i - 1, chunks);
        }
        const name = line.match(/function\s+(\w+)/)?.[1];
        currentChunk = { start: i, content: [lines[i]], type: 'function', name };
      }
      // Class declarations
      else if (line.match(/^(export\s+)?(abstract\s+)?class\s+\w+/)) {
        if (currentChunk) {
          this.finalizeChunk(filePath, currentChunk, i - 1, chunks);
        }
        const name = line.match(/class\s+(\w+)/)?.[1];
        currentChunk = { start: i, content: [lines[i]], type: 'class', name };
      }
      // Interface declarations
      else if (line.match(/^(export\s+)?interface\s+\w+/)) {
        if (currentChunk) {
          this.finalizeChunk(filePath, currentChunk, i - 1, chunks);
        }
        const name = line.match(/interface\s+(\w+)/)?.[1];
        currentChunk = { start: i, content: [lines[i]], type: 'interface', name };
      }
      // React components
      else if (line.match(/^(export\s+)?(const|function)\s+\w+.*=.*\(/)) {
        if (currentChunk) {
          this.finalizeChunk(filePath, currentChunk, i - 1, chunks);
        }
        const name = line.match(/(const|function)\s+(\w+)/)?.[2];
        currentChunk = { start: i, content: [lines[i]], type: 'component', name };
      }
      else if (currentChunk) {
        currentChunk.content.push(lines[i]);
      }
    }

    // Finalize last chunk
    if (currentChunk) {
      this.finalizeChunk(filePath, currentChunk, lines.length - 1, chunks);
    }
  }

  /**
   * Extract Python chunks
   */
  private extractPythonChunks(filePath: string, lines: string[], chunks: CodeChunk[]): void {
    let currentChunk: { start: number; content: string[]; type: string; name?: string } | null = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Function definitions
      if (line.match(/^def\s+\w+/)) {
        if (currentChunk) {
          this.finalizeChunk(filePath, currentChunk, i - 1, chunks);
        }
        const name = line.match(/def\s+(\w+)/)?.[1];
        currentChunk = { start: i, content: [lines[i]], type: 'function', name };
      }
      // Class definitions
      else if (line.match(/^class\s+\w+/)) {
        if (currentChunk) {
          this.finalizeChunk(filePath, currentChunk, i - 1, chunks);
        }
        const name = line.match(/class\s+(\w+)/)?.[1];
        currentChunk = { start: i, content: [lines[i]], type: 'class', name };
      }
      else if (currentChunk) {
        currentChunk.content.push(lines[i]);
      }
    }

    // Finalize last chunk
    if (currentChunk) {
      this.finalizeChunk(filePath, currentChunk, lines.length - 1, chunks);
    }
  }

  /**
   * Extract generic chunks (for other file types)
   */
  private extractGenericChunks(filePath: string, lines: string[], chunks: CodeChunk[]): void {
    // Create chunks of ~50 lines each for generic files
    const chunkSize = 50;
    for (let i = 0; i < lines.length; i += chunkSize) {
      const chunkLines = lines.slice(i, i + chunkSize);
      const chunk: CodeChunk = {
        id: `${filePath}-${i}`,
        filePath,
        content: chunkLines.join('\n'),
        startLine: i + 1,
        endLine: Math.min(i + chunkSize, lines.length),
        type: 'other'
      };
      chunks.push(chunk);
    }
  }

  /**
   * Finalize a code chunk
   */
  private finalizeChunk(
    filePath: string, 
    chunkData: { start: number; content: string[]; type: string; name?: string }, 
    endLine: number, 
    chunks: CodeChunk[]
  ): void {
    const chunk: CodeChunk = {
      id: `${filePath}-${chunkData.start}`,
      filePath,
      content: chunkData.content.join('\n'),
      startLine: chunkData.start + 1,
      endLine: endLine + 1,
      type: chunkData.type as any,
      name: chunkData.name,
      description: this.generateDescription(chunkData.content.join('\n'), chunkData.type)
    };
    chunks.push(chunk);
  }

  /**
   * Generate description for a code chunk
   */
  private generateDescription(content: string, type: string): string {
    const lines = content.split('\n');
    const firstLine = lines[0]?.trim() || '';
    
    // Extract comments or docstrings
    for (const line of lines.slice(1, 5)) {
      const trimmed = line.trim();
      if (trimmed.startsWith('//') || trimmed.startsWith('*') || trimmed.startsWith('#')) {
        return trimmed.replace(/^[\/\*#\s]+/, '');
      }
    }
    
    return `${type}: ${firstLine}`;
  }

  /**
   * Check if directory should be skipped
   */
  private shouldSkipDirectory(name: string): boolean {
    const skipDirs = ['node_modules', '.git', '.next', 'dist', 'build', '__pycache__', '.vscode', '.idea'];
    return skipDirs.includes(name) || name.startsWith('.');
  }

  /**
   * Check if file should be processed
   */
  private isSupportedFile(filename: string): boolean {
    const ext = this.getFileExtension(filename);
    return this.supportedExtensions.includes(ext);
  }

  /**
   * Get file extension
   */
  private getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot) : '';
  }

  /**
   * Load existing index from storage
   */
  private async loadExistingIndex(projectPath: string): Promise<SemanticIndex | null> {
    try {
      // In a full implementation, this would load from persistent storage
      // For now, return null to always create new index
      return null;
    } catch (error) {
      console.warn('Failed to load existing index:', error);
      return null;
    }
  }

  /**
   * Create new index
   */
  private async createNewIndex(projectPath: string): Promise<void> {
    this.index = {
      projectPath,
      chunks: [],
      lastIndexed: 0,
      version: '1.0.0'
    };
  }

  /**
   * Save index to storage
   */
  private async saveIndex(): Promise<void> {
    try {
      // In a full implementation, this would save to persistent storage
      console.log('Index saved (placeholder implementation)');
    } catch (error) {
      console.warn('Failed to save index:', error);
    }
  }
}

// Singleton instance
export const workspaceSemanticIndexer = new WorkspaceSemanticIndexer();
