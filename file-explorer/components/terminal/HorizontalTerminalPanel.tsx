"use client"

import React, { useState } from 'react';
import { ChevronUp, ChevronDown, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import TerminalPanel from './TerminalPanel';

interface HorizontalTerminalPanelProps {
  isVisible: boolean;
  onToggleVisibility: () => void;
  onClose: () => void;
  className?: string;
}

export default function HorizontalTerminalPanel({
  isVisible,
  onToggleVisibility,
  onClose,
  className = ''
}: HorizontalTerminalPanelProps) {
  const [isMinimized, setIsMinimized] = useState(false);

  if (!isVisible) return null;

  return (
    <div className={`border-t border-zinc-700 bg-zinc-900 flex flex-col ${
      isMinimized ? 'h-8' : 'h-[30%] min-h-[180px]'
    } ${className}`}>
      {/* Terminal Header with minimize/maximize controls */}
      <div className="flex items-center justify-between px-3 py-1 bg-zinc-950 border-b border-zinc-800">
        <div className="flex items-center gap-2">
          <span className="text-sm text-zinc-300 font-mono">TERMINAL</span>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMinimized(!isMinimized)}
            className="h-6 w-6 p-0 text-zinc-400 hover:text-zinc-200"
            title={isMinimized ? "Maximize Terminal" : "Minimize Terminal"}
          >
            {isMinimized ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0 text-zinc-400 hover:text-zinc-200"
            title="Close Terminal"
          >
            <X className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* Terminal Content - only show when not minimized */}
      {!isMinimized && (
        <div className="flex-1 overflow-hidden">
          <TerminalPanel className="h-full" />
        </div>
      )}
    </div>
  );
}
