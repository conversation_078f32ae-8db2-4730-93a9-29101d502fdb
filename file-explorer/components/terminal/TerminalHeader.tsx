"use client"

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ExternalLink, Plus, Terminal as TerminalIcon, X, ChevronDown, ChevronUp } from 'lucide-react';

interface TerminalHeaderProps {
  onDetach: () => void;
  onToggleCollapse?: () => void;
  isCollapsed?: boolean;
}

interface TerminalSession {
  id: string;
  name: string;
  shell: string;
  createdAt: number;
}

export default function TerminalHeader({ onDetach, onToggleCollapse, isCollapsed }: TerminalHeaderProps) {
  const [sessions, setSessions] = useState<TerminalSession[]>([
    { id: 'session-1', name: 'Terminal 1', shell: 'bash', createdAt: Date.now() }
  ]);
  const [activeSessionId, setActiveSessionId] = useState<string>('session-1');
  const [activeShell, setActiveShell] = useState<string>('bash');

  const createNewSession = () => {
    const newSession: TerminalSession = {
      id: `session-${Date.now()}`,
      name: `Terminal ${sessions.length + 1}`,
      shell: activeShell,
      createdAt: Date.now()
    };
    setSessions([...sessions, newSession]);
    setActiveSessionId(newSession.id);
  };

  const closeSession = (sessionId: string) => {
    if (sessions.length <= 1) return; // Keep at least one session
    
    const newSessions = sessions.filter(s => s.id !== sessionId);
    setSessions(newSessions);
    
    if (activeSessionId === sessionId) {
      setActiveSessionId(newSessions[0]?.id || '');
    }
  };

  const switchToSession = (sessionId: string) => {
    setActiveSessionId(sessionId);
  };

  return (
    <div className="flex items-center justify-between px-3 py-2 border-b border-editor-border bg-editor-sidebar-bg/30 backdrop-blur-sm">
      {/* Left side - Title and Shell Selector */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <TerminalIcon className="h-4 w-4 text-editor-highlight" />
          <span className="font-semibold text-sm">Terminal</span>
        </div>
        
        {/* Shell Type Selector */}
        <Select value={activeShell} onValueChange={setActiveShell}>
          <SelectTrigger className="w-24 h-6 text-xs">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="bash">bash</SelectItem>
            <SelectItem value="zsh">zsh</SelectItem>
            <SelectItem value="sh">sh</SelectItem>
            <SelectItem value="fish">fish</SelectItem>
            <SelectItem value="powershell">pwsh</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Right side - Session Tabs and Controls */}
      <div className="flex items-center gap-2">
        {/* Terminal Session Tabs */}
        {sessions.map((session) => (
          <div
            key={session.id}
            className={`flex items-center gap-1 px-2 py-1 rounded text-xs cursor-pointer ${
              session.id === activeSessionId
                ? 'bg-editor-highlight/20 text-editor-highlight border border-editor-highlight/30'
                : 'bg-editor-sidebar-bg text-muted-foreground hover:bg-editor-sidebar-bg/70'
            }`}
            onClick={() => switchToSession(session.id)}
          >
            <TerminalIcon className="w-3 h-3" />
            <span>{session.name}</span>
            {sessions.length > 1 && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  closeSession(session.id);
                }}
                className="ml-1 hover:bg-red-500/20 rounded p-0.5"
              >
                <X className="w-2 h-2" />
              </button>
            )}
          </div>
        ))}

        {/* New Terminal Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={createNewSession}
          className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
          title="New Terminal"
        >
          <Plus className="w-3 h-3" />
        </Button>

        {/* Collapse/Expand Button */}
        {onToggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-muted-foreground hover:text-foreground"
            onClick={onToggleCollapse}
            title={isCollapsed ? "Expand Terminal" : "Collapse Terminal"}
          >
            {isCollapsed ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
          </Button>
        )}

        {/* Detach Button */}
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-muted-foreground hover:text-foreground"
          onClick={onDetach}
          title="Open in new window"
        >
          <ExternalLink className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}
