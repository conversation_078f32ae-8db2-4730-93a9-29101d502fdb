"use client"

import React, { useState, useEffect } from 'react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import TerminalHeader from './TerminalHeader';
import TerminalPanel from './TerminalPanel';

interface ResizableBottomTerminalProps {
  onDetach: () => void;
  children: React.ReactNode; // Main content above terminal
}

export default function ResizableBottomTerminal({ onDetach, children }: ResizableBottomTerminalProps) {
  const [terminalHeight, setTerminalHeight] = useState(40); // Default to 40% of height
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Load saved terminal height from localStorage
  useEffect(() => {
    const savedHeight = localStorage.getItem('terminalPanelHeight');
    if (savedHeight) {
      const height = parseInt(savedHeight, 10);
      if (height >= 20 && height <= 80) {
        setTerminalHeight(height);
      }
    }
  }, []);

  // Save terminal height to localStorage
  const handleTerminalResize = (sizes: number[]) => {
    const newTerminalHeight = sizes[1]; // Second panel is terminal
    setTerminalHeight(newTerminalHeight);
    localStorage.setItem('terminalPanelHeight', newTerminalHeight.toString());
    
    // Auto-collapse if very small
    if (newTerminalHeight < 10) {
      setIsCollapsed(true);
    } else {
      setIsCollapsed(false);
    }
  };

  const toggleTerminal = () => {
    if (isCollapsed) {
      setIsCollapsed(false);
      setTerminalHeight(40); // Restore to default height
    } else {
      setIsCollapsed(true);
      setTerminalHeight(5); // Minimize but keep visible
    }
  };

  return (
    <ResizablePanelGroup
      direction="vertical"
      className="h-full w-full"
      onLayout={handleTerminalResize}
    >
      {/* Main content panel */}
      <ResizablePanel 
        defaultSize={100 - terminalHeight}
        minSize={20}
        className="overflow-hidden"
      >
        {children}
      </ResizablePanel>

      {/* Resizable handle */}
      <ResizableHandle 
        className="h-1 bg-editor-border hover:bg-editor-highlight/50 transition-colors cursor-row-resize group"
        withHandle={false}
      >
        <div className="absolute inset-x-0 top-0 h-1 bg-editor-highlight/30 opacity-0 group-hover:opacity-100 transition-opacity" />
      </ResizableHandle>

      {/* Terminal panel */}
      <ResizablePanel 
        defaultSize={terminalHeight}
        minSize={5}
        maxSize={80}
        className="overflow-hidden"
        collapsible={true}
        collapsedSize={5}
      >
        <div className="h-full flex flex-col bg-background">
          {/* Terminal Header */}
          <TerminalHeader 
            onDetach={onDetach}
            onToggleCollapse={toggleTerminal}
            isCollapsed={isCollapsed}
          />
          
          {/* Terminal Content */}
          {!isCollapsed && (
            <div className="flex-1 overflow-hidden">
              <TerminalPanel className="h-full" />
            </div>
          )}
        </div>
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}
