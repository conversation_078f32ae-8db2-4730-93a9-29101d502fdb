// ✅ Task 100: Terminal Output UI Log Enhancements
// Display terminal output triggered by agents in a structured, readable, and persistent UI component

"use client"

import React, { useEffect, useState, useRef, useCallback } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Trash2, Download, Filter, Search, Terminal, Bot, User, AlertCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export interface TerminalLogEntry {
  id: string;
  timestamp: number;
  agentId: string;
  command?: string;
  output: string;
  type: 'command' | 'output' | 'error' | 'system';
  sessionId?: string;
  success?: boolean;
}

export interface TerminalLogsState {
  logs: TerminalLogEntry[];
  filteredLogs: TerminalLogEntry[];
  searchTerm: string;
  filterAgent: string;
  filterType: string;
  autoScroll: boolean;
}

export function TerminalLogsPanel() {
  const [state, setState] = useState<TerminalLogsState>({
    logs: [],
    filteredLogs: [],
    searchTerm: '',
    filterAgent: 'all',
    filterType: 'all',
    autoScroll: true
  });

  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const logsEndRef = useRef<HTMLDivElement>(null);

  // ✅ Task 100 Step 1: Listen for terminal log events
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleTerminalLog = (event: CustomEvent<TerminalLogEntry>) => {
      const logEntry = event.detail;
      
      setState(prev => {
        const newLogs = [...prev.logs, logEntry];
        // Limit to last 1000 entries to prevent memory issues
        const limitedLogs = newLogs.slice(-1000);
        
        return {
          ...prev,
          logs: limitedLogs,
          filteredLogs: filterLogs(limitedLogs, prev.searchTerm, prev.filterAgent, prev.filterType)
        };
      });
    };

    // Listen for terminal log events
    window.addEventListener('terminal-log', handleTerminalLog as EventListener);

    // Also listen for terminal API if available
    if ((window as any).terminalAPI?.onLog) {
      (window as any).terminalAPI.onLog((logData: any) => {
        const logEntry: TerminalLogEntry = {
          id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          agentId: logData.agentId || 'system',
          command: logData.command,
          output: logData.output || logData.message || String(logData),
          type: logData.type || 'output',
          sessionId: logData.sessionId,
          success: logData.success
        };

        setState(prev => {
          const newLogs = [...prev.logs, logEntry];
          const limitedLogs = newLogs.slice(-1000);
          
          return {
            ...prev,
            logs: limitedLogs,
            filteredLogs: filterLogs(limitedLogs, prev.searchTerm, prev.filterAgent, prev.filterType)
          };
        });
      });
    }

    return () => {
      window.removeEventListener('terminal-log', handleTerminalLog as EventListener);
    };
  }, []);

  // ✅ Task 100: Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (state.autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [state.filteredLogs, state.autoScroll]);

  // ✅ Task 100: Filter logs based on search and filters
  const filterLogs = useCallback((
    logs: TerminalLogEntry[], 
    searchTerm: string, 
    filterAgent: string, 
    filterType: string
  ): TerminalLogEntry[] => {
    return logs.filter(log => {
      const matchesSearch = !searchTerm || 
        log.output.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.command?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.agentId.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesAgent = filterAgent === 'all' || log.agentId === filterAgent;
      const matchesType = filterType === 'all' || log.type === filterType;
      
      return matchesSearch && matchesAgent && matchesType;
    });
  }, []);

  // ✅ Task 100: Update filters
  const updateSearch = useCallback((searchTerm: string) => {
    setState(prev => ({
      ...prev,
      searchTerm,
      filteredLogs: filterLogs(prev.logs, searchTerm, prev.filterAgent, prev.filterType)
    }));
  }, [filterLogs]);

  const updateAgentFilter = useCallback((filterAgent: string) => {
    setState(prev => ({
      ...prev,
      filterAgent,
      filteredLogs: filterLogs(prev.logs, prev.searchTerm, filterAgent, prev.filterType)
    }));
  }, [filterLogs]);

  const updateTypeFilter = useCallback((filterType: string) => {
    setState(prev => ({
      ...prev,
      filterType,
      filteredLogs: filterLogs(prev.logs, prev.searchTerm, prev.filterAgent, filterType)
    }));
  }, [filterLogs]);

  // ✅ Task 100: Clear all logs
  const clearLogs = useCallback(() => {
    setState(prev => ({
      ...prev,
      logs: [],
      filteredLogs: []
    }));
  }, []);

  // ✅ Task 100: Export logs
  const exportLogs = useCallback(() => {
    const logContent = state.filteredLogs.map(log => {
      const timestamp = new Date(log.timestamp).toLocaleString();
      const prefix = `[${timestamp}] [${log.agentId}] [${log.type.toUpperCase()}]`;
      const command = log.command ? ` Command: ${log.command}\n` : '';
      return `${prefix}${command} ${log.output}`;
    }).join('\n\n');

    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `terminal-logs-${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [state.filteredLogs]);

  // ✅ Task 100: Get unique agents for filter
  const uniqueAgents = Array.from(new Set(state.logs.map(log => log.agentId))).sort();

  // ✅ Task 100: Format timestamp
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // ✅ Task 100: Get log entry styling
  const getLogEntryStyle = (log: TerminalLogEntry): string => {
    const baseStyle = "font-mono text-sm border-l-2 pl-3 py-1";
    
    switch (log.type) {
      case 'command':
        return `${baseStyle} border-blue-500 bg-blue-50 dark:bg-blue-950/20`;
      case 'output':
        return `${baseStyle} border-green-500 bg-green-50 dark:bg-green-950/20`;
      case 'error':
        return `${baseStyle} border-red-500 bg-red-50 dark:bg-red-950/20`;
      case 'system':
        return `${baseStyle} border-gray-500 bg-gray-50 dark:bg-gray-950/20`;
      default:
        return `${baseStyle} border-gray-300`;
    }
  };

  // ✅ Task 100: Get log entry icon
  const getLogEntryIcon = (log: TerminalLogEntry) => {
    switch (log.type) {
      case 'command':
        return <Terminal className="w-3 h-3 text-blue-600" />;
      case 'output':
        return <Bot className="w-3 h-3 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-3 h-3 text-red-600" />;
      case 'system':
        return <User className="w-3 h-3 text-gray-600" />;
      default:
        return <Terminal className="w-3 h-3 text-gray-600" />;
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Terminal className="w-5 h-5" />
            Terminal Logs
            <Badge variant="secondary" className="ml-2">
              {state.filteredLogs.length}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setState(prev => ({ ...prev, autoScroll: !prev.autoScroll }))}
              className={state.autoScroll ? 'bg-green-100 dark:bg-green-900' : ''}
            >
              Auto-scroll
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={exportLogs}
              disabled={state.filteredLogs.length === 0}
            >
              <Download className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={clearLogs}
              disabled={state.logs.length === 0}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* ✅ Task 100: Filters */}
        <div className="flex items-center gap-2 mt-3">
          <div className="flex-1">
            <Input
              placeholder="Search logs..."
              value={state.searchTerm}
              onChange={(e) => updateSearch(e.target.value)}
              className="h-8"
            />
          </div>
          
          <Select value={state.filterAgent} onValueChange={updateAgentFilter}>
            <SelectTrigger className="w-32 h-8">
              <SelectValue placeholder="Agent" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Agents</SelectItem>
              {uniqueAgents.map(agent => (
                <SelectItem key={agent} value={agent}>{agent}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={state.filterType} onValueChange={updateTypeFilter}>
            <SelectTrigger className="w-24 h-8">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="command">Command</SelectItem>
              <SelectItem value="output">Output</SelectItem>
              <SelectItem value="error">Error</SelectItem>
              <SelectItem value="system">System</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-full" ref={scrollAreaRef}>
          <div className="p-4 space-y-2">
            {state.filteredLogs.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Terminal className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No terminal logs to display</p>
                <p className="text-sm">Agent commands will appear here</p>
              </div>
            ) : (
              state.filteredLogs.map((log) => (
                <div key={log.id} className={getLogEntryStyle(log)}>
                  <div className="flex items-start gap-2">
                    <div className="flex items-center gap-1 min-w-0">
                      {getLogEntryIcon(log)}
                      <span className="text-xs text-muted-foreground">
                        {formatTimestamp(log.timestamp)}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {log.agentId}
                      </Badge>
                      {log.sessionId && (
                        <Badge variant="secondary" className="text-xs">
                          {log.sessionId.slice(-8)}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  {log.command && (
                    <div className="mt-1 text-blue-700 dark:text-blue-300">
                      <span className="text-xs font-medium">$ </span>
                      {log.command}
                    </div>
                  )}
                  
                  <div className="mt-1 whitespace-pre-wrap break-words">
                    {log.output}
                  </div>
                </div>
              ))
            )}
            <div ref={logsEndRef} />
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
