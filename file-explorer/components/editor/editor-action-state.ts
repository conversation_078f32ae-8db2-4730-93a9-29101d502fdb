// components/editor/editor-action-state.ts

export interface EditorActionState {
  // File state
  isDirty: boolean;
  canSave: boolean;
  activeFilePath: string | null;
  activeFileName: string | null;

  // Edit operations
  canUndo: boolean;
  canRedo: boolean;
  hasSelection: boolean;

  // AI Assistant
  aiContextEnabled: boolean;
  aiAssistantAvailable: boolean;
  aiSuggestionsCount: number;

  // Editor features
  isFormatting: boolean;
  isSearchOpen: boolean;
  isRunning: boolean;

  // Monaco editor state
  cursorPosition: { line: number; column: number };
  selectionRange: { startLine: number; startColumn: number; endLine: number; endColumn: number } | null;

  // File type capabilities
  supportsFormatting: boolean;
  supportsLinting: boolean;
  supportsDebugging: boolean;
}

export interface EditorActionEvent {
  id: string;
  type: 'action_state_changed' | 'action_triggered' | 'ai_state_changed';
  timestamp: number;
  windowId: string;
  actionState?: EditorActionState;
  actionType?: 'save' | 'undo' | 'redo' | 'format' | 'search' | 'ai_assist';
  actionData?: any;
}

export type EditorActionEventHandler = (event: EditorActionEvent) => void;

export class EditorActionStateService {
  private state: EditorActionState = {
    isDirty: false,
    canSave: false,
    activeFilePath: null,
    activeFileName: null,
    canUndo: false,
    canRedo: false,
    hasSelection: false,
    aiContextEnabled: true,
    aiAssistantAvailable: false,
    aiSuggestionsCount: 0,
    isFormatting: false,
    isSearchOpen: false,
    isRunning: false,
    cursorPosition: { line: 1, column: 1 },
    selectionRange: null,
    supportsFormatting: false,
    supportsLinting: false,
    supportsDebugging: false
  };

  private eventHandlers: EditorActionEventHandler[] = [];
  private isInitialized = false;
  private windowId: string;

  constructor() {
    this.windowId = this.generateWindowId();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    // Set up IPC event listeners for cross-window synchronization
    this.setupIPCListeners();

    // Set up file-saved event listener
    this.setupFileSavedListener();

    this.isInitialized = true;
    console.log('EditorActionStateService initialized with windowId:', this.windowId);
  }

  /**
   * Get current action state
   */
  getState(): EditorActionState {
    return { ...this.state };
  }

  /**
   * Update action state
   */
  async updateState(updates: Partial<EditorActionState>): Promise<void> {
    const previousState = { ...this.state };
    Object.assign(this.state, updates);

    // Only broadcast if state actually changed
    const hasChanges = Object.keys(updates).some(key =>
      previousState[key as keyof EditorActionState] !== this.state[key as keyof EditorActionState]
    );

    if (hasChanges) {
      this.broadcastEvent({
        id: this.generateEventId(),
        type: 'action_state_changed',
        timestamp: Date.now(),
        windowId: this.windowId,
        actionState: this.getState()
      });
    }
  }

  /**
   * Update file-related state
   */
  async updateFileState(filePath: string | null, fileName: string | null, isDirty: boolean): Promise<void> {
    const fileType = fileName ? fileName.split('.').pop()?.toLowerCase() : null;

    await this.updateState({
      activeFilePath: filePath,
      activeFileName: fileName,
      isDirty,
      canSave: filePath !== null, // Can save if we have a file path, regardless of dirty state
      supportsFormatting: ['js', 'jsx', 'ts', 'tsx', 'json', 'css', 'scss', 'html', 'xml'].includes(fileType || ''),
      supportsLinting: ['js', 'jsx', 'ts', 'tsx', 'json'].includes(fileType || ''),
      supportsDebugging: ['js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cs'].includes(fileType || ''),
      aiAssistantAvailable: ['js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cs', 'cpp', 'c'].includes(fileType || '')
    });
  }

  /**
   * Update Monaco editor state
   */
  async updateEditorState(canUndo: boolean, canRedo: boolean, hasSelection: boolean, cursorPosition: { line: number; column: number }, selectionRange?: any): Promise<void> {
    await this.updateState({
      canUndo,
      canRedo,
      hasSelection,
      cursorPosition,
      selectionRange: selectionRange || null
    });
  }

  /**
   * Trigger an action
   */
  async triggerAction(actionType: 'save' | 'undo' | 'redo' | 'format' | 'search' | 'ai_assist', actionData?: any): Promise<void> {
    this.broadcastEvent({
      id: this.generateEventId(),
      type: 'action_triggered',
      timestamp: Date.now(),
      windowId: this.windowId,
      actionType,
      actionData
    });
  }

  /**
   * Execute save action with file content
   */
  async executeSave(filePath: string, content: string): Promise<boolean> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.saveFile(filePath, content);

        if (result.success) {
          // Update state to reflect save - only reset dirty state
          await this.updateState({
            isDirty: false
          });

          // Broadcast save success to all windows
          this.broadcastEvent({
            id: this.generateEventId(),
            type: 'action_triggered',
            timestamp: Date.now(),
            windowId: this.windowId,
            actionType: 'save',
            actionData: {
              filePath,
              content,
              success: true
            }
          });

          console.log('EditorActionStateService: File saved successfully:', filePath);
          return true;
        } else {
          console.error('EditorActionStateService: Save failed:', result.error);
          return false;
        }
      }
      return false;
    } catch (error) {
      console.error('EditorActionStateService: Save error:', error);
      return false;
    }
  }

  /**
   * Update AI state
   */
  async updateAIState(aiContextEnabled: boolean, aiSuggestionsCount: number): Promise<void> {
    await this.updateState({
      aiContextEnabled,
      aiSuggestionsCount
    });
  }

  /**
   * Set running state
   */
  async setRunningState(isRunning: boolean): Promise<void> {
    await this.updateState({ isRunning });
  }

  /**
   * Set formatting state
   */
  async setFormattingState(isFormatting: boolean): Promise<void> {
    await this.updateState({ isFormatting });
  }

  /**
   * Set search state
   */
  async setSearchState(isSearchOpen: boolean): Promise<void> {
    await this.updateState({ isSearchOpen });
  }

  /**
   * Add event handler
   */
  onActionStateEvent(handler: EditorActionEventHandler): void {
    this.eventHandlers.push(handler);
  }

  /**
   * Remove event handler
   */
  offActionStateEvent(handler: EditorActionEventHandler): void {
    const index = this.eventHandlers.indexOf(handler);
    if (index > -1) {
      this.eventHandlers.splice(index, 1);
    }
  }

  /**
   * Shutdown the service
   */
  shutdown(): void {
    this.state = {
      isDirty: false,
      canSave: false,
      activeFilePath: null,
      activeFileName: null,
      canUndo: false,
      canRedo: false,
      hasSelection: false,
      aiContextEnabled: true,
      aiAssistantAvailable: false,
      aiSuggestionsCount: 0,
      isFormatting: false,
      isSearchOpen: false,
      isRunning: false,
      cursorPosition: { line: 1, column: 1 },
      selectionRange: null,
      supportsFormatting: false,
      supportsLinting: false,
      supportsDebugging: false
    };
    this.eventHandlers = [];
    this.isInitialized = false;
    console.log('EditorActionStateService shutdown');
  }

  // Private methods
  private generateEventId(): string {
    return `action-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateWindowId(): string {
    return `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupIPCListeners(): void {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
      // Listen for action state events from other windows
      window.electronAPI.ipc.on('editor-action-event', (event: EditorActionEvent) => {
        if (event.windowId !== this.windowId) {
          this.handleRemoteActionEvent(event);
        }
      });
    }
  }

  private setupFileSavedListener(): void {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
      // Listen for file-saved events from main process
      window.electronAPI.ipc.on('file-saved', (saveEvent: { filePath: string; content: string; timestamp: number }) => {
        console.log('EditorActionStateService: Received file-saved event:', saveEvent.filePath);

        // Update state if this is the active file
        if (this.state.activeFilePath === saveEvent.filePath) {
          this.updateState({
            isDirty: false
          });

          console.log('EditorActionStateService: Updated state for saved file:', saveEvent.filePath);
        }
      });
    }
  }

  private handleRemoteActionEvent(event: EditorActionEvent): void {
    // Handle action events from other windows
    if (event.actionState) {
      // Update local state to match remote state
      this.state = { ...event.actionState };
    }

    // Notify local handlers
    this.eventHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error in action state event handler:', error);
      }
    });
  }

  private broadcastEvent(event: EditorActionEvent): void {
    // Broadcast to other windows via IPC
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
      window.electronAPI.ipc.send('editor-action-event', event);
    }

    // Notify local handlers
    this.eventHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error in action state event handler:', error);
      }
    });
  }
}

// Global editor action state service instance
export const globalEditorActionStateService = new EditorActionStateService();
