// components/editor/editor-action-provider.tsx
'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import {
  EditorActionState,
  EditorActionEvent,
  globalEditorActionStateService
} from './editor-action-state';

interface EditorActionContextType {
  // State
  actionState: EditorActionState;
  isInitialized: boolean;

  // File actions
  updateFileState: (filePath: string | null, fileName: string | null, isDirty: boolean) => Promise<void>;

  // Editor actions
  updateEditorState: (canUndo: boolean, canRedo: boolean, hasSelection: boolean, cursorPosition: { line: number; column: number }, selectionRange?: any) => Promise<void>;

  // Action triggers
  triggerSave: () => Promise<void>;
  executeSave: (filePath: string, content: string) => Promise<boolean>;
  triggerUndo: () => Promise<void>;
  triggerRedo: () => Promise<void>;
  triggerFormat: () => Promise<void>;
  triggerSearch: () => Promise<void>;
  triggerAIAssist: () => Promise<void>;

  // State setters
  setRunningState: (isRunning: boolean) => Promise<void>;
  setFormattingState: (isFormatting: boolean) => Promise<void>;
  setSearchState: (isSearchOpen: boolean) => Promise<void>;
  updateAIState: (aiContextEnabled: boolean, aiSuggestionsCount: number) => Promise<void>;
}

const EditorActionContext = createContext<EditorActionContextType | null>(null);

export function useEditorActions() {
  const context = useContext(EditorActionContext);
  if (!context) {
    throw new Error('useEditorActions must be used within an EditorActionProvider');
  }
  return context;
}

interface EditorActionProviderProps {
  children: React.ReactNode;
}

export function EditorActionProvider({ children }: EditorActionProviderProps) {
  const [actionState, setActionState] = useState<EditorActionState>(() => globalEditorActionStateService.getState());
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize action state from service
  useEffect(() => {
    const initializeActionState = async () => {
      try {
        const initialState = globalEditorActionStateService.getState();
        setActionState(initialState);
        console.log('EditorActionProvider initialized with state:', initialState);

        // Mark as initialized after a short delay
        setTimeout(() => {
          setIsInitialized(true);
        }, 100);
      } catch (error) {
        console.error('Failed to initialize editor action state:', error);
        setIsInitialized(true); // Initialize anyway to prevent hanging
      }
    };

    initializeActionState();

    // Set up event listeners for action state updates and cross-window sync
    const handleActionStateEvent = (event: EditorActionEvent) => {
      console.log('EditorActionProvider received event:', event.type, event);

      if (event.actionState) {
        setActionState(event.actionState);
        if (!isInitialized) {
          setIsInitialized(true);
        }
      }

      // Handle action triggers from other windows
      if (event.type === 'action_triggered' && event.actionType) {
        console.log('Handling action trigger from other window:', event.actionType);

        // Execute the action locally without triggering another broadcast
        switch (event.actionType) {
          case 'save':
            // Handle save action from other windows
            if (event.actionData?.success) {
              // Save was successful in another window, update our state
              console.log('Save completed in other window, updating local state');

              // Update file sync service if available
              if (event.actionData.filePath && event.actionData.content) {
                try {
                  import('@/components/background/file-sync-service').then(({ globalFileSyncService }) => {
                    globalFileSyncService.saveFile(
                      event.actionData.filePath,
                      event.actionData.content,
                      undefined
                    );
                  });
                } catch (error) {
                  console.error('Failed to sync save event:', error);
                }
              }
            } else {
              // Save action trigger from other window - execute save if we have content
              console.log('Save action triggered from other window');
              // The Monaco editor component will handle this via its own event listener
            }
            break;
          case 'undo':
            // Trigger undo in Monaco editor
            console.log('Undo action triggered from other window');
            break;
          case 'redo':
            // Trigger redo in Monaco editor
            console.log('Redo action triggered from other window');
            break;
          case 'format':
            // Trigger format in Monaco editor
            console.log('Format action triggered from other window');
            break;
          case 'search':
            // Trigger search in Monaco editor
            console.log('Search action triggered from other window');
            break;
          case 'ai_assist':
            // Trigger AI assist in Monaco editor
            console.log('AI assist action triggered from other window');
            break;
        }
      }
    };

    globalEditorActionStateService.onActionStateEvent(handleActionStateEvent);

    // Cleanup
    return () => {
      globalEditorActionStateService.offActionStateEvent(handleActionStateEvent);
    };
  }, [isInitialized]);

  // File actions
  const updateFileState = useCallback(async (filePath: string | null, fileName: string | null, isDirty: boolean) => {
    await globalEditorActionStateService.updateFileState(filePath, fileName, isDirty);
  }, []);

  // Editor actions
  const updateEditorState = useCallback(async (canUndo: boolean, canRedo: boolean, hasSelection: boolean, cursorPosition: { line: number; column: number }, selectionRange?: any) => {
    await globalEditorActionStateService.updateEditorState(canUndo, canRedo, hasSelection, cursorPosition, selectionRange);
  }, []);

  // Action triggers
  const triggerSave = useCallback(async () => {
    await globalEditorActionStateService.triggerAction('save');
  }, []);

  const executeSave = useCallback(async (filePath: string, content: string) => {
    return await globalEditorActionStateService.executeSave(filePath, content);
  }, []);

  const triggerUndo = useCallback(async () => {
    await globalEditorActionStateService.triggerAction('undo');
  }, []);

  const triggerRedo = useCallback(async () => {
    await globalEditorActionStateService.triggerAction('redo');
  }, []);

  const triggerFormat = useCallback(async () => {
    await globalEditorActionStateService.triggerAction('format');
  }, []);

  const triggerSearch = useCallback(async () => {
    await globalEditorActionStateService.triggerAction('search');
  }, []);

  const triggerAIAssist = useCallback(async () => {
    await globalEditorActionStateService.triggerAction('ai_assist');
  }, []);

  // State setters
  const setRunningState = useCallback(async (isRunning: boolean) => {
    await globalEditorActionStateService.setRunningState(isRunning);
  }, []);

  const setFormattingState = useCallback(async (isFormatting: boolean) => {
    await globalEditorActionStateService.setFormattingState(isFormatting);
  }, []);

  const setSearchState = useCallback(async (isSearchOpen: boolean) => {
    await globalEditorActionStateService.setSearchState(isSearchOpen);
  }, []);

  const updateAIState = useCallback(async (aiContextEnabled: boolean, aiSuggestionsCount: number) => {
    await globalEditorActionStateService.updateAIState(aiContextEnabled, aiSuggestionsCount);
  }, []);

  const contextValue: EditorActionContextType = {
    // State
    actionState,
    isInitialized,

    // File actions
    updateFileState,

    // Editor actions
    updateEditorState,

    // Action triggers
    triggerSave,
    executeSave,
    triggerUndo,
    triggerRedo,
    triggerFormat,
    triggerSearch,
    triggerAIAssist,

    // State setters
    setRunningState,
    setFormattingState,
    setSearchState,
    updateAIState
  };

  return (
    <EditorActionContext.Provider value={contextValue}>
      {children}
    </EditorActionContext.Provider>
  );
}
