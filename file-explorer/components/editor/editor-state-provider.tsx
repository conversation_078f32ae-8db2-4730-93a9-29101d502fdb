"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import {
  EditorState,
  EditorTabState,
  EditorStateEvent,
  globalEditorStateService
} from '@/components/background/editor-state-service';

interface EditorStateContextType {
  // State
  openTabs: EditorTabState[];
  activeTabId: string | null;
  editorOptions: EditorState['editorOptions'];
  isInitialized: boolean;

  // Computed
  activeTab: EditorTabState | null;

  // Actions
  openTab: (tabData: EditorTabState) => Promise<void>;
  closeTab: (tabId: string) => Promise<void>;
  switchToTab: (tabId: string) => Promise<void>;
  updateTab: (tabId: string, updates: Partial<EditorTabState>) => Promise<void>;
  updateOptions: (options: Partial<EditorState['editorOptions']>) => Promise<void>;

  // Utilities
  getTabByPath: (path: string) => EditorTabState | null;
  isTabOpen: (path: string) => boolean;
}

const EditorStateContext = createContext<EditorStateContextType | null>(null);

export function useEditorState() {
  const context = useContext(EditorStateContext);
  if (!context) {
    throw new Error('useEditorState must be used within an EditorStateProvider');
  }
  return context;
}

interface EditorStateProviderProps {
  children: React.ReactNode;
}

export function EditorStateProvider({ children }: EditorStateProviderProps) {
  const [state, setState] = useState<EditorState>(() => globalEditorStateService.getState());
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize state from service
  useEffect(() => {
    const initializeState = async () => {
      try {
        const initialState = globalEditorStateService.getState();
        setState(initialState);
        console.log('EditorStateProvider initialized with state:', initialState);

        // Mark as initialized after a short delay to allow for initial state requests
        setTimeout(() => {
          setIsInitialized(true);
        }, 500);
      } catch (error) {
        console.error('Failed to initialize editor state:', error);
        setIsInitialized(true); // Initialize anyway to prevent hanging
      }
    };

    initializeState();

    // Set up event listeners for state updates and cross-window sync
    const handleEditorStateEvent = (event: EditorStateEvent) => {
      console.log('EditorStateProvider received event:', event.type, event);

      if (event.editorState) {
        setState(event.editorState);
        if (!isInitialized) {
          setIsInitialized(true);
        }
      }
    };

    globalEditorStateService.onEditorStateEvent(handleEditorStateEvent);

    // Cleanup
    return () => {
      globalEditorStateService.offEditorStateEvent(handleEditorStateEvent);
    };
  }, [isInitialized]);

  // Actions
  const openTab = useCallback(async (tabData: EditorTabState) => {
    await globalEditorStateService.openTab(tabData);
  }, []);

  const closeTab = useCallback(async (tabId: string) => {
    await globalEditorStateService.closeTab(tabId);
  }, []);

  const switchToTab = useCallback(async (tabId: string) => {
    await globalEditorStateService.switchToTab(tabId);
  }, []);

  const updateTab = useCallback(async (tabId: string, updates: Partial<EditorTabState>) => {
    await globalEditorStateService.updateTab(tabId, updates);
  }, []);

  const updateOptions = useCallback(async (options: Partial<EditorState['editorOptions']>) => {
    await globalEditorStateService.updateOptions(options);
  }, []);

  // Computed values
  const activeTab = state.activeTabId
    ? state.openTabs.find(tab => tab.id === state.activeTabId) || null
    : null;

  // Utilities
  const getTabByPath = useCallback((path: string) => {
    return state.openTabs.find(tab => tab.path === path) || null;
  }, [state.openTabs]);

  const isTabOpen = useCallback((path: string) => {
    return state.openTabs.some(tab => tab.path === path);
  }, [state.openTabs]);

  const contextValue: EditorStateContextType = {
    // State
    openTabs: state.openTabs,
    activeTabId: state.activeTabId,
    editorOptions: state.editorOptions,
    isInitialized,

    // Computed
    activeTab,

    // Actions
    openTab,
    closeTab,
    switchToTab,
    updateTab,
    updateOptions,

    // Utilities
    getTabByPath,
    isTabOpen
  };

  return (
    <EditorStateContext.Provider value={contextValue}>
      {children}
    </EditorStateContext.Provider>
  );
}

// Helper hook for creating tab data from file system items
export function useCreateTabFromFile() {
  return useCallback((file: any) => {
    const tabData: EditorTabState = {
      id: file.id || `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: file.name,
      type: file.type,
      path: file.path,
      content: file.content,
      isDirty: false
    };
    return tabData;
  }, []);
}
