// components/middleware/continuous-learning-agent.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from '../agents/agent-base';

export interface LearningPattern {
  id: string;
  pattern: string;
  category: 'success' | 'failure' | 'optimization' | 'error_resolution';
  frequency: number;
  effectiveness: number; // 0-1
  context: string[];
  examples: string[];
  recommendations: string[];
  lastSeen: number;
  projectSpecific: boolean;
}

export interface PerformanceMetric {
  agentId: string;
  taskType: string;
  successRate: number;
  averageTime: number;
  averageTokens: number;
  errorTypes: Record<string, number>;
  improvementTrend: number; // -1 to 1
  lastUpdated: number;
}

export interface OptimizationSuggestion {
  id: string;
  targetAgent: string;
  type: 'prompt_optimization' | 'model_selection' | 'context_improvement' | 'rule_update';
  description: string;
  expectedImpact: number; // 0-1
  effort: number; // 1-10
  priority: 'low' | 'medium' | 'high';
  data: any;
}

export class ContinuousLearningAgent extends AgentBase {
  private learningPatterns: Map<string, LearningPattern> = new Map();
  private performanceMetrics: Map<string, PerformanceMetric> = new Map();
  private optimizationSuggestions: OptimizationSuggestion[] = [];
  private learningHistory: any[] = [];

  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return [
      'pattern_recognition',
      'performance_analysis',
      'optimization_suggestions',
      'cross_project_learning',
      'adaptive_improvement',
      'knowledge_distillation'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Continuous Learning agent, responsible for improving system capabilities through pattern recognition and knowledge acquisition.

CORE RESPONSIBILITIES:
1. Analyze completed tasks to identify successful implementation patterns
2. Extract valuable knowledge from both successes and failures
3. Improve agent performance through prompt refinement and optimization
4. Identify transferable patterns across projects
5. Monitor learning effectiveness and track improvements
6. Generate actionable optimization recommendations

LEARNING METHODOLOGY:
- Pattern recognition from task completion data
- Performance trend analysis across agents
- Cross-project knowledge transfer (anonymized)
- Prompt effectiveness optimization
- Context utilization improvement
- Error pattern learning for prevention

Focus on continuous system improvement through structured learning while maintaining balance between stability and innovation.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      // Analyze learning opportunities
      const learningAnalysis = await this.analyzeLearningOpportunities();
      
      // Generate optimization suggestions
      const optimizations = await this.generateOptimizationSuggestions();
      
      // Update performance models
      const performanceUpdate = await this.updatePerformanceModels();
      
      // Create learning report
      const report = this.generateLearningReport(learningAnalysis, optimizations, performanceUpdate);

      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 500;

      return this.createSuccessResponse(
        report,
        tokensUsed,
        executionTime,
        optimizations.map(opt => opt.description),
        {
          patternsLearned: learningAnalysis.newPatterns,
          optimizationSuggestions: optimizations.length,
          performanceImprovement: performanceUpdate.overallImprovement
        }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Learning analysis failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  // Public methods for learning data collection
  public recordTaskCompletion(
    agentId: string,
    taskType: string,
    success: boolean,
    executionTime: number,
    tokensUsed: number,
    context: AgentContext,
    result?: string,
    error?: string
  ): void {
    // Update performance metrics
    this.updatePerformanceMetrics(agentId, taskType, success, executionTime, tokensUsed, error);

    // Extract learning patterns
    this.extractLearningPatterns(agentId, taskType, success, context, result, error);

    // Analyze for optimization opportunities
    this.analyzeOptimizationOpportunities(agentId, taskType, success, context, result);
  }

  public recordErrorResolution(
    originalError: string,
    resolutionStrategy: string,
    success: boolean,
    agentId: string,
    context: AgentContext
  ): void {
    const pattern: LearningPattern = {
      id: `error_resolution_${Date.now()}`,
      pattern: `Error: ${originalError.substring(0, 100)} -> Strategy: ${resolutionStrategy}`,
      category: success ? 'success' : 'failure',
      frequency: 1,
      effectiveness: success ? 0.8 : 0.2,
      context: [agentId, 'error_resolution'],
      examples: [originalError],
      recommendations: success ? [`Use strategy: ${resolutionStrategy}`] : [`Avoid strategy: ${resolutionStrategy}`],
      lastSeen: Date.now(),
      projectSpecific: false
    };

    this.learningPatterns.set(pattern.id, pattern);
  }

  public getOptimizationSuggestions(agentId?: string): OptimizationSuggestion[] {
    if (agentId) {
      return this.optimizationSuggestions.filter(opt => opt.targetAgent === agentId);
    }
    return [...this.optimizationSuggestions];
  }

  public getLearningPatterns(category?: string): LearningPattern[] {
    const patterns = Array.from(this.learningPatterns.values());
    if (category) {
      return patterns.filter(p => p.category === category);
    }
    return patterns;
  }

  public getPerformanceMetrics(agentId?: string): PerformanceMetric[] {
    const metrics = Array.from(this.performanceMetrics.values());
    if (agentId) {
      return metrics.filter(m => m.agentId === agentId);
    }
    return metrics;
  }

  private updatePerformanceMetrics(
    agentId: string,
    taskType: string,
    success: boolean,
    executionTime: number,
    tokensUsed: number,
    error?: string
  ): void {
    const key = `${agentId}_${taskType}`;
    let metric = this.performanceMetrics.get(key);

    if (!metric) {
      metric = {
        agentId,
        taskType,
        successRate: 0,
        averageTime: 0,
        averageTokens: 0,
        errorTypes: {},
        improvementTrend: 0,
        lastUpdated: Date.now()
      };
    }

    // Update metrics with exponential smoothing
    const alpha = 0.1; // Learning rate
    
    metric.successRate = metric.successRate * (1 - alpha) + (success ? 1 : 0) * alpha;
    metric.averageTime = metric.averageTime * (1 - alpha) + executionTime * alpha;
    metric.averageTokens = metric.averageTokens * (1 - alpha) + tokensUsed * alpha;

    // Track error types
    if (!success && error) {
      const errorType = this.categorizeError(error);
      metric.errorTypes[errorType] = (metric.errorTypes[errorType] || 0) + 1;
    }

    // Calculate improvement trend (simplified)
    const previousSuccess = metric.successRate;
    metric.improvementTrend = success ? 
      Math.min(1, metric.improvementTrend + 0.1) : 
      Math.max(-1, metric.improvementTrend - 0.1);

    metric.lastUpdated = Date.now();
    this.performanceMetrics.set(key, metric);
  }

  private extractLearningPatterns(
    agentId: string,
    taskType: string,
    success: boolean,
    context: AgentContext,
    result?: string,
    error?: string
  ): void {
    // Extract patterns from successful implementations
    if (success && result) {
      const patterns = this.identifySuccessPatterns(result, context);
      patterns.forEach(pattern => this.addOrUpdatePattern(pattern));
    }

    // Extract patterns from failures
    if (!success && error) {
      const patterns = this.identifyFailurePatterns(error, context);
      patterns.forEach(pattern => this.addOrUpdatePattern(pattern));
    }

    // Extract context utilization patterns
    const contextPatterns = this.analyzeContextUtilization(agentId, taskType, context, success);
    contextPatterns.forEach(pattern => this.addOrUpdatePattern(pattern));
  }

  private identifySuccessPatterns(result: string, context: AgentContext): LearningPattern[] {
    const patterns: LearningPattern[] = [];

    // Pattern: Successful code structures
    const codeStructures = this.extractCodeStructures(result);
    codeStructures.forEach(structure => {
      patterns.push({
        id: `success_structure_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
        pattern: `Successful ${structure.type}: ${structure.pattern}`,
        category: 'success',
        frequency: 1,
        effectiveness: 0.8,
        context: [context.task, structure.type],
        examples: [structure.example],
        recommendations: [`Consider using ${structure.type} pattern for similar tasks`],
        lastSeen: Date.now(),
        projectSpecific: true
      });
    });

    // Pattern: Effective prompt-result relationships
    if (context.task.length > 10) {
      patterns.push({
        id: `prompt_success_${Date.now()}`,
        pattern: `Effective prompt-result: ${context.task.substring(0, 50)} -> ${result.substring(0, 100)}`,
        category: 'success',
        frequency: 1,
        effectiveness: 0.7,
        context: ['prompt_optimization'],
        examples: [context.task],
        recommendations: ['Use similar prompt structure for related tasks'],
        lastSeen: Date.now(),
        projectSpecific: false
      });
    }

    return patterns;
  }

  private identifyFailurePatterns(error: string, context: AgentContext): LearningPattern[] {
    const patterns: LearningPattern[] = [];

    // Pattern: Common error types
    const errorType = this.categorizeError(error);
    patterns.push({
      id: `failure_${errorType}_${Date.now()}`,
      pattern: `Common ${errorType} error: ${error.substring(0, 100)}`,
      category: 'failure',
      frequency: 1,
      effectiveness: 0.2,
      context: [errorType, context.task.substring(0, 30)],
      examples: [error],
      recommendations: [`Add specific handling for ${errorType} errors`, 'Improve error prevention measures'],
      lastSeen: Date.now(),
      projectSpecific: true
    });

    // Pattern: Context-error relationships
    if (context.codeContext) {
      patterns.push({
        id: `context_error_${Date.now()}`,
        pattern: `Context led to error: ${context.codeContext.substring(0, 50)} -> ${error.substring(0, 50)}`,
        category: 'failure',
        frequency: 1,
        effectiveness: 0.3,
        context: ['context_analysis'],
        examples: [context.codeContext],
        recommendations: ['Review context quality', 'Consider context filtering'],
        lastSeen: Date.now(),
        projectSpecific: true
      });
    }

    return patterns;
  }

  private analyzeContextUtilization(
    agentId: string,
    taskType: string,
    context: AgentContext,
    success: boolean
  ): LearningPattern[] {
    const patterns: LearningPattern[] = [];

    // Analyze context size vs success rate
    const contextSize = (context.codeContext?.length || 0) + (context.files?.length || 0) * 100;
    
    patterns.push({
      id: `context_utilization_${Date.now()}`,
      pattern: `Context size ${contextSize} chars for ${taskType} -> ${success ? 'success' : 'failure'}`,
      category: success ? 'optimization' : 'failure',
      frequency: 1,
      effectiveness: success ? 0.6 : 0.4,
      context: [agentId, taskType, 'context_analysis'],
      examples: [`Context size: ${contextSize}`],
      recommendations: success ? 
        ['Context size appropriate for task type'] : 
        ['Consider adjusting context size', 'Review context relevance'],
      lastSeen: Date.now(),
      projectSpecific: false
    });

    return patterns;
  }

  private extractCodeStructures(result: string): Array<{type: string, pattern: string, example: string}> {
    const structures: Array<{type: string, pattern: string, example: string}> = [];

    // Extract function patterns
    const functionMatches = result.match(/function\s+\w+\s*\([^)]*\)\s*{[^}]*}/g);
    if (functionMatches) {
      functionMatches.forEach(match => {
        structures.push({
          type: 'function',
          pattern: 'function declaration with parameters',
          example: match.substring(0, 100)
        });
      });
    }

    // Extract class patterns
    const classMatches = result.match(/class\s+\w+[^{]*{[^}]*}/g);
    if (classMatches) {
      classMatches.forEach(match => {
        structures.push({
          type: 'class',
          pattern: 'class definition with methods',
          example: match.substring(0, 100)
        });
      });
    }

    // Extract React component patterns
    const reactMatches = result.match(/const\s+\w+.*=.*\([^)]*\)\s*=>\s*{[^}]*}/g);
    if (reactMatches) {
      reactMatches.forEach(match => {
        structures.push({
          type: 'react_component',
          pattern: 'functional React component',
          example: match.substring(0, 100)
        });
      });
    }

    return structures;
  }

  private categorizeError(error: string): string {
    const errorLower = error.toLowerCase();
    
    if (errorLower.includes('syntax')) return 'syntax';
    if (errorLower.includes('type')) return 'type';
    if (errorLower.includes('import') || errorLower.includes('module')) return 'import';
    if (errorLower.includes('undefined') || errorLower.includes('null')) return 'null_reference';
    if (errorLower.includes('async') || errorLower.includes('promise')) return 'async';
    if (errorLower.includes('network') || errorLower.includes('fetch')) return 'network';
    
    return 'general';
  }

  private addOrUpdatePattern(pattern: LearningPattern): void {
    // Check for similar existing patterns
    const similar = Array.from(this.learningPatterns.values()).find(p => 
      this.calculatePatternSimilarity(p, pattern) > 0.8
    );

    if (similar) {
      // Update existing pattern
      similar.frequency += 1;
      similar.effectiveness = (similar.effectiveness + pattern.effectiveness) / 2;
      similar.lastSeen = Date.now();
      
      // Merge recommendations
      pattern.recommendations.forEach(rec => {
        if (!similar.recommendations.includes(rec)) {
          similar.recommendations.push(rec);
        }
      });
    } else {
      // Add new pattern
      this.learningPatterns.set(pattern.id, pattern);
    }
  }

  private calculatePatternSimilarity(p1: LearningPattern, p2: LearningPattern): number {
    // Simple similarity calculation based on pattern text
    const words1 = p1.pattern.toLowerCase().split(' ');
    const words2 = p2.pattern.toLowerCase().split(' ');
    
    const commonWords = words1.filter(word => words2.includes(word));
    return commonWords.length / Math.max(words1.length, words2.length);
  }

  private analyzeOptimizationOpportunities(
    agentId: string,
    taskType: string,
    success: boolean,
    context: AgentContext,
    result?: string
  ): void {
    // Generate optimization suggestions based on patterns
    const metric = this.performanceMetrics.get(`${agentId}_${taskType}`);
    if (!metric) return;

    // Suggest prompt optimization if success rate is low
    if (metric.successRate < 0.7) {
      this.addOptimizationSuggestion({
        id: `prompt_opt_${agentId}_${Date.now()}`,
        targetAgent: agentId,
        type: 'prompt_optimization',
        description: `Low success rate (${(metric.successRate * 100).toFixed(1)}%) for ${taskType} tasks`,
        expectedImpact: 0.3,
        effort: 4,
        priority: 'high',
        data: {
          currentSuccessRate: metric.successRate,
          taskType,
          suggestedChanges: ['Add more specific examples', 'Clarify success criteria', 'Improve error handling guidance']
        }
      });
    }

    // Suggest model selection optimization if response times are slow
    if (metric.averageTime > 15000) {
      this.addOptimizationSuggestion({
        id: `model_opt_${agentId}_${Date.now()}`,
        targetAgent: agentId,
        type: 'model_selection',
        description: `Slow response times (${(metric.averageTime / 1000).toFixed(1)}s) for ${taskType}`,
        expectedImpact: 0.4,
        effort: 2,
        priority: 'medium',
        data: {
          currentResponseTime: metric.averageTime,
          suggestedModels: ['gpt-3.5-turbo', 'claude-3-haiku'],
          reasoning: 'Faster models for simpler tasks'
        }
      });
    }

    // Suggest context improvement if failures are high
    const contextErrors = Object.keys(metric.errorTypes).filter(type => 
      ['context', 'unclear', 'incomplete'].some(keyword => type.includes(keyword))
    );
    
    if (contextErrors.length > 0) {
      this.addOptimizationSuggestion({
        id: `context_opt_${agentId}_${Date.now()}`,
        targetAgent: agentId,
        type: 'context_improvement',
        description: `Context-related errors detected in ${taskType} tasks`,
        expectedImpact: 0.25,
        effort: 3,
        priority: 'medium',
        data: {
          errorTypes: contextErrors,
          suggestions: ['Provide more relevant context', 'Filter irrelevant information', 'Add context validation']
        }
      });
    }
  }

  private addOptimizationSuggestion(suggestion: OptimizationSuggestion): void {
    // Avoid duplicate suggestions
    const existing = this.optimizationSuggestions.find(s => 
      s.targetAgent === suggestion.targetAgent && 
      s.type === suggestion.type &&
      s.description === suggestion.description
    );

    if (!existing) {
      this.optimizationSuggestions.push(suggestion);
      
      // Keep only recent suggestions (last 50)
      if (this.optimizationSuggestions.length > 50) {
        this.optimizationSuggestions.sort((a, b) => b.expectedImpact - a.expectedImpact);
        this.optimizationSuggestions = this.optimizationSuggestions.slice(0, 50);
      }
    }
  }

  private async analyzeLearningOpportunities(): Promise<{
    newPatterns: number;
    improvementOpportunities: string[];
    crossProjectLearnings: string[];
  }> {
    const recentPatterns = Array.from(this.learningPatterns.values())
      .filter(p => Date.now() - p.lastSeen < 86400000); // Last 24 hours

    const improvementOpportunities: string[] = [];
    const crossProjectLearnings: string[] = [];

    // Analyze recent patterns for improvement opportunities
    const successPatterns = recentPatterns.filter(p => p.category === 'success' && p.effectiveness > 0.7);
    const failurePatterns = recentPatterns.filter(p => p.category === 'failure');

    if (successPatterns.length > 0) {
      improvementOpportunities.push(`${successPatterns.length} successful patterns identified for knowledge base`);
    }

    if (failurePatterns.length > 0) {
      improvementOpportunities.push(`${failurePatterns.length} failure patterns require attention`);
    }

    // Identify cross-project learnings (patterns that appear across different contexts)
    const generalPatterns = Array.from(this.learningPatterns.values())
      .filter(p => !p.projectSpecific && p.frequency > 2);

    generalPatterns.forEach(pattern => {
      crossProjectLearnings.push(`Pattern "${pattern.pattern}" appears in multiple contexts`);
    });

    return {
      newPatterns: recentPatterns.length,
      improvementOpportunities,
      crossProjectLearnings
    };
  }

  private async generateOptimizationSuggestions(): Promise<OptimizationSuggestion[]> {
    const suggestions: OptimizationSuggestion[] = [];

    // Analyze performance metrics for optimization opportunities
    for (const metric of this.performanceMetrics.values()) {
      // Rule-based optimization suggestions
      if (metric.successRate < 0.6) {
        suggestions.push({
          id: `perf_opt_${metric.agentId}_${Date.now()}`,
          targetAgent: metric.agentId,
          type: 'rule_update',
          description: `Poor performance in ${metric.taskType}: ${(metric.successRate * 100).toFixed(1)}% success rate`,
          expectedImpact: 0.4,
          effort: 5,
          priority: 'high',
          data: {
            metric,
            suggestedActions: ['Review agent rules', 'Update success criteria', 'Add performance monitoring']
          }
        });
      }

      if (metric.averageTokens > 5000) {
        suggestions.push({
          id: `token_opt_${metric.agentId}_${Date.now()}`,
          targetAgent: metric.agentId,
          type: 'prompt_optimization',
          description: `High token usage in ${metric.taskType}: ${metric.averageTokens.toFixed(0)} avg tokens`,
          expectedImpact: 0.2,
          effort: 3,
          priority: 'medium',
          data: {
            currentTokens: metric.averageTokens,
            suggestions: ['Optimize prompt length', 'Improve context relevance', 'Use more efficient phrasing']
          }
        });
      }
    }

    return suggestions.slice(0, 10); // Return top 10 suggestions
  }

  private async updatePerformanceModels(): Promise<{
    overallImprovement: number;
    modelUpdates: string[];
  }> {
    const modelUpdates: string[] = [];
    let totalImprovement = 0;
    let updateCount = 0;

    // Calculate overall system improvement
    for (const metric of this.performanceMetrics.values()) {
      if (metric.improvementTrend > 0) {
        totalImprovement += metric.improvementTrend;
        updateCount++;
        modelUpdates.push(`${metric.agentId} ${metric.taskType}: +${(metric.improvementTrend * 100).toFixed(1)}%`);
      }
    }

    const overallImprovement = updateCount > 0 ? totalImprovement / updateCount : 0;

    return {
      overallImprovement,
      modelUpdates
    };
  }

  private generateLearningReport(
    learningAnalysis: any,
    optimizations: OptimizationSuggestion[],
    performanceUpdate: any
  ): string {
    return `[CONTINUOUS LEARNING REPORT]

LEARNING ANALYSIS:
- New patterns identified: ${learningAnalysis.newPatterns}
- Total patterns in knowledge base: ${this.learningPatterns.size}
- Active performance metrics: ${this.performanceMetrics.size}

IMPROVEMENT OPPORTUNITIES:
${learningAnalysis.improvementOpportunities.map((opp: string) => `- ${opp}`).join('\n')}

CROSS-PROJECT LEARNINGS:
${learningAnalysis.crossProjectLearnings.map((learning: string) => `- ${learning}`).join('\n')}

OPTIMIZATION SUGGESTIONS:
${optimizations.slice(0, 5).map(opt => `
- ${opt.description}
  Impact: ${(opt.expectedImpact * 100).toFixed(0)}% | Effort: ${opt.effort}/10 | Priority: ${opt.priority}
`).join('')}

PERFORMANCE UPDATES:
- Overall system improvement: ${(performanceUpdate.overallImprovement * 100).toFixed(1)}%
- Agents with positive trends: ${performanceUpdate.modelUpdates.length}

MODEL PERFORMANCE TRENDS:
${performanceUpdate.modelUpdates.slice(0, 5).map((update: string) => `- ${update}`).join('\n')}

PATTERN CATEGORIES:
- Success patterns: ${Array.from(this.learningPatterns.values()).filter(p => p.category === 'success').length}
- Failure patterns: ${Array.from(this.learningPatterns.values()).filter(p => p.category === 'failure').length}
- Optimization patterns: ${Array.from(this.learningPatterns.values()).filter(p => p.category === 'optimization').length}
- Error resolution patterns: ${Array.from(this.learningPatterns.values()).filter(p => p.category === 'error_resolution').length}

RECOMMENDATIONS:
1. Implement high-priority optimization suggestions
2. Monitor performance improvements after changes
3. Continue collecting learning data from all agents
4. Consider A/B testing for prompt optimizations
5. Update agent rules based on successful patterns

NEXT LEARNING CYCLE:
- Review new patterns in 24 hours
- Assess optimization effectiveness
- Expand cross-project pattern recognition
- Enhance automated improvement suggestions

[END LEARNING REPORT]`;
  }

  // Cleanup and maintenance methods
  public cleanupOldPatterns(maxAge: number = 7 * 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;
    
    for (const [id, pattern] of this.learningPatterns) {
      if (pattern.lastSeen < cutoff && pattern.frequency < 3) {
        this.learningPatterns.delete(id);
      }
    }
  }

  public exportLearningData(): any {
    return {
      patterns: Array.from(this.learningPatterns.values()),
      metrics: Array.from(this.performanceMetrics.values()),
      suggestions: this.optimizationSuggestions,
      exportTimestamp: Date.now()
    };
  }

  public importLearningData(data: any): void {
    if (data.patterns) {
      data.patterns.forEach((pattern: LearningPattern) => {
        this.learningPatterns.set(pattern.id, pattern);
      });
    }

    if (data.metrics) {
      data.metrics.forEach((metric: PerformanceMetric) => {
        this.performanceMetrics.set(`${metric.agentId}_${metric.taskType}`, metric);
      });
    }

    if (data.suggestions) {
      this.optimizationSuggestions.push(...data.suggestions);
    }
  }
}