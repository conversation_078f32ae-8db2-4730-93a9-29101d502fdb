// components/middleware/resource-optimizer.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from '../agents/agent-base';

export interface ModelRecommendation {
  agentId: string;
  recommendedModel: string;
  estimatedCost: number;
  estimatedTokens: number;
  rationale: string;
}

export class ResourceOptimizerAgent extends AgentBase {
  private modelPricing: Record<string, number> = {
    'gpt-4': 0.03,
    'gpt-3.5-turbo': 0.002,
    'claude-3-opus': 0.015,
    'claude-3-sonnet': 0.003
  };

  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return ['model_selection', 'cost_optimization', 'resource_allocation'];
  }

  public getSystemPrompt(): string {
    return `You are the Resource Optimizer, responsible for selecting optimal models, managing resources, and ensuring cost-effective operation.

Make intelligent resource allocation decisions balancing quality, performance, and cost:
- Select appropriate models based on task complexity
- Monitor and optimize token usage
- Implement cost-saving strategies
- Track performance metrics

Focus on data-driven decisions based on historical performance and current system state.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const recommendation = await this.optimizeResources(context);
    
    return this.createSuccessResponse(
      JSON.stringify(recommendation, null, 2),
      this.estimateTokens(context),
      Date.now(),
      ['Monitor cost efficiency', 'Track performance metrics'],
      { recommendation }
    );
  }

  private async optimizeResources(context: AgentContext): Promise<ModelRecommendation> {
    const taskComplexity = this.assessComplexity(context.task);
    const estimatedTokens = this.estimateTokens(context);
    
    let recommendedModel = 'gpt-3.5-turbo';
    if (taskComplexity > 0.7) recommendedModel = 'claude-3-sonnet';
    if (taskComplexity > 0.9) recommendedModel = 'gpt-4';

    const estimatedCost = estimatedTokens * this.modelPricing[recommendedModel];

    return {
      agentId: context.metadata?.agentId || 'unknown',
      recommendedModel,
      estimatedCost,
      estimatedTokens,
      rationale: `Selected ${recommendedModel} based on complexity score ${taskComplexity}`
    };
  }

  private assessComplexity(task: string): number {
    const complexWords = ['complex', 'advanced', 'sophisticated', 'algorithm', 'architecture'];
    const simpleWords = ['simple', 'basic', 'straightforward'];
    
    let score = 0.5;
    complexWords.forEach(word => {
      if (task.toLowerCase().includes(word)) score += 0.2;
    });
    simpleWords.forEach(word => {
      if (task.toLowerCase().includes(word)) score -= 0.2;
    });
    
    return Math.max(0, Math.min(1, score));
  }
}
