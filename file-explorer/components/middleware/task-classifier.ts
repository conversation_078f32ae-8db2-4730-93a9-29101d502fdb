// components/middleware/task-classifier.ts
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>onfig, AgentContext, AgentResponse } from '../agents/agent-base';

export interface TaskClassification {
  type: 'code_generation' | 'code_modification' | 'research' | 'architecture' | 'design' | 'testing';
  complexity: 'trivial' | 'simple' | 'moderate' | 'complex' | 'very_complex';
  domain: string[];
  estimatedTokens: number;
  recommendedAgent: string;
  confidence: number;
}

export class TaskClassifierAgent extends AgentBase {
  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return ['task_analysis', 'complexity_assessment', 'agent_recommendation'];
  }

  public getSystemPrompt(): string {
    return `You are the Task Classifier, responsible for analyzing user requests and determining their complexity, type, and optimal delegation path.

Analyze requests to determine:
- Primary task objective and scope
- Required technical capabilities
- Complexity level and domain area
- Context requirements and dependencies
- Optimal agent assignment

Provide structured classification output for efficient delegation.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const classification = await this.classifyTask(context);
    
    return this.createSuccessResponse(
      JSON.stringify(classification, null, 2),
      this.estimateTokens(context),
      Date.now(),
      [`Recommended agent: ${classification.recommendedAgent}`],
      { classification }
    );
  }

  private async classifyTask(context: AgentContext): Promise<TaskClassification> {
    const task = context.task.toLowerCase();
    
    // Task type classification
    let type: TaskClassification['type'] = 'code_generation';
    if (task.includes('research') || task.includes('analyze')) type = 'research';
    else if (task.includes('design') || task.includes('ui')) type = 'design';
    else if (task.includes('test')) type = 'testing';
    else if (task.includes('architecture')) type = 'architecture';
    else if (task.includes('fix') || task.includes('modify')) type = 'code_modification';

    // Complexity assessment
    let complexity: TaskClassification['complexity'] = 'moderate';
    if (task.includes('simple') || task.includes('basic')) complexity = 'simple';
    else if (task.includes('complex') || task.includes('advanced')) complexity = 'complex';

    // Domain identification
    const domain: string[] = [];
    if (task.includes('frontend') || task.includes('ui')) domain.push('frontend');
    if (task.includes('backend') || task.includes('api')) domain.push('backend');
    if (task.includes('database')) domain.push('database');

    // Agent recommendation
    const recommendedAgent = this.recommendAgent(type, complexity);

    return {
      type,
      complexity,
      domain,
      estimatedTokens: this.estimateTokens(context),
      recommendedAgent,
      confidence: 0.85
    };
  }

  private recommendAgent(type: string, complexity: string): string {
    if (type === 'research') return 'researcher';
    if (type === 'architecture') return 'architect';
    if (type === 'design') return 'designer';
    if (type === 'testing') return 'tester';

    // Implementation agents by complexity
    switch (complexity) {
      case 'simple': return 'intern';
      case 'moderate': return 'junior';
      case 'complex': return 'midlevel';
      default: return 'senior';
    }
  }
}
