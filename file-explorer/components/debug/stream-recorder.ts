// components/debug/stream-recorder.ts

import type { StreamChunk, StreamCallback } from '../agents/llm-request-service'
import type { AgentConfig } from '../agents/agent-base'
import { recordStreamSession } from './stream-replay-store'
import { estimateCost } from '../agents/llm-provider-registry'

interface RecordedChunk {
  delta: string
  timestamp: number
}

interface StreamRecordingSession {
  agentId: string
  agentName: string
  model: string
  provider: string
  startTime: number
  chunks: RecordedChunk[]
  totalTokens: number
  isComplete: boolean
}

export class StreamRecorder {
  private static instance: StreamRecorder | null = null
  private activeSessions: Map<string, StreamRecordingSession> = new Map()

  private constructor() {}

  public static getInstance(): StreamRecorder {
    if (!StreamRecorder.instance) {
      StreamRecorder.instance = new StreamRecorder()
    }
    return StreamRecorder.instance
  }

  /**
   * Create a recording wrapper around a stream callback
   * This captures all chunks for later replay
   */
  public wrapStreamCallback(
    agent: AgentConfig,
    originalCallback: StreamCallback,
    sessionId?: string
  ): StreamCallback {
    // Only record in development mode
    if (process.env.NODE_ENV !== 'development') {
      return originalCallback
    }

    const recordingSessionId = sessionId || `${agent.id}_${Date.now()}`
    
    // Initialize recording session
    const session: StreamRecordingSession = {
      agentId: agent.id,
      agentName: agent.name || agent.id,
      model: agent.model || 'unknown',
      provider: agent.provider || 'unknown',
      startTime: Date.now(),
      chunks: [],
      totalTokens: 0,
      isComplete: false
    }

    this.activeSessions.set(recordingSessionId, session)

    console.log(`🎬 StreamRecorder: Started recording session ${recordingSessionId} for ${session.agentName}`)

    return (chunk: StreamChunk) => {
      try {
        // Record the chunk
        this.recordChunk(recordingSessionId, chunk)

        // Call the original callback
        originalCallback(chunk)

        // If stream is complete, finalize the recording
        if (chunk.isComplete) {
          this.finalizeRecording(recordingSessionId, chunk)
        }
      } catch (error) {
        console.warn('StreamRecorder: Error in wrapped callback:', error)
        // Still call original callback even if recording fails
        originalCallback(chunk)
      }
    }
  }

  /**
   * Record a single chunk
   */
  private recordChunk(sessionId: string, chunk: StreamChunk): void {
    const session = this.activeSessions.get(sessionId)
    if (!session || session.isComplete) {
      return
    }

    // Only record chunks with actual content
    if (chunk.delta && chunk.delta.length > 0) {
      session.chunks.push({
        delta: chunk.delta,
        timestamp: Date.now()
      })
    }

    // Update token count if available
    if (chunk.tokensUsed) {
      session.totalTokens = chunk.tokensUsed.total || chunk.tokensUsed.output || 0
    }
  }

  /**
   * Finalize recording and save to store
   */
  private finalizeRecording(sessionId: string, finalChunk: StreamChunk): void {
    const session = this.activeSessions.get(sessionId)
    if (!session || session.isComplete) {
      return
    }

    session.isComplete = true

    // Calculate final metrics
    const finalTokens = finalChunk.tokensUsed?.total || session.totalTokens
    const estimatedCost = this.calculateCost(session, finalTokens)
    const finishReason = finalChunk.finishReason || 'stop'

    // Record the session for replay
    recordStreamSession(
      session.agentId,
      session.agentName,
      session.model,
      session.provider,
      session.chunks,
      finalTokens,
      estimatedCost,
      finishReason
    )

    // Clean up
    this.activeSessions.delete(sessionId)

    console.log(`🎬 StreamRecorder: Finalized recording ${sessionId} - ${session.chunks.length} chunks, ${finalTokens} tokens, $${estimatedCost.toFixed(4)}`)
  }

  /**
   * Calculate estimated cost for the session
   */
  private calculateCost(session: StreamRecordingSession, tokens: number): number {
    try {
      // Estimate cost based on provider and tokens
      // Assume roughly equal input/output tokens for estimation
      const inputTokens = Math.floor(tokens * 0.3) // Rough estimate
      const outputTokens = Math.floor(tokens * 0.7) // Rough estimate
      
      return estimateCost(session.provider as any, inputTokens, outputTokens)
    } catch (error) {
      console.warn('StreamRecorder: Failed to calculate cost:', error)
      return 0
    }
  }

  /**
   * Cancel an active recording session
   */
  public cancelRecording(sessionId: string): void {
    const session = this.activeSessions.get(sessionId)
    if (session) {
      this.activeSessions.delete(sessionId)
      console.log(`🎬 StreamRecorder: Cancelled recording ${sessionId}`)
    }
  }

  /**
   * Get active recording sessions (for debugging)
   */
  public getActiveSessions(): string[] {
    return Array.from(this.activeSessions.keys())
  }

  /**
   * Clear all active sessions
   */
  public clearActiveSessions(): void {
    const count = this.activeSessions.size
    this.activeSessions.clear()
    console.log(`🎬 StreamRecorder: Cleared ${count} active sessions`)
  }

  /**
   * Get recording statistics
   */
  public getStats(): {
    activeSessions: number
    totalRecorded: number
  } {
    return {
      activeSessions: this.activeSessions.size,
      totalRecorded: 0 // Would need to track this separately
    }
  }
}

// Global instance
export const streamRecorder = StreamRecorder.getInstance()

/**
 * Convenience function to wrap stream callbacks with recording
 */
export function withStreamRecording(
  agent: AgentConfig,
  callback: StreamCallback,
  sessionId?: string
): StreamCallback {
  return streamRecorder.wrapStreamCallback(agent, callback, sessionId)
}

/**
 * Development-only function to manually record a stream session
 * Useful for testing or when integrating with existing streaming code
 */
export function recordManualStreamSession(
  agentId: string,
  agentName: string,
  model: string,
  provider: string,
  chunks: Array<{ content: string; timestamp: number }>,
  tokens: number,
  cost: number,
  finishReason: string = 'stop'
): void {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  // Convert content chunks to delta chunks
  const deltaChunks = chunks.map((chunk, index) => ({
    delta: index === 0 ? chunk.content : chunk.content.slice(chunks[index - 1].content.length),
    timestamp: chunk.timestamp
  }))

  recordStreamSession(
    agentId,
    agentName,
    model,
    provider,
    deltaChunks,
    tokens,
    cost,
    finishReason
  )
}
