// components/debug/TokenUsageOverlay.tsx

"use client"

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  Activity,
  DollarSign,
  Zap,
  Brain,
  Cpu,
  Play,
  Pause,
  RotateCcw,
  Eye,
  EyeOff,
  ChevronUp,
  ChevronDown,
  Film
} from 'lucide-react'

import { getAnalyticsService } from '@/services/analytics-service'
import { CostTracker } from '@/lib/cost-tracker'
import type { AgentAnalyticsMetrics } from '@/types/analytics'
import StreamReplayDebugger from './StreamReplayDebugger'
import { useStreamReplayStore, setGlobalStreamReplayStore } from './stream-replay-store'

interface TokenUsageStats {
  tokensUsed: number
  estimatedCost: number
  requestsPerMinute: number
  mostActiveAgent: string
  mostExpensiveModel: string
  currentActiveAgents: number
  tasksInProgress: number
  systemLoad: number
  lastUpdated: number
}

interface OverlayState {
  isVisible: boolean
  isPaused: boolean
  isExpanded: boolean
  showStreamReplay: boolean
}

// Custom hook for interval updates
function useInterval(callback: () => void, delay: number | null) {
  const savedCallback = React.useRef(callback);

  React.useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  React.useEffect(() => {
    if (delay === null) return;

    const id = setInterval(() => savedCallback.current(), delay);
    return () => clearInterval(id);
  }, [delay]);
}

// Custom hook for token usage data
function useTokenUsageData(isPaused: boolean): TokenUsageStats {
  const [stats, setStats] = useState<TokenUsageStats>({
    tokensUsed: 0,
    estimatedCost: 0,
    requestsPerMinute: 0,
    mostActiveAgent: 'none',
    mostExpensiveModel: 'none',
    currentActiveAgents: 0,
    tasksInProgress: 0,
    systemLoad: 0,
    lastUpdated: Date.now()
  });

  const analyticsService = useMemo(() => getAnalyticsService(), []);
  const costTracker = useMemo(() => new CostTracker(), []);

  const updateStats = useCallback(async () => {
    try {
      const metrics = await analyticsService.getAnalyticsMetrics();
      const monthlySummary = costTracker.getMonthlyCostSummary();

      // Calculate requests per minute (approximate)
      const now = Date.now();
      const oneMinuteAgo = now - 60000;
      const recentEntries = costTracker.getCostHistory(new Date(oneMinuteAgo), new Date(now));
      const requestsPerMinute = recentEntries.length;

      // Find most expensive model
      let mostExpensiveModel = 'none';
      let highestCost = 0;
      Object.entries(monthlySummary.modelBreakdown).forEach(([model, data]) => {
        if (data.cost > highestCost) {
          highestCost = data.cost;
          mostExpensiveModel = model;
        }
      });

      setStats({
        tokensUsed: metrics.totalTokensUsed,
        estimatedCost: metrics.totalCost,
        requestsPerMinute,
        mostActiveAgent: metrics.mostActiveAgent,
        mostExpensiveModel,
        currentActiveAgents: metrics.currentActiveAgents,
        tasksInProgress: metrics.tasksInProgress,
        systemLoad: metrics.systemLoad,
        lastUpdated: now
      });
    } catch (error) {
      console.warn('TokenUsageOverlay: Failed to update stats:', error);
    }
  }, [analyticsService, costTracker]);

  // Update every 2 seconds when not paused
  useInterval(updateStats, isPaused ? null : 2000);

  // Initial load
  useEffect(() => {
    updateStats();
  }, [updateStats]);

  return stats;
}

export default function TokenUsageOverlay() {
  // Development mode check
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const [overlayState, setOverlayState] = useState<OverlayState>(() => {
    // Try to restore state from localStorage
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('tokenUsageOverlay');
      if (saved) {
        try {
          return JSON.parse(saved);
        } catch {
          // Fall back to defaults
        }
      }
    }
    return {
      isVisible: true,
      isPaused: false,
      isExpanded: false,
      showStreamReplay: false
    };
  });

  const stats = useTokenUsageData(overlayState.isPaused);
  const streamReplayStore = useStreamReplayStore();
  const { sessions } = streamReplayStore;

  // Set global store for access from LLM service
  useEffect(() => {
    setGlobalStreamReplayStore(streamReplayStore);
  }, [streamReplayStore]);

  // Save state to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('tokenUsageOverlay', JSON.stringify(overlayState));
    }
  }, [overlayState]);

  const toggleVisibility = useCallback(() => {
    setOverlayState(prev => ({ ...prev, isVisible: !prev.isVisible }));
  }, []);

  const togglePause = useCallback(() => {
    setOverlayState(prev => ({ ...prev, isPaused: !prev.isPaused }));
  }, []);

  const toggleExpanded = useCallback(() => {
    setOverlayState(prev => ({ ...prev, isExpanded: !prev.isExpanded }));
  }, []);

  const toggleStreamReplay = useCallback(() => {
    setOverlayState(prev => ({ ...prev, showStreamReplay: !prev.showStreamReplay }));
  }, []);

  const clearMetrics = useCallback(async () => {
    try {
      const analyticsService = getAnalyticsService();
      const costTrackerInstance = new CostTracker();

      // Clear stress test results from analytics
      analyticsService.clearStressTestResults();

      // Clear cost history from cost tracker
      await costTrackerInstance.clearCostHistory();

      console.log('TokenUsageOverlay: Metrics cleared');
    } catch (error) {
      console.warn('TokenUsageOverlay: Failed to clear metrics:', error);
    }
  }, []);

  const formatCurrency = (amount: number): string => {
    return `$${amount.toFixed(4)}`;
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  if (!overlayState.isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-[9999]">
        <Button
          onClick={toggleVisibility}
          size="sm"
          variant="outline"
          className="bg-background/80 backdrop-blur-sm border-border/50"
        >
          <Eye className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="fixed bottom-4 right-4 z-[9999] max-w-[300px]">
        <Card className="bg-background/95 backdrop-blur-sm border-border/50 shadow-lg">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Token Usage
                {overlayState.isPaused && (
                  <Badge variant="secondary" className="text-xs">
                    Paused
                  </Badge>
                )}
              </CardTitle>
              <div className="flex items-center gap-1">
                <Button
                  onClick={togglePause}
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                >
                  {overlayState.isPaused ? (
                    <Play className="h-3 w-3" />
                  ) : (
                    <Pause className="h-3 w-3" />
                  )}
                </Button>
                <Button
                  onClick={toggleExpanded}
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                >
                  {overlayState.isExpanded ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronUp className="h-3 w-3" />
                  )}
                </Button>
                <Button
                  onClick={toggleVisibility}
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                >
                  <EyeOff className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              {/* Core Metrics */}
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center gap-2">
                  <Zap className="h-3 w-3 text-blue-500" />
                  <div>
                    <div className="font-medium">{formatNumber(stats.tokensUsed)}</div>
                    <div className="text-muted-foreground">Tokens</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-3 w-3 text-green-500" />
                  <div>
                    <div className="font-medium">{formatCurrency(stats.estimatedCost)}</div>
                    <div className="text-muted-foreground">Cost</div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center gap-2">
                  <Activity className="h-3 w-3 text-orange-500" />
                  <div>
                    <div className="font-medium">{stats.requestsPerMinute}</div>
                    <div className="text-muted-foreground">Req/min</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Cpu className="h-3 w-3 text-purple-500" />
                  <div>
                    <div className="font-medium">{stats.systemLoad.toFixed(1)}%</div>
                    <div className="text-muted-foreground">Load</div>
                  </div>
                </div>
              </div>

              {overlayState.isExpanded && (
                <>
                  <Separator />

                  {/* Expanded Details */}
                  <div className="space-y-2 text-xs">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Most Active Agent:</span>
                      <Badge variant="outline" className="text-xs">
                        {stats.mostActiveAgent}
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Most Expensive Model:</span>
                      <Badge variant="outline" className="text-xs">
                        {stats.mostExpensiveModel}
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Active Agents:</span>
                      <span className="font-medium">{stats.currentActiveAgents}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Tasks in Progress:</span>
                      <span className="font-medium">{stats.tasksInProgress}</span>
                    </div>
                  </div>

                  <Separator />

                  {/* Controls */}
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={clearMetrics}
                      size="sm"
                      variant="outline"
                      className="flex-1 h-7 text-xs"
                    >
                      <RotateCcw className="h-3 w-3 mr-1" />
                      Clear
                    </Button>
                    <Button
                      onClick={toggleStreamReplay}
                      size="sm"
                      variant={overlayState.showStreamReplay ? "default" : "outline"}
                      className="flex-1 h-7 text-xs"
                      title={`${overlayState.showStreamReplay ? 'Hide' : 'Show'} Stream Replay Debugger`}
                    >
                      <Film className="h-3 w-3 mr-1" />
                      Replay
                      {sessions.length > 0 && (
                        <Badge variant="secondary" className="ml-1 text-xs px-1 py-0">
                          {sessions.length}
                        </Badge>
                      )}
                    </Button>
                  </div>
                </>
              )}

              {/* Last Updated */}
              <div className="text-xs text-muted-foreground text-center">
                Updated: {formatTimestamp(stats.lastUpdated)}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Stream Replay Debugger Panel */}
      {overlayState.showStreamReplay && (
        <div className="fixed inset-4 z-[9998] bg-background/95 backdrop-blur-sm border border-border rounded-lg shadow-2xl">
          <StreamReplayDebugger />
        </div>
      )}
    </>
  );
}
