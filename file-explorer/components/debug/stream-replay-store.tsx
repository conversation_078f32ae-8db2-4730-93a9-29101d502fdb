// components/debug/stream-replay-store.ts

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react'

export interface StreamChunk {
  delta: string
  timestamp: number
  originalDelay?: number // Time between this chunk and the previous one
}

export interface StreamSession {
  id: string
  agentId: string
  agentName: string
  timestamp: number
  model: string
  chunks: StreamChunk[]
  tokens: number
  cost: number
  duration: number // Total duration of the stream
  provider: string
  finishReason: string
}

export interface ReplayState {
  status: 'stopped' | 'playing' | 'paused' | 'completed'
  playbackSpeed: number
  currentChunkIndex: number
}

interface StreamReplayContextType {
  // State
  sessions: StreamSession[]
  selectedSessionId: string | null
  replayState: ReplayState
  maxSessions: number

  // Actions
  addSession: (session: Omit<StreamSession, 'id'>) => void
  selectSession: (sessionId: string) => void
  clearSessions: () => void
  removeSession: (sessionId: string) => void

  // Replay controls
  startReplay: () => void
  pauseReplay: () => void
  stopReplay: () => void
  restartReplay: () => void
  skipToEnd: () => void
  setPlaybackSpeed: (speed: number) => void
  setCurrentChunkIndex: (index: number) => void
}

// Create React Context
const StreamReplayContext = createContext<StreamReplayContextType | undefined>(undefined)

// Context Provider Component
interface StreamReplayProviderProps {
  children: ReactNode
}

export function StreamReplayProvider({ children }: StreamReplayProviderProps) {
  const [sessions, setSessions] = useState<StreamSession[]>([])
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null)
  const [replayState, setReplayState] = useState<ReplayState>({
    status: 'stopped',
    playbackSpeed: 1.0,
    currentChunkIndex: 0
  })
  const maxSessions = 10 // Keep last 10 sessions

  // Session management
  const addSession = useCallback((sessionData: Omit<StreamSession, 'id'>) => {
    const session: StreamSession = {
      ...sessionData,
      id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    setSessions(prevSessions => {
      const newSessions = [session, ...prevSessions]

      // Keep only the last maxSessions
      if (newSessions.length > maxSessions) {
        newSessions.splice(maxSessions)
      }

      return newSessions
    })

    // Auto-select the new session if none is selected
    setSelectedSessionId(prev => prev || session.id)

    console.log(`StreamReplayStore: Added session for ${sessionData.agentName} (${sessionData.chunks.length} chunks)`)
  }, [])

  const selectSession = useCallback((sessionId: string) => {
    setSelectedSessionId(sessionId)
    setReplayState(prev => ({
      ...prev,
      status: 'stopped',
      currentChunkIndex: 0
    }))
  }, [])

  const clearSessions = useCallback(() => {
    setSessions([])
    setSelectedSessionId(null)
    setReplayState({
      status: 'stopped',
      playbackSpeed: 1.0,
      currentChunkIndex: 0
    })
    console.log('StreamReplayStore: All sessions cleared')
  }, [])

  const removeSession = useCallback((sessionId: string) => {
    setSessions(prevSessions => {
      const newSessions = prevSessions.filter(s => s.id !== sessionId)
      return newSessions
    })

    setSelectedSessionId(prev => {
      if (prev === sessionId) {
        setSessions(currentSessions => {
          const newSelectedId = currentSessions.length > 0 ? currentSessions[0].id : null
          return currentSessions
        })
        setReplayState({ status: 'stopped', playbackSpeed: 1.0, currentChunkIndex: 0 })
        return null
      }
      return prev
    })
  }, [])

  // Replay controls
  const startReplay = useCallback(() => {
    setReplayState(prev => ({ ...prev, status: 'playing' }))
  }, [])

  const pauseReplay = useCallback(() => {
    setReplayState(prev => ({ ...prev, status: 'paused' }))
  }, [])

  const stopReplay = useCallback(() => {
    setReplayState(prev => ({ ...prev, status: 'stopped', currentChunkIndex: 0 }))
  }, [])

  const restartReplay = useCallback(() => {
    setReplayState(prev => ({ ...prev, status: 'playing', currentChunkIndex: 0 }))
  }, [])

  const skipToEnd = useCallback(() => {
    const selectedSession = sessions.find(s => s.id === selectedSessionId)
    if (selectedSession) {
      setReplayState(prev => ({
        ...prev,
        status: 'completed',
        currentChunkIndex: selectedSession.chunks.length
      }))
    }
  }, [sessions, selectedSessionId])

  const setPlaybackSpeed = useCallback((speed: number) => {
    setReplayState(prev => ({
      ...prev,
      playbackSpeed: Math.max(0.25, Math.min(4, speed))
    }))
  }, [])

  const setCurrentChunkIndex = useCallback((index: number) => {
    setReplayState(prev => ({ ...prev, currentChunkIndex: index }))
  }, [])

  const contextValue: StreamReplayContextType = {
    sessions,
    selectedSessionId,
    replayState,
    maxSessions,
    addSession,
    selectSession,
    clearSessions,
    removeSession,
    startReplay,
    pauseReplay,
    stopReplay,
    restartReplay,
    skipToEnd,
    setPlaybackSpeed,
    setCurrentChunkIndex
  }

  return (
    <StreamReplayContext.Provider value={contextValue}>
      {children}
    </StreamReplayContext.Provider>
  )
}

// Custom hook to use the context
export function useStreamReplayStore(): StreamReplayContextType {
  const context = useContext(StreamReplayContext)
  if (context === undefined) {
    throw new Error('useStreamReplayStore must be used within a StreamReplayProvider')
  }
  return context
}

// Helper function to create a stream session from LLM response data
export function createStreamSession(
  agentId: string,
  agentName: string,
  model: string,
  provider: string,
  chunks: Array<{ delta: string; timestamp: number }>,
  tokens: number,
  cost: number,
  finishReason: string
): Omit<StreamSession, 'id'> {
  // Calculate original delays between chunks
  const processedChunks: StreamChunk[] = chunks.map((chunk, index) => ({
    delta: chunk.delta,
    timestamp: chunk.timestamp,
    originalDelay: index > 0 ? chunk.timestamp - chunks[index - 1].timestamp : 0
  }))

  const duration = chunks.length > 0
    ? chunks[chunks.length - 1].timestamp - chunks[0].timestamp
    : 0

  return {
    agentId,
    agentName,
    timestamp: Date.now(),
    model,
    chunks: processedChunks,
    tokens,
    cost,
    duration,
    provider,
    finishReason
  }
}

// Global instance for easy access from LLM service
let globalStreamReplayStore: StreamReplayContextType | null = null

export function setGlobalStreamReplayStore(store: StreamReplayContextType) {
  globalStreamReplayStore = store
}

export function getStreamReplayStore(): StreamReplayContextType | null {
  return globalStreamReplayStore
}

// Development mode only - record stream sessions
export function recordStreamSession(
  agentId: string,
  agentName: string,
  model: string,
  provider: string,
  chunks: Array<{ delta: string; timestamp: number }>,
  tokens: number,
  cost: number,
  finishReason: string
): void {
  // Only record in development mode
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  try {
    const store = getStreamReplayStore()
    if (!store) {
      console.warn('StreamReplayStore: Store not available, skipping session recording')
      return
    }

    const session = createStreamSession(
      agentId,
      agentName,
      model,
      provider,
      chunks,
      tokens,
      cost,
      finishReason
    )

    store.addSession(session)
    console.log(`🎬 StreamReplayStore: Recorded session for ${agentName} with ${chunks.length} chunks`)
  } catch (error) {
    console.warn('StreamReplayStore: Failed to record session:', error)
  }
}
