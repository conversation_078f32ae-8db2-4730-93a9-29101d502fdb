// components/analytics/AnalyticsCharts.tsx

"use client"

import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>ltip, 
  Legend, 
  ResponsiveContainer 
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import type { ChartDataPoint, TimeSeriesDataPoint, AnalyticsChartConfig } from "@/types/analytics"
import { CHART_COLORS } from "@/types/analytics"

interface AnalyticsLineChartProps {
  title: string
  description?: string
  data: TimeSeriesDataPoint[]
  dataKey?: string
  height?: number
  color?: string
}

export function AnalyticsLineChart({ 
  title, 
  description, 
  data, 
  dataKey = "value", 
  height = 300,
  color = CHART_COLORS.primary[0]
}: AnalyticsLineChartProps) {
  const chartConfig = {
    [dataKey]: {
      label: title,
      color: color,
    },
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <LineChart data={data} height={height}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line 
              type="monotone" 
              dataKey={dataKey} 
              stroke={color}
              strokeWidth={2}
              dot={{ fill: color, strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

interface AnalyticsAreaChartProps {
  title: string
  description?: string
  data: TimeSeriesDataPoint[]
  dataKey?: string
  height?: number
  color?: string
}

export function AnalyticsAreaChart({ 
  title, 
  description, 
  data, 
  dataKey = "value", 
  height = 300,
  color = CHART_COLORS.primary[0]
}: AnalyticsAreaChartProps) {
  const chartConfig = {
    [dataKey]: {
      label: title,
      color: color,
    },
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <AreaChart data={data} height={height}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Area 
              type="monotone" 
              dataKey={dataKey} 
              stroke={color}
              fill={color}
              fillOpacity={0.3}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

interface AnalyticsBarChartProps {
  title: string
  description?: string
  data: ChartDataPoint[]
  dataKey?: string
  height?: number
  color?: string
}

export function AnalyticsBarChart({ 
  title, 
  description, 
  data, 
  dataKey = "value", 
  height = 300,
  color = CHART_COLORS.primary[0]
}: AnalyticsBarChartProps) {
  const chartConfig = {
    [dataKey]: {
      label: title,
      color: color,
    },
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart data={data} height={height}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="name" 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar 
              dataKey={dataKey} 
              fill={color}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

interface AnalyticsPieChartProps {
  title: string
  description?: string
  data: ChartDataPoint[]
  height?: number
  colors?: string[]
}

export function AnalyticsPieChart({ 
  title, 
  description, 
  data, 
  height = 300,
  colors = CHART_COLORS.rainbow
}: AnalyticsPieChartProps) {
  const chartConfig = data.reduce((config, item, index) => {
    config[item.name] = {
      label: item.name,
      color: colors[index % colors.length],
    }
    return config
  }, {} as Record<string, { label: string; color: string }>)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <PieChart height={height}>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
            <ChartTooltip content={<ChartTooltipContent />} />
            <Legend />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

interface MetricCardProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease' | 'neutral'
    period: string
  }
  icon?: string
  color?: string
  description?: string
}

export function MetricCard({ 
  title, 
  value, 
  change, 
  icon, 
  color = 'blue',
  description 
}: MetricCardProps) {
  const getChangeColor = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return 'text-green-600'
      case 'decrease':
        return 'text-red-600'
      case 'neutral':
      default:
        return 'text-gray-600'
    }
  }

  const getChangeIcon = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return '↗️'
      case 'decrease':
        return '↘️'
      case 'neutral':
      default:
        return '➡️'
    }
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          {icon && <span className="text-lg">{icon}</span>}
        </div>
        <div className="space-y-1">
          <div className="text-2xl font-bold">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </div>
          {change && (
            <p className={`text-xs ${getChangeColor(change.type)}`}>
              {getChangeIcon(change.type)} {change.value}% {change.period}
            </p>
          )}
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
