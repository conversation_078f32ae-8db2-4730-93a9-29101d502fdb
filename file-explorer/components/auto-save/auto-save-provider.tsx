"use client"

import Re<PERSON>, { createContext, useContext, use<PERSON><PERSON>back, ReactNode } from 'react';
import { useAutoSave } from './auto-save-engine';
import { AutoSaveService } from './auto-save-service';

interface AutoSaveContextType {
  triggerManualSave: () => Promise<void>;
  isAutoSaveEnabled: boolean;
}

const AutoSaveContext = createContext<AutoSaveContextType | null>(null);

interface AutoSaveProviderProps {
  children: ReactNode;
}

/**
 * ✅ Auto-Save Provider
 * Provides auto-save functionality throughout the application
 * Integrates with SystemSettings for configuration
 */
export const AutoSaveProvider: React.FC<AutoSaveProviderProps> = ({ children }) => {
  const autoSaveService = AutoSaveService.getInstance();

  // ✅ Auto-save callback that saves real application data
  const autoSaveCallback = useCallback(async () => {
    await autoSaveService.saveAll();
  }, [autoSaveService]);

  // ✅ Hook up the auto-save engine with real save logic
  useAutoSave(autoSaveCallback);

  // ✅ Manual save trigger
  const triggerManualSave = useCallback(async () => {
    await autoSaveService.triggerManualSave();
  }, [autoSaveService]);

  // ✅ Check if auto-save is enabled (for UI indicators)
  const isAutoSaveEnabled = true; // Will be determined by the hook internally

  const value: AutoSaveContextType = {
    triggerManualSave,
    isAutoSaveEnabled
  };

  return (
    <AutoSaveContext.Provider value={value}>
      {children}
    </AutoSaveContext.Provider>
  );
};

/**
 * ✅ Hook to access auto-save functionality
 */
export const useAutoSaveContext = (): AutoSaveContextType => {
  const context = useContext(AutoSaveContext);
  if (!context) {
    throw new Error('useAutoSaveContext must be used within an AutoSaveProvider');
  }
  return context;
};
