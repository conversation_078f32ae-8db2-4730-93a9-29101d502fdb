// components/auto-save/auto-save-service.ts
import { getGlobalSettingsManager } from '../settings/global-settings';

/**
 * ✅ Auto-Save Service
 * Handles saving different types of application data
 * Uses real persistence mechanisms (not mocks)
 */
export class AutoSaveService {
  private static instance: AutoSaveService | null = null;
  private settingsManager = getGlobalSettingsManager();

  public static getInstance(): AutoSaveService {
    if (!AutoSaveService.instance) {
      AutoSaveService.instance = new AutoSaveService();
    }
    return AutoSaveService.instance;
  }

  /**
   * ✅ Save All Application Data
   * Main auto-save function that persists critical app state
   */
  public async saveAll(): Promise<void> {
    const startTime = Date.now();
    console.log('🔄 Auto-save: Starting comprehensive save...');

    try {
      // Save settings (agents, system, cost, privacy, editor)
      await this.saveSettings();

      // Save agent states if available
      await this.saveAgentStates();

      // Save editor content if available
      await this.saveEditorContent();

      // Save kanban board state if available
      await this.saveKanbanState();

      const duration = Date.now() - startTime;
      console.log(`✅ Auto-save: Completed in ${duration}ms`);
    } catch (error) {
      console.error('❌ Auto-save: Failed to save application data:', error);
      throw error;
    }
  }

  /**
   * ✅ Save Settings Data
   * Persists all settings through SettingsManager
   */
  private async saveSettings(): Promise<void> {
    try {
      this.settingsManager.saveSettings();
      console.log('💾 Auto-save: Settings saved');
    } catch (error) {
      console.error('❌ Auto-save: Settings save failed:', error);
    }
  }

  /**
   * ✅ Save Agent States
   * Persists agent configurations and states
   */
  private async saveAgentStates(): Promise<void> {
    try {
      // Check if we're in browser environment
      if (typeof window === 'undefined') return;

      // Save to localStorage as backup
      const settings = this.settingsManager.getSettings();
      localStorage.setItem('synapse-agent-backup', JSON.stringify({
        agents: settings.agents,
        timestamp: Date.now()
      }));

      // If Electron API is available, save through IPC
      if (window.electronAPI && window.electronAPI.saveAgentStates) {
        const result = await window.electronAPI.saveAgentStates(settings.agents);
        if (!result.success) {
          console.warn('Electron agent states save failed:', result.error);
        }
      }

      console.log('🤖 Auto-save: Agent states saved');
    } catch (error) {
      console.error('❌ Auto-save: Agent states save failed:', error);
    }
  }

  /**
   * ✅ Save Editor Content
   * Persists current editor content if available
   */
  private async saveEditorContent(): Promise<void> {
    try {
      // Check if we're in browser environment
      if (typeof window === 'undefined') return;

      // Get editor content from global state or DOM
      const editorContent = this.getEditorContent();
      if (!editorContent) return;

      // Save to localStorage
      localStorage.setItem('synapse-editor-backup', JSON.stringify({
        content: editorContent.content,
        filePath: editorContent.filePath,
        timestamp: Date.now()
      }));

      // If Electron API is available, save file
      if (window.electronAPI && window.electronAPI.saveFile && editorContent.filePath) {
        const result = await window.electronAPI.saveFile(editorContent.filePath, editorContent.content);
        if (!result.success) {
          console.warn('Electron file save failed:', result.error);
        }
      }

      console.log('📝 Auto-save: Editor content saved');
    } catch (error) {
      console.error('❌ Auto-save: Editor content save failed:', error);
    }
  }

  /**
   * ✅ Save Kanban Board State
   * Persists kanban board data
   */
  private async saveKanbanState(): Promise<void> {
    try {
      // Check if we're in browser environment
      if (typeof window === 'undefined') return;

      // Get kanban state from localStorage or global state
      const kanbanState = this.getKanbanState();
      if (!kanbanState) return;

      // Save to localStorage
      localStorage.setItem('synapse-kanban-backup', JSON.stringify({
        ...kanbanState,
        timestamp: Date.now()
      }));

      // If Electron API is available, save through IPC
      if (window.electronAPI && window.electronAPI.saveBoardState) {
        const result = await window.electronAPI.saveBoardState(kanbanState);
        if (!result.success) {
          console.warn('Electron board state save failed:', result.error);
        }
      }

      console.log('📋 Auto-save: Kanban state saved');
    } catch (error) {
      console.error('❌ Auto-save: Kanban state save failed:', error);
    }
  }

  /**
   * ✅ Get Current Editor Content
   * Retrieves editor content from various sources
   */
  private getEditorContent(): { content: string; filePath: string | null } | null {
    try {
      // Try to get from Monaco editor if available
      if (typeof window !== 'undefined' && (window as any).monaco) {
        const editor = (window as any).monacoEditor;
        if (editor) {
          return {
            content: editor.getValue(),
            filePath: (window as any).currentFilePath || null
          };
        }
      }

      // Try to get from localStorage
      const stored = localStorage.getItem('synapse-current-file');
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          content: parsed.content || '',
          filePath: parsed.path || null
        };
      }

      return null;
    } catch (error) {
      console.error('❌ Auto-save: Failed to get editor content:', error);
      return null;
    }
  }

  /**
   * ✅ Get Current Kanban State
   * Retrieves kanban board state
   */
  private getKanbanState(): any | null {
    try {
      // Try to get from localStorage
      const stored = localStorage.getItem('kanban-boards');
      if (stored) {
        return JSON.parse(stored);
      }

      return null;
    } catch (error) {
      console.error('❌ Auto-save: Failed to get kanban state:', error);
      return null;
    }
  }

  /**
   * ✅ Manual Save Trigger
   * Allows manual triggering of auto-save
   */
  public async triggerManualSave(): Promise<void> {
    console.log('🔄 Manual save triggered');
    await this.saveAll();
  }
}
