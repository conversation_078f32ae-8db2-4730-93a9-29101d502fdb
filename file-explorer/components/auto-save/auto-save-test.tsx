"use client"

import React, { useState, useEffect } from 'react';
import { useSystemSettings } from '../settings/settings-context';
import { useAutoSaveContext } from './auto-save-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock, Save } from 'lucide-react';

/**
 * ✅ Auto-Save Test Component
 * Displays real-time auto-save status and allows testing
 */
export const AutoSaveTest: React.FC = () => {
  const { systemSettings } = useSystemSettings();
  const { triggerManualSave } = useAutoSaveContext();
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);
  const [nextSaveCountdown, setNextSaveCountdown] = useState<number>(0);
  const [isTesting, setIsTesting] = useState(false);

  // Calculate next save time
  useEffect(() => {
    if (!systemSettings.autoSave || !lastSaveTime) {
      setNextSaveCountdown(0);
      return;
    }

    const interval = setInterval(() => {
      const now = Date.now();
      const nextSaveTime = lastSaveTime.getTime() + (systemSettings.autoSaveInterval * 1000);
      const remaining = Math.max(0, Math.ceil((nextSaveTime - now) / 1000));
      setNextSaveCountdown(remaining);
    }, 1000);

    return () => clearInterval(interval);
  }, [lastSaveTime, systemSettings.autoSave, systemSettings.autoSaveInterval]);

  // Listen for auto-save events
  useEffect(() => {
    const handleAutoSave = () => {
      setLastSaveTime(new Date());
    };

    // Listen for console logs that indicate auto-save
    const originalLog = console.log;
    console.log = (...args) => {
      if (args[0] === '✅ Auto-save completed') {
        handleAutoSave();
      }
      originalLog(...args);
    };

    return () => {
      console.log = originalLog;
    };
  }, []);

  const handleTestSave = async () => {
    setIsTesting(true);
    try {
      await triggerManualSave();
      setLastSaveTime(new Date());
    } catch (error) {
      console.error('Test save failed:', error);
    } finally {
      setIsTesting(false);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString();
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Save className="h-5 w-5" />
          Auto-Save Status
        </CardTitle>
        <CardDescription>
          Real-time monitoring of auto-save functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Auto-Save Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Auto-Save</span>
          <Badge variant={systemSettings.autoSave ? 'default' : 'secondary'}>
            {systemSettings.autoSave ? (
              <>
                <CheckCircle className="h-3 w-3 mr-1" />
                Enabled
              </>
            ) : (
              <>
                <XCircle className="h-3 w-3 mr-1" />
                Disabled
              </>
            )}
          </Badge>
        </div>

        {/* Interval Setting */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Interval</span>
          <Badge variant="outline">
            {systemSettings.autoSaveInterval}s
          </Badge>
        </div>

        {/* Last Save Time */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Last Save</span>
          <span className="text-sm text-muted-foreground">
            {lastSaveTime ? formatTime(lastSaveTime) : 'Never'}
          </span>
        </div>

        {/* Next Save Countdown */}
        {systemSettings.autoSave && nextSaveCountdown > 0 && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Next Save</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {nextSaveCountdown}s
            </Badge>
          </div>
        )}

        {/* Test Button */}
        <Button
          onClick={handleTestSave}
          disabled={isTesting}
          variant="outline"
          className="w-full"
        >
          {isTesting ? 'Testing...' : 'Test Save Now'}
        </Button>

        {/* Status Messages */}
        <div className="text-xs text-muted-foreground space-y-1">
          <div>• Settings: {systemSettings.autoSave ? 'Active' : 'Inactive'}</div>
          <div>• Interval: Every {systemSettings.autoSaveInterval} seconds</div>
          <div>• Saves: Agent states, editor content, kanban boards</div>
        </div>
      </CardContent>
    </Card>
  );
};
