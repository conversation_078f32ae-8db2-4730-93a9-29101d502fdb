// components/auto-save/auto-save-engine.ts
import { useEffect, useRef, useCallback } from 'react';
import { useSystemSettings } from '../settings/settings-context';

/**
 * ✅ Auto-Save Engine Hook
 * Manages periodic auto-save based on SystemSettings
 * Respects autoSave boolean and autoSaveInterval timing
 */
export function useAutoSave(callback: () => void | Promise<void>) {
  const { systemSettings } = useSystemSettings();
  const { autoSave, autoSaveInterval } = systemSettings;
  const timer = useRef<NodeJS.Timeout | null>(null);
  const callbackRef = useRef(callback);

  // Keep callback reference current
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Stable callback that uses the current reference
  const executeCallback = useCallback(async () => {
    try {
      console.log('🔄 Auto-save triggered');
      const result = callbackRef.current();
      if (result instanceof Promise) {
        await result;
      }
      console.log('✅ Auto-save completed');
    } catch (error) {
      console.error('❌ Auto-save failed:', error);
    }
  }, []);

  useEffect(() => {
    // Clear existing timer
    if (timer.current) {
      clearInterval(timer.current);
      timer.current = null;
    }

    // Don't start timer if auto-save is disabled
    if (!autoSave) {
      console.log('⏸️ Auto-save disabled');
      return;
    }

    // Start new timer with current interval
    const intervalMs = autoSaveInterval * 1000;
    console.log(`⏰ Auto-save enabled: every ${autoSaveInterval} seconds`);
    
    timer.current = setInterval(executeCallback, intervalMs);

    // Cleanup function
    return () => {
      if (timer.current) {
        clearInterval(timer.current);
        timer.current = null;
      }
    };
  }, [autoSave, autoSaveInterval, executeCallback]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timer.current) {
        clearInterval(timer.current);
      }
    };
  }, []);
}

/**
 * ✅ Debounced Auto-Save Hook
 * Prevents excessive saves during rapid changes
 */
export function useDebouncedAutoSave(
  callback: () => void | Promise<void>,
  debounceMs: number = 250
) {
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  const debouncedCallback = useCallback(() => {
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    debounceTimer.current = setTimeout(async () => {
      try {
        console.log('🔄 Debounced auto-save triggered');
        const result = callback();
        if (result instanceof Promise) {
          await result;
        }
        console.log('✅ Debounced auto-save completed');
      } catch (error) {
        console.error('❌ Debounced auto-save failed:', error);
      }
    }, debounceMs);
  }, [callback, debounceMs]);

  useAutoSave(debouncedCallback);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);
}
