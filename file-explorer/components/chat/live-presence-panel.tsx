// components/chat/live-presence-panel.tsx

"use client"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { 
  Brain, 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Users
} from "lucide-react"
import { useAgentPresence } from "@/hooks/useAgentPresence"
import type { LiveAgentState } from "@/types/agent-events"

interface LivePresencePanelProps {
  className?: string
  showOnlyActive?: boolean
}

export default function LivePresencePanel({ className, showOnlyActive = false }: LivePresencePanelProps) {
  const {
    agents,
    activeTaskCount,
    getActiveAgents,
    getCurrentTasks
  } = useAgentPresence()

  const displayAgents = showOnlyActive ? getActiveAgents() : agents
  const currentTasks = getCurrentTasks()

  const getStatusIcon = (status: LiveAgentState['status']) => {
    switch (status) {
      case 'thinking':
        return <Brain className="h-3 w-3 text-blue-500 animate-pulse" />
      case 'working':
        return <Loader2 className="h-3 w-3 text-green-500 animate-spin" />
      case 'done':
        return <CheckCircle className="h-3 w-3 text-green-600" />
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-500" />
      case 'idle':
      default:
        return <Clock className="h-3 w-3 text-gray-400" />
    }
  }

  const getStatusColor = (status: LiveAgentState['status']) => {
    switch (status) {
      case 'thinking':
        return "bg-blue-500/10 text-blue-600 border-blue-500/20"
      case 'working':
        return "bg-green-500/10 text-green-600 border-green-500/20"
      case 'done':
        return "bg-green-500/10 text-green-600 border-green-500/20"
      case 'error':
        return "bg-red-500/10 text-red-600 border-red-500/20"
      case 'idle':
      default:
        return "bg-gray-500/10 text-gray-600 border-gray-500/20"
    }
  }

  const getAgentIcon = (agentType: string) => {
    const icons: Record<string, string> = {
      micromanager: "🧠",
      intern: "🛠️",
      junior: "📦",
      midlevel: "⚙️",
      senior: "🧱",
      architect: "🏗️",
      designer: "🎨",
      tester: "🧪",
      researcher: "📘"
    }
    return icons[agentType] || "🤖"
  }

  const getStatusText = (agent: LiveAgentState) => {
    switch (agent.status) {
      case 'thinking':
        return "Analyzing request..."
      case 'working':
        return agent.currentTask ? `Working on: ${agent.currentTask.description.slice(0, 30)}...` : "Working..."
      case 'done':
        return "Task completed"
      case 'error':
        return "Encountered an error"
      case 'idle':
      default:
        return "Available for tasks"
    }
  }

  const formatTimeElapsed = (timestamp: number) => {
    const elapsed = Date.now() - timestamp
    const seconds = Math.floor(elapsed / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ago`
    } else if (minutes > 0) {
      return `${minutes}m ago`
    } else if (seconds > 30) {
      return `${seconds}s ago`
    }
    return "just now"
  }

  if (displayAgents.length === 0) {
    return (
      <div className={cn("p-3 text-center text-muted-foreground text-xs", className)}>
        {showOnlyActive ? "No agents are currently active" : "No agents available"}
      </div>
    )
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* Header */}
      <div className="flex items-center gap-2 px-3 py-1 border-b border-editor-border">
        <Users className="h-3 w-3 text-muted-foreground" />
        <span className="text-xs font-medium text-muted-foreground">
          {showOnlyActive ? "Active Agents" : "Agent Presence"}
        </span>
        {activeTaskCount > 0 && (
          <Badge variant="outline" className="text-xs h-4">
            {activeTaskCount} active
          </Badge>
        )}
      </div>

      {/* Agent list */}
      <div className="px-3 space-y-2">
        {displayAgents.map((agent) => {
          const task = currentTasks.find(t => t.agentId === agent.agentId)
          
          return (
            <div key={agent.agentId} className="flex items-start gap-2 p-2 rounded-md hover:bg-accent/50 transition-colors">
              <div className="flex-shrink-0 mt-0.5">
                <span className="text-sm">{getAgentIcon(agent.agentType)}</span>
              </div>
              
              <div className="flex-1 min-w-0 space-y-1">
                <div className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className={cn("text-xs h-4 flex items-center gap-1", getStatusColor(agent.status))}
                  >
                    {getStatusIcon(agent.status)}
                    {agent.agentName}
                  </Badge>
                  
                  <span className="text-xs text-muted-foreground">
                    {formatTimeElapsed(agent.lastActivity)}
                  </span>
                </div>
                
                <p className="text-xs text-muted-foreground">
                  {getStatusText(agent)}
                </p>
                
                {task && (
                  <div className="flex items-center gap-2 text-xs">
                    <div className="flex-1 bg-accent rounded-full h-1">
                      <div 
                        className="bg-primary h-1 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      />
                    </div>
                    <span className="text-muted-foreground font-mono">
                      {task.progress}%
                    </span>
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
