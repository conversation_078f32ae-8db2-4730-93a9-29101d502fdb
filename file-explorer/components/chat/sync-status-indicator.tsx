"use client"

import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Wifi, WifiOff, RefreshCw } from "lucide-react"
import { globalChatState } from "@/services/global-chat-state"

interface SyncStatusIndicatorProps {
  className?: string
}

export default function SyncStatusIndicator({ className }: SyncStatusIndicatorProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [isSyncing, setIsSyncing] = useState(false)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)

  useEffect(() => {
    // Check if we're in an Electron environment with IPC capabilities
    const hasIPC = typeof window !== 'undefined' && window.electronAPI
    setIsConnected(hasIPC)

    // Subscribe to chat state changes to detect sync activity
    const unsubscribe = globalChatState.subscribe((state) => {
      setIsSyncing(false) // Reset syncing state when we receive updates
      setLastSyncTime(new Date())
    })

    // Test sync on mount
    if (hasIPC) {
      setIsSyncing(true)
      globalChatState.syncWithOtherWindows().finally(() => {
        setIsSyncing(false)
        setLastSyncTime(new Date())
      })
    }

    return unsubscribe
  }, [])

  const getStatusDisplay = () => {
    if (!isConnected) {
      return {
        icon: <WifiOff className="h-3 w-3" />,
        text: "Offline",
        className: "bg-red-500/10 text-red-600 border-red-500/20"
      }
    }

    if (isSyncing) {
      return {
        icon: <RefreshCw className="h-3 w-3 animate-spin" />,
        text: "Syncing",
        className: "bg-blue-500/10 text-blue-600 border-blue-500/20"
      }
    }

    return {
      icon: <Wifi className="h-3 w-3" />,
      text: lastSyncTime ? "Synced" : "Connected",
      className: "bg-green-500/10 text-green-600 border-green-500/20"
    }
  }

  const status = getStatusDisplay()

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Badge
        variant="outline"
        className={cn(
          "text-xs flex items-center gap-1 transition-colors cursor-help",
          status.className
        )}
        title={
          isConnected
            ? lastSyncTime
              ? `Last synced: ${lastSyncTime.toLocaleTimeString()}`
              : "Real-time sync enabled"
            : "Offline - no sync available"
        }
      >
        {status.icon}
        {status.text}
      </Badge>
    </div>
  )
}
