name: Validate Model Metadata

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'components/agents/**/*-models.ts'
      - 'components/agents/**/model-*.ts'
      - 'components/agents/**/metadata-*.ts'
      - 'scripts/validateModelMetadata.*'
      - '.github/workflows/validate-metadata.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'components/agents/**/*-models.ts'
      - 'components/agents/**/model-*.ts'
      - 'components/agents/**/metadata-*.ts'
      - 'scripts/validateModelMetadata.*'
      - '.github/workflows/validate-metadata.yml'

jobs:
  validate-metadata:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: 'file-explorer/package-lock.json'
        
    - name: Install dependencies
      working-directory: ./file-explorer
      run: npm ci
      
    - name: Validate Model Metadata
      working-directory: ./file-explorer
      run: npm run validate:models
      
    - name: Run TypeScript validation (if ts-node available)
      working-directory: ./file-explorer
      run: |
        if npm list ts-node > /dev/null 2>&1; then
          npm run validate:models:ts
        else
          echo "ts-node not available, skipping TypeScript validation"
        fi
      continue-on-error: true
      
    - name: Upload validation results
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: validation-results-${{ matrix.node-version }}
        path: |
          file-explorer/validation-report.txt
        retention-days: 7

  security-check:
    runs-on: ubuntu-latest
    needs: validate-metadata
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Check for sensitive data in metadata
      run: |
        echo "🔍 Checking for sensitive data patterns..."
        
        # Check for potential API keys or secrets
        if grep -r -i "api[_-]key\|secret\|token\|password" file-explorer/components/agents/*-models.ts; then
          echo "❌ Potential sensitive data found in metadata files"
          exit 1
        fi
        
        # Check for hardcoded URLs that might contain secrets
        if grep -r "https://.*@" file-explorer/components/agents/*-models.ts; then
          echo "❌ Potential credentials in URLs found"
          exit 1
        fi
        
        # Check for test/development endpoints
        if grep -r -i "localhost\|127\.0\.0\.1\|test\..*\.com\|dev\..*\.com" file-explorer/components/agents/*-models.ts; then
          echo "❌ Development/test endpoints found in metadata"
          exit 1
        fi
        
        echo "✅ No sensitive data patterns detected"

  compliance-report:
    runs-on: ubuntu-latest
    needs: [validate-metadata, security-check]
    if: always()
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Generate compliance report
      run: |
        echo "📊 Model Metadata Compliance Report" > compliance-report.md
        echo "=================================" >> compliance-report.md
        echo "" >> compliance-report.md
        echo "**Validation Status:** ${{ needs.validate-metadata.result }}" >> compliance-report.md
        echo "**Security Check:** ${{ needs.security-check.result }}" >> compliance-report.md
        echo "" >> compliance-report.md
        echo "**Files Checked:**" >> compliance-report.md
        find file-explorer/components/agents -name "*-models.ts" -o -name "model-*.ts" -o -name "metadata-*.ts" | while read file; do
          echo "- $file" >> compliance-report.md
        done
        echo "" >> compliance-report.md
        echo "**Generated:** $(date)" >> compliance-report.md
        
    - name: Upload compliance report
      uses: actions/upload-artifact@v4
      with:
        name: compliance-report
        path: compliance-report.md
        retention-days: 30
