// pages/api/telemetry.ts

import { NextApiRequest, NextApiResponse } from 'next';

/**
 * ✅ Privacy-Aware Telemetry API Endpoint
 * Handles anonymized usage data collection
 * GDPR compliant - no PII stored
 */

interface TelemetryEvent {
  eventName: string;
  data: Record<string, any>;
  timestamp: number;
  sessionId: string;
}

interface TelemetryRequest {
  events: TelemetryEvent[];
}

// In-memory storage for demo (in production, use a proper database)
const telemetryData: TelemetryEvent[] = [];
const maxStoredEvents = 1000; // Limit memory usage

/**
 * ✅ Validate telemetry event
 */
function validateEvent(event: any): event is TelemetryEvent {
  return (
    typeof event === 'object' &&
    typeof event.eventName === 'string' &&
    typeof event.data === 'object' &&
    typeof event.timestamp === 'number' &&
    typeof event.sessionId === 'string' &&
    event.eventName.length > 0 &&
    event.eventName.length < 100 &&
    event.sessionId.length > 0 &&
    event.sessionId.length < 100
  );
}

/**
 * ✅ Sanitize event data to ensure no PII
 */
function sanitizeEvent(event: TelemetryEvent): TelemetryEvent {
  const sanitizedData: Record<string, any> = {};
  
  // Only allow specific data types and keys
  for (const [key, value] of Object.entries(event.data)) {
    if (typeof key === 'string' && key.length < 50) {
      if (typeof value === 'number' || typeof value === 'boolean') {
        sanitizedData[key] = value;
      } else if (typeof value === 'string' && value.length < 100) {
        // Only store string length, not content
        sanitizedData[`${key}_length`] = value.length;
      }
    }
  }
  
  return {
    eventName: event.eventName,
    data: sanitizedData,
    timestamp: event.timestamp,
    sessionId: event.sessionId
  };
}

/**
 * ✅ Store telemetry events
 */
function storeEvents(events: TelemetryEvent[]): void {
  // Sanitize and validate events
  const validEvents = events
    .filter(validateEvent)
    .map(sanitizeEvent);
  
  // Add to storage
  telemetryData.push(...validEvents);
  
  // Limit storage size
  if (telemetryData.length > maxStoredEvents) {
    telemetryData.splice(0, telemetryData.length - maxStoredEvents);
  }
  
  console.log(`📊 Stored ${validEvents.length} telemetry events (total: ${telemetryData.length})`);
}

/**
 * ✅ Get telemetry statistics
 */
function getTelemetryStats() {
  const eventCounts: Record<string, number> = {};
  const sessionCounts: Record<string, number> = {};
  
  for (const event of telemetryData) {
    eventCounts[event.eventName] = (eventCounts[event.eventName] || 0) + 1;
    sessionCounts[event.sessionId] = (sessionCounts[event.sessionId] || 0) + 1;
  }
  
  return {
    totalEvents: telemetryData.length,
    uniqueSessions: Object.keys(sessionCounts).length,
    eventTypes: Object.keys(eventCounts).length,
    topEvents: Object.entries(eventCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([name, count]) => ({ name, count })),
    timeRange: telemetryData.length > 0 ? {
      earliest: Math.min(...telemetryData.map(e => e.timestamp)),
      latest: Math.max(...telemetryData.map(e => e.timestamp))
    } : null
  };
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Telemetry-Session');
  
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }
  
  if (req.method === 'POST') {
    try {
      const body: TelemetryRequest = req.body;
      
      if (!body || !Array.isArray(body.events)) {
        return res.status(400).json({ 
          error: 'Invalid request body. Expected { events: TelemetryEvent[] }' 
        });
      }
      
      if (body.events.length > 100) {
        return res.status(400).json({ 
          error: 'Too many events. Maximum 100 events per request.' 
        });
      }
      
      // Store events
      storeEvents(body.events);
      
      res.status(200).json({ 
        success: true, 
        eventsReceived: body.events.length,
        timestamp: Date.now()
      });
      
    } catch (error) {
      console.error('Telemetry API error:', error);
      res.status(500).json({ 
        error: 'Internal server error' 
      });
    }
  } else if (req.method === 'GET') {
    // Return telemetry statistics (for debugging/monitoring)
    try {
      const stats = getTelemetryStats();
      res.status(200).json(stats);
    } catch (error) {
      console.error('Telemetry stats error:', error);
      res.status(500).json({ 
        error: 'Internal server error' 
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST', 'OPTIONS']);
    res.status(405).json({ 
      error: `Method ${req.method} not allowed` 
    });
  }
}
