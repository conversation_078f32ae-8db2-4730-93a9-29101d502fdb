{"metadata": {"generatedAt": "2025-05-28T14:26:53.658Z", "version": "1.0.0", "description": "Model registry snapshot with verified metadata from official sources", "totalModels": 19, "totalProviders": 6}, "providerStats": {"anthropic": {"name": "Anthropic", "count": 4, "models": ["claude-3-5-sonnet-********", "claude-3-haiku-********", "claude-3-opus-********", "claude-3-sonnet-********"]}, "deepseek": {"name": "DeepSeek", "count": 2, "models": ["deepseek-chat", "deepseek-coder"]}, "fireworks": {"name": "Fireworks AI", "count": 2, "models": ["accounts/fireworks/models/llama-v3p1-70b-instruct", "accounts/fireworks/models/mixtral-8x7b-instruct"]}, "google": {"name": "Google AI", "count": 3, "models": ["gemini-1.5-flash", "gemini-1.5-pro", "gemini-pro"]}, "openai": {"name": "OpenAI", "count": 5, "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini"]}, "openrouter": {"name": "OpenRouter", "count": 3, "models": ["anthropic/claude-3-sonnet", "meta-llama/llama-3.1-70b-instruct", "mistralai/mixtral-8x7b-instruct"]}}, "models": [{"provider": "anthropic", "providerName": "Anthropic", "modelId": "claude-3-5-sonnet-********", "label": "Claude 3.5 Sonnet", "description": "Most intelligent Claude model with enhanced reasoning and coding capabilities", "contextSize": 200000, "pricing": {"input": 0.003, "output": 0.015}, "tags": ["advanced-reasoning", "code", "creative-writing", "complex-analysis", "latest"]}, {"provider": "anthropic", "providerName": "Anthropic", "modelId": "claude-3-haiku-********", "label": "Claude 3 Haiku", "description": "Fastest Claude model for quick responses", "contextSize": 200000, "pricing": {"input": 0.00025, "output": 0.00125}, "tags": ["fast", "affordable", "conversational"]}, {"provider": "anthropic", "providerName": "Anthropic", "modelId": "claude-3-opus-********", "label": "Claude 3 Opus", "description": "Most powerful Claude model with exceptional reasoning and analysis capabilities", "contextSize": 200000, "pricing": {"input": 0.015, "output": 0.075}, "tags": ["advanced-reasoning", "complex-analysis", "creative-writing", "code"]}, {"provider": "anthropic", "providerName": "Anthropic", "modelId": "claude-3-sonnet-********", "label": "Claude 3 Sonnet", "description": "Balanced performance and speed for most use cases", "contextSize": 200000, "pricing": {"input": 0.003, "output": 0.015}, "tags": ["general-purpose", "code", "complex-analysis", "creative-writing"]}, {"provider": "deepseek", "providerName": "DeepSeek", "modelId": "deepseek-chat", "label": "DeepSeek Chat", "description": "General-purpose conversational AI model with strong reasoning capabilities", "contextSize": 32768, "pricing": {"input": 0.0014, "output": 0.0028}, "tags": ["advanced-reasoning", "conversational", "general-purpose", "affordable"]}, {"provider": "deepseek", "providerName": "DeepSeek", "modelId": "deepseek-coder", "label": "DeepSeek Coder", "description": "Specialized coding model with excellent programming capabilities", "contextSize": 32768, "pricing": {"input": 0.0014, "output": 0.0028}, "tags": ["code", "programming", "debugging", "developer-friendly"]}, {"provider": "fireworks", "providerName": "Fireworks AI", "modelId": "accounts/fireworks/models/llama-v3p1-70b-instruct", "label": "Llama 3.1 70B Instruct", "description": "Meta's flagship open-source model optimized for fast inference on Fireworks", "contextSize": 131072, "pricing": {"input": 0.0009, "output": 0.0009}, "tags": ["open-weight", "advanced-reasoning", "fast", "long-context"]}, {"provider": "fireworks", "providerName": "Fireworks AI", "modelId": "accounts/fireworks/models/mixtral-8x7b-instruct", "label": "Mixtral 8x7B Instruct", "description": "Mistral's mixture of experts model optimized for Fireworks infrastructure", "contextSize": 32768, "pricing": {"input": 0.0009, "output": 0.0009}, "tags": ["open-weight", "mixture-of-experts", "fast", "affordable"]}, {"provider": "google", "providerName": "Google AI", "modelId": "gemini-1.5-flash", "label": "Gemini 1.5 Flash", "description": "Faster, more efficient version optimized for speed and cost", "contextSize": 1000000, "pricing": {"input": 7.5e-05, "output": 0.0003}, "tags": ["multimodal", "fast", "affordable", "long-context"]}, {"provider": "google", "providerName": "Google AI", "modelId": "gemini-1.5-pro", "label": "Gemini 1.5 Pro", "description": "Enhanced version with extended context window and improved capabilities", "contextSize": 1000000, "pricing": {"input": 0.007, "output": 0.021}, "tags": ["multimodal", "long-context", "advanced-reasoning", "vision", "code"]}, {"provider": "google", "providerName": "Google AI", "modelId": "gemini-pro", "label": "Gemini Pro", "description": "Google's flagship multimodal AI model for complex reasoning tasks", "contextSize": 32768, "pricing": {"input": 0.000125, "output": 0.000375}, "tags": ["multimodal", "advanced-reasoning", "vision", "fast"]}, {"provider": "openai", "providerName": "OpenAI", "modelId": "gpt-3.5-turbo", "label": "GPT-3.5 Turbo", "description": "Fast and efficient model for most conversational tasks", "contextSize": 16385, "pricing": {"input": 0.0015, "output": 0.002}, "tags": ["fast", "affordable", "conversational", "general-purpose"]}, {"provider": "openai", "providerName": "OpenAI", "modelId": "gpt-4", "label": "GPT-4", "description": "Most capable GPT-4 model with superior reasoning and complex task handling", "contextSize": 8192, "pricing": {"input": 0.03, "output": 0.06}, "tags": ["advanced-reasoning", "general-purpose", "creative-writing", "complex-analysis"]}, {"provider": "openai", "providerName": "OpenAI", "modelId": "gpt-4-turbo", "label": "GPT-4 Turbo", "description": "Faster and more efficient version of GPT-4 with extended context", "contextSize": 128000, "pricing": {"input": 0.01, "output": 0.03}, "tags": ["fast", "high-context", "advanced-reasoning", "general-purpose"]}, {"provider": "openai", "providerName": "OpenAI", "modelId": "gpt-4o", "label": "GPT-4o", "description": "Multimodal flagship model with vision, audio, and text capabilities", "contextSize": 128000, "pricing": {"input": 0.005, "output": 0.015}, "tags": ["multimodal", "vision", "fast", "advanced-reasoning", "latest"]}, {"provider": "openai", "providerName": "OpenAI", "modelId": "gpt-4o-mini", "label": "GPT-4o Mini", "description": "Smaller, faster, and more affordable version of GPT-4o", "contextSize": 128000, "pricing": {"input": 0.00015, "output": 0.0006}, "tags": ["fast", "affordable", "multimodal", "general-purpose"]}, {"provider": "openrouter", "providerName": "OpenRouter", "modelId": "anthropic/claude-3-sonnet", "label": "Claude 3 Sonnet", "description": "Anthropic's balanced model via OpenRouter", "contextSize": 200000, "pricing": {"input": 0.003, "output": 0.015}, "tags": ["advanced-reasoning", "general-purpose", "creative-writing"], "originalProvider": "Anthropic"}, {"provider": "openrouter", "providerName": "OpenRouter", "modelId": "meta-llama/llama-3.1-70b-instruct", "label": "Llama 3.1 70B Instruct", "description": "Meta's flagship open-source model with excellent reasoning capabilities", "contextSize": 131072, "pricing": {"input": 0.00088, "output": 0.00088}, "tags": ["open-weight", "advanced-reasoning", "long-context"], "originalProvider": "Meta"}, {"provider": "openrouter", "providerName": "OpenRouter", "modelId": "mistralai/mixtral-8x7b-instruct", "label": "Mixtral 8x7B Instruct", "description": "High-quality sparse mixture of experts model with strong reasoning capabilities", "contextSize": 32768, "pricing": {"input": 0.00024, "output": 0.00024}, "tags": ["open-weight", "advanced-reasoning", "fast", "mixture-of-experts"], "originalProvider": "Mistral AI"}]}