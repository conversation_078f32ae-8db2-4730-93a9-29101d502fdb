// lib/budget-enforcer.ts

import { LLMProvider } from '../components/agents/llm-provider-registry';
import { CostSettings } from '../components/settings/settings-manager';
import { costTracker, CostTracker } from './cost-tracker';
import { BudgetExceededError } from './budget-error';
import { alertManager } from './alert-manager';

/**
 * ✅ Budget Enforcement Service
 * Enforces budget limits before LLM calls are made
 * Provides real-time budget checking and cost tracking
 */

export interface BudgetCheckResult {
  allowed: boolean;
  currentCost: number;
  estimatedCost: number;
  budgetLimit: number;
  wouldCost: number;
  remainingBudget: number;
  utilizationPercentage: number;
  reason?: string;
}

export class BudgetEnforcer {
  private static instance: BudgetEnforcer | null = null;
  private costTracker: CostTracker;

  private constructor() {
    this.costTracker = costTracker;
  }

  public static getInstance(): BudgetEnforcer {
    if (!BudgetEnforcer.instance) {
      BudgetEnforcer.instance = new BudgetEnforcer();
    }
    return BudgetEnforcer.instance;
  }

  /**
   * ✅ Check if an LLM call is allowed within budget
   */
  public checkBudget(
    provider: LLMProvider,
    model: string,
    inputTokens: number,
    outputTokens: number,
    costSettings: CostSettings
  ): BudgetCheckResult {
    // If budget tracking is disabled, allow all requests
    if (!costSettings.trackUsage) {
      return {
        allowed: true,
        currentCost: 0,
        estimatedCost: 0,
        budgetLimit: costSettings.budgetLimit,
        wouldCost: 0,
        remainingBudget: costSettings.budgetLimit,
        utilizationPercentage: 0,
        reason: 'Budget tracking disabled'
      };
    }

    const currentCost = this.costTracker.getCurrentMonthlyCost();
    const estimatedCost = this.costTracker.estimateCallCost(provider, inputTokens, outputTokens);
    const wouldCost = currentCost + estimatedCost;
    const remainingBudget = Math.max(0, costSettings.budgetLimit - currentCost);
    const utilizationPercentage = costSettings.budgetLimit > 0 ? (wouldCost / costSettings.budgetLimit) * 100 : 0;

    // Check if this call would exceed the budget
    if (wouldCost > costSettings.budgetLimit) {
      return {
        allowed: false,
        currentCost,
        estimatedCost,
        budgetLimit: costSettings.budgetLimit,
        wouldCost,
        remainingBudget,
        utilizationPercentage,
        reason: `Would exceed budget limit: $${wouldCost.toFixed(2)} > $${costSettings.budgetLimit.toFixed(2)}`
      };
    }

    return {
      allowed: true,
      currentCost,
      estimatedCost,
      budgetLimit: costSettings.budgetLimit,
      wouldCost,
      remainingBudget,
      utilizationPercentage,
      reason: 'Within budget limits'
    };
  }

  /**
   * ✅ Enforce budget before LLM call (throws error if exceeded)
   */
  public enforceBudget(
    provider: LLMProvider,
    model: string,
    inputTokens: number,
    outputTokens: number,
    costSettings: CostSettings
  ): void {
    const budgetCheck = this.checkBudget(provider, model, inputTokens, outputTokens, costSettings);

    if (!budgetCheck.allowed) {
      console.error(`💸 Budget exceeded: ${budgetCheck.reason}`);

      throw new BudgetExceededError(
        provider,
        model,
        budgetCheck.currentCost,
        budgetCheck.estimatedCost,
        budgetCheck.budgetLimit,
        budgetCheck.wouldCost
      );
    }

    // Log budget status for monitoring
    if (budgetCheck.utilizationPercentage >= costSettings.alertThreshold) {
      console.warn(`⚠️ Budget alert: ${budgetCheck.utilizationPercentage.toFixed(1)}% of budget used (${costSettings.alertThreshold}% threshold)`);
    } else {
      console.log(`💰 Budget check passed: $${budgetCheck.estimatedCost.toFixed(4)} estimated cost, ${budgetCheck.utilizationPercentage.toFixed(1)}% budget used`);
    }
  }

  /**
   * ✅ Record cost after successful LLM call and check thresholds
   */
  public async recordCost(
    provider: LLMProvider,
    model: string,
    inputTokens: number,
    outputTokens: number,
    costSettings: CostSettings,
    agentId?: string,
    taskId?: string
  ): Promise<void> {
    try {
      await this.costTracker.recordCost(
        provider,
        model,
        inputTokens,
        outputTokens,
        agentId,
        taskId
      );

      // ✅ Check thresholds after recording cost
      if (costSettings.trackUsage) {
        const alert = alertManager.checkThresholds(costSettings);
        if (alert) {
          console.log(`BudgetEnforcer: Threshold alert triggered: ${alert.type}`);
        }
      }
    } catch (error) {
      console.error('BudgetEnforcer: Failed to record cost:', error);
      // Don't throw here - cost recording failure shouldn't break the LLM call
    }
  }

  /**
   * ✅ Get current budget status
   */
  public getBudgetStatus(costSettings: CostSettings) {
    return this.costTracker.getBudgetStatus(costSettings.budgetLimit, costSettings.alertThreshold);
  }

  /**
   * ✅ Get monthly cost summary
   */
  public getMonthlyCostSummary() {
    return this.costTracker.getMonthlyCostSummary();
  }

  /**
   * ✅ Check if budget enforcement is enabled
   */
  public isBudgetEnforcementEnabled(costSettings: CostSettings): boolean {
    return costSettings.trackUsage && costSettings.budgetLimit > 0;
  }

  /**
   * ✅ Get cost tracker instance
   */
  public getCostTracker(): CostTracker {
    return this.costTracker;
  }
}

// Export singleton instance
export const budgetEnforcer = BudgetEnforcer.getInstance();
