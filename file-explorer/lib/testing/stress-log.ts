// lib/testing/stress-log.ts

export interface StressTestResult {
  testId: string
  testType: 'concurrency' | 'timeout' | 'failure' | 'memory' | 'custom'
  startTime: number
  endTime?: number
  duration?: number
  totalTasks: number
  successfulTasks: number
  failedTasks: number
  timeoutTasks: number
  averageResponseTime: number
  maxResponseTime: number
  minResponseTime: number
  memoryUsage?: {
    initial: number
    peak: number
    final: number
  }
  systemMetrics?: {
    cpuUsage: number
    activeAgents: number
    queueLength: number
    errorRate: number
  }
  taskResults: StressTaskResult[]
  errors: StressTestError[]
  warnings: string[]
  metadata?: Record<string, any>
}

export interface StressTaskResult {
  taskId: string
  agentType: string
  startTime: number
  endTime?: number
  duration?: number
  status: 'success' | 'failure' | 'timeout' | 'cancelled'
  responseTime: number
  tokensUsed?: number
  cost?: number
  errorMessage?: string
  retryCount?: number
  metadata?: Record<string, any>
}

export interface StressTestError {
  timestamp: number
  type: 'agent_error' | 'system_error' | 'timeout' | 'memory' | 'network'
  message: string
  taskId?: string
  agentType?: string
  stack?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export interface StressTestConfig {
  testType: 'concurrency' | 'timeout' | 'failure' | 'memory' | 'custom'
  concurrency: number
  duration: number
  taskCount: number
  timeoutMs?: number
  failureRate?: number
  agentTypes?: string[]
  customPrompts?: string[]
  enableMetrics: boolean
  enableMemoryTracking: boolean
  stopOnError: boolean
  maxRetries: number
}

class StressTestLogger {
  private logs: Map<string, StressTestResult> = new Map()
  private activeTests: Set<string> = new Set()
  private maxLogSize = 100 // Keep last 100 test results

  startTest(testId: string, config: StressTestConfig): StressTestResult {
    if (this.activeTests.has(testId)) {
      throw new Error(`Test ${testId} is already running`)
    }

    const result: StressTestResult = {
      testId,
      testType: config.testType,
      startTime: Date.now(),
      totalTasks: config.taskCount,
      successfulTasks: 0,
      failedTasks: 0,
      timeoutTasks: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      taskResults: [],
      errors: [],
      warnings: [],
      metadata: {
        config,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js',
        timestamp: new Date().toISOString()
      }
    }

    this.logs.set(testId, result)
    this.activeTests.add(testId)

    console.log(`🧪 Stress test started: ${testId} (${config.testType})`)
    return result
  }

  logTaskResult(testId: string, taskResult: StressTaskResult): void {
    const test = this.logs.get(testId)
    if (!test) {
      console.warn(`Test ${testId} not found for task result logging`)
      return
    }

    test.taskResults.push(taskResult)

    // Update aggregated metrics
    switch (taskResult.status) {
      case 'success':
        test.successfulTasks++
        break
      case 'failure':
        test.failedTasks++
        break
      case 'timeout':
        test.timeoutTasks++
        break
    }

    // Update response time metrics
    if (taskResult.responseTime > 0) {
      test.maxResponseTime = Math.max(test.maxResponseTime, taskResult.responseTime)
      test.minResponseTime = Math.min(test.minResponseTime, taskResult.responseTime)
      
      const completedTasks = test.taskResults.filter(t => t.responseTime > 0)
      test.averageResponseTime = completedTasks.reduce((sum, t) => sum + t.responseTime, 0) / completedTasks.length
    }
  }

  logError(testId: string, error: Omit<StressTestError, 'timestamp'>): void {
    const test = this.logs.get(testId)
    if (!test) {
      console.warn(`Test ${testId} not found for error logging`)
      return
    }

    const fullError: StressTestError = {
      ...error,
      timestamp: Date.now()
    }

    test.errors.push(fullError)
    console.error(`🚨 Stress test error in ${testId}:`, fullError)
  }

  logWarning(testId: string, message: string): void {
    const test = this.logs.get(testId)
    if (!test) {
      console.warn(`Test ${testId} not found for warning logging`)
      return
    }

    test.warnings.push(`${new Date().toISOString()}: ${message}`)
    console.warn(`⚠️ Stress test warning in ${testId}: ${message}`)
  }

  updateSystemMetrics(testId: string, metrics: StressTestResult['systemMetrics']): void {
    const test = this.logs.get(testId)
    if (!test) return

    test.systemMetrics = metrics
  }

  updateMemoryUsage(testId: string, memoryUsage: StressTestResult['memoryUsage']): void {
    const test = this.logs.get(testId)
    if (!test) return

    test.memoryUsage = memoryUsage
  }

  finishTest(testId: string): StressTestResult | null {
    const test = this.logs.get(testId)
    if (!test) {
      console.warn(`Test ${testId} not found for finishing`)
      return null
    }

    test.endTime = Date.now()
    test.duration = test.endTime - test.startTime

    // Fix infinite minResponseTime if no tasks completed
    if (test.minResponseTime === Infinity) {
      test.minResponseTime = 0
    }

    this.activeTests.delete(testId)

    // Cleanup old logs if we exceed max size
    if (this.logs.size > this.maxLogSize) {
      const oldestKey = Array.from(this.logs.keys())[0]
      this.logs.delete(oldestKey)
    }

    console.log(`✅ Stress test completed: ${testId}`, {
      duration: test.duration,
      successRate: `${((test.successfulTasks / test.totalTasks) * 100).toFixed(1)}%`,
      avgResponseTime: `${test.averageResponseTime.toFixed(0)}ms`
    })

    return test
  }

  getTestResult(testId: string): StressTestResult | null {
    return this.logs.get(testId) || null
  }

  getAllTestResults(): StressTestResult[] {
    return Array.from(this.logs.values()).sort((a, b) => b.startTime - a.startTime)
  }

  getActiveTests(): string[] {
    return Array.from(this.activeTests)
  }

  isTestActive(testId: string): boolean {
    return this.activeTests.has(testId)
  }

  clearLogs(): void {
    this.logs.clear()
    this.activeTests.clear()
    console.log('🧹 Stress test logs cleared')
  }

  exportResults(format: 'json' | 'csv' = 'json'): string {
    const results = this.getAllTestResults()
    
    if (format === 'json') {
      return JSON.stringify(results, null, 2)
    }
    
    if (format === 'csv') {
      const headers = [
        'testId', 'testType', 'startTime', 'duration', 'totalTasks', 
        'successfulTasks', 'failedTasks', 'timeoutTasks', 'successRate',
        'averageResponseTime', 'maxResponseTime', 'minResponseTime'
      ]
      
      const rows = results.map(result => [
        result.testId,
        result.testType,
        new Date(result.startTime).toISOString(),
        result.duration || 0,
        result.totalTasks,
        result.successfulTasks,
        result.failedTasks,
        result.timeoutTasks,
        ((result.successfulTasks / result.totalTasks) * 100).toFixed(1),
        result.averageResponseTime.toFixed(0),
        result.maxResponseTime,
        result.minResponseTime === Infinity ? 0 : result.minResponseTime
      ])
      
      return [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
    }
    
    return ''
  }
}

// Global instance
let globalStressLogger: StressTestLogger | null = null

export function getStressTestLogger(): StressTestLogger {
  if (!globalStressLogger) {
    globalStressLogger = new StressTestLogger()
  }
  return globalStressLogger
}

// Utility functions
export function generateTestId(testType: string): string {
  return `stress-${testType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

export function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  return `${(ms / 60000).toFixed(1)}m`
}

export function formatMemoryUsage(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`
}
