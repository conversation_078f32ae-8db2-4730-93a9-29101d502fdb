// lib/testing/stress-tester.ts

import { CompleteAgentManager } from "@/components/agents/agent-manager-complete"
import { getStressTestLogger, generateTestId, type StressTestConfig, type StressTaskResult } from "./stress-log"

export interface StressTestOptions {
  testType: 'concurrency' | 'timeout' | 'failure' | 'memory' | 'custom'
  concurrency?: number
  taskCount?: number
  duration?: number
  timeoutMs?: number
  failureRate?: number
  agentTypes?: string[]
  customPrompts?: string[]
  enableMetrics?: boolean
  enableMemoryTracking?: boolean
  stopOnError?: boolean
  maxRetries?: number
}

export class StressTester {
  private agentManager: CompleteAgentManager
  private logger = getStressTestLogger()
  private isTestModeEnabled: boolean

  constructor(agentManager: CompleteAgentManager, testModeEnabled: boolean = false) {
    this.agentManager = agentManager
    this.isTestModeEnabled = testModeEnabled
  }

  async runHighConcurrencyTest(options: Partial<StressTestOptions> = {}): Promise<string> {
    if (!this.isTestModeEnabled) {
      throw new Error('Stress testing is disabled. Enable test mode first.')
    }

    const config: StressTestConfig = {
      testType: 'concurrency',
      concurrency: options.concurrency || 20,
      taskCount: options.taskCount || 50,
      duration: options.duration || 60000, // 1 minute
      enableMetrics: options.enableMetrics ?? true,
      enableMemoryTracking: options.enableMemoryTracking ?? true,
      stopOnError: options.stopOnError ?? false,
      maxRetries: options.maxRetries || 3,
      agentTypes: options.agentTypes || ['micromanager', 'senior', 'intern']
    }

    const testId = generateTestId('concurrency')
    const testResult = this.logger.startTest(testId, config)

    try {
      console.log(`🚀 Starting high concurrency test with ${config.taskCount} tasks, ${config.concurrency} concurrent`)

      // Track initial memory if enabled
      if (config.enableMemoryTracking && typeof performance !== 'undefined' && (performance as any).memory) {
        const initialMemory = (performance as any).memory.usedJSHeapSize
        this.logger.updateMemoryUsage(testId, {
          initial: initialMemory,
          peak: initialMemory,
          final: initialMemory
        })
      }

      // Generate test tasks
      const tasks = Array.from({ length: config.taskCount }, (_, i) => ({
        id: `stress-task-${i}`,
        prompt: `Stress test task #${i + 1}: Analyze and provide a brief summary of a hypothetical software architecture pattern. This is a test task for load testing purposes.`,
        agentType: config.agentTypes![i % config.agentTypes!.length],
        priority: Math.random() > 0.5 ? 'high' : 'normal',
        isStressTest: true,
        testId
      }))

      // Execute tasks with controlled concurrency
      const results = await this.executeConcurrentTasks(testId, tasks, config.concurrency)

      // Track final memory
      if (config.enableMemoryTracking && typeof performance !== 'undefined' && (performance as any).memory) {
        const finalMemory = (performance as any).memory.usedJSHeapSize
        const currentMemoryUsage = testResult.memoryUsage
        this.logger.updateMemoryUsage(testId, {
          initial: currentMemoryUsage?.initial || finalMemory,
          peak: currentMemoryUsage?.peak || finalMemory,
          final: finalMemory
        })
      }

      // Update system metrics
      this.updateSystemMetrics(testId)

      this.logger.finishTest(testId)
      return testId

    } catch (error) {
      this.logger.logError(testId, {
        type: 'system_error',
        message: error instanceof Error ? error.message : 'Unknown error',
        severity: 'critical',
        stack: error instanceof Error ? error.stack : undefined
      })
      this.logger.finishTest(testId)
      throw error
    }
  }

  async runTimeoutSimulation(options: Partial<StressTestOptions> = {}): Promise<string> {
    if (!this.isTestModeEnabled) {
      throw new Error('Stress testing is disabled. Enable test mode first.')
    }

    const config: StressTestConfig = {
      testType: 'timeout',
      concurrency: options.concurrency || 10,
      taskCount: options.taskCount || 20,
      duration: options.duration || 30000,
      timeoutMs: options.timeoutMs || 5000, // Very short timeout
      enableMetrics: options.enableMetrics ?? true,
      enableMemoryTracking: options.enableMemoryTracking ?? false,
      stopOnError: options.stopOnError ?? false,
      maxRetries: options.maxRetries || 1,
      agentTypes: options.agentTypes || ['micromanager']
    }

    const testId = generateTestId('timeout')
    this.logger.startTest(testId, config)

    try {
      console.log(`⏱️ Starting timeout simulation with ${config.timeoutMs}ms timeout`)

      // Generate tasks designed to potentially timeout
      const tasks = Array.from({ length: config.taskCount }, (_, i) => ({
        id: `timeout-task-${i}`,
        prompt: `Complex analysis task #${i + 1}: Perform a detailed analysis of a large codebase with multiple dependencies, architectural patterns, and provide comprehensive documentation. This task is designed to test timeout handling and should take significant processing time.`,
        agentType: config.agentTypes![i % config.agentTypes!.length],
        timeout: config.timeoutMs,
        isStressTest: true,
        testId
      }))

      await this.executeConcurrentTasks(testId, tasks, config.concurrency)
      this.updateSystemMetrics(testId)
      this.logger.finishTest(testId)
      return testId

    } catch (error) {
      this.logger.logError(testId, {
        type: 'system_error',
        message: error instanceof Error ? error.message : 'Unknown error',
        severity: 'critical'
      })
      this.logger.finishTest(testId)
      throw error
    }
  }

  async runFailureSimulation(options: Partial<StressTestOptions> = {}): Promise<string> {
    if (!this.isTestModeEnabled) {
      throw new Error('Stress testing is disabled. Enable test mode first.')
    }

    const config: StressTestConfig = {
      testType: 'failure',
      concurrency: options.concurrency || 15,
      taskCount: options.taskCount || 30,
      duration: options.duration || 45000,
      failureRate: options.failureRate || 0.3, // 30% failure rate
      enableMetrics: options.enableMetrics ?? true,
      enableMemoryTracking: options.enableMemoryTracking ?? false,
      stopOnError: options.stopOnError ?? false,
      maxRetries: options.maxRetries || 2,
      agentTypes: options.agentTypes || ['micromanager', 'senior', 'intern']
    }

    const testId = generateTestId('failure')
    this.logger.startTest(testId, config)

    try {
      console.log(`💥 Starting failure simulation with ${(config.failureRate! * 100).toFixed(0)}% failure rate`)

      // Generate tasks with some designed to fail
      const tasks = Array.from({ length: config.taskCount }, (_, i) => {
        const shouldFail = Math.random() < config.failureRate!
        return {
          id: `failure-task-${i}`,
          prompt: shouldFail 
            ? `INVALID_PROMPT_FOR_TESTING_${i}: This prompt is intentionally malformed to trigger error handling in the agent system. It contains invalid characters and impossible requests: <<<INVALID>>>` 
            : `Valid test task #${i + 1}: Provide a brief explanation of a common software design pattern.`,
          agentType: config.agentTypes![i % config.agentTypes!.length],
          shouldFail,
          isStressTest: true,
          testId
        }
      })

      await this.executeConcurrentTasks(testId, tasks, config.concurrency)
      this.updateSystemMetrics(testId)
      this.logger.finishTest(testId)
      return testId

    } catch (error) {
      this.logger.logError(testId, {
        type: 'system_error',
        message: error instanceof Error ? error.message : 'Unknown error',
        severity: 'critical'
      })
      this.logger.finishTest(testId)
      throw error
    }
  }

  async runMemoryStressTest(options: Partial<StressTestOptions> = {}): Promise<string> {
    if (!this.isTestModeEnabled) {
      throw new Error('Stress testing is disabled. Enable test mode first.')
    }

    const config: StressTestConfig = {
      testType: 'memory',
      concurrency: options.concurrency || 25,
      taskCount: options.taskCount || 100,
      duration: options.duration || 120000, // 2 minutes
      enableMetrics: options.enableMetrics ?? true,
      enableMemoryTracking: true,
      stopOnError: options.stopOnError ?? false,
      maxRetries: options.maxRetries || 1,
      agentTypes: options.agentTypes || ['micromanager', 'senior', 'intern', 'designer']
    }

    const testId = generateTestId('memory')
    this.logger.startTest(testId, config)

    try {
      console.log(`🧠 Starting memory stress test with ${config.taskCount} tasks`)

      // Generate memory-intensive tasks
      const tasks = Array.from({ length: config.taskCount }, (_, i) => ({
        id: `memory-task-${i}`,
        prompt: `Memory intensive task #${i + 1}: Generate a comprehensive analysis including multiple large data structures, detailed explanations, code examples, and extensive documentation. Include lists, tables, and complex nested information to test memory usage patterns.`,
        agentType: config.agentTypes![i % config.agentTypes!.length],
        isStressTest: true,
        testId
      }))

      // Monitor memory during execution
      const memoryMonitor = this.startMemoryMonitoring(testId)

      await this.executeConcurrentTasks(testId, tasks, config.concurrency)
      
      clearInterval(memoryMonitor)
      this.updateSystemMetrics(testId)
      this.logger.finishTest(testId)
      return testId

    } catch (error) {
      this.logger.logError(testId, {
        type: 'memory',
        message: error instanceof Error ? error.message : 'Unknown error',
        severity: 'critical'
      })
      this.logger.finishTest(testId)
      throw error
    }
  }

  private async executeConcurrentTasks(testId: string, tasks: any[], concurrency: number): Promise<void> {
    const semaphore = new Array(concurrency).fill(null)
    let taskIndex = 0

    const executeTask = async (task: any): Promise<void> => {
      const startTime = Date.now()
      let taskResult: StressTaskResult = {
        taskId: task.id,
        agentType: task.agentType,
        startTime,
        status: 'failure',
        responseTime: 0
      }

      try {
        // Simulate task execution through agent manager
        const result = await this.executeStressTask(task)
        
        taskResult = {
          ...taskResult,
          endTime: Date.now(),
          duration: Date.now() - startTime,
          status: result.success ? 'success' : 'failure',
          responseTime: Date.now() - startTime,
          tokensUsed: result.tokensUsed,
          cost: result.cost,
          errorMessage: result.error,
          metadata: result.metadata
        }

      } catch (error) {
        taskResult = {
          ...taskResult,
          endTime: Date.now(),
          duration: Date.now() - startTime,
          status: 'failure',
          responseTime: Date.now() - startTime,
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        }

        this.logger.logError(testId, {
          type: 'agent_error',
          message: error instanceof Error ? error.message : 'Unknown error',
          taskId: task.id,
          agentType: task.agentType,
          severity: 'medium'
        })
      }

      this.logger.logTaskResult(testId, taskResult)
    }

    // Execute tasks with controlled concurrency
    const workers = semaphore.map(async () => {
      while (taskIndex < tasks.length) {
        const task = tasks[taskIndex++]
        if (task) {
          await executeTask(task)
        }
      }
    })

    await Promise.all(workers)
  }

  private async executeStressTask(task: any): Promise<any> {
    // This is a simplified task execution for stress testing
    // In a real implementation, this would integrate with the actual agent system
    
    return new Promise((resolve) => {
      // Simulate variable response times
      const responseTime = Math.random() * 2000 + 500 // 500-2500ms
      
      setTimeout(() => {
        if (task.shouldFail) {
          resolve({
            success: false,
            error: 'Intentional failure for stress testing',
            tokensUsed: 0,
            cost: 0
          })
        } else {
          resolve({
            success: true,
            tokensUsed: Math.floor(Math.random() * 1000) + 100,
            cost: Math.random() * 0.01,
            metadata: {
              agentType: task.agentType,
              isStressTest: true
            }
          })
        }
      }, responseTime)
    })
  }

  private startMemoryMonitoring(testId: string): NodeJS.Timeout {
    let peakMemory = 0
    
    return setInterval(() => {
      if (typeof performance !== 'undefined' && (performance as any).memory) {
        const currentMemory = (performance as any).memory.usedJSHeapSize
        peakMemory = Math.max(peakMemory, currentMemory)
        
        const testResult = this.logger.getTestResult(testId)
        if (testResult?.memoryUsage) {
          this.logger.updateMemoryUsage(testId, {
            initial: testResult.memoryUsage.initial,
            peak: peakMemory,
            final: currentMemory
          })
        }
      }
    }, 1000) // Check every second
  }

  private updateSystemMetrics(testId: string): void {
    // Simulate system metrics collection
    const metrics = {
      cpuUsage: Math.random() * 100,
      activeAgents: Math.floor(Math.random() * 10) + 1,
      queueLength: Math.floor(Math.random() * 20),
      errorRate: Math.random() * 10
    }

    this.logger.updateSystemMetrics(testId, metrics)
  }

  getTestResults(): any[] {
    return this.logger.getAllTestResults()
  }

  getActiveTests(): string[] {
    return this.logger.getActiveTests()
  }

  clearTestLogs(): void {
    if (!this.isTestModeEnabled) {
      throw new Error('Cannot clear logs when test mode is disabled')
    }
    this.logger.clearLogs()
  }

  exportResults(format: 'json' | 'csv' = 'json'): string {
    return this.logger.exportResults(format)
  }
}

// Global instance
let globalStressTester: StressTester | null = null

export function getStressTester(agentManager?: CompleteAgentManager, testModeEnabled: boolean = false): StressTester {
  if (!globalStressTester && agentManager) {
    globalStressTester = new StressTester(agentManager, testModeEnabled)
  }
  return globalStressTester!
}

// Utility functions for quick testing
export async function runQuickConcurrencyTest(taskCount: number = 10): Promise<string> {
  const tester = getStressTester()
  return await tester.runHighConcurrencyTest({ taskCount, concurrency: 5 })
}

export async function runQuickTimeoutTest(): Promise<string> {
  const tester = getStressTester()
  return await tester.runTimeoutSimulation({ taskCount: 5, timeoutMs: 3000 })
}

export async function runQuickFailureTest(): Promise<string> {
  const tester = getStressTester()
  return await tester.runFailureSimulation({ taskCount: 10, failureRate: 0.5 })
}
