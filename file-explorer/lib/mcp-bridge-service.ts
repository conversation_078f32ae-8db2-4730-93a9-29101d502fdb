// lib/mcp-bridge-service.ts
// ✅ Browser-safe MCP bridge using Electron IPC instead of direct Node.js modules
import { AgentContext, AgentResponse } from '../components/agents/types';
import { LLMMessage, LLMResponse } from '../components/agents/llm-request-service';

export interface MCPConfig {
  enabled: boolean;
  serverCommand?: string;
  serverArgs?: string[];
  timeout?: number;
  maxRetries?: number;
}

export interface MCPTaskRequest {
  task: string;
  context: AgentContext;
  agentId: string;
  messages: LLMMessage[];
  metadata?: Record<string, any>;
}

export interface MCPTaskResponse {
  success: boolean;
  content: string;
  tokensUsed?: {
    prompt: number;
    completion: number;
    total: number;
  };
  model?: string;
  finishReason?: string;
  error?: string;
  mcpMetadata?: {
    serverUsed: string;
    toolsInvoked: string[];
    responseTime: number;
  };
}

export interface MCPConnection {
  serverId: string;
  connected: boolean;
  serverInfo?: {
    name: string;
    version: string;
    capabilities: string[];
  };
  lastConnected?: number;
  connectionAttempts?: number;
}

export class MCPBridgeService {
  private static instance: MCPBridgeService;
  private connections: Map<string, MCPConnection> = new Map();
  private defaultConfig: MCPConfig = {
    enabled: false,
    timeout: 30000,
    maxRetries: 3
  };

  private constructor() {}

  public static getInstance(): MCPBridgeService {
    if (!MCPBridgeService.instance) {
      MCPBridgeService.instance = new MCPBridgeService();
    }
    return MCPBridgeService.instance;
  }

  /**
   * ✅ Initialize MCP connection for a specific server via Electron IPC
   */
  public async initializeConnection(
    serverId: string,
    config: MCPConfig
  ): Promise<boolean> {
    try {
      if (!config.enabled) {
        console.log(`🔌 MCP: Server ${serverId} is disabled`);
        return false;
      }

      if (!config.serverCommand) {
        console.error(`❌ MCP: No server command specified for ${serverId}`);
        return false;
      }

      console.log(`🔌 MCP: Initializing connection to ${serverId}...`);

      // ✅ Use Electron IPC for MCP operations to avoid Node.js module issues in browser
      if (typeof window !== 'undefined' && window.electronAPI?.mcp) {
        const result = await window.electronAPI.mcp.initializeConnection(serverId, {
          command: config.serverCommand,
          args: config.serverArgs || [],
          timeout: config.timeout || 30000,
          maxRetries: config.maxRetries || 3
        });

        if (result.success) {
          const connection: MCPConnection = {
            serverId,
            connected: true,
            serverInfo: {
              name: serverId,
              version: result.serverInfo?.version || '1.0.0',
              capabilities: result.serverInfo?.capabilities || []
            },
            lastConnected: Date.now(),
            connectionAttempts: 1
          };

          this.connections.set(serverId, connection);

          console.log(`✅ MCP: Connected to ${serverId}`, {
            capabilities: connection.serverInfo?.capabilities
          });

          return true;
        } else {
          console.error(`❌ MCP: Failed to connect to ${serverId}: ${result.error}`);
          return false;
        }
      } else {
        console.warn(`⚠️ MCP: Electron API not available. MCP connections require Electron environment.`);
        return false;
      }
    } catch (error) {
      console.error(`❌ MCP: Failed to connect to ${serverId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Send task to MCP model for processing via Electron IPC
   */
  public async sendTaskToModel(
    serverId: string,
    request: MCPTaskRequest
  ): Promise<MCPTaskResponse> {
    const startTime = Date.now();

    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        throw new Error(`MCP server ${serverId} is not connected`);
      }

      console.log(`🚀 MCP: Sending task to ${serverId} for agent ${request.agentId}`);

      // ✅ Use Electron IPC for MCP task execution
      if (typeof window !== 'undefined' && window.electronAPI?.mcp) {
        const result = await window.electronAPI.mcp.sendTask(serverId, {
          task: request.task,
          context: request.context,
          agentId: request.agentId,
          messages: request.messages,
          metadata: {
            maxTokens: request.metadata?.maxTokens || 4000,
            temperature: request.metadata?.temperature || 0.7,
            systemPrompt: request.metadata?.systemPrompt
          }
        });

        const responseTime = Date.now() - startTime;

        if (result.success) {
          const mcpResponse: MCPTaskResponse = {
            success: true,
            content: result.content || '',
            tokensUsed: {
              prompt: result.tokensUsed?.prompt || 0,
              completion: result.tokensUsed?.completion || 0,
              total: result.tokensUsed?.total || 0
            },
            model: result.model || 'mcp-model',
            finishReason: result.finishReason || 'stop',
            mcpMetadata: {
              serverUsed: serverId,
              toolsInvoked: result.toolsInvoked || [],
              responseTime
            }
          };

          console.log(`✅ MCP: Task completed via ${serverId} in ${responseTime}ms`);
          return mcpResponse;
        } else {
          throw new Error(result.error || 'MCP task execution failed');
        }
      } else {
        throw new Error('Electron API not available for MCP operations');
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error(`❌ MCP: Task failed for ${serverId}:`, error);

      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : 'Unknown MCP error',
        mcpMetadata: {
          serverUsed: serverId,
          toolsInvoked: [],
          responseTime
        }
      };
    }
  }

  /**
   * ✅ Receive and process response from MCP
   */
  public async receiveResponse(
    serverId: string,
    responseId: string
  ): Promise<MCPTaskResponse | null> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        console.warn(`⚠️ MCP: Server ${serverId} not connected for response ${responseId}`);
        return null;
      }

      // In a real implementation, this would retrieve a specific response
      // For now, we'll return null as responses are handled synchronously
      console.log(`📥 MCP: Attempting to receive response ${responseId} from ${serverId}`);
      return null;

    } catch (error) {
      console.error(`❌ MCP: Failed to receive response from ${serverId}:`, error);
      return null;
    }
  }

  /**
   * ✅ Sync agent state with MCP server via Electron IPC
   */
  public async syncAgentState(
    serverId: string,
    agentId: string,
    state: {
      currentTask?: string;
      status: 'idle' | 'working' | 'error';
      lastUpdate: number;
      metadata?: Record<string, any>;
    }
  ): Promise<boolean> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        console.warn(`⚠️ MCP: Server ${serverId} not connected for state sync`);
        return false;
      }

      console.log(`🔄 MCP: Syncing agent ${agentId} state with ${serverId}`);

      // ✅ Use Electron IPC for state synchronization
      if (typeof window !== 'undefined' && window.electronAPI?.mcp) {
        const result = await window.electronAPI.mcp.syncAgentState(serverId, agentId, state);

        if (result.success) {
          console.log(`✅ MCP: Agent ${agentId} state synced with ${serverId}`);
          return true;
        } else {
          console.warn(`⚠️ MCP: State sync failed for ${agentId}: ${result.error}`);
          return false;
        }
      } else {
        console.warn(`⚠️ MCP: Electron API not available for state sync`);
        return false;
      }
    } catch (error) {
      console.error(`❌ MCP: Failed to sync agent state with ${serverId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Check if MCP server is available and connected
   */
  public isServerConnected(serverId: string): boolean {
    const connection = this.connections.get(serverId);
    return connection?.connected || false;
  }

  /**
   * ✅ Get list of connected MCP servers
   */
  public getConnectedServers(): string[] {
    return Array.from(this.connections.keys()).filter(serverId =>
      this.connections.get(serverId)?.connected
    );
  }

  /**
   * ✅ Get server capabilities
   */
  public getServerCapabilities(serverId: string): string[] {
    const connection = this.connections.get(serverId);
    return connection?.serverInfo?.capabilities || [];
  }

  /**
   * ✅ Disconnect from MCP server via Electron IPC
   */
  public async disconnectServer(serverId: string): Promise<boolean> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection) {
        return true; // Already disconnected
      }

      console.log(`🔌 MCP: Disconnecting from ${serverId}...`);

      // ✅ Use Electron IPC for disconnection
      if (typeof window !== 'undefined' && window.electronAPI?.mcp) {
        const result = await window.electronAPI.mcp.disconnectServer(serverId);

        if (result.success) {
          this.connections.delete(serverId);
          console.log(`✅ MCP: Disconnected from ${serverId}`);
          return true;
        } else {
          console.error(`❌ MCP: Failed to disconnect from ${serverId}: ${result.error}`);
          return false;
        }
      } else {
        // Fallback: just remove from local connections map
        this.connections.delete(serverId);
        console.log(`✅ MCP: Removed ${serverId} from local connections (Electron API unavailable)`);
        return true;
      }

    } catch (error) {
      console.error(`❌ MCP: Failed to disconnect from ${serverId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Disconnect from all MCP servers
   */
  public async disconnectAll(): Promise<void> {
    const serverIds = Array.from(this.connections.keys());

    for (const serverId of serverIds) {
      await this.disconnectServer(serverId);
    }

    console.log('🔌 MCP: All servers disconnected');
  }



  /**
   * ✅ Convert MCP response to LLM format
   */
  public convertMCPToLLMResponse(mcpResponse: MCPTaskResponse): LLMResponse {
    return {
      content: mcpResponse.content,
      tokensUsed: mcpResponse.tokensUsed || { prompt: 0, completion: 0, total: 0 },
      model: mcpResponse.model || 'mcp-model',
      provider: 'mcp' as any, // Cast to satisfy type
      finishReason: mcpResponse.finishReason || 'stop',
      responseTime: mcpResponse.mcpMetadata?.responseTime || 0
    };
  }

  /**
   * ✅ Test MCP connection via Electron IPC
   */
  public async testConnection(serverId: string): Promise<{
    success: boolean;
    latency?: number;
    capabilities?: string[];
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        return {
          success: false,
          error: `Server ${serverId} is not connected`
        };
      }

      // ✅ Use Electron IPC for connection testing
      if (typeof window !== 'undefined' && window.electronAPI?.mcp) {
        const result = await window.electronAPI.mcp.testConnection(serverId);
        const latency = Date.now() - startTime;

        return {
          success: result.success,
          latency,
          capabilities: connection.serverInfo?.capabilities,
          error: result.error
        };
      } else {
        return {
          success: false,
          latency: Date.now() - startTime,
          error: 'Electron API not available for connection testing'
        };
      }

    } catch (error) {
      return {
        success: false,
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown test error'
      };
    }
  }
}

// Export singleton instance
export const mcpBridgeService = MCPBridgeService.getInstance();
