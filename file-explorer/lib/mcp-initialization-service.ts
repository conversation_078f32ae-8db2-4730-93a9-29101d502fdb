// lib/mcp-initialization-service.ts
import { mcpBridgeService } from './mcp-bridge-service';
import { settingsManager } from '../components/settings/settings-manager';

export interface MCPInitializationResult {
  success: boolean;
  connectedServers: string[];
  failedServers: string[];
  errors: Record<string, string>;
}

export class MCPInitializationService {
  private static instance: MCPInitializationService;
  private initialized = false;
  private autoConnectInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  public static getInstance(): MCPInitializationService {
    if (!MCPInitializationService.instance) {
      MCPInitializationService.instance = new MCPInitializationService();
    }
    return MCPInitializationService.instance;
  }

  /**
   * ✅ Initialize MCP connections based on settings
   */
  public async initialize(): Promise<MCPInitializationResult> {
    if (this.initialized) {
      console.log('🔌 MCP: Already initialized');
      return this.getConnectionStatus();
    }

    console.log('🔌 MCP: Initializing MCP connections...');

    const mcpSettings = settingsManager.getMCPSettings();
    const result: MCPInitializationResult = {
      success: true,
      connectedServers: [],
      failedServers: [],
      errors: {}
    };

    if (!mcpSettings.enabled) {
      console.log('🔌 MCP: MCP is disabled in settings');
      this.initialized = true;
      return result;
    }

    // Connect to all enabled servers
    for (const [serverId, serverConfig] of Object.entries(mcpSettings.servers)) {
      if (!serverConfig.enabled) {
        console.log(`🔌 MCP: Server ${serverId} is disabled, skipping`);
        continue;
      }

      try {
        console.log(`🔌 MCP: Connecting to ${serverId}...`);
        
        const connected = await mcpBridgeService.initializeConnection(serverId, {
          enabled: true,
          serverCommand: serverConfig.command,
          serverArgs: serverConfig.args,
          timeout: mcpSettings.timeout,
          maxRetries: mcpSettings.maxRetries
        });

        if (connected) {
          result.connectedServers.push(serverId);
          console.log(`✅ MCP: Connected to ${serverId}`);
        } else {
          result.failedServers.push(serverId);
          result.errors[serverId] = 'Connection failed';
          console.error(`❌ MCP: Failed to connect to ${serverId}`);
        }
      } catch (error) {
        result.failedServers.push(serverId);
        result.errors[serverId] = error instanceof Error ? error.message : 'Unknown error';
        console.error(`❌ MCP: Error connecting to ${serverId}:`, error);
      }
    }

    // Set up auto-reconnect for failed servers if any succeeded
    if (result.connectedServers.length > 0 && result.failedServers.length > 0) {
      this.setupAutoReconnect();
    }

    // Overall success if at least one server connected
    result.success = result.connectedServers.length > 0;

    this.initialized = true;
    console.log(`🔌 MCP: Initialization complete. Connected: ${result.connectedServers.length}, Failed: ${result.failedServers.length}`);

    return result;
  }

  /**
   * ✅ Get current connection status
   */
  public getConnectionStatus(): MCPInitializationResult {
    const connectedServers = mcpBridgeService.getConnectedServers();
    const mcpSettings = settingsManager.getMCPSettings();
    
    const allServerIds = Object.keys(mcpSettings.servers);
    const failedServers = allServerIds.filter(id => 
      mcpSettings.servers[id].enabled && !connectedServers.includes(id)
    );

    return {
      success: connectedServers.length > 0,
      connectedServers,
      failedServers,
      errors: {}
    };
  }

  /**
   * ✅ Test connection to a specific MCP server
   */
  public async testConnection(serverId: string): Promise<{
    success: boolean;
    latency?: number;
    capabilities?: string[];
    error?: string;
  }> {
    try {
      console.log(`🧪 MCP: Testing connection to ${serverId}...`);
      
      const result = await mcpBridgeService.testConnection(serverId);
      
      if (result.success) {
        console.log(`✅ MCP: Test successful for ${serverId} (${result.latency}ms)`);
      } else {
        console.error(`❌ MCP: Test failed for ${serverId}: ${result.error}`);
      }
      
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown test error';
      console.error(`❌ MCP: Test error for ${serverId}:`, error);
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * ✅ Reconnect to a specific server
   */
  public async reconnectServer(serverId: string): Promise<boolean> {
    try {
      console.log(`🔄 MCP: Reconnecting to ${serverId}...`);
      
      const mcpSettings = settingsManager.getMCPSettings();
      const serverConfig = mcpSettings.servers[serverId];
      
      if (!serverConfig) {
        console.error(`❌ MCP: Server ${serverId} not found in settings`);
        return false;
      }

      // Disconnect first if connected
      if (mcpBridgeService.isServerConnected(serverId)) {
        await mcpBridgeService.disconnectServer(serverId);
      }

      // Reconnect
      const connected = await mcpBridgeService.initializeConnection(serverId, {
        enabled: true,
        serverCommand: serverConfig.command,
        serverArgs: serverConfig.args,
        timeout: mcpSettings.timeout,
        maxRetries: mcpSettings.maxRetries
      });

      if (connected) {
        console.log(`✅ MCP: Reconnected to ${serverId}`);
      } else {
        console.error(`❌ MCP: Failed to reconnect to ${serverId}`);
      }

      return connected;
    } catch (error) {
      console.error(`❌ MCP: Reconnection error for ${serverId}:`, error);
      return false;
    }
  }

  /**
   * ✅ Setup auto-reconnect for failed servers
   */
  private setupAutoReconnect(): void {
    if (this.autoConnectInterval) {
      clearInterval(this.autoConnectInterval);
    }

    // Try to reconnect every 60 seconds
    this.autoConnectInterval = setInterval(async () => {
      const status = this.getConnectionStatus();
      
      if (status.failedServers.length > 0) {
        console.log(`🔄 MCP: Auto-reconnect attempt for ${status.failedServers.length} failed servers`);
        
        for (const serverId of status.failedServers) {
          try {
            await this.reconnectServer(serverId);
          } catch (error) {
            console.warn(`⚠️ MCP: Auto-reconnect failed for ${serverId}:`, error);
          }
        }
      }
    }, 60000); // 60 seconds

    console.log('🔄 MCP: Auto-reconnect enabled (60s interval)');
  }

  /**
   * ✅ Shutdown MCP connections
   */
  public async shutdown(): Promise<void> {
    console.log('🔌 MCP: Shutting down MCP connections...');

    // Clear auto-reconnect interval
    if (this.autoConnectInterval) {
      clearInterval(this.autoConnectInterval);
      this.autoConnectInterval = null;
    }

    // Disconnect all servers
    await mcpBridgeService.disconnectAll();

    this.initialized = false;
    console.log('✅ MCP: Shutdown complete');
  }

  /**
   * ✅ Reinitialize MCP connections (useful after settings changes)
   */
  public async reinitialize(): Promise<MCPInitializationResult> {
    console.log('🔄 MCP: Reinitializing MCP connections...');
    
    await this.shutdown();
    return await this.initialize();
  }

  /**
   * ✅ Check if MCP is initialized and ready
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * ✅ Get list of available MCP servers from settings
   */
  public getAvailableServers(): Array<{
    id: string;
    name: string;
    enabled: boolean;
    connected: boolean;
    command: string;
  }> {
    const mcpSettings = settingsManager.getMCPSettings();
    const connectedServers = mcpBridgeService.getConnectedServers();

    return Object.entries(mcpSettings.servers).map(([id, config]) => ({
      id,
      name: config.name,
      enabled: config.enabled,
      connected: connectedServers.includes(id),
      command: config.command
    }));
  }

  /**
   * ✅ Get MCP server capabilities
   */
  public getServerCapabilities(serverId: string): string[] {
    return mcpBridgeService.getServerCapabilities(serverId);
  }
}

// Export singleton instance
export const mcpInitializationService = MCPInitializationService.getInstance();
