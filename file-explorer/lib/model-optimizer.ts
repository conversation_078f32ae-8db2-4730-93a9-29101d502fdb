// lib/model-optimizer.ts

import { LLMProvider, getProviderConfig, getAllProviders } from '../components/agents/llm-provider-registry';
import { unifiedModelService } from '../components/agents/unified-model-service';
import { modelRegistryService } from '../components/agents/model-registry-service';
import { CostSettings } from '../components/settings/settings-manager';

/**
 * ✅ Cost-Aware Model Optimization Service
 * Selects optimal models based on cost preferences and compatibility
 */

export interface ModelOption {
  provider: LLMProvider;
  modelId: string;
  displayName: string;
  costPer1kTokens: {
    input: number;
    output: number;
  };
  totalCostEstimate: number; // For estimated token usage
  contextSize: number;
  capabilities: string[];
  qualityScore: number; // 1-10 scale for model quality
}

export interface ModelSelectionCriteria {
  requiredCapabilities?: string[];
  minContextSize?: number;
  maxCostPer1kTokens?: number;
  estimatedInputTokens?: number;
  estimatedOutputTokens?: number;
  preferredProviders?: LLMProvider[];
  excludedProviders?: LLMProvider[];
}

export class ModelOptimizer {
  private static instance: ModelOptimizer | null = null;

  private constructor() {}

  public static getInstance(): ModelOptimizer {
    if (!ModelOptimizer.instance) {
      ModelOptimizer.instance = new ModelOptimizer();
    }
    return ModelOptimizer.instance;
  }

  /**
   * ✅ Select optimal model based on cost preferences
   */
  public selectOptimalModel(
    criteria: ModelSelectionCriteria,
    costSettings: CostSettings,
    currentProvider?: LLMProvider,
    currentModel?: string
  ): ModelOption | null {
    const availableModels = this.getAvailableModels(criteria);

    if (availableModels.length === 0) {
      console.warn('ModelOptimizer: No compatible models found for criteria');
      return null;
    }

    // If cost optimization is disabled, use quality-based selection
    if (!costSettings.preferCheaperModels) {
      const bestQualityModel = this.selectBestQualityModel(availableModels);
      console.log(`🧠 Optimizer: selected best quality model: ${bestQualityModel.provider}/${bestQualityModel.modelId}`);
      return bestQualityModel;
    }

    // Cost optimization enabled - select cheapest compatible model
    const cheapestModel = this.selectCheapestModel(availableModels);
    console.log(`🧠 Optimizer: selected cheapest model for task: ${cheapestModel.provider}/${cheapestModel.modelId} (cost: $${cheapestModel.totalCostEstimate.toFixed(4)})`);
    
    return cheapestModel;
  }

  /**
   * ✅ Get all available models that meet criteria
   */
  public getAvailableModels(criteria: ModelSelectionCriteria): ModelOption[] {
    const models: ModelOption[] = [];
    const providers = criteria.preferredProviders || getAllProviders();

    for (const provider of providers) {
      if (criteria.excludedProviders?.includes(provider)) {
        continue;
      }

      try {
        const providerModels = this.getProviderModels(provider, criteria);
        models.push(...providerModels);
      } catch (error) {
        console.warn(`ModelOptimizer: Failed to get models for ${provider}:`, error);
      }
    }

    return models;
  }

  /**
   * ✅ Select cheapest compatible model
   */
  public selectCheapestModel(models: ModelOption[]): ModelOption {
    return models.reduce((cheapest, current) => 
      current.totalCostEstimate < cheapest.totalCostEstimate ? current : cheapest
    );
  }

  /**
   * ✅ Select best quality model (fallback when cost optimization disabled)
   */
  public selectBestQualityModel(models: ModelOption[]): ModelOption {
    return models.reduce((best, current) => 
      current.qualityScore > best.qualityScore ? current : best
    );
  }

  /**
   * ✅ Get models for a specific provider
   */
  private getProviderModels(provider: LLMProvider, criteria: ModelSelectionCriteria): ModelOption[] {
    const config = getProviderConfig(provider);
    const models: ModelOption[] = [];

    // Get available models from registry or static list
    const availableModelIds = this.getProviderModelIds(provider);

    for (const modelId of availableModelIds) {
      try {
        const modelOption = this.createModelOption(provider, modelId, criteria);
        
        // Check if model meets criteria
        if (this.meetsSelectionCriteria(modelOption, criteria)) {
          models.push(modelOption);
        }
      } catch (error) {
        console.warn(`ModelOptimizer: Failed to create model option for ${provider}/${modelId}:`, error);
      }
    }

    return models;
  }

  /**
   * ✅ Create model option with cost and metadata
   */
  private createModelOption(
    provider: LLMProvider, 
    modelId: string, 
    criteria: ModelSelectionCriteria
  ): ModelOption {
    const config = getProviderConfig(provider);
    const unifiedModel = unifiedModelService.getModelById(modelId);
    
    // Estimate total cost based on expected token usage
    const inputTokens = criteria.estimatedInputTokens || 1000;
    const outputTokens = criteria.estimatedOutputTokens || 500;
    const totalCostEstimate = this.calculateTotalCost(provider, inputTokens, outputTokens);

    return {
      provider,
      modelId,
      displayName: unifiedModel?.label || modelId,
      costPer1kTokens: config.costPer1kTokens,
      totalCostEstimate,
      contextSize: unifiedModel?.contextSize || config.maxTokensSupported,
      capabilities: unifiedModel?.tags || [],
      qualityScore: this.getModelQualityScore(provider, modelId)
    };
  }

  /**
   * ✅ Check if model meets selection criteria
   */
  private meetsSelectionCriteria(model: ModelOption, criteria: ModelSelectionCriteria): boolean {
    // Check context size requirement
    if (criteria.minContextSize && model.contextSize < criteria.minContextSize) {
      return false;
    }

    // Check cost limit
    if (criteria.maxCostPer1kTokens) {
      const avgCost = (model.costPer1kTokens.input + model.costPer1kTokens.output) / 2;
      if (avgCost > criteria.maxCostPer1kTokens) {
        return false;
      }
    }

    // Check required capabilities
    if (criteria.requiredCapabilities) {
      const hasAllCapabilities = criteria.requiredCapabilities.every(cap =>
        model.capabilities.some(modelCap => 
          modelCap.toLowerCase().includes(cap.toLowerCase())
        )
      );
      if (!hasAllCapabilities) {
        return false;
      }
    }

    return true;
  }

  /**
   * ✅ Get available model IDs for provider
   */
  private getProviderModelIds(provider: LLMProvider): string[] {
    try {
      // Try to get from model registry service first (dynamic models)
      const registryModels = modelRegistryService.getEnrichedModels(provider);
      if (registryModels.length > 0) {
        return registryModels.map(m => m.id);
      }

      // Fallback to static model list
      const config = getProviderConfig(provider);
      return Object.keys(config.modelMap);
    } catch (error) {
      console.warn(`ModelOptimizer: Failed to get model IDs for ${provider}, using static list`);
      const config = getProviderConfig(provider);
      return Object.keys(config.modelMap);
    }
  }

  /**
   * ✅ Calculate total estimated cost for a request
   */
  private calculateTotalCost(provider: LLMProvider, inputTokens: number, outputTokens: number): number {
    const config = getProviderConfig(provider);
    const inputCost = (inputTokens / 1000) * config.costPer1kTokens.input;
    const outputCost = (outputTokens / 1000) * config.costPer1kTokens.output;
    return inputCost + outputCost;
  }

  /**
   * ✅ Get quality score for model (1-10 scale)
   */
  private getModelQualityScore(provider: LLMProvider, modelId: string): number {
    // Quality scoring based on model capabilities and performance
    // Higher scores for more capable models
    
    const modelLower = modelId.toLowerCase();
    
    // GPT-4 series (highest quality)
    if (modelLower.includes('gpt-4o') || modelLower.includes('gpt-4-turbo')) {
      return 10;
    }
    if (modelLower.includes('gpt-4')) {
      return 9;
    }
    
    // Claude series (high quality)
    if (modelLower.includes('claude-3-opus')) {
      return 10;
    }
    if (modelLower.includes('claude-3-sonnet')) {
      return 8;
    }
    if (modelLower.includes('claude-3-haiku')) {
      return 7;
    }
    
    // GPT-3.5 series (medium quality)
    if (modelLower.includes('gpt-3.5-turbo')) {
      return 6;
    }
    
    // Other models (provider-based scoring)
    switch (provider) {
      case 'openai':
        return 7; // Default for OpenAI models
      case 'anthropic':
        return 8; // Default for Anthropic models
      case 'google':
        return 7; // Gemini models
      case 'openrouter':
        return 6; // Varies by underlying model
      case 'deepseek':
        return 5; // Specialized coding models
      case 'fireworks':
        return 5; // Fast inference models
      default:
        return 5; // Conservative default
    }
  }

  /**
   * ✅ Get cost comparison between models
   */
  public compareModelCosts(models: ModelOption[]): {
    cheapest: ModelOption;
    mostExpensive: ModelOption;
    averageCost: number;
    costSavings: number; // Savings by choosing cheapest vs most expensive
  } {
    if (models.length === 0) {
      throw new Error('No models to compare');
    }

    const cheapest = this.selectCheapestModel(models);
    const mostExpensive = models.reduce((expensive, current) => 
      current.totalCostEstimate > expensive.totalCostEstimate ? current : expensive
    );
    
    const averageCost = models.reduce((sum, model) => sum + model.totalCostEstimate, 0) / models.length;
    const costSavings = mostExpensive.totalCostEstimate - cheapest.totalCostEstimate;

    return {
      cheapest,
      mostExpensive,
      averageCost,
      costSavings
    };
  }
}

// Export singleton instance
export const modelOptimizer = ModelOptimizer.getInstance();
