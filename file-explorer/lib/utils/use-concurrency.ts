// lib/utils/use-concurrency.ts
"use client"

import { useCallback, useEffect, useRef } from 'react';
import { useSystemSettings } from '../../components/settings/settings-context';
import { 
  ConcurrencyManager, 
  Task, 
  TaskOptions, 
  ConcurrencyStats,
  getGlobalConcurrencyManager,
  updateGlobalConcurrencyLimit
} from './concurrency-manager';

/**
 * ✅ React hook for concurrency-aware operations
 * Automatically uses SystemSettings.maxConcurrentTasks
 */
export function useConcurrency() {
  const { systemSettings } = useSystemSettings();
  const { maxConcurrentTasks } = systemSettings;
  const managerRef = useRef<ConcurrencyManager | null>(null);

  // Initialize or get global concurrency manager
  if (!managerRef.current) {
    managerRef.current = getGlobalConcurrencyManager(maxConcurrentTasks);
  }

  // Update limit when settings change
  useEffect(() => {
    if (managerRef.current) {
      managerRef.current.setLimit(maxConcurrentTasks);
    }
    // Also update global manager
    updateGlobalConcurrencyLimit(maxConcurrentTasks);
  }, [maxConcurrentTasks]);

  /**
   * ✅ Run a task with concurrency control
   */
  const runTask = useCallback(<T>(
    task: Task<T>,
    options: TaskOptions = {}
  ): Promise<T> => {
    if (!managerRef.current) {
      throw new Error('Concurrency manager not initialized');
    }
    return managerRef.current.run(task, options);
  }, []);

  /**
   * ✅ Run multiple tasks with concurrency control
   */
  const runTasks = useCallback(async <T>(
    tasks: Array<{ task: Task<T>; options?: TaskOptions }>,
    options: { 
      failFast?: boolean; 
      collectResults?: boolean;
    } = {}
  ): Promise<T[]> => {
    const { failFast = false, collectResults = true } = options;

    if (failFast) {
      // Fail fast: stop on first error
      const results: T[] = [];
      for (const { task, options: taskOptions } of tasks) {
        const result = await runTask(task, taskOptions);
        if (collectResults) results.push(result);
      }
      return results;
    } else {
      // Run all tasks, collect results and errors
      const promises = tasks.map(({ task, options: taskOptions }) => 
        runTask(task, taskOptions).catch(error => ({ error }))
      );
      
      const results = await Promise.all(promises);
      
      if (collectResults) {
        return results.filter(result => !('error' in result)) as T[];
      }
      
      return [];
    }
  }, [runTask]);

  /**
   * ✅ Get current concurrency statistics
   */
  const getStats = useCallback((): ConcurrencyStats => {
    if (!managerRef.current) {
      return {
        active: 0,
        queued: 0,
        completed: 0,
        failed: 0,
        limit: maxConcurrentTasks,
        averageWaitTime: 0,
        averageExecutionTime: 0
      };
    }
    return managerRef.current.getStats();
  }, [maxConcurrentTasks]);

  /**
   * ✅ Clear all queued tasks
   */
  const clearQueue = useCallback((): void => {
    if (managerRef.current) {
      managerRef.current.clearQueue();
    }
  }, []);

  /**
   * ✅ Wait for all tasks to complete
   */
  const waitForCompletion = useCallback((): Promise<void> => {
    if (!managerRef.current) {
      return Promise.resolve();
    }
    return managerRef.current.waitForCompletion();
  }, []);

  /**
   * ✅ Get queue information
   */
  const getQueueInfo = useCallback(() => {
    if (!managerRef.current) {
      return [];
    }
    return managerRef.current.getQueueInfo();
  }, []);

  return {
    runTask,
    runTasks,
    getStats,
    clearQueue,
    waitForCompletion,
    getQueueInfo,
    maxConcurrentTasks,
    manager: managerRef.current
  };
}

/**
 * ✅ Hook for agent-specific concurrency management
 */
export function useAgentConcurrency() {
  const { runTask, getStats, maxConcurrentTasks } = useConcurrency();

  /**
   * ✅ Run an agent task with proper labeling and priority
   */
  const runAgentTask = useCallback(async <T>(
    agentId: string,
    task: Task<T>,
    options: Omit<TaskOptions, 'label'> & { operation?: string } = {}
  ): Promise<T> => {
    const { operation = 'task', ...taskOptions } = options;
    const label = `Agent ${agentId} ${operation}`;
    
    console.log(`🤖 Starting agent task: ${label}`);
    
    return runTask(task, {
      ...taskOptions,
      label,
      priority: taskOptions.priority || 'medium'
    });
  }, [runTask]);

  /**
   * ✅ Run LLM call with concurrency control
   */
  const runLLMCall = useCallback(async <T>(
    provider: string,
    model: string,
    task: Task<T>,
    options: Omit<TaskOptions, 'label'> = {}
  ): Promise<T> => {
    const label = `LLM ${provider}/${model}`;
    
    return runTask(task, {
      ...options,
      label,
      priority: options.priority || 'high' // LLM calls get high priority
    });
  }, [runTask]);

  /**
   * ✅ Run file operation with concurrency control
   */
  const runFileOperation = useCallback(async <T>(
    operation: string,
    filePath: string,
    task: Task<T>,
    options: Omit<TaskOptions, 'label'> = {}
  ): Promise<T> => {
    const label = `File ${operation}: ${filePath}`;
    
    return runTask(task, {
      ...options,
      label,
      priority: options.priority || 'medium'
    });
  }, [runTask]);

  return {
    runAgentTask,
    runLLMCall,
    runFileOperation,
    getStats,
    maxConcurrentTasks
  };
}

/**
 * ✅ Hook for API-specific concurrency management
 */
export function useAPIConcurrency() {
  const { runTask, getStats, maxConcurrentTasks } = useConcurrency();

  /**
   * ✅ Run API call with concurrency control and retry logic
   */
  const runAPICall = useCallback(async <T>(
    endpoint: string,
    task: Task<T>,
    options: TaskOptions & { 
      retries?: number; 
      retryDelay?: number;
    } = {}
  ): Promise<T> => {
    const { retries = 0, retryDelay = 1000, ...taskOptions } = options;
    const label = `API ${endpoint}`;
    
    let lastError: any;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await runTask(task, {
          ...taskOptions,
          label: attempt > 0 ? `${label} (retry ${attempt})` : label,
          priority: taskOptions.priority || 'high'
        });
      } catch (error) {
        lastError = error;
        
        if (attempt < retries) {
          console.warn(`🔄 API call ${label} failed, retrying in ${retryDelay}ms (attempt ${attempt + 1}/${retries + 1})`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }
    
    throw lastError;
  }, [runTask]);

  return {
    runAPICall,
    getStats,
    maxConcurrentTasks
  };
}

/**
 * ✅ Hook for monitoring concurrency performance
 */
export function useConcurrencyMonitor() {
  const { getStats, getQueueInfo } = useConcurrency();

  /**
   * ✅ Get detailed performance metrics
   */
  const getPerformanceMetrics = useCallback(() => {
    const stats = getStats();
    const queueInfo = getQueueInfo();
    
    return {
      ...stats,
      queueDetails: queueInfo,
      utilizationRate: stats.limit > 0 ? (stats.active / stats.limit) * 100 : 0,
      successRate: stats.completed + stats.failed > 0 
        ? (stats.completed / (stats.completed + stats.failed)) * 100 
        : 0,
      isOverloaded: stats.queued > stats.limit * 2, // Queue is more than 2x the limit
      recommendations: {
        increaseLimit: stats.queued > stats.limit && stats.averageWaitTime > 5000,
        decreaseLimit: stats.active < stats.limit * 0.5 && stats.queued === 0
      }
    };
  }, [getStats, getQueueInfo]);

  return {
    getPerformanceMetrics,
    getStats,
    getQueueInfo
  };
}
