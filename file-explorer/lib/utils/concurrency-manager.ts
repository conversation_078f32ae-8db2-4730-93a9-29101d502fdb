// lib/utils/concurrency-manager.ts

/**
 * ✅ Central Concurrency Management System
 * Enforces SystemSettings.maxConcurrentTasks limit across all async operations
 * Provides queuing, priority handling, and reactive limit updates
 */

export type Task<T> = () => Promise<T>;

export interface TaskOptions {
  priority?: 'low' | 'medium' | 'high';
  label?: string;
  timeout?: number;
  abortSignal?: AbortSignal;
}

export interface QueuedTask<T> {
  task: Task<T>;
  options: TaskOptions;
  resolve: (value: T) => void;
  reject: (error: any) => void;
  id: string;
  queuedAt: number;
}

export interface ConcurrencyStats {
  active: number;
  queued: number;
  completed: number;
  failed: number;
  limit: number;
  averageWaitTime: number;
  averageExecutionTime: number;
}

/**
 * ✅ Main Concurrency Manager Class
 * Manages task execution with configurable concurrency limits
 */
export class ConcurrencyManager {
  private queue: QueuedTask<any>[] = [];
  private active = 0;
  private limit: number;
  private completed = 0;
  private failed = 0;
  private totalWaitTime = 0;
  private totalExecutionTime = 0;
  private taskIdCounter = 0;

  constructor(limit: number) {
    this.limit = Math.max(1, limit); // Ensure at least 1 concurrent task
  }

  /**
   * ✅ Execute a task with concurrency control
   * @param task - The async task to execute
   * @param options - Task options including priority and label
   * @returns Promise that resolves with task result
   */
  async run<T>(task: Task<T>, options: TaskOptions = {}): Promise<T> {
    const taskId = `task-${++this.taskIdCounter}`;
    const queuedAt = Date.now();
    const label = options.label || `Task ${taskId}`;

    console.log(`🎯 ConcurrencyManager: Queuing ${label} (active: ${this.active}/${this.limit})`);

    return new Promise<T>((resolve, reject) => {
      const queuedTask: QueuedTask<T> = {
        task,
        options,
        resolve,
        reject,
        id: taskId,
        queuedAt
      };

      // Add to queue with priority sorting
      this.addToQueue(queuedTask);
      
      // Try to process immediately if capacity available
      this.processQueue();
    });
  }

  /**
   * ✅ Add task to queue with priority handling
   */
  private addToQueue<T>(queuedTask: QueuedTask<T>): void {
    const priority = queuedTask.options.priority || 'medium';
    const priorityOrder = { high: 0, medium: 1, low: 2 };

    // Insert task based on priority
    let insertIndex = this.queue.length;
    for (let i = 0; i < this.queue.length; i++) {
      const existingPriority = this.queue[i].options.priority || 'medium';
      if (priorityOrder[priority] < priorityOrder[existingPriority]) {
        insertIndex = i;
        break;
      }
    }

    this.queue.splice(insertIndex, 0, queuedTask);
  }

  /**
   * ✅ Process queued tasks up to concurrency limit
   */
  private processQueue(): void {
    while (this.active < this.limit && this.queue.length > 0) {
      const queuedTask = this.queue.shift()!;
      this.executeTask(queuedTask);
    }
  }

  /**
   * ✅ Execute a single task
   */
  private async executeTask<T>(queuedTask: QueuedTask<T>): Promise<void> {
    const { task, options, resolve, reject, id, queuedAt } = queuedTask;
    const label = options.label || `Task ${id}`;
    
    this.active++;
    const waitTime = Date.now() - queuedAt;
    this.totalWaitTime += waitTime;

    console.log(`🚀 ConcurrencyManager: Starting ${label} (waited ${waitTime}ms, active: ${this.active}/${this.limit})`);

    const startTime = Date.now();

    try {
      // Handle abort signal
      if (options.abortSignal?.aborted) {
        throw new Error(`Task ${label} was aborted`);
      }

      // Execute the task with optional timeout
      let result: T;
      if (options.timeout) {
        result = await this.executeWithTimeout(task, options.timeout, label);
      } else {
        result = await task();
      }

      const executionTime = Date.now() - startTime;
      this.totalExecutionTime += executionTime;
      this.completed++;

      console.log(`✅ ConcurrencyManager: Completed ${label} in ${executionTime}ms`);
      resolve(result);

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.totalExecutionTime += executionTime;
      this.failed++;

      console.error(`❌ ConcurrencyManager: Failed ${label} after ${executionTime}ms:`, error);
      reject(error);

    } finally {
      this.active--;
      // Process next tasks in queue
      this.processQueue();
    }
  }

  /**
   * ✅ Execute task with timeout
   */
  private async executeWithTimeout<T>(task: Task<T>, timeoutMs: number, label: string): Promise<T> {
    let timeoutId: NodeJS.Timeout;

    const timeoutPromise = new Promise<never>((_, reject) => {
      timeoutId = setTimeout(() => {
        reject(new Error(`${label} timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    });

    try {
      const result = await Promise.race([task(), timeoutPromise]);
      clearTimeout(timeoutId!);
      return result;
    } catch (error) {
      clearTimeout(timeoutId!);
      throw error;
    }
  }

  /**
   * ✅ Update concurrency limit at runtime
   * @param newLimit - New maximum concurrent tasks
   */
  setLimit(newLimit: number): void {
    const oldLimit = this.limit;
    this.limit = Math.max(1, newLimit);
    
    console.log(`🔄 ConcurrencyManager: Limit updated from ${oldLimit} to ${this.limit}`);
    
    // If limit increased, process more tasks
    if (this.limit > oldLimit) {
      this.processQueue();
    }
  }

  /**
   * ✅ Get current concurrency statistics
   */
  getStats(): ConcurrencyStats {
    const totalTasks = this.completed + this.failed;
    return {
      active: this.active,
      queued: this.queue.length,
      completed: this.completed,
      failed: this.failed,
      limit: this.limit,
      averageWaitTime: totalTasks > 0 ? this.totalWaitTime / totalTasks : 0,
      averageExecutionTime: totalTasks > 0 ? this.totalExecutionTime / totalTasks : 0
    };
  }

  /**
   * ✅ Clear all queued tasks
   */
  clearQueue(): void {
    const clearedCount = this.queue.length;
    this.queue.forEach(queuedTask => {
      queuedTask.reject(new Error('Task queue cleared'));
    });
    this.queue = [];
    console.log(`🧹 ConcurrencyManager: Cleared ${clearedCount} queued tasks`);
  }

  /**
   * ✅ Wait for all active tasks to complete
   */
  async waitForCompletion(): Promise<void> {
    while (this.active > 0 || this.queue.length > 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * ✅ Get queue information for debugging
   */
  getQueueInfo(): Array<{ id: string; label: string; priority: string; waitTime: number }> {
    const now = Date.now();
    return this.queue.map(task => ({
      id: task.id,
      label: task.options.label || task.id,
      priority: task.options.priority || 'medium',
      waitTime: now - task.queuedAt
    }));
  }
}

/**
 * ✅ Global concurrency manager instance
 * Will be initialized with settings value
 */
let globalConcurrencyManager: ConcurrencyManager | null = null;

/**
 * ✅ Get or create global concurrency manager
 */
export function getGlobalConcurrencyManager(limit?: number): ConcurrencyManager {
  if (!globalConcurrencyManager) {
    globalConcurrencyManager = new ConcurrencyManager(limit || 3); // Default fallback
  }
  return globalConcurrencyManager;
}

/**
 * ✅ Update global concurrency manager limit
 */
export function updateGlobalConcurrencyLimit(newLimit: number): void {
  if (globalConcurrencyManager) {
    globalConcurrencyManager.setLimit(newLimit);
  }
}
