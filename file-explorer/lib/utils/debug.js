"use strict";
// lib/utils/debug.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.setGlobalDebugMode = setGlobalDebugMode;
exports.getDebugMode = getDebugMode;
exports.debugLog = debugLog;
exports.debugLogCategory = debugLogCategory;
exports.debugLLM = debugLLM;
exports.debugAgent = debugAgent;
exports.debugConcurrency = debugConcurrency;
exports.debugFile = debugFile;
exports.debugIPC = debugIPC;
exports.debugBoard = debugBoard;
exports.debugSettings = debugSettings;
exports.debugTiming = debugTiming;
exports.debugError = debugError;
exports.debugWarn = debugWarn;
exports.debugPerformance = debugPerformance;
exports.debugState = debugState;
exports.debugObject = debugObject;
exports.debugNetwork = debugNetwork;
exports.debugIf = debugIf;
exports.debugTrace = debugTrace;
exports.debugGroup = debugGroup;
exports.debugTable = debugTable;
exports.debugMemory = debugMemory;
exports.debugExecutionTime = debugExecutionTime;
exports.debugAsyncExecutionTime = debugAsyncExecutionTime;
/**
 * ✅ Debug Mode Utility System
 * Provides controlled diagnostic output based on SystemSettings.debugMode
 * Replaces console.log with settings-aware debug logging
 */
// Global debug state for non-React contexts
let globalDebugMode = false;
/**
 * ✅ Set global debug mode (for use in non-React contexts)
 */
function setGlobalDebugMode(enabled) {
    globalDebugMode = enabled;
}
/**
 * ✅ Get current debug mode state
 */
function getDebugMode() {
    return globalDebugMode;
}
/**
 * ✅ Main debug logging function
 * Only outputs when debugMode is enabled
 */
function debugLog(...args) {
    if (globalDebugMode) {
        console.debug('[AgentSystem DEBUG]', ...args);
    }
}
/**
 * ✅ Debug logging with category
 */
function debugLogCategory(category, ...args) {
    if (globalDebugMode) {
        console.debug(`[AgentSystem DEBUG:${category}]`, ...args);
    }
}
/**
 * ✅ Debug logging for LLM operations
 */
function debugLLM(...args) {
    debugLogCategory('LLM', ...args);
}
/**
 * ✅ Debug logging for Agent operations
 */
function debugAgent(...args) {
    debugLogCategory('AGENT', ...args);
}
/**
 * ✅ Debug logging for Concurrency operations
 */
function debugConcurrency(...args) {
    debugLogCategory('CONCURRENCY', ...args);
}
/**
 * ✅ Debug logging for File operations
 */
function debugFile(...args) {
    debugLogCategory('FILE', ...args);
}
/**
 * ✅ Debug logging for IPC operations
 */
function debugIPC(...args) {
    debugLogCategory('IPC', ...args);
}
/**
 * ✅ Debug logging for Board operations
 */
function debugBoard(...args) {
    debugLogCategory('BOARD', ...args);
}
/**
 * ✅ Debug logging for Settings operations
 */
function debugSettings(...args) {
    debugLogCategory('SETTINGS', ...args);
}
/**
 * ✅ Debug logging with timing information
 */
function debugTiming(label, startTime, ...args) {
    if (globalDebugMode) {
        const duration = Date.now() - startTime;
        console.debug(`[AgentSystem DEBUG:TIMING] ${label} took ${duration}ms`, ...args);
    }
}
/**
 * ✅ Debug logging for errors (always shows in debug mode)
 */
function debugError(error, context, ...args) {
    if (globalDebugMode) {
        const contextStr = context ? `:${context}` : '';
        console.debug(`[AgentSystem DEBUG:ERROR${contextStr}]`, error, ...args);
    }
}
/**
 * ✅ Debug logging for warnings
 */
function debugWarn(...args) {
    if (globalDebugMode) {
        console.debug('[AgentSystem DEBUG:WARN]', ...args);
    }
}
/**
 * ✅ Debug logging for performance metrics
 */
function debugPerformance(metric, value, unit = 'ms', ...args) {
    if (globalDebugMode) {
        console.debug(`[AgentSystem DEBUG:PERF] ${metric}: ${value}${unit}`, ...args);
    }
}
/**
 * ✅ Debug logging for state changes
 */
function debugState(component, oldState, newState, ...args) {
    if (globalDebugMode) {
        console.debug(`[AgentSystem DEBUG:STATE:${component}]`, {
            from: oldState,
            to: newState,
            ...args
        });
    }
}
/**
 * ✅ Debug logging with object inspection
 */
function debugObject(label, obj, ...args) {
    if (globalDebugMode) {
        console.debug(`[AgentSystem DEBUG:OBJECT] ${label}:`, obj, ...args);
    }
}
/**
 * ✅ Debug logging for network requests
 */
function debugNetwork(method, url, status, ...args) {
    if (globalDebugMode) {
        const statusStr = status ? ` (${status})` : '';
        console.debug(`[AgentSystem DEBUG:NETWORK] ${method} ${url}${statusStr}`, ...args);
    }
}
/**
 * ✅ Conditional debug logging
 */
function debugIf(condition, ...args) {
    if (globalDebugMode && condition) {
        console.debug('[AgentSystem DEBUG:CONDITIONAL]', ...args);
    }
}
/**
 * ✅ Debug logging with stack trace
 */
function debugTrace(...args) {
    if (globalDebugMode) {
        console.debug('[AgentSystem DEBUG:TRACE]', ...args);
        console.trace();
    }
}
/**
 * ✅ Debug group for related logs
 */
function debugGroup(label, callback) {
    if (globalDebugMode) {
        console.group(`[AgentSystem DEBUG:GROUP] ${label}`);
        try {
            callback();
        }
        finally {
            console.groupEnd();
        }
    }
}
/**
 * ✅ Debug table for structured data
 */
function debugTable(label, data, ...args) {
    if (globalDebugMode) {
        console.debug(`[AgentSystem DEBUG:TABLE] ${label}`, ...args);
        console.table(data);
    }
}
/**
 * ✅ Debug memory usage
 */
function debugMemory(label) {
    if (globalDebugMode && typeof performance !== 'undefined' && performance.memory) {
        const memory = performance.memory;
        const memoryInfo = {
            used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
        };
        console.debug(`[AgentSystem DEBUG:MEMORY${label ? `:${label}` : ''}]`, memoryInfo);
    }
}
/**
 * ✅ Debug function execution time
 */
function debugExecutionTime(label, fn) {
    if (!globalDebugMode) {
        return fn();
    }
    const startTime = Date.now();
    try {
        const result = fn();
        const duration = Date.now() - startTime;
        console.debug(`[AgentSystem DEBUG:EXECUTION] ${label} executed in ${duration}ms`);
        return result;
    }
    catch (error) {
        const duration = Date.now() - startTime;
        console.debug(`[AgentSystem DEBUG:EXECUTION] ${label} failed after ${duration}ms:`, error);
        throw error;
    }
}
/**
 * ✅ Debug async function execution time
 */
async function debugAsyncExecutionTime(label, fn) {
    if (!globalDebugMode) {
        return fn();
    }
    const startTime = Date.now();
    try {
        const result = await fn();
        const duration = Date.now() - startTime;
        console.debug(`[AgentSystem DEBUG:ASYNC] ${label} executed in ${duration}ms`);
        return result;
    }
    catch (error) {
        const duration = Date.now() - startTime;
        console.debug(`[AgentSystem DEBUG:ASYNC] ${label} failed after ${duration}ms:`, error);
        throw error;
    }
}
