// lib/utils/debug.ts

/**
 * ✅ Debug Mode Utility System
 * Provides controlled diagnostic output based on SystemSettings.debugMode
 * Replaces console.log with settings-aware debug logging
 */

// Global debug state for non-React contexts
let globalDebugMode = false;

/**
 * ✅ Set global debug mode (for use in non-React contexts)
 */
export function setGlobalDebugMode(enabled: boolean): void {
  globalDebugMode = enabled;
}

/**
 * ✅ Get current debug mode state
 */
export function getDebugMode(): boolean {
  return globalDebugMode;
}

/**
 * ✅ Main debug logging function
 * Only outputs when debugMode is enabled
 */
export function debugLog(...args: any[]): void {
  if (globalDebugMode) {
    console.debug('[AgentSystem DEBUG]', ...args);
  }
}

/**
 * ✅ Debug logging with category
 */
export function debugLogCategory(category: string, ...args: any[]): void {
  if (globalDebugMode) {
    console.debug(`[AgentSystem DEBUG:${category}]`, ...args);
  }
}

/**
 * ✅ Debug logging for LLM operations
 */
export function debugLLM(...args: any[]): void {
  debugLogCategory('LLM', ...args);
}

/**
 * ✅ Debug logging for Agent operations
 */
export function debugAgent(...args: any[]): void {
  debugLogCategory('AGENT', ...args);
}

/**
 * ✅ Debug logging for Concurrency operations
 */
export function debugConcurrency(...args: any[]): void {
  debugLogCategory('CONCURRENCY', ...args);
}

/**
 * ✅ Debug logging for File operations
 */
export function debugFile(...args: any[]): void {
  debugLogCategory('FILE', ...args);
}

/**
 * ✅ Debug logging for IPC operations
 */
export function debugIPC(...args: any[]): void {
  debugLogCategory('IPC', ...args);
}

/**
 * ✅ Debug logging for Board operations
 */
export function debugBoard(...args: any[]): void {
  debugLogCategory('BOARD', ...args);
}

/**
 * ✅ Debug logging for Settings operations
 */
export function debugSettings(...args: any[]): void {
  debugLogCategory('SETTINGS', ...args);
}

/**
 * ✅ Debug logging with timing information
 */
export function debugTiming(label: string, startTime: number, ...args: any[]): void {
  if (globalDebugMode) {
    const duration = Date.now() - startTime;
    console.debug(`[AgentSystem DEBUG:TIMING] ${label} took ${duration}ms`, ...args);
  }
}

/**
 * ✅ Debug logging for errors (always shows in debug mode)
 */
export function debugError(error: any, context?: string, ...args: any[]): void {
  if (globalDebugMode) {
    const contextStr = context ? `:${context}` : '';
    console.debug(`[AgentSystem DEBUG:ERROR${contextStr}]`, error, ...args);
  }
}

/**
 * ✅ Debug logging for warnings
 */
export function debugWarn(...args: any[]): void {
  if (globalDebugMode) {
    console.debug('[AgentSystem DEBUG:WARN]', ...args);
  }
}

/**
 * ✅ Debug logging for performance metrics
 */
export function debugPerformance(metric: string, value: number, unit = 'ms', ...args: any[]): void {
  if (globalDebugMode) {
    console.debug(`[AgentSystem DEBUG:PERF] ${metric}: ${value}${unit}`, ...args);
  }
}

/**
 * ✅ Debug logging for state changes
 */
export function debugState(component: string, oldState: any, newState: any, ...args: any[]): void {
  if (globalDebugMode) {
    console.debug(`[AgentSystem DEBUG:STATE:${component}]`, {
      from: oldState,
      to: newState,
      ...args
    });
  }
}

/**
 * ✅ Debug logging with object inspection
 */
export function debugObject(label: string, obj: any, ...args: any[]): void {
  if (globalDebugMode) {
    console.debug(`[AgentSystem DEBUG:OBJECT] ${label}:`, obj, ...args);
  }
}

/**
 * ✅ Debug logging for network requests
 */
export function debugNetwork(method: string, url: string, status?: number, ...args: any[]): void {
  if (globalDebugMode) {
    const statusStr = status ? ` (${status})` : '';
    console.debug(`[AgentSystem DEBUG:NETWORK] ${method} ${url}${statusStr}`, ...args);
  }
}

/**
 * ✅ Conditional debug logging
 */
export function debugIf(condition: boolean, ...args: any[]): void {
  if (globalDebugMode && condition) {
    console.debug('[AgentSystem DEBUG:CONDITIONAL]', ...args);
  }
}

/**
 * ✅ Debug logging with stack trace
 */
export function debugTrace(...args: any[]): void {
  if (globalDebugMode) {
    console.debug('[AgentSystem DEBUG:TRACE]', ...args);
    console.trace();
  }
}

/**
 * ✅ Debug group for related logs
 */
export function debugGroup(label: string, callback: () => void): void {
  if (globalDebugMode) {
    console.group(`[AgentSystem DEBUG:GROUP] ${label}`);
    try {
      callback();
    } finally {
      console.groupEnd();
    }
  }
}

/**
 * ✅ Debug table for structured data
 */
export function debugTable(label: string, data: any[], ...args: any[]): void {
  if (globalDebugMode) {
    console.debug(`[AgentSystem DEBUG:TABLE] ${label}`, ...args);
    console.table(data);
  }
}

/**
 * ✅ Debug memory usage
 */
export function debugMemory(label?: string): void {
  if (globalDebugMode && typeof performance !== 'undefined' && (performance as any).memory) {
    const memory = (performance as any).memory;
    const memoryInfo = {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
    };
    console.debug(`[AgentSystem DEBUG:MEMORY${label ? `:${label}` : ''}]`, memoryInfo);
  }
}

/**
 * ✅ Debug function execution time
 */
export function debugExecutionTime<T>(label: string, fn: () => T): T {
  if (!globalDebugMode) {
    return fn();
  }
  
  const startTime = Date.now();
  try {
    const result = fn();
    const duration = Date.now() - startTime;
    console.debug(`[AgentSystem DEBUG:EXECUTION] ${label} executed in ${duration}ms`);
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.debug(`[AgentSystem DEBUG:EXECUTION] ${label} failed after ${duration}ms:`, error);
    throw error;
  }
}

/**
 * ✅ Debug async function execution time
 */
export async function debugAsyncExecutionTime<T>(label: string, fn: () => Promise<T>): Promise<T> {
  if (!globalDebugMode) {
    return fn();
  }
  
  const startTime = Date.now();
  try {
    const result = await fn();
    const duration = Date.now() - startTime;
    console.debug(`[AgentSystem DEBUG:ASYNC] ${label} executed in ${duration}ms`);
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.debug(`[AgentSystem DEBUG:ASYNC] ${label} failed after ${duration}ms:`, error);
    throw error;
  }
}
