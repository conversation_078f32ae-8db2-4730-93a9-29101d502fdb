"use strict";
// lib/utils/timeout.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeoutError = void 0;
exports.withTimeout = withTimeout;
exports.withTimeoutAndAbort = withTimeoutAndAbort;
exports.fetchWithTimeout = fetchWithTimeout;
exports.withTimeoutError = withTimeoutError;
exports.isTimeoutError = isTimeoutError;
exports.formatTimeoutError = formatTimeoutError;
/**
 * ✅ Wrap any promise with timeout functionality
 * @param promise - The promise to wrap with timeout
 * @param timeoutMs - Timeout in milliseconds
 * @param label - Label for error messages (default: 'Operation')
 * @returns Promise that resolves with result or rejects with timeout error
 */
async function withTimeout(promise, timeoutMs, label = 'Operation') {
    let timeoutId;
    const timeout = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
            reject(new Error(`${label} timed out after ${timeoutMs}ms`));
        }, timeoutMs);
    });
    try {
        const result = await Promise.race([promise, timeout]);
        clearTimeout(timeoutId);
        return result;
    }
    catch (error) {
        clearTimeout(timeoutId);
        throw error;
    }
}
/**
 * ✅ Advanced timeout wrapper with abort controller support
 * @param promise - The promise to wrap
 * @param options - Timeout options including abort controller
 * @returns Promise that resolves with result or rejects with timeout error
 */
async function withTimeoutAndAbort(promise, options) {
    const { timeoutMs, label = 'Operation', abortController } = options;
    let timeoutId;
    const timeout = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
            // Abort the operation if abort controller is provided
            if (abortController) {
                abortController.abort();
            }
            reject(new Error(`${label} timed out after ${timeoutMs}ms`));
        }, timeoutMs);
    });
    try {
        const result = await Promise.race([promise, timeout]);
        clearTimeout(timeoutId);
        return result;
    }
    catch (error) {
        clearTimeout(timeoutId);
        throw error;
    }
}
/**
 * ✅ Create a timeout-aware fetch wrapper
 * @param url - URL to fetch
 * @param options - Fetch options
 * @param timeoutMs - Timeout in milliseconds
 * @param label - Label for error messages
 * @returns Promise that resolves with Response or rejects with timeout error
 */
async function fetchWithTimeout(url, options = {}, timeoutMs, label = 'Fetch Request') {
    const abortController = new AbortController();
    const fetchPromise = fetch(url, {
        ...options,
        signal: abortController.signal
    });
    return withTimeoutAndAbort(fetchPromise, {
        timeoutMs,
        label: `${label} (${url})`,
        abortController
    });
}
/**
 * ✅ Timeout error type for better error handling
 */
class TimeoutError extends Error {
    constructor(operation, timeoutMs) {
        super(`${operation} timed out after ${timeoutMs}ms`);
        this.isTimeout = true;
        this.name = 'TimeoutError';
        this.operation = operation;
        this.timeoutMs = timeoutMs;
    }
}
exports.TimeoutError = TimeoutError;
/**
 * ✅ Enhanced timeout wrapper that throws TimeoutError
 * @param promise - The promise to wrap
 * @param timeoutMs - Timeout in milliseconds
 * @param label - Label for error messages
 * @returns Promise that resolves with result or rejects with TimeoutError
 */
async function withTimeoutError(promise, timeoutMs, label = 'Operation') {
    let timeoutId;
    const timeout = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
            reject(new TimeoutError(label, timeoutMs));
        }, timeoutMs);
    });
    try {
        const result = await Promise.race([promise, timeout]);
        clearTimeout(timeoutId);
        return result;
    }
    catch (error) {
        clearTimeout(timeoutId);
        throw error;
    }
}
/**
 * ✅ Check if an error is a timeout error
 * @param error - Error to check
 * @returns True if error is a timeout error
 */
function isTimeoutError(error) {
    return error instanceof TimeoutError ||
        (error instanceof Error && error.message.includes('timed out after'));
}
/**
 * ✅ Format timeout error for user display
 * @param error - Timeout error
 * @returns User-friendly error message
 */
function formatTimeoutError(error) {
    if (isTimeoutError(error)) {
        return `Operation timed out. Please try again or increase the timeout setting.`;
    }
    return error.message;
}
