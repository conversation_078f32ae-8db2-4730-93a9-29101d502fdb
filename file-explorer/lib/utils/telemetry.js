"use strict";
// lib/utils/telemetry.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.setGlobalTelemetryEnabled = setGlobalTelemetryEnabled;
exports.getTelemetryEnabled = getTelemetryEnabled;
exports.trackEvent = trackEvent;
exports.flushTelemetry = flushTelemetry;
exports.trackAgentEvent = trackAgentEvent;
exports.trackLLMEvent = trackLLMEvent;
exports.trackPerformance = trackPerformance;
exports.trackError = trackError;
exports.trackFeatureUsage = trackFeatureUsage;
exports.trackSystemMetric = trackSystemMetric;
exports.trackConcurrencyMetric = trackConcurrencyMetric;
exports.trackTimeout = trackTimeout;
exports.trackSettingsChange = trackSettingsChange;
/**
 * ✅ Privacy-Aware Telemetry System
 * Collects anonymized usage data based on SystemSettings.enableTelemetry
 * GDPR compliant - no PII, user input, or file content collected
 */
// Global telemetry state for non-React contexts
let globalTelemetryEnabled = false;
let telemetryQueue = [];
let isProcessingQueue = false;
const defaultConfig = {
    endpoint: '/api/telemetry',
    batchSize: 10,
    flushInterval: 30000, // 30 seconds
    maxQueueSize: 100
};
// Generate session ID (not tied to user identity)
const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
/**
 * ✅ Set global telemetry mode (for use in non-React contexts)
 */
function setGlobalTelemetryEnabled(enabled) {
    globalTelemetryEnabled = enabled;
    if (!enabled) {
        // Clear queue when disabled
        telemetryQueue = [];
    }
}
/**
 * ✅ Get current telemetry enabled state
 */
function getTelemetryEnabled() {
    return globalTelemetryEnabled;
}
/**
 * ✅ Sanitize data to remove PII and sensitive information
 */
function sanitizeData(data) {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
        // Skip sensitive keys
        if (isSensitiveKey(key)) {
            continue;
        }
        // Sanitize values
        if (typeof value === 'string') {
            sanitized[key] = sanitizeString(value);
        }
        else if (typeof value === 'number' || typeof value === 'boolean') {
            sanitized[key] = value;
        }
        else if (Array.isArray(value)) {
            sanitized[key] = value.length; // Only store array length
        }
        else if (value && typeof value === 'object') {
            sanitized[key] = sanitizeData(value); // Recursive sanitization
        }
    }
    return sanitized;
}
/**
 * ✅ Check if a key contains sensitive information
 */
function isSensitiveKey(key) {
    const sensitiveKeys = [
        'password', 'token', 'key', 'secret', 'auth', 'credential',
        'email', 'name', 'username', 'user', 'content', 'input',
        'output', 'response', 'prompt', 'message', 'text', 'code',
        'file', 'path', 'url', 'ip', 'address', 'location'
    ];
    const lowerKey = key.toLowerCase();
    return sensitiveKeys.some(sensitive => lowerKey.includes(sensitive));
}
/**
 * ✅ Sanitize string values
 */
function sanitizeString(value) {
    // Only keep length and basic characteristics
    return `string_length_${value.length}`;
}
/**
 * ✅ Main telemetry tracking function
 */
function trackEvent(eventName, data = {}) {
    if (!globalTelemetryEnabled) {
        return;
    }
    try {
        // Sanitize data to remove PII
        const sanitizedData = sanitizeData(data);
        const event = {
            eventName,
            data: sanitizedData,
            timestamp: Date.now(),
            sessionId
        };
        // Add to queue
        telemetryQueue.push(event);
        // Limit queue size
        if (telemetryQueue.length > defaultConfig.maxQueueSize) {
            telemetryQueue = telemetryQueue.slice(-defaultConfig.maxQueueSize);
        }
        // Process queue if batch size reached
        if (telemetryQueue.length >= defaultConfig.batchSize) {
            processQueue();
        }
    }
    catch (error) {
        // Telemetry must fail silently
        console.debug('Telemetry error (silent):', error);
    }
}
/**
 * ✅ Process telemetry queue
 */
async function processQueue() {
    if (!globalTelemetryEnabled || isProcessingQueue || telemetryQueue.length === 0) {
        return;
    }
    isProcessingQueue = true;
    const eventsToSend = telemetryQueue.splice(0, defaultConfig.batchSize);
    try {
        // Check if we're in a browser environment
        if (typeof fetch !== 'undefined') {
            await fetch(defaultConfig.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Telemetry-Session': sessionId
                },
                body: JSON.stringify({ events: eventsToSend })
            });
        }
    }
    catch (error) {
        // Telemetry must fail silently - don't re-queue events
        console.debug('Telemetry send failed (silent):', error);
    }
    finally {
        isProcessingQueue = false;
    }
}
/**
 * ✅ Flush all queued events
 */
async function flushTelemetry() {
    if (!globalTelemetryEnabled) {
        return;
    }
    while (telemetryQueue.length > 0) {
        await processQueue();
    }
}
/**
 * ✅ Track agent operation events
 */
function trackAgentEvent(agentId, operation, data = {}) {
    trackEvent('agent_operation', {
        agentId,
        operation,
        ...data
    });
}
/**
 * ✅ Track LLM operation events
 */
function trackLLMEvent(provider, operation, data = {}) {
    trackEvent('llm_operation', {
        provider,
        operation,
        ...data
    });
}
/**
 * ✅ Track performance metrics
 */
function trackPerformance(metric, value, unit = 'ms', context) {
    trackEvent('performance_metric', {
        metric,
        value,
        unit,
        context
    });
}
/**
 * ✅ Track error events (without sensitive details)
 */
function trackError(errorType, context, data = {}) {
    trackEvent('error_occurred', {
        errorType,
        context,
        ...data
    });
}
/**
 * ✅ Track feature usage
 */
function trackFeatureUsage(feature, action, data = {}) {
    trackEvent('feature_usage', {
        feature,
        action,
        ...data
    });
}
/**
 * ✅ Track system metrics
 */
function trackSystemMetric(metric, value, data = {}) {
    trackEvent('system_metric', {
        metric,
        value,
        ...data
    });
}
/**
 * ✅ Track concurrency metrics
 */
function trackConcurrencyMetric(active, queued, limit) {
    trackEvent('concurrency_metric', {
        active,
        queued,
        limit,
        utilization: limit > 0 ? (active / limit) * 100 : 0
    });
}
/**
 * ✅ Track timeout events
 */
function trackTimeout(operation, timeoutMs, actualMs) {
    trackEvent('timeout_occurred', {
        operation,
        timeoutMs,
        actualMs
    });
}
/**
 * ✅ Track settings changes (without values)
 */
function trackSettingsChange(setting, category) {
    trackEvent('settings_changed', {
        setting,
        category
    });
}
// Auto-flush queue periodically
if (typeof setInterval !== 'undefined') {
    setInterval(() => {
        if (globalTelemetryEnabled && telemetryQueue.length > 0) {
            processQueue();
        }
    }, defaultConfig.flushInterval);
}
// Flush on page unload (browser only)
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        if (globalTelemetryEnabled && telemetryQueue.length > 0) {
            // Use sendBeacon for reliable delivery on page unload
            if (navigator.sendBeacon) {
                const eventsToSend = telemetryQueue.splice(0);
                navigator.sendBeacon(defaultConfig.endpoint, JSON.stringify({ events: eventsToSend }));
            }
        }
    });
}
