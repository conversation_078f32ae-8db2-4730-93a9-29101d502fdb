// lib/utils/use-timeout.ts
"use client"

import { useCallback } from 'react';
import { useSystemSettings } from '../../components/settings/settings-context';
import { withTimeout, withTimeoutError, fetchWithTimeout, TimeoutError, isTimeoutError } from './timeout';

/**
 * ✅ React hook for timeout-aware operations
 * Automatically uses SystemSettings.defaultTimeout
 */
export function useTimeout() {
  const { systemSettings } = useSystemSettings();
  const { defaultTimeout } = systemSettings;

  /**
   * ✅ Wrap a promise with the current default timeout
   */
  const withDefaultTimeout = useCallback(<T>(
    promise: Promise<T>,
    label = 'Operation'
  ): Promise<T> => {
    return withTimeout(promise, defaultTimeout, label);
  }, [defaultTimeout]);

  /**
   * ✅ Wrap a promise with TimeoutError for better error handling
   */
  const withDefaultTimeoutError = useCallback(<T>(
    promise: Promise<T>,
    label = 'Operation'
  ): Promise<T> => {
    return withTimeoutError(promise, defaultTimeout, label);
  }, [defaultTimeout]);

  /**
   * ✅ Fetch with default timeout
   */
  const fetchWithDefaultTimeout = useCallback((
    url: string,
    options: RequestInit = {},
    label = 'API Request'
  ): Promise<Response> => {
    return fetchWithTimeout(url, options, defaultTimeout, label);
  }, [defaultTimeout]);

  /**
   * ✅ Custom timeout (overrides default)
   */
  const withCustomTimeout = useCallback(<T>(
    promise: Promise<T>,
    timeoutMs: number,
    label = 'Operation'
  ): Promise<T> => {
    return withTimeout(promise, timeoutMs, label);
  }, []);

  return {
    defaultTimeout,
    withDefaultTimeout,
    withDefaultTimeoutError,
    fetchWithDefaultTimeout,
    withCustomTimeout,
    isTimeoutError,
    TimeoutError
  };
}

/**
 * ✅ Hook for timeout-aware API calls
 * Provides common patterns for API operations
 */
export function useTimeoutAPI() {
  const { 
    defaultTimeout, 
    withDefaultTimeoutError, 
    fetchWithDefaultTimeout 
  } = useTimeout();

  /**
   * ✅ Make a timeout-aware API call with JSON response
   */
  const apiCall = useCallback(async <T>(
    url: string,
    options: RequestInit = {},
    label = 'API Call'
  ): Promise<T> => {
    const response = await fetchWithDefaultTimeout(url, options, label);
    
    if (!response.ok) {
      throw new Error(`${label} failed: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
  }, [fetchWithDefaultTimeout]);

  /**
   * ✅ Make a timeout-aware LLM API call
   */
  const llmCall = useCallback(async <T>(
    promise: Promise<T>,
    modelName = 'LLM',
    operation = 'completion'
  ): Promise<T> => {
    return withDefaultTimeoutError(
      promise, 
      `${modelName} ${operation}`
    );
  }, [withDefaultTimeoutError]);

  /**
   * ✅ Make a timeout-aware agent operation
   */
  const agentOperation = useCallback(async <T>(
    promise: Promise<T>,
    agentName = 'Agent',
    operation = 'task execution'
  ): Promise<T> => {
    return withDefaultTimeoutError(
      promise,
      `${agentName} ${operation}`
    );
  }, [withDefaultTimeoutError]);

  return {
    defaultTimeout,
    apiCall,
    llmCall,
    agentOperation,
    isTimeoutError
  };
}
