// lib/utils/use-telemetry.ts
"use client"

import { useEffect } from 'react';
import { useSystemSettings } from '../../components/settings/settings-context';
import {
  setGlobalTelemetryEnabled,
  trackEvent,
  trackAgentEvent,
  trackLLMEvent,
  trackPerformance,
  trackError,
  trackFeatureUsage,
  trackSystemMetric,
  trackConcurrencyMetric,
  trackTimeout,
  trackSettingsChange,
  flushTelemetry
} from './telemetry';

/**
 * ✅ React hook for telemetry operations
 * Automatically syncs with SystemSettings.enableTelemetry
 */
export function useTelemetry() {
  let enableTelemetry = false;

  try {
    const { systemSettings } = useSystemSettings();
    enableTelemetry = systemSettings.enableTelemetry || false;
  } catch (error) {
    // Fallback when settings context not available
    enableTelemetry = false;
  }

  // Update global telemetry state when settings change
  useEffect(() => {
    setGlobalTelemetryEnabled(enableTelemetry);
    if (enableTelemetry) {
      trackEvent('telemetry_enabled');
    }

    // ✅ Also update Electron main process if available
    if (typeof window !== 'undefined' && window.electronAPI?.ipc?.invoke) {
      window.electronAPI.ipc.invoke('llm:updateTelemetryMode', enableTelemetry)
        .catch((error: any) => console.warn('Failed to sync telemetry mode to Electron:', error));
    }
  }, [enableTelemetry]);

  return {
    enableTelemetry,
    trackEvent,
    trackAgentEvent,
    trackLLMEvent,
    trackPerformance,
    trackError,
    trackFeatureUsage,
    trackSystemMetric,
    trackConcurrencyMetric,
    trackTimeout,
    trackSettingsChange,
    flushTelemetry
  };
}

/**
 * ✅ Hook for component-specific telemetry
 */
export function useComponentTelemetry(componentName: string) {
  const telemetry = useTelemetry();

  const trackComponentEvent = (action: string, data: Record<string, any> = {}) => {
    telemetry.trackFeatureUsage(componentName, action, data);
  };

  const trackComponentError = (errorType: string, data: Record<string, any> = {}) => {
    telemetry.trackError(errorType, componentName, data);
  };

  const trackComponentPerformance = (metric: string, value: number, unit = 'ms') => {
    telemetry.trackPerformance(`${componentName}_${metric}`, value, unit, componentName);
  };

  return {
    ...telemetry,
    trackComponentEvent,
    trackComponentError,
    trackComponentPerformance
  };
}

/**
 * ✅ Hook for agent-specific telemetry
 */
export function useAgentTelemetry(agentId: string) {
  const telemetry = useTelemetry();

  const trackAgentStart = (operation: string, data: Record<string, any> = {}) => {
    telemetry.trackAgentEvent(agentId, `${operation}_started`, data);
  };

  const trackAgentComplete = (operation: string, duration: number, data: Record<string, any> = {}) => {
    telemetry.trackAgentEvent(agentId, `${operation}_completed`, { duration, ...data });
    telemetry.trackPerformance(`agent_${operation}_duration`, duration, 'ms', agentId);
  };

  const trackAgentError = (operation: string, errorType: string, data: Record<string, any> = {}) => {
    telemetry.trackAgentEvent(agentId, `${operation}_failed`, { errorType, ...data });
    telemetry.trackError(`agent_${errorType}`, agentId, data);
  };

  const trackAgentMetric = (metric: string, value: number, data: Record<string, any> = {}) => {
    telemetry.trackSystemMetric(`agent_${agentId}_${metric}`, value, data);
  };

  return {
    ...telemetry,
    trackAgentStart,
    trackAgentComplete,
    trackAgentError,
    trackAgentMetric
  };
}

/**
 * ✅ Hook for LLM-specific telemetry
 */
export function useLLMTelemetry(provider?: string) {
  const telemetry = useTelemetry();

  const trackLLMCall = (operation: string, model: string, data: Record<string, any> = {}) => {
    if (provider) {
      telemetry.trackLLMEvent(provider, operation, { model, ...data });
    }
  };

  const trackLLMPerformance = (operation: string, model: string, duration: number, data: Record<string, any> = {}) => {
    if (provider) {
      telemetry.trackLLMEvent(provider, `${operation}_performance`, {
        model,
        duration,
        ...data
      });
      telemetry.trackPerformance(`llm_${operation}_duration`, duration, 'ms', `${provider}/${model}`);
    }
  };

  const trackLLMError = (operation: string, model: string, errorType: string, data: Record<string, any> = {}) => {
    if (provider) {
      telemetry.trackLLMEvent(provider, `${operation}_failed`, {
        model,
        errorType,
        ...data
      });
      telemetry.trackError(`llm_${errorType}`, `${provider}/${model}`, data);
    }
  };

  const trackLLMTokens = (model: string, inputTokens: number, outputTokens: number) => {
    if (provider) {
      telemetry.trackLLMEvent(provider, 'token_usage', {
        model,
        inputTokens,
        outputTokens,
        totalTokens: inputTokens + outputTokens
      });
    }
  };

  return {
    ...telemetry,
    trackLLMCall,
    trackLLMPerformance,
    trackLLMError,
    trackLLMTokens
  };
}

/**
 * ✅ Hook for concurrency telemetry
 */
export function useConcurrencyTelemetry() {
  const telemetry = useTelemetry();

  const trackConcurrencyState = (active: number, queued: number, limit: number) => {
    telemetry.trackConcurrencyMetric(active, queued, limit);
  };

  const trackTaskQueued = (taskType: string, priority: string, waitTime?: number) => {
    telemetry.trackEvent('task_queued', {
      taskType,
      priority,
      waitTime
    });
  };

  const trackTaskStarted = (taskType: string, queueTime: number) => {
    telemetry.trackEvent('task_started', {
      taskType,
      queueTime
    });
    telemetry.trackPerformance('task_queue_time', queueTime, 'ms', taskType);
  };

  const trackTaskCompleted = (taskType: string, executionTime: number, queueTime: number) => {
    telemetry.trackEvent('task_completed', {
      taskType,
      executionTime,
      queueTime,
      totalTime: executionTime + queueTime
    });
    telemetry.trackPerformance('task_execution_time', executionTime, 'ms', taskType);
  };

  return {
    ...telemetry,
    trackConcurrencyState,
    trackTaskQueued,
    trackTaskStarted,
    trackTaskCompleted
  };
}

/**
 * ✅ Hook for settings telemetry
 */
export function useSettingsTelemetry() {
  const telemetry = useTelemetry();

  const trackSettingChanged = (setting: string, category = 'system') => {
    telemetry.trackSettingsChange(setting, category);
  };

  const trackSettingsExport = () => {
    telemetry.trackFeatureUsage('settings', 'export');
  };

  const trackSettingsImport = () => {
    telemetry.trackFeatureUsage('settings', 'import');
  };

  const trackSettingsReset = (category?: string) => {
    telemetry.trackFeatureUsage('settings', 'reset', { category });
  };

  return {
    ...telemetry,
    trackSettingChanged,
    trackSettingsExport,
    trackSettingsImport,
    trackSettingsReset
  };
}
