// lib/cost-tracker.ts

import { LLMProvider, estimateCost } from '../components/agents/llm-provider-registry';
import { getConfigStoreBrowser } from '../components/background/config-store-browser';

/**
 * ✅ Cost Tracking Service
 * Tracks API usage costs and enforces budget limits
 * Provides real-time cost monitoring and budget enforcement
 */

export interface CostEntry {
  id: string;
  timestamp: number;
  provider: LLMProvider;
  model: string;
  inputTokens: number;
  outputTokens: number;
  cost: number;
  agentId?: string;
  taskId?: string;
}

export interface MonthlyCostSummary {
  month: string; // YYYY-MM format
  totalCost: number;
  totalTokens: number;
  callCount: number;
  providerBreakdown: Record<LLMProvider, {
    cost: number;
    tokens: number;
    calls: number;
  }>;
  modelBreakdown: Record<string, {
    cost: number;
    tokens: number;
    calls: number;
  }>;
}

export interface BudgetStatus {
  currentMonthlyCost: number;
  budgetLimit: number;
  remainingBudget: number;
  utilizationPercentage: number;
  alertThreshold: number;
  isOverBudget: boolean;
  isNearThreshold: boolean;
}

export class CostTracker {
  private static instance: CostTracker | null = null;
  private configStore = getConfigStoreBrowser();
  private costEntries: CostEntry[] = [];
  private initialized = false;

  private constructor() {
    this.loadCostHistory();
  }

  public static getInstance(): CostTracker {
    if (!CostTracker.instance) {
      CostTracker.instance = new CostTracker();
    }
    return CostTracker.instance;
  }

  /**
   * ✅ Initialize cost tracker
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.configStore.initialize();
      await this.loadCostHistory();
      this.initialized = true;
      console.log('CostTracker: Initialized successfully');
    } catch (error) {
      console.error('CostTracker: Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * ✅ Record a new cost entry
   */
  public async recordCost(
    provider: LLMProvider,
    model: string,
    inputTokens: number,
    outputTokens: number,
    agentId?: string,
    taskId?: string
  ): Promise<CostEntry> {
    const cost = estimateCost(provider, inputTokens, outputTokens);

    const entry: CostEntry = {
      id: `cost_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      provider,
      model,
      inputTokens,
      outputTokens,
      cost,
      agentId,
      taskId
    };

    this.costEntries.push(entry);
    await this.saveCostHistory();

    console.log(`💰 Cost recorded: $${cost.toFixed(4)} for ${provider}/${model} (${inputTokens + outputTokens} tokens)`);

    return entry;
  }

  /**
   * ✅ Get current month's total cost
   */
  public getCurrentMonthlyCost(): number {
    const currentMonth = this.getCurrentMonthKey();
    const monthlyEntries = this.costEntries.filter(entry =>
      this.getMonthKey(entry.timestamp) === currentMonth
    );

    return monthlyEntries.reduce((total, entry) => total + entry.cost, 0);
  }

  /**
   * ✅ Estimate cost for a potential LLM call
   */
  public estimateCallCost(
    provider: LLMProvider,
    inputTokens: number,
    outputTokens: number
  ): number {
    return estimateCost(provider, inputTokens, outputTokens);
  }

  /**
   * ✅ Check if a potential call would exceed budget
   */
  public wouldExceedBudget(
    provider: LLMProvider,
    inputTokens: number,
    outputTokens: number,
    budgetLimit: number
  ): boolean {
    const currentCost = this.getCurrentMonthlyCost();
    const estimatedCost = this.estimateCallCost(provider, inputTokens, outputTokens);

    return (currentCost + estimatedCost) > budgetLimit;
  }

  /**
   * ✅ Get budget status
   */
  public getBudgetStatus(budgetLimit: number, alertThreshold: number): BudgetStatus {
    const currentMonthlyCost = this.getCurrentMonthlyCost();
    const remainingBudget = Math.max(0, budgetLimit - currentMonthlyCost);
    const utilizationPercentage = budgetLimit > 0 ? (currentMonthlyCost / budgetLimit) * 100 : 0;
    const isOverBudget = currentMonthlyCost > budgetLimit;
    const isNearThreshold = utilizationPercentage >= alertThreshold;

    return {
      currentMonthlyCost,
      budgetLimit,
      remainingBudget,
      utilizationPercentage,
      alertThreshold,
      isOverBudget,
      isNearThreshold
    };
  }

  /**
   * ✅ Get monthly cost summary
   */
  public getMonthlyCostSummary(month?: string): MonthlyCostSummary {
    const targetMonth = month || this.getCurrentMonthKey();
    const monthlyEntries = this.costEntries.filter(entry =>
      this.getMonthKey(entry.timestamp) === targetMonth
    );

    const totalCost = monthlyEntries.reduce((sum, entry) => sum + entry.cost, 0);
    const totalTokens = monthlyEntries.reduce((sum, entry) => sum + entry.inputTokens + entry.outputTokens, 0);
    const callCount = monthlyEntries.length;

    // Provider breakdown
    const providerBreakdown: Record<string, any> = {};
    monthlyEntries.forEach(entry => {
      if (!providerBreakdown[entry.provider]) {
        providerBreakdown[entry.provider] = { cost: 0, tokens: 0, calls: 0 };
      }
      providerBreakdown[entry.provider].cost += entry.cost;
      providerBreakdown[entry.provider].tokens += entry.inputTokens + entry.outputTokens;
      providerBreakdown[entry.provider].calls += 1;
    });

    // Model breakdown
    const modelBreakdown: Record<string, any> = {};
    monthlyEntries.forEach(entry => {
      const modelKey = `${entry.provider}/${entry.model}`;
      if (!modelBreakdown[modelKey]) {
        modelBreakdown[modelKey] = { cost: 0, tokens: 0, calls: 0 };
      }
      modelBreakdown[modelKey].cost += entry.cost;
      modelBreakdown[modelKey].tokens += entry.inputTokens + entry.outputTokens;
      modelBreakdown[modelKey].calls += 1;
    });

    return {
      month: targetMonth,
      totalCost,
      totalTokens,
      callCount,
      providerBreakdown,
      modelBreakdown
    };
  }

  /**
   * ✅ Get cost history for a date range
   */
  public getCostHistory(startDate: Date, endDate: Date): CostEntry[] {
    return this.costEntries.filter(entry =>
      entry.timestamp >= startDate.getTime() &&
      entry.timestamp <= endDate.getTime()
    );
  }

  /**
   * ✅ Clear cost history (for testing or reset)
   */
  public async clearCostHistory(): Promise<void> {
    this.costEntries = [];
    await this.saveCostHistory();
    console.log('CostTracker: Cost history cleared');
  }

  /**
   * ✅ Export cost data
   */
  public exportCostData(): {
    entries: CostEntry[];
    summary: MonthlyCostSummary;
    exportedAt: number;
  } {
    return {
      entries: [...this.costEntries],
      summary: this.getMonthlyCostSummary(),
      exportedAt: Date.now()
    };
  }

  // Private helper methods

  private getCurrentMonthKey(): string {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
  }

  private getMonthKey(timestamp: number): string {
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  }

  private async loadCostHistory(): Promise<void> {
    try {
      if (typeof window === 'undefined') return;

      // Try to load from config store first
      if (this.configStore.isInitialized()) {
        const storedEntries = await this.configStore.getGlobalSetting('cost', 'entries');
        if (storedEntries && Array.isArray(storedEntries)) {
          this.costEntries = storedEntries;
          console.log(`CostTracker: Loaded ${this.costEntries.length} cost entries from config store`);
          return;
        }
      }

      // Fallback to localStorage
      const stored = localStorage.getItem('synapse-cost-history');
      if (stored) {
        const parsed = JSON.parse(stored);
        if (Array.isArray(parsed)) {
          this.costEntries = parsed;
          console.log(`CostTracker: Loaded ${this.costEntries.length} cost entries from localStorage`);
        }
      }
    } catch (error) {
      console.error('CostTracker: Failed to load cost history:', error);
      this.costEntries = [];
    }
  }

  private async saveCostHistory(): Promise<void> {
    try {
      if (typeof window === 'undefined') return;

      // Save to config store if available
      if (this.configStore.isInitialized()) {
        await this.configStore.setGlobalSetting('cost', 'entries', this.costEntries);
      }

      // Also save to localStorage as backup
      localStorage.setItem('synapse-cost-history', JSON.stringify(this.costEntries));
    } catch (error) {
      console.error('CostTracker: Failed to save cost history:', error);
    }
  }
}

// Export singleton instance
export const costTracker = CostTracker.getInstance();
