// lib/alert-manager.ts

import { CostSettings } from '../components/settings/settings-manager';
import { costTracker, BudgetStatus } from './cost-tracker';
import { getConfigStoreBrowser } from '../components/background/config-store-browser';

/**
 * ✅ Alert Threshold System
 * Manages threshold alerts to prevent spam and provide clear notifications
 * Works in both Electron and Web environments
 */

export interface ThresholdAlert {
  id: string;
  type: 'threshold_exceeded' | 'budget_exceeded' | 'cost_warning';
  message: string;
  currentCost: number;
  threshold: number;
  budgetLimit: number;
  utilizationPercentage: number;
  timestamp: number;
  acknowledged: boolean;
  month: string; // YYYY-MM format
}

export interface AlertState {
  lastAlertMonth: string;
  thresholdAlertTriggered: boolean;
  budgetAlertTriggered: boolean;
  alertHistory: ThresholdAlert[];
}

export class AlertManager {
  private static instance: AlertManager | null = null;
  private configStore = getConfigStoreBrowser();
  private alertState: AlertState = {
    lastAlertMonth: '',
    thresholdAlertTriggered: false,
    budgetAlertTriggered: false,
    alertHistory: []
  };
  private alertListeners: ((alert: ThresholdAlert) => void)[] = [];
  private initialized = false;

  private constructor() {
    this.loadAlertState();
  }

  public static getInstance(): AlertManager {
    if (!AlertManager.instance) {
      AlertManager.instance = new AlertManager();
    }
    return AlertManager.instance;
  }

  /**
   * ✅ Initialize alert manager
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.configStore.initialize();
      await this.loadAlertState();
      this.initialized = true;
      console.log('AlertManager: Initialized successfully');
    } catch (error) {
      console.error('AlertManager: Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * ✅ Check thresholds and trigger alerts if needed
   */
  public checkThresholds(costSettings: CostSettings): ThresholdAlert | null {
    if (!costSettings.trackUsage) {
      return null;
    }

    const budgetStatus = costTracker.getBudgetStatus(costSettings.budgetLimit, costSettings.alertThreshold);
    const currentMonth = this.getCurrentMonthKey();

    // Reset alert state for new month
    if (this.alertState.lastAlertMonth !== currentMonth) {
      this.resetMonthlyAlerts(currentMonth);
    }

    // Check threshold alert (only trigger once per month)
    if (budgetStatus.isNearThreshold && !this.alertState.thresholdAlertTriggered) {
      const alert = this.createThresholdAlert(budgetStatus, costSettings);
      this.triggerAlert(alert);
      this.alertState.thresholdAlertTriggered = true;
      this.saveAlertState();
      return alert;
    }

    // Check budget exceeded alert (only trigger once per month)
    if (budgetStatus.isOverBudget && !this.alertState.budgetAlertTriggered) {
      const alert = this.createBudgetAlert(budgetStatus, costSettings);
      this.triggerAlert(alert);
      this.alertState.budgetAlertTriggered = true;
      this.saveAlertState();
      return alert;
    }

    return null;
  }

  /**
   * ✅ Force check thresholds (for manual testing)
   */
  public forceCheckThresholds(costSettings: CostSettings): ThresholdAlert | null {
    const budgetStatus = costTracker.getBudgetStatus(costSettings.budgetLimit, costSettings.alertThreshold);

    if (budgetStatus.isOverBudget) {
      const alert = this.createBudgetAlert(budgetStatus, costSettings);
      this.triggerAlert(alert);
      return alert;
    }

    if (budgetStatus.isNearThreshold) {
      const alert = this.createThresholdAlert(budgetStatus, costSettings);
      this.triggerAlert(alert);
      return alert;
    }

    return null;
  }

  /**
   * ✅ Subscribe to alert notifications
   */
  public onAlert(listener: (alert: ThresholdAlert) => void): () => void {
    this.alertListeners.push(listener);

    // Return unsubscribe function
    return () => {
      const index = this.alertListeners.indexOf(listener);
      if (index > -1) {
        this.alertListeners.splice(index, 1);
      }
    };
  }

  /**
   * ✅ Get alert history
   */
  public getAlertHistory(): ThresholdAlert[] {
    return [...this.alertState.alertHistory];
  }

  /**
   * ✅ Get current alert state
   */
  public getAlertState(): AlertState {
    return { ...this.alertState };
  }

  /**
   * ✅ Acknowledge an alert
   */
  public acknowledgeAlert(alertId: string): void {
    const alert = this.alertState.alertHistory.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.saveAlertState();
      console.log(`AlertManager: Alert ${alertId} acknowledged`);
    }
  }

  /**
   * ✅ Clear alert history
   */
  public clearAlertHistory(): void {
    this.alertState.alertHistory = [];
    this.saveAlertState();
    console.log('AlertManager: Alert history cleared');
  }

  /**
   * ✅ Reset alerts for testing
   */
  public resetAlertsForTesting(): void {
    const currentMonth = this.getCurrentMonthKey();
    this.alertState.thresholdAlertTriggered = false;
    this.alertState.budgetAlertTriggered = false;
    this.alertState.lastAlertMonth = currentMonth;
    this.saveAlertState();
    console.log('AlertManager: Alerts reset for testing');
  }

  // Private helper methods

  private createThresholdAlert(budgetStatus: BudgetStatus, costSettings: CostSettings): ThresholdAlert {
    return {
      id: `threshold_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'threshold_exceeded',
      message: `⚠️ You've exceeded your alert threshold: $${budgetStatus.currentMonthlyCost.toFixed(2)} (${budgetStatus.utilizationPercentage.toFixed(1)}% of $${budgetStatus.budgetLimit.toFixed(2)} budget)`,
      currentCost: budgetStatus.currentMonthlyCost,
      threshold: (budgetStatus.budgetLimit * costSettings.alertThreshold) / 100,
      budgetLimit: budgetStatus.budgetLimit,
      utilizationPercentage: budgetStatus.utilizationPercentage,
      timestamp: Date.now(),
      acknowledged: false,
      month: this.getCurrentMonthKey()
    };
  }

  private createBudgetAlert(budgetStatus: BudgetStatus, costSettings: CostSettings): ThresholdAlert {
    return {
      id: `budget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'budget_exceeded',
      message: `🚨 Budget exceeded: $${budgetStatus.currentMonthlyCost.toFixed(2)} exceeds your monthly limit of $${budgetStatus.budgetLimit.toFixed(2)}`,
      currentCost: budgetStatus.currentMonthlyCost,
      threshold: budgetStatus.budgetLimit,
      budgetLimit: budgetStatus.budgetLimit,
      utilizationPercentage: budgetStatus.utilizationPercentage,
      timestamp: Date.now(),
      acknowledged: false,
      month: this.getCurrentMonthKey()
    };
  }

  private triggerAlert(alert: ThresholdAlert): void {
    // Add to history
    this.alertState.alertHistory.push(alert);

    // Keep only last 50 alerts
    if (this.alertState.alertHistory.length > 50) {
      this.alertState.alertHistory = this.alertState.alertHistory.slice(-50);
    }

    // Notify listeners
    this.alertListeners.forEach(listener => {
      try {
        listener(alert);
      } catch (error) {
        console.error('AlertManager: Error in alert listener:', error);
      }
    });

    // Console log for debugging
    console.warn(`AlertManager: ${alert.message}`);
  }

  private resetMonthlyAlerts(newMonth: string): void {
    this.alertState.lastAlertMonth = newMonth;
    this.alertState.thresholdAlertTriggered = false;
    this.alertState.budgetAlertTriggered = false;
    this.saveAlertState();
    console.log(`AlertManager: Reset alerts for new month: ${newMonth}`);
  }

  private getCurrentMonthKey(): string {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
  }

  private async loadAlertState(): Promise<void> {
    try {
      if (typeof window === 'undefined') return;

      // Try to load from config store first
      if (this.configStore.isInitialized()) {
        const storedState = await this.configStore.getGlobalSetting('alerts', 'state');
        if (storedState && typeof storedState === 'object') {
          this.alertState = { ...this.alertState, ...storedState };
          console.log('AlertManager: Loaded alert state from config store');
          return;
        }
      }

      // Fallback to localStorage
      const stored = localStorage.getItem('synapse-alert-state');
      if (stored) {
        const parsed = JSON.parse(stored);
        if (parsed && typeof parsed === 'object') {
          this.alertState = { ...this.alertState, ...parsed };
          console.log('AlertManager: Loaded alert state from localStorage');
        }
      }
    } catch (error) {
      console.error('AlertManager: Failed to load alert state:', error);
      // Use default state
    }
  }

  private async saveAlertState(): Promise<void> {
    try {
      if (typeof window === 'undefined') return;

      // Save to config store if available
      if (this.configStore.isInitialized()) {
        await this.configStore.setGlobalSetting('alerts', 'state', this.alertState);
      }

      // Also save to localStorage as backup
      localStorage.setItem('synapse-alert-state', JSON.stringify(this.alertState));
    } catch (error) {
      console.error('AlertManager: Failed to save alert state:', error);
    }
  }
}

// Export singleton instance
export const alertManager = AlertManager.getInstance();
