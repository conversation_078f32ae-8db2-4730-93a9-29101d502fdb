// lib/io/settings-exporter.ts
import { AllSettings, SystemSettings, AgentSettings, CostSettings, PrivacySettings, EditorSettings } from '../../components/settings/settings-manager';

export interface ValidationResult {
  success: boolean;
  error?: string;
  warnings?: string[];
}

export interface ExportOptions {
  includeApiKeys?: boolean;
  includeAgents?: boolean;
  includeSystem?: boolean;
  includeCost?: boolean;
  includePrivacy?: boolean;
  includeEditor?: boolean;
}

/**
 * Validates system settings structure
 */
function validateSystemSettings(settings: any): boolean {
  if (!settings || typeof settings !== 'object') return false;
  
  const required = ['theme', 'autoSave', 'autoSaveInterval', 'maxConcurrentTasks', 'defaultTimeout', 'enableTelemetry', 'debugMode', 'testModeEnabled'];
  return required.every(key => key in settings);
}

/**
 * Validates agent settings structure
 */
function validateAgentSettings(agents: any): boolean {
  if (!Array.isArray(agents)) return false;
  
  return agents.every(agent => {
    if (!agent || typeof agent !== 'object') return false;
    const required = ['id', 'name', 'enabled', 'model', 'provider', 'maxTokens', 'temperature', 'capabilities'];
    return required.every(key => key in agent);
  });
}

/**
 * Validates cost settings structure
 */
function validateCostSettings(settings: any): boolean {
  if (!settings || typeof settings !== 'object') return false;
  
  const required = ['budgetLimit', 'alertThreshold', 'trackUsage', 'showCostEstimates', 'preferCheaperModels'];
  return required.every(key => key in settings);
}

/**
 * Validates privacy settings structure
 */
function validatePrivacySettings(settings: any): boolean {
  if (!settings || typeof settings !== 'object') return false;
  
  const required = ['shareUsageData', 'localOnly', 'encryptPrompts', 'clearHistoryOnExit', 'maxHistoryDays'];
  return required.every(key => key in settings);
}

/**
 * Validates editor settings structure
 */
function validateEditorSettings(settings: any): boolean {
  if (!settings || typeof settings !== 'object') return false;
  
  const required = ['fontSize', 'fontFamily', 'tabSize', 'wordWrap', 'lineNumbers', 'minimap', 'autoFormat', 'autoComplete'];
  return required.every(key => key in settings);
}

/**
 * Validates the complete settings structure
 */
export function validateSettingsSchema(settings: any): ValidationResult {
  const warnings: string[] = [];
  
  try {
    if (!settings || typeof settings !== 'object') {
      return { success: false, error: 'Invalid settings format - must be an object' };
    }

    // Validate system settings
    if (settings.system && !validateSystemSettings(settings.system)) {
      return { success: false, error: 'Invalid system settings structure' };
    }

    // Validate agent settings
    if (settings.agents && !validateAgentSettings(settings.agents)) {
      return { success: false, error: 'Invalid agent settings structure' };
    }

    // Validate cost settings
    if (settings.cost && !validateCostSettings(settings.cost)) {
      return { success: false, error: 'Invalid cost settings structure' };
    }

    // Validate privacy settings
    if (settings.privacy && !validatePrivacySettings(settings.privacy)) {
      return { success: false, error: 'Invalid privacy settings structure' };
    }

    // Validate editor settings
    if (settings.editor && !validateEditorSettings(settings.editor)) {
      return { success: false, error: 'Invalid editor settings structure' };
    }

    // Check for API keys (warn if present)
    if (settings.apiKeys && Object.keys(settings.apiKeys).length > 0) {
      warnings.push('API keys found in import file - these will be skipped for security');
    }

    return { success: true, warnings: warnings.length > 0 ? warnings : undefined };
  } catch (error) {
    return { success: false, error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
}

/**
 * Exports settings to a JSON blob
 */
export function exportSettings(settings: AllSettings, options: ExportOptions = {}): Blob {
  const {
    includeApiKeys = false,
    includeAgents = true,
    includeSystem = true,
    includeCost = true,
    includePrivacy = true,
    includeEditor = true
  } = options;

  const exportData: Partial<AllSettings> = {};

  if (includeSystem) {
    exportData.system = settings.system;
  }

  if (includeAgents) {
    exportData.agents = settings.agents;
  }

  if (includeCost) {
    exportData.cost = settings.cost;
  }

  if (includePrivacy) {
    exportData.privacy = settings.privacy;
  }

  if (includeEditor) {
    exportData.editor = settings.editor;
  }

  if (includeApiKeys) {
    exportData.apiKeys = settings.apiKeys;
  } else {
    // Always include empty apiKeys object for schema consistency
    exportData.apiKeys = {};
  }

  // Add metadata
  const exportWithMetadata = {
    ...exportData,
    _metadata: {
      exportedAt: new Date().toISOString(),
      version: '1.0.0',
      source: 'Synapse Agent System'
    }
  };

  const json = JSON.stringify(exportWithMetadata, null, 2);
  return new Blob([json], { type: 'application/json' });
}

/**
 * Imports and validates settings from JSON string
 */
export function importSettings(json: string): ValidationResult & { settings?: Partial<AllSettings> } {
  try {
    const parsed = JSON.parse(json);
    
    // Remove metadata if present
    if (parsed._metadata) {
      delete parsed._metadata;
    }

    const validation = validateSettingsSchema(parsed);
    
    if (!validation.success) {
      return validation;
    }

    // Remove API keys for security (never import them)
    if (parsed.apiKeys) {
      delete parsed.apiKeys;
    }

    return {
      success: true,
      warnings: validation.warnings,
      settings: parsed
    };
  } catch (error) {
    return {
      success: false,
      error: `Invalid JSON format: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
