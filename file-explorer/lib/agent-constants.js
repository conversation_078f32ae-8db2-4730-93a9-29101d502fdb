"use strict";
// lib/agent-constants.ts
// Shared constants for agent system IPC communication
Object.defineProperty(exports, "__esModule", { value: true });
exports.AGENT_EVENTS = exports.AGENT_COMMANDS = void 0;
exports.AGENT_COMMANDS = {
    GET_STATE: 'agent:get-state',
    UPDATE_AGENT_STATUS: 'agent:update-agent-status',
    ADD_MESSAGE: 'agent:add-message',
    ASSIGN_TASK: 'agent:assign-task',
    UPDATE_TASK: 'agent:update-task',
    CLEAR_MESSAGES: 'agent:clear-messages',
    SET_RUNNING_STATE: 'agent:set-running-state',
};
exports.AGENT_EVENTS = {
    STATE_UPDATE: 'agent:state-update',
    MESSAGE_ADDED: 'agent:message-added',
    AGENT_STATUS_CHANGED: 'agent:agent-status-changed',
    TASK_ASSIGNED: 'agent:task-assigned',
    RUNNING_STATE_CHANGED: 'agent:running-state-changed',
};
