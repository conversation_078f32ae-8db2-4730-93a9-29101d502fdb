"use client"

import React from "react";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import FileSidebar from "@/components/file-sidebar";

function ErrorBoundary({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) {
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Explorer Error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

export default function ExplorerWindowPage() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="h-screen w-full bg-background text-foreground">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border bg-background">
            <h1 className="text-lg font-semibold">File Explorer</h1>
          </div>

          {/* Explorer Content */}
          <div className="flex-1 overflow-hidden">
            <ErrorBoundary fallback={<div className="p-4 text-center text-red-500">Failed to load File Explorer</div>}>
              <FileSidebar onFileSelect={() => {}} />
            </ErrorBoundary>
          </div>
        </div>
        <Toaster />
      </div>
    </ThemeProvider>
  );
}
