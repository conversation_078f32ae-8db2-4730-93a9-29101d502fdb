"use client"

import React from 'react';
import { ClientSettingsWrapper } from '@/components/settings/client-settings-wrapper';
import TerminalHeader from '@/components/terminal/TerminalHeader';
import TerminalPanel from '@/components/terminal/TerminalPanel';

function ErrorBoundary({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) {
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Terminal Error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

export default function TerminalPage() {
  return (
    <ClientSettingsWrapper>
      <div className="h-screen w-full bg-background flex flex-col">
        <TerminalHeader onDetach={() => {}} />
        <div className="flex-1 overflow-hidden">
          <ErrorBoundary fallback={<div className="p-4 text-center text-red-500">Failed to load Terminal</div>}>
            <TerminalPanel className="h-full" />
          </ErrorBoundary>
        </div>
      </div>
    </ClientSettingsWrapper>
  );
}
