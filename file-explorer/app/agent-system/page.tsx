"use client"

import React from "react";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import { CompleteAgentSystem } from '@/components/agents/complete-integration';
import { SharedAgentStateProvider } from '@/components/agents/shared-agent-state';
import { ClientSettingsWrapper } from '@/components/settings/client-settings-wrapper';
import { useAlertNotifications } from '@/components/budget/use-threshold-alerts';
import { AlertBanner } from '@/components/budget/alert-display';

function AgentSystemContent() {
  // ✅ Initialize alert notifications for Agent System window
  useAlertNotifications();

  return (
    <div className="h-screen w-full bg-background text-foreground">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border bg-background">
          <h1 className="text-lg font-semibold">Agent System</h1>
        </div>

        {/* ✅ Alert Banner for cost threshold alerts */}
        <div className="px-4 py-1">
          <AlertBanner />
        </div>

        {/* Agent System Content */}
        <div className="flex-1 overflow-hidden">
          <ErrorBoundary fallback={<div className="p-4 text-center text-red-500">Failed to load Agent System</div>}>
            <CompleteAgentSystem />
          </ErrorBoundary>
        </div>
      </div>
      <Toaster />
    </div>
  );
}

// Simple error boundary component
function ErrorBoundary({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) {
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Agent System Error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

export default function AgentSystemWindowPage() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <ClientSettingsWrapper>
        <SharedAgentStateProvider>
          <AgentSystemContent />
        </SharedAgentStateProvider>
      </ClientSettingsWrapper>
    </ThemeProvider>
  );
}
