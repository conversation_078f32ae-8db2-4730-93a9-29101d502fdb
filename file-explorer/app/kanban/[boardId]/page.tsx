import { KanbanWindowClient } from './kanban-window-client';

// Required for static export with dynamic routes
export async function generateStaticParams() {
  return [
    { boardId: 'default-board-1' },
    { boardId: 'main' },
  ];
}

interface KanbanWindowPageProps {
  params: Promise<{ boardId: string }>;
}

export default async function KanbanWindowPage({ params }: KanbanWindowPageProps) {
  const { boardId } = await params;

  return <KanbanWindowClient boardId={boardId} />;
}