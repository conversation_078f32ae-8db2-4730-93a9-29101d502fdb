"use client"

import React from 'react';
import dynamic from 'next/dynamic';
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import { BoardProvider } from "@/components/kanban/board-context";
import { SearchProvider } from "@/components/kanban/search-provider";
import { AgentBoardControllerProvider } from "@/components/kanban/agent-board-controller";

const KanbanBoard = dynamic(() => import('@/components/kanban/kanban-board'), {
  ssr: false,
  loading: () => <div className="p-4 text-center">Loading Kanban Board...</div>
});

interface KanbanWindowClientProps {
  boardId: string;
}

function ErrorBoundary({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) {
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Kanban Error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

export function KanbanWindowClient({ boardId }: KanbanWindowClientProps) {
  if (!boardId) {
    return <div className="p-8 text-center text-red-500">Board ID not provided.</div>;
  }

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <BoardProvider>
        <AgentBoardControllerProvider>
          <SearchProvider>
            <div className="flex flex-col h-screen bg-[#f5f5f5] dark:bg-[#1e1e1e]">
              {/* No header or main layout here, just the board */}
              <ErrorBoundary fallback={<div className="p-4 text-center text-red-500">Failed to load Kanban Board</div>}>
                <KanbanBoard boardId={boardId} />
              </ErrorBoundary>
            </div>
          </SearchProvider>
        </AgentBoardControllerProvider>
      </BoardProvider>
      <Toaster />
    </ThemeProvider>
  );
}
