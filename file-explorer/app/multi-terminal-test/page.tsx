'use client';

import React from 'react';
import MultiSessionTerminal from '@/components/terminal/MultiSessionTerminal';

export default function MultiTerminalTestPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">Task 104: Multi-Session Terminal Test</h1>
          <p className="text-gray-300">
            Test the new multi-session terminal functionality with tabbed interface.
          </p>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 h-[600px] flex flex-col">
          <div className="p-4 border-b border-gray-700">
            <h2 className="text-xl font-semibold">Multi-Session Terminal</h2>
            <p className="text-sm text-gray-400 mt-1">
              Create multiple terminal sessions, switch between them, and manage them independently.
            </p>
          </div>
          
          <div className="flex-1">
            <MultiSessionTerminal 
              className="h-full"
              onReady={(terminal) => {
                console.log('✅ Multi-session terminal ready:', terminal);
              }}
            />
          </div>
        </div>

        <div className="mt-6 bg-gray-800 rounded-lg border border-gray-700 p-4">
          <h3 className="text-lg font-semibold mb-3">Features Implemented</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-green-400">✅ Backend Features</h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• Unique session IDs per PTY process</li>
                <li>• Session creation/disposal via IPC</li>
                <li>• Independent shell processes</li>
                <li>• Session data/exit event handling</li>
                <li>• Session listing and management</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-400">✅ Frontend Features</h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• Tab-based session switching</li>
                <li>• Session creation with "+" button</li>
                <li>• Session closing with "X" button</li>
                <li>• Real-time terminal interaction</li>
                <li>• Dynamic xterm.js loading</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-6 bg-gray-800 rounded-lg border border-gray-700 p-4">
          <h3 className="text-lg font-semibold mb-3">Testing Instructions</h3>
          <div className="text-sm text-gray-300 space-y-2">
            <p><strong>1. Create Sessions:</strong> Click the "New" button to create additional terminal sessions</p>
            <p><strong>2. Switch Sessions:</strong> Click on different tabs to switch between terminal sessions</p>
            <p><strong>3. Independent State:</strong> Each session maintains its own command history and shell state</p>
            <p><strong>4. Close Sessions:</strong> Click the "X" button on tabs to close sessions (when multiple exist)</p>
            <p><strong>5. Real Shell:</strong> Each session runs a real interactive shell process</p>
          </div>
        </div>
      </div>
    </div>
  );
}
