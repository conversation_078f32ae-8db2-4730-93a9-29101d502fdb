"use client"

import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { useState, useEffect } from "react"
import dynamic from "next/dynamic"
import ResizablePanel from "@/components/ui/resizable-panel"
import ResizableLeftPanel from "@/components/ui/resizable-left-panel"
import {

  Split,
  Zap,
  Settings,
  ChevronDown,
  Play,
  PanelLeft,
  Files,
  Search,
  GitBranch,
  Bug,
  ExpandIcon as Extension,
  MessageSquare,
  X,
  Plus,
  Save,
  Maximize2,
  Minimize2,
  ExternalLink,
  Trello,
  Bot as Robot,
  Clock,
  Terminal,
} from "lucide-react"
import FileSidebar, { FileSystemItem } from "@/components/file-sidebar"
import CodeEditor from "@/components/code-editor"
import AiChatPanel from "@/components/ai-chat-panel"
import AgentChatPanel from "@/components/chat/AgentChatPanel"
import CommandPalette from "@/components/command-palette"
import { ThemeToggle } from "@/components/theme-toggle"
import { KanbanBoard } from "@/components/kanban/kanban-board"
import { AgentActivityPanel } from "@/components/kanban/agent-activity-panel"
import { BoardProvider } from "@/components/kanban/board-context"
import { SearchProvider } from "@/components/kanban/search-provider"
import { AgentBoardControllerProvider } from "@/components/kanban/agent-board-controller"
import { Header as KanbanHeader } from "@/components/kanban/header"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Dialog, DialogContent, RebuiltDialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import { useSettings } from '@/components/settings/settings-context'
import { CompleteAgentSystem } from '@/components/agents/complete-integration'
import { SharedAgentStateProvider } from '@/components/agents/shared-agent-state'
import { EditorStateProvider } from '@/components/editor/editor-state-provider'
import { EditorActionProvider } from '@/components/editor/editor-action-provider'
import { ClientSettingsWrapper } from '@/components/settings/client-settings-wrapper'
import SettingsCenter from '@/components/settings/SettingsCenter'
import { Toaster } from '@/components/ui/toaster'
import { useAlertNotifications } from '@/components/budget/use-threshold-alerts'
import { AlertBanner } from '@/components/budget/alert-display'
import TaskTimelineInspector from '@/components/inspector/TaskTimelineInspector'
import ConcurrencyOverlay, { toggleConcurrencyOverlay } from '@/components/inspector/ConcurrencyOverlay'
import ProjectStatusBar from '@/components/ui/project-status-bar'
// ✅ Task 100 Step 4: Import TerminalLogsPanel
import { TerminalLogsPanel } from '@/components/terminal/TerminalLogsPanel'
import TerminalPanel from '@/components/terminal/TerminalPanel'
import TerminalHeader from '@/components/terminal/TerminalHeader'
import ResizableBottomTerminal from '@/components/terminal/ResizableBottomTerminal'


// Create a client-side only wrapper to avoid SSR issues
const ClientOnlyHome = dynamic(() => Promise.resolve(HomeComponent), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-screen bg-background">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Loading CodeFusion...</p>
      </div>
    </div>
  )
});



function HomeComponent() {
  const [selectedFile, setSelectedFile] = useState<FileSystemItem | null>(null)
  const { theme } = useTheme()
  const [settingsManager, setSettingsManager] = useState<any>(null)

  // ✅ Initialize settings manager safely
  useEffect(() => {
    const initializeSettings = async () => {
      try {
        const { getGlobalSettingsManager } = await import('@/components/settings/global-settings');
        const manager = getGlobalSettingsManager();
        setSettingsManager(manager);
      } catch (error) {
        console.warn('Failed to initialize settings manager:', error);
      }
    };

    initializeSettings();
  }, []);

  // Renamed activePanel to sidebarActivePanel for clarity
  const [sidebarActivePanel, setSidebarActivePanel] = useState<string | null>("explorer")
  const [showRightPanel, setShowRightPanel] = useState(false)
  const [editorLayout, setEditorLayout] = useState<"single" | "split-horizontal" | "split-vertical">("single")
  const [showSettingsDialog, setShowSettingsDialog] = useState(false)
  const [chatPanelWidth, setChatPanelWidth] = useState(384) // Default width
  const [timelinePanelWidth, setTimelinePanelWidth] = useState(500) // Default width for timeline
  const [showKeyboardShortcutsDialog, setShowKeyboardShortcutsDialog] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  // New state to manage which tab is active in the main content area when docked
  const [mainContentActiveTab, setMainContentActiveTab] = useState<'editor' | 'kanban' | 'agentSystem' | 'terminalLogs'>('editor');
  // ✅ Settings Center state
  const [showSettingsCenter, setShowSettingsCenter] = useState(false);

  // Add debugging for main content tab changes
  const handleMainContentTabChange = (tab: 'editor' | 'kanban' | 'agentSystem' | 'terminalLogs') => {
    console.log(`Main content tab changing from ${mainContentActiveTab} to ${tab}`);
    setMainContentActiveTab(tab);
  };

  // Wrapper for Radix UI onValueChange that expects string
  const handleMainContentTabChangeFromString = (value: string) => {
    if (value === 'editor' || value === 'kanban' || value === 'agentSystem' || value === 'terminalLogs') {
      handleMainContentTabChange(value);
    }
  };
  // Helper for Kanban's internal agent panel (keep this separate)
  const [showKanbanAgentPanel, setShowKanbanAgentPanel] = useState(false);

  // ✅ Horizontal Terminal Panel state
  const [showHorizontalTerminal, setShowHorizontalTerminal] = useState(false);
  // Add missing agent system state
  const [showAgentSystem, setShowAgentSystem] = useState(false);

  // ✅ Initialize alert notifications for global coverage
  useAlertNotifications();

  // ✅ Agent Manager for Settings Center
  const [agentManager] = useState(() => {
    try {
      const { CompleteAgentManager } = require('@/components/agents/agent-manager-complete');
      return new CompleteAgentManager();
    } catch (error) {
      console.warn('Failed to initialize agent manager:', error);
      return null;
    }
  });

  // Floating panels state (expanded to include editor, kanban, agentSystem, timeline, terminal)
  const [floatingPanels, setFloatingPanels] = useState<{
    editor: boolean
    explorer: boolean
    chat: boolean
    kanban: boolean // New
    agentSystem: boolean // New
    timeline: boolean // New
    terminal: boolean // New
  }>({
    editor: false,
    explorer: false,
    chat: false,
    kanban: false,
    agentSystem: false,
    timeline: false,
    terminal: false,
  })

  // Simulate a running process
  const handleRun = () => {
    setIsRunning(true)

    // Simulate process completion after 3 seconds
    setTimeout(() => {
      setIsRunning(false)
    }, 3000)
  }



  // Renamed from togglePanel to toggleSidebarPanel for clarity
  const toggleSidebarPanel = (panel: string) => {
    console.log(`toggleSidebarPanel called with: ${panel}, current sidebarActivePanel: ${sidebarActivePanel}`);
    if (sidebarActivePanel === panel) {
      setSidebarActivePanel(null)
    } else {
      setSidebarActivePanel(panel)
    }
  }

  const toggleRightPanel = (panel: string) => {
    console.log(`toggleRightPanel called with: ${panel}, current showRightPanel: ${showRightPanel}, sidebarActivePanel: ${sidebarActivePanel}`);
    if (showRightPanel && sidebarActivePanel === panel) {
      setShowRightPanel(false)
    } else {
      setShowRightPanel(true)
      setSidebarActivePanel(panel)
    }
  }



  // Chat panel resize handler
  const handleChatPanelResize = (newWidth: number) => {
    setChatPanelWidth(newWidth)
    // Persist to localStorage
    localStorage.setItem('chatPanelWidth', newWidth.toString())
  }

  // Timeline panel resize handler
  const handleTimelinePanelResize = (newWidth: number) => {
    setTimelinePanelWidth(newWidth)
    // Persist to localStorage
    localStorage.setItem('timelinePanelWidth', newWidth.toString())
  }

  // Load saved panel widths on mount
  useEffect(() => {
    const savedChatWidth = localStorage.getItem('chatPanelWidth')
    if (savedChatWidth) {
      const width = parseInt(savedChatWidth, 10)
      if (width >= 300 && width <= 600) {
        setChatPanelWidth(width)
      }
    }

    const savedTimelineWidth = localStorage.getItem('timelinePanelWidth')
    if (savedTimelineWidth) {
      const width = parseInt(savedTimelineWidth, 10)
      if (width >= 400 && width <= 800) {
        setTimelinePanelWidth(width)
      }
    }
  }, [])

  // Detach panel to floating window using Electron BrowserWindows
  const detachPanel = (panelName: keyof typeof floatingPanels) => {
    console.log(`detachPanel called with: ${panelName}`);

    // Use Electron API to open external windows
    if (typeof window !== 'undefined' && window.electronAPI) {
      if (panelName === "kanban") {
        window.electronAPI.openKanbanWindow("default-board-1");
        if (mainContentActiveTab === "kanban") handleMainContentTabChange("editor");
      } else if (panelName === "agentSystem") {
        window.electronAPI.openAgentSystemWindow();
        if (mainContentActiveTab === "agentSystem") handleMainContentTabChange("editor");
      } else if (panelName === "editor") {
        window.electronAPI.openEditorWindow(selectedFile?.path);
        if (mainContentActiveTab === "editor") handleMainContentTabChange("kanban");
      } else if (panelName === "explorer") {
        window.electronAPI.openExplorerWindow();
        setSidebarActivePanel(null);
      } else if (panelName === "chat") {
        window.electronAPI.openChatWindow();
        setShowRightPanel(false);
      } else if (panelName === "timeline") {
        window.electronAPI.openTimelineWindow();
        setSidebarActivePanel(null);
      } else if (panelName === "terminal") {
        window.electronAPI.openTerminalWindow();
        if (mainContentActiveTab === "terminal") handleMainContentTabChange("editor");
      }
    } else {
      console.warn("Electron API not available - floating panels only work in desktop app");
    }

    // Update floating panel state for UI consistency
    setFloatingPanels((prev) => ({ ...prev, [panelName]: true }))

    // Hide the panel in the main UI if it's currently docked there
    if (panelName === "chat") {
      setShowRightPanel(false)
    } else if (panelName === "explorer") {
      setSidebarActivePanel(null)
    } else if (panelName === "agentSystem") {
      if (mainContentActiveTab === "agentSystem") handleMainContentTabChange("editor");
    } else if (panelName === "editor") {
      if (mainContentActiveTab === "editor") handleMainContentTabChange("kanban");
    }
  }

  // Dock floating panel back to main window
  const dockPanel = (panelName: keyof typeof floatingPanels) => {
    setFloatingPanels((prev) => ({ ...prev, [panelName]: false }))

    // Show the panel in the main UI and make it active
    if (panelName === "chat") {
      setShowRightPanel(true)
      setSidebarActivePanel("chat")
    } else if (panelName === "explorer") {
      setSidebarActivePanel("explorer")
    } else if (panelName === "kanban") {
      handleMainContentTabChange("kanban");
    } else if (panelName === "agentSystem") {
      handleMainContentTabChange("agentSystem");
    } else if (panelName === "editor") {
      handleMainContentTabChange("editor");
    } else if (panelName === "timeline") {
      setSidebarActivePanel("timeline");
    } else if (panelName === "terminal") {
      handleMainContentTabChange("terminal");
    }
  }

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+B to toggle sidebar
      if ((e.ctrlKey || e.metaKey) && e.key === "b") {
        e.preventDefault()
        if (sidebarActivePanel && ["explorer", "search", "git", "debug", "extensions"].includes(sidebarActivePanel)) {
          setSidebarActivePanel(null)
        } else {
          setSidebarActivePanel("explorer")
        }
      }



      // Ctrl+S to save
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault()
        // Simulate save
        console.log("Saving file...")
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [sidebarActivePanel])

  return (
    <ClientSettingsWrapper>
      <div className="flex flex-col h-screen bg-background text-foreground">
      {/* Top navbar */}
      <div className="h-10 border-b border-editor-border bg-background flex items-center px-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <Zap className="h-4 w-4 text-editor-highlight mr-1.5" />
            <span className="font-medium">CodeFusion</span>
          </div>
          <div className="flex items-center space-x-1 text-sm text-muted-foreground">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <span className="px-2 py-1 hover:bg-accent hover:text-accent-foreground rounded cursor-pointer">
                  File
                </span>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => console.log("New File")}>
                  <Plus className="mr-2 h-4 w-4" />
                  <span>New File</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+N</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => console.log("Save File")}>
                  <Save className="mr-2 h-4 w-4" />
                  <span>Save</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+S</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => console.log("Exit")}>
                  <X className="mr-2 h-4 w-4" />
                  <span>Exit</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <span className="px-2 py-1 hover:bg-accent hover:text-accent-foreground rounded cursor-pointer">
                  Edit
                </span>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => console.log("Undo")}>
                  <span>Undo</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+Z</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => console.log("Redo")}>
                  <span>Redo</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+Y</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => console.log("Find")}>
                  <span>Find</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+F</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => console.log("Replace")}>
                  <span>Replace</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+H</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <span className="px-2 py-1 hover:bg-accent hover:text-accent-foreground rounded cursor-pointer">
                  View
                </span>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => toggleSidebarPanel("explorer")}>
                  <Files className="mr-2 h-4 w-4" />
                  <span>Explorer</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+B</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => toggleSidebarPanel("search")}>
                  <Search className="mr-2 h-4 w-4" />
                  <span>Search</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+Shift+F</span>
                </DropdownMenuItem>

                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setEditorLayout("single")}>
                  <span>Single Editor</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setEditorLayout("split-horizontal")}>
                  <span>Split Horizontally</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setEditorLayout("split-vertical")}>
                  <span>Split Vertically</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleMainContentTabChange("agentSystem")}> {/* Updated */}
                  <Robot className="mr-2 h-4 w-4" />
                  <span>Agent System</span> {/* Removed dynamic text here */}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleMainContentTabChange("kanban")}> {/* New */}
                  <Trello className="mr-2 h-4 w-4" />
                  <span>Kanban Board</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleMainContentTabChange("terminalLogs")}> {/* ✅ Task 100 */}
                  <Terminal className="mr-2 h-4 w-4" />
                  <span>Terminal Logs</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <span
              className="px-2 py-1 hover:bg-accent hover:text-accent-foreground rounded cursor-pointer"
              onClick={() => toggleSidebarPanel("explorer")}
            >
              Go
            </span>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <span className="px-2 py-1 hover:bg-accent hover:text-accent-foreground rounded cursor-pointer">
                  Run
                </span>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={handleRun}>
                  <Play className="mr-2 h-4 w-4" />
                  <span>Run</span>
                  <span className="ml-auto text-xs text-muted-foreground">F5</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => toggleSidebarPanel("debug")}>
                  <Bug className="mr-2 h-4 w-4" />
                  <span>Debug</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+F5</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>



            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <span className="px-2 py-1 hover:bg-accent hover:text-accent-foreground rounded cursor-pointer">
                  View
                </span>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleMainContentTabChange("kanban")}> {/* Updated */}
                  <Trello className="mr-2 h-4 w-4" />
                  <span>Kanban Board</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => toggleSidebarPanel("explorer")}>
                  <Files className="mr-2 h-4 w-4" />
                  <span>Explorer</span>
                  <span className="ml-auto text-xs text-muted-foreground">Ctrl+B</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <span className="px-2 py-1 hover:bg-accent hover:text-accent-foreground rounded cursor-pointer">
                  Help
                </span>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setShowKeyboardShortcutsDialog(true)}>
                  <span>Keyboard Shortcuts</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => console.log("Documentation")}>
                  <span>Documentation</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => console.log("About")}>
                  <span>About</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="ml-auto flex items-center space-x-2">
          <CommandPalette />
          <ThemeToggle />
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-muted-foreground hover:text-foreground"
            onClick={() => setShowSettingsDialog(true)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="flex flex-1 overflow-hidden">
        {/* Left activity bar */}
        <div className="w-12 border-r border-editor-border bg-editor-activity-bar-bg flex flex-col items-center py-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10 mb-2",
                    sidebarActivePanel === "explorer" // Updated
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => toggleSidebarPanel("explorer")}
                >
                  <Files className="activity-bar-icon" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">Explorer</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10 mb-2",
                    sidebarActivePanel === "search" // Updated
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => toggleSidebarPanel("search")}
                >
                  <Search className="activity-bar-icon" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">Search</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10 mb-2",
                    sidebarActivePanel === "git" // Updated
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => toggleSidebarPanel("git")}
                >
                  <GitBranch className="activity-bar-icon" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">Source Control</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10 mb-2",
                    sidebarActivePanel === "debug" // Updated
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => toggleSidebarPanel("debug")}
                >
                  <Bug className="activity-bar-icon" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">Run and Debug</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10 mb-2",
                    sidebarActivePanel === "extensions" // Updated
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => toggleSidebarPanel("extensions")}
                >
                  <Extension className="activity-bar-icon" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">Extensions</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10 mb-2",
                    mainContentActiveTab === "terminal" || floatingPanels.terminal
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => {
                    if (mainContentActiveTab === "terminal") {
                      // If terminal is already active, switch back to editor
                      handleMainContentTabChange("editor");
                    } else {
                      // Switch to terminal
                      handleMainContentTabChange("terminal");
                    }
                  }}
                >
                  <Terminal className="activity-bar-icon" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">Terminal</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Timeline Inspector Button - Dev only */}
          {process.env.NODE_ENV === 'development' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={cn(
                      "h-10 w-10 mb-2",
                      sidebarActivePanel === "timeline"
                        ? "bg-accent text-accent-foreground"
                        : "text-muted-foreground hover:text-foreground",
                    )}
                    onClick={() => toggleSidebarPanel("timeline")}
                  >
                    <Clock className="activity-bar-icon" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">Task Timeline Inspector</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10 mb-2",
                    mainContentActiveTab === "kanban" && !floatingPanels.kanban // Updated condition
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => {
                    handleMainContentTabChange("kanban");
                    if (floatingPanels.kanban) dockPanel("kanban"); // Dock if floating
                  }}
                >
                  <Trello className="activity-bar-icon" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">Kanban Board</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Agent System Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10 mb-2",
                    mainContentActiveTab === "agentSystem" && !floatingPanels.agentSystem // Updated condition
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => {
                    handleMainContentTabChange("agentSystem");
                    if (floatingPanels.agentSystem) dockPanel("agentSystem"); // Dock if floating
                  }}
                >
                  <Robot className="activity-bar-icon" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">Agent System</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <div className="mt-auto">
            {/* ✅ Settings Button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={cn(
                      "h-10 w-10 mb-2",
                      showSettingsCenter
                        ? "bg-accent text-accent-foreground"
                        : "text-muted-foreground hover:text-foreground",
                    )}
                    onClick={() => settingsManager && setShowSettingsCenter(true)}
                    disabled={!settingsManager}
                  >
                    <Settings className="activity-bar-icon" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">Settings</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={cn(
                      "h-10 w-10 transition-all duration-200",
                      showRightPanel && sidebarActivePanel === "chat" // Updated
                        ? "bg-editor-highlight text-editor-highlight-fg shadow-md ring-2 ring-editor-highlight/20"
                        : "text-muted-foreground hover:text-foreground hover:bg-accent/50",
                    )}
                    onClick={() => toggleRightPanel("chat")}
                  >
                    <MessageSquare className="activity-bar-icon" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">Agent Chat</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Left sidebar panel */}
        {sidebarActivePanel && ["explorer", "search", "git", "debug", "extensions", "timeline"].includes(sidebarActivePanel) && !floatingPanels.explorer && ( // Updated
          sidebarActivePanel === "timeline" ? (
            <ResizableLeftPanel
              width={timelinePanelWidth}
              onWidthChange={handleTimelinePanelResize}
              minWidth={400}
              maxWidth={800}
              className="animate-in slide-in-from-left"
            >
              <div className="flex items-center justify-between p-2 border-b border-editor-border">
                <h2 className="text-sm font-medium">Timeline</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-muted-foreground hover:text-foreground"
                  onClick={() => detachPanel("timeline")}
                  title="Open in new window"
                >
                  <ExternalLink className="sidebar-icon" />
                </Button>
              </div>
              <div className="h-full">
                <TaskTimelineInspector />
              </div>
            </ResizableLeftPanel>
          ) : (
            <div className="w-64 border-r border-editor-border bg-editor-sidebar-bg">
              <div className="flex items-center justify-between p-2 border-b border-editor-border">
                <h2 className="text-sm font-medium">{sidebarActivePanel.charAt(0).toUpperCase() + sidebarActivePanel.slice(1)}</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-muted-foreground hover:text-foreground"
                  onClick={() => detachPanel(sidebarActivePanel as keyof typeof floatingPanels)}
                  title="Open in new window"
                >
                  <ExternalLink className="sidebar-icon" />
                </Button>
              </div>
            {sidebarActivePanel === "explorer" && <FileSidebar onFileSelect={setSelectedFile} />}
            {sidebarActivePanel === "search" && (
              <div className="h-full flex flex-col">
                <div className="p-4 border-b border-editor-border">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 sidebar-icon text-muted-foreground" />
                    <Input placeholder="Search in files..." className="pl-8 h-9 bg-background border-input text-sm" />
                  </div>
                </div>
                <ScrollArea className="flex-1 p-4">
                  <p className="text-muted-foreground text-sm">Type to search in files</p>
                </ScrollArea>
              </div>
            )}
            {sidebarActivePanel === "git" && (
              <div className="h-full flex flex-col">
                <div className="p-4 border-b border-editor-border">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <GitBranch className="sidebar-icon mr-1" />
                    <span>main</span>
                  </div>
                </div>
                <ScrollArea className="flex-1 p-4">
                  <p className="text-muted-foreground text-sm">No changes detected</p>
                </ScrollArea>
              </div>
            )}
            {sidebarActivePanel === "debug" && (
              <div className="h-full flex flex-col">
                <div className="p-4 border-b border-editor-border">
                  <h2 className="text-lg font-medium">Run and Debug</h2>
                </div>
                <ScrollArea className="flex-1 p-4">
                  <p className="text-muted-foreground text-sm">No active debug session</p>
                  <Button className="mt-4 w-full" onClick={handleRun}>
                    Start Debugging
                  </Button>
                </ScrollArea>
              </div>
            )}
            {sidebarActivePanel === "extensions" && (
              <div className="h-full flex flex-col">
                <div className="p-4 border-b border-editor-border">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 sidebar-icon text-muted-foreground" />
                    <Input placeholder="Search extensions..." className="pl-8 h-9 bg-background border-input text-sm" />
                  </div>
                </div>
                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    <div className="p-3 border border-editor-border rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer">
                      <div className="font-medium">Python</div>
                      <div className="text-xs text-muted-foreground mt-1">IntelliSense, linting, debugging...</div>
                    </div>
                    <div className="p-3 border border-editor-border rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer">
                      <div className="font-medium">ESLint</div>
                      <div className="text-xs text-muted-foreground mt-1">Integrates ESLint into VS Code</div>
                    </div>
                    <div className="p-3 border border-editor-border rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer">
                      <div className="font-medium">Prettier</div>
                      <div className="text-xs text-muted-foreground mt-1">Code formatter using prettier</div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
            )}

            </div>
          )
        )}

        {/* Main content area wrapper */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Main content header */}
          <div className="h-9 border-b border-editor-border flex items-center justify-between px-3">
            <div className="flex items-center space-x-1 text-sm text-muted-foreground">
              {/* Tabs for main content: Editor, Kanban, Agent System */}
              <Tabs value={mainContentActiveTab} onValueChange={handleMainContentTabChangeFromString} className="h-full">
                <TabsList className="bg-transparent h-full">
                  <TabsTrigger value="editor" className="text-xs h-7 data-[state=active]:bg-background">
                    Editor
                  </TabsTrigger>
                  <TabsTrigger value="kanban" className="text-xs h-7 data-[state=active]:bg-background">
                    Kanban
                  </TabsTrigger>
                  <TabsTrigger value="agentSystem" className="text-xs h-7 data-[state=active]:bg-background">
                    Agent System
                  </TabsTrigger>
                  <TabsTrigger value="terminal" className="text-xs h-7 data-[state=active]:bg-background">
                    Terminal
                  </TabsTrigger>
                  <TabsTrigger value="terminalLogs" className="text-xs h-7 data-[state=active]:bg-background">
                    Terminal Logs
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            <div className="flex items-center">
              {/* Dynamic detach button based on active tab */}
              {mainContentActiveTab !== "kanban" && mainContentActiveTab !== "terminal" && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-muted-foreground hover:text-foreground"
                  onClick={() => detachPanel(mainContentActiveTab as keyof typeof floatingPanels)}
                  title="Open in new window"
                >
                  <ExternalLink className="sidebar-icon" />
                </Button>
              )}
            </div>
          </div>

          {/* ✅ Alert Banner for cost threshold alerts */}
          <div className="px-3 py-1">
            <AlertBanner />
          </div>

          {/* Main content area */}
          <div className="flex-1 overflow-hidden">
            {mainContentActiveTab === "agentSystem" ? (
              <div className="h-full">
                <SharedAgentStateProvider>
                  <CompleteAgentSystem />
                </SharedAgentStateProvider>
              </div>
            ) : mainContentActiveTab === "kanban" ? (
              <div className="h-full flex flex-col">
                <BoardProvider>
                  <SearchProvider>
                    <AgentBoardControllerProvider>
                      <KanbanHeader
                        showAgentPanel={showKanbanAgentPanel}
                        toggleAgentPanel={() => setShowKanbanAgentPanel(!showKanbanAgentPanel)}
                        onExitKanban={() => handleMainContentTabChange("editor")}
                        onDetach={() => detachPanel("kanban")}
                      />
                      <div className="flex flex-1 overflow-hidden">
                        <div className="flex-1 overflow-auto">
                          <KanbanBoard boardId="main" />
                        </div>
                        {showKanbanAgentPanel && (
                          <div className="w-80 border-l border-border bg-background">
                            <AgentActivityPanel />
                          </div>
                        )}
                      </div>
                    </AgentBoardControllerProvider>
                  </SearchProvider>
                </BoardProvider>
              </div>
            ) : mainContentActiveTab === "terminal" ? (
              <ResizableBottomTerminal
                onDetach={() => detachPanel("terminal")}
              >
                <div className="h-full">
                  <CodeEditor file={selectedFile} />
                </div>
              </ResizableBottomTerminal>
            ) : mainContentActiveTab === "terminalLogs" ? (
              <div className="h-full">
                <TerminalLogsPanel />
              </div>
            ) : (
              <EditorStateProvider>
                {editorLayout === "single" && (
                  <div className="h-full">
                    <CodeEditor file={selectedFile} />
                  </div>
                )}
                {editorLayout === "split-horizontal" && (
                  <div className="flex h-full">
                    <div className="w-1/2 border-r border-editor-border">
                      <CodeEditor file={selectedFile} />
                    </div>
                    <div className="w-1/2">
                      <CodeEditor file={null} />
                    </div>
                  </div>
                )}
                {editorLayout === "split-vertical" && (
                  <div className="flex flex-col h-full">
                    <div className="h-1/2 border-b border-editor-border">
                      <CodeEditor file={selectedFile} />
                    </div>
                    <div className="h-1/2">
                      <CodeEditor file={null} />
                    </div>
                  </div>
                )}
              </EditorStateProvider>
            )}
          </div>


        </div>

        {/* Right panel (Agent Chat) */}
        {showRightPanel && sidebarActivePanel === "chat" && (
          <ResizablePanel
            width={chatPanelWidth}
            onWidthChange={handleChatPanelResize}
            minWidth={300}
            maxWidth={600}
            className="animate-in slide-in-from-right"
          >
            <SharedAgentStateProvider>
              <AgentChatPanel
                onClose={() => setShowRightPanel(false)}
                onDetach={() => detachPanel("chat")}
              />
            </SharedAgentStateProvider>
          </ResizablePanel>
        )}
        </div>


      </div>

      {/* Bottom status bar */}
      <div className="h-6 border-t border-editor-border bg-editor-statusbar-bg flex items-center px-4 text-xs text-editor-statusbar-fg">
        <div className="ml-4 flex items-center space-x-4">
          <div className="flex items-center cursor-pointer" onClick={() => toggleSidebarPanel("explorer")}>
            <PanelLeft className="h-3 w-3 mr-1" />
            <span>Explorer</span>
          </div>
          <div
            className="flex items-center cursor-pointer"
            onClick={() => {
              if (editorLayout === "single") {
                setEditorLayout("split-horizontal")
              } else {
                setEditorLayout("single")
              }
            }}
          >
            <Split className="h-3 w-3 mr-1" />
            <span>Split Editor</span>
          </div>
          <div className="flex items-center cursor-pointer" onClick={handleRun}>
            <Play className="h-3 w-3 mr-1" />
            <span>Run</span>
          </div>
          <div className="flex items-center cursor-pointer" onClick={() => handleMainContentTabChange("agentSystem")}>
            <Robot className="h-3 w-3 mr-1" />
            <span>Agent System</span>
          </div>
          <div className="flex items-center cursor-pointer" onClick={() => handleMainContentTabChange("terminalLogs")}>
            <Terminal className="h-3 w-3 mr-1" />
            <span>Terminal Logs</span>
          </div>
          <div
            className="flex items-center cursor-pointer"
            onClick={() => {
              if (mainContentActiveTab === "terminal") {
                handleMainContentTabChange("editor");
              } else {
                handleMainContentTabChange("terminal");
              }
            }}
          >
            <Terminal className="h-3 w-3 mr-1" />
            <span>Terminal</span>
          </div>
        </div>
        <div className="ml-auto">
          <span>UTF-8</span>
          <span className="mx-2">•</span>
          <span>JavaScript</span>
        </div>
      </div>

      {/* Settings Dialog */}
      <Dialog open={showSettingsDialog} onOpenChange={setShowSettingsDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Settings</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="theme" className="text-right">
                Theme
              </label>
              <div className="col-span-3">
                <div className="flex items-center space-x-4">
                  <ThemeToggle />
                  <span className="text-sm text-muted-foreground">Choose between light, dark, or system theme</span>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="font-size" className="text-right">
                Font Size
              </label>
              <div className="col-span-3">
                <Input id="font-size" defaultValue="14px" />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="tab-size" className="text-right">
                Tab Size
              </label>
              <div className="col-span-3">
                <Input id="tab-size" defaultValue="2" />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="word-wrap" className="text-right">
                Word Wrap
              </label>
              <div className="col-span-3">
                <select id="word-wrap" className="w-full p-2 rounded-md border border-input bg-background">
                  <option value="off">Off</option>
                  <option value="on">On</option>
                  <option value="wordWrapColumn">Word Wrap Column</option>
                  <option value="bounded">Bounded</option>
                </select>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Keyboard Shortcuts Dialog */}
      <Dialog open={showKeyboardShortcutsDialog} onOpenChange={setShowKeyboardShortcutsDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Keyboard Shortcuts</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-[400px]">
            <div className="space-y-4 p-2">
              <div>
                <h3 className="font-medium mb-2">Editor</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Save File</span>
                    <span className="text-muted-foreground">Ctrl+S</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Find</span>
                    <span className="text-muted-foreground">Ctrl+F</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Replace</span>
                    <span className="text-muted-foreground">Ctrl+H</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="font-medium mb-2">View</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Toggle Sidebar</span>
                    <span className="text-muted-foreground">Ctrl+B</span>
                  </div>

                  <div className="flex justify-between">
                    <span>Command Palette</span>
                    <span className="text-muted-foreground">Ctrl+P</span>
                  </div>
                  {process.env.NODE_ENV === 'development' && (
                    <div className="flex justify-between">
                      <span>Concurrency Overlay</span>
                      <span className="text-muted-foreground">Ctrl+Shift+O</span>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <h3 className="font-medium mb-2">Run</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Run</span>
                    <span className="text-muted-foreground">F5</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Debug</span>
                    <span className="text-muted-foreground">Ctrl+F5</span>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* ✅ Settings Center Dialog */}
      <Dialog open={showSettingsCenter} onOpenChange={setShowSettingsCenter}>
        <RebuiltDialogContent className="max-w-7xl w-[90vw] max-h-[90vh] bg-background rounded-lg shadow-lg p-0">
          <DialogHeader>
            <DialogTitle className="sr-only">Settings</DialogTitle>
          </DialogHeader>
          {settingsManager ? (
            <SettingsCenter
              settingsManager={settingsManager}
              agentManager={agentManager}
              onClose={() => setShowSettingsCenter(false)}
            />
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading settings...</p>
              </div>
            </div>
          )}
        </RebuiltDialogContent>
      </Dialog>

      {/*
        Floating Panels are now handled by native Electron BrowserWindows
        - Kanban: Opens via window.electronAPI.openKanbanWindow(boardId)
        - Other panels (Explorer, Chat, Agent System, Editor) would need additional Electron window implementations

        The detachPanel() function triggers the appropriate Electron API calls
        instead of rendering React components in floating divs.
      */}

      {/* ✅ Concurrency Inspector Overlay */}
      <ConcurrencyOverlay />

      {/* ✅ Toast Notifications */}
      <Toaster />

      {/* ✅ Project Status Bar */}
      <ProjectStatusBar />
      </div>
    </ClientSettingsWrapper>
  )
}

export default function Home() {
  return <ClientOnlyHome />;
}