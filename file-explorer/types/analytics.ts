// types/analytics.ts

export interface AgentAnalyticsMetrics {
  // Core metrics
  totalTasks: number
  completedTasks: number
  failedTasks: number
  averageCompletionTime: number
  totalTokensUsed: number
  totalCost: number
  
  // Time-based metrics
  tasksToday: number
  tasksThisWeek: number
  tasksThisMonth: number
  costToday: number
  costThisWeek: number
  costThisMonth: number
  
  // Agent performance
  mostActiveAgent: string
  mostEfficientAgent: string
  agentSuccessRates: Record<string, number>
  agentAverageTime: Record<string, number>
  agentTokenUsage: Record<string, number>
  agentCostBreakdown: Record<string, number>
  
  // Model usage
  modelUsageBreakdown: Record<string, {
    calls: number
    tokens: number
    cost: number
    successRate: number
  }>
  
  // Trends
  dailyTaskTrend: Array<{ date: string; tasks: number; cost: number }>
  weeklyTokenTrend: Array<{ week: string; tokens: number }>
  monthlyUsageTrend: Array<{ month: string; tasks: number; cost: number; tokens: number }>
  
  // Performance insights
  peakUsageHours: Array<{ hour: number; tasks: number }>
  taskTypeDistribution: Record<string, number>
  errorRateByAgent: Record<string, number>
  
  // Real-time metrics
  currentActiveAgents: number
  tasksInProgress: number
  queuedTasks: number
  systemLoad: number
  
  // Quality metrics
  averageTaskComplexity: number
  codeQualityScore: number
  userSatisfactionScore: number
  
  lastUpdated: number
}

export interface ChartDataPoint {
  name: string
  value: number
  label?: string
  color?: string
  percentage?: number
}

export interface TimeSeriesDataPoint {
  timestamp: number
  date: string
  value: number
  label?: string
  category?: string
}

export interface AnalyticsFilter {
  dateRange: {
    start: Date
    end: Date
  }
  agentTypes?: string[]
  taskTypes?: string[]
  status?: ('success' | 'error' | 'cancelled')[]
  models?: string[]
  providers?: string[]
}

export interface AnalyticsChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter'
  title: string
  description?: string
  dataKey: string
  xAxisKey?: string
  yAxisKey?: string
  colors?: string[]
  showLegend?: boolean
  showTooltip?: boolean
  height?: number
}

export interface AnalyticsDashboardCard {
  id: string
  title: string
  description?: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease' | 'neutral'
    period: string
  }
  icon?: string
  color?: string
  trend?: TimeSeriesDataPoint[]
  format?: 'number' | 'currency' | 'percentage' | 'duration' | 'bytes'
}

export interface AnalyticsReport {
  id: string
  title: string
  description: string
  generatedAt: number
  period: {
    start: Date
    end: Date
  }
  metrics: AgentAnalyticsMetrics
  insights: AnalyticsInsight[]
  recommendations: AnalyticsRecommendation[]
}

export interface AnalyticsInsight {
  id: string
  type: 'performance' | 'cost' | 'usage' | 'quality' | 'trend'
  title: string
  description: string
  severity: 'info' | 'warning' | 'critical' | 'positive'
  impact: 'low' | 'medium' | 'high'
  data?: Record<string, any>
  actionable: boolean
}

export interface AnalyticsRecommendation {
  id: string
  title: string
  description: string
  category: 'optimization' | 'cost_reduction' | 'performance' | 'quality'
  priority: 'low' | 'medium' | 'high' | 'critical'
  estimatedImpact: string
  implementation: {
    difficulty: 'easy' | 'medium' | 'hard'
    timeRequired: string
    steps: string[]
  }
  metrics?: {
    potentialSavings?: number
    performanceGain?: number
    qualityImprovement?: number
  }
}

export interface AnalyticsExport {
  format: 'json' | 'csv' | 'pdf' | 'excel'
  data: AgentAnalyticsMetrics
  charts?: {
    type: string
    data: any[]
    config: AnalyticsChartConfig
  }[]
  period: {
    start: Date
    end: Date
  }
  generatedAt: number
}

// Chart color schemes
export const CHART_COLORS = {
  primary: ['#3b82f6', '#1d4ed8', '#1e40af', '#1e3a8a'],
  success: ['#10b981', '#059669', '#047857', '#065f46'],
  warning: ['#f59e0b', '#d97706', '#b45309', '#92400e'],
  error: ['#ef4444', '#dc2626', '#b91c1c', '#991b1b'],
  neutral: ['#6b7280', '#4b5563', '#374151', '#1f2937'],
  rainbow: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316']
} as const

// Metric formatting utilities
export const METRIC_FORMATS = {
  number: (value: number) => value.toLocaleString(),
  currency: (value: number) => `$${value.toFixed(2)}`,
  percentage: (value: number) => `${value.toFixed(1)}%`,
  duration: (value: number) => {
    if (value < 1000) return `${value}ms`
    if (value < 60000) return `${(value / 1000).toFixed(1)}s`
    if (value < 3600000) return `${(value / 60000).toFixed(1)}m`
    return `${(value / 3600000).toFixed(1)}h`
  },
  bytes: (value: number) => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = value
    let unitIndex = 0
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    return `${size.toFixed(1)} ${units[unitIndex]}`
  }
} as const

// Time period utilities
export const TIME_PERIODS = {
  today: () => {
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    return { start, end: now }
  },
  yesterday: () => {
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
    const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    return { start, end }
  },
  thisWeek: () => {
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())
    return { start, end: now }
  },
  thisMonth: () => {
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth(), 1)
    return { start, end: now }
  },
  last30Days: () => {
    const now = new Date()
    const start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    return { start, end: now }
  },
  last7Days: () => {
    const now = new Date()
    const start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    return { start, end: now }
  }
} as const
