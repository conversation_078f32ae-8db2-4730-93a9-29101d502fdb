// types/chat.ts

export type MessageRole = "user" | "agent" | "system"
export type MessageStatus = "sending" | "sent" | "processing" | "completed" | "error"
export type SystemMessageSource = "micromanager" | "validator" | "executor" | "system" | "file_system" | "terminal"

export interface AgentChatMessage {
  id: string
  content: string
  role: MessageRole
  timestamp: Date
  status: MessageStatus
  agentType?: string
  agentId?: string
  taskId?: string
  tokensUsed?: number
  cost?: number
  stream?: boolean
  isStreaming?: boolean
  linkedToMessageId?: string // For system messages that reference other messages
  source?: SystemMessageSource // For system messages
  metadata?: Record<string, any>
}

export interface AgentChatHistory {
  workspaceId: string
  workspacePath: string
  messages: AgentChatMessage[]
  createdAt: string
  updatedAt: string
  messageCount: number
  totalTokensUsed: number
  totalCost: number
}

export interface SystemFeedbackMessage {
  id: string
  content: string
  source: SystemMessageSource
  linkedToMessageId?: string
  severity: "info" | "warning" | "error" | "success"
  timestamp: Date
  metadata?: {
    taskId?: string
    agentId?: string
    filePath?: string
    operation?: string
    details?: Record<string, any>
  }
}

export interface ChatHistoryStats {
  totalMessages: number
  totalTokensUsed: number
  totalCost: number
  averageResponseTime: number
  mostActiveAgent: string
  sessionCount: number
  lastActivity: Date
}

export interface ChatPersistenceConfig {
  maxMessages: number
  maxHistoryDays: number
  autoCleanup: boolean
  enableSystemMessages: boolean
  enableThreading: boolean
  compressionEnabled: boolean
}
