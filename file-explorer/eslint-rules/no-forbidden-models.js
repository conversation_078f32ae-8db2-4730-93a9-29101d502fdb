/**
 * ESLint rule to prevent usage of forbidden model IDs and test logic
 * Enforces User Guidelines by blocking test/mock/placeholder content
 */

module.exports = {
  meta: {
    type: 'problem',
    docs: {
      description: 'Disallow usage of test/mock/placeholder models or logic',
      category: 'Best Practices',
      recommended: true,
    },
    messages: {
      forbiddenModel: 'Forbidden model ID or keyword "{{ value }}" detected. Use production-safe model IDs only.',
      forbiddenTestLogic: 'Test/mock/placeholder logic "{{ value }}" detected. Use real implementations only.',
      forbiddenPlaceholder: 'Placeholder content "{{ value }}" detected. Use actual data only.',
    },
    schema: [], // no options
    fixable: null, // not auto-fixable
  },

  create(context) {
    // Forbidden keywords that should never appear in production code
    const forbiddenKeywords = [
      'test',
      'mock',
      'placeholder',
      'demo',
      'fake',
      'dummy',
      'scaffold',
      'sample',
      'example',
      'temp',
      'temporary',
      'debug',
      'dev-only',
      'todo-remove',
      'fixme',
      'hack',
      'workaround'
    ];

    // Specific forbidden model patterns
    const forbiddenModelPatterns = [
      /test[-_]?model/i,
      /mock[-_]?model/i,
      /placeholder[-_]?model/i,
      /demo[-_]?model/i,
      /fake[-_]?model/i,
      /dummy[-_]?model/i,
      /sample[-_]?model/i,
      /example[-_]?model/i,
      /gpt[-_]?3\.5[-_]?turbo[-_]?test/i,
      /claude[-_]?test/i,
      /anthropic[-_]?test/i,
      /openai[-_]?test/i,
    ];

    // Patterns to exclude from checking (legitimate uses)
    const excludePatterns = [
      // CSS classes and Tailwind utilities
      /placeholder[-:]text/i,
      /placeholder[-:]muted/i,
      /placeholder[-:]foreground/i,
      /placeholder[-:]opacity/i,
      /placeholder[-:]color/i,

      // File paths and extensions
      /\.test\./i,
      /__tests__/i,
      /test[-_]?dir/i,

      // Documentation and comments
      /example[-_]?usage/i,
      /code[-_]?example/i,
      /usage[-_]?example/i,

      // Technical terms
      /temperature/i,
      /attempt/i,
      /retry[-_]?attempt/i,
      /latest/i,
      /update[-_]?stat/i,
      /execute[-_]?step/i,
      /get[-_]?system[-_]?prompt/i,

      // Legitimate debugging
      /debug[-_]?mode/i,
      /supports[-_]?debug/i,
      /advanced[-_]?debug/i,
      /simple[-_]?debug/i,
    ];

    // Check if a string contains forbidden content
    function containsForbiddenContent(value) {
      if (typeof value !== 'string') return false;

      // Skip if it matches exclude patterns
      for (const excludePattern of excludePatterns) {
        if (excludePattern.test(value)) {
          return false;
        }
      }

      const lowerValue = value.toLowerCase();

      // Check for forbidden keywords (but only as whole words or in specific contexts)
      for (const keyword of forbiddenKeywords) {
        // Create word boundary regex for more precise matching
        const wordBoundaryRegex = new RegExp(`\\b${keyword}\\b`, 'i');
        if (wordBoundaryRegex.test(value)) {
          // Additional context checks for common false positives
          if (keyword === 'test' && /\b(test[-_]?dir|test[-_]?file|test[-_]?coverage|testing[-_]?framework)\b/i.test(value)) {
            continue; // Skip legitimate test-related terms
          }
          if (keyword === 'mock' && /\bmock[-_]?(data|response|api)\b/i.test(value)) {
            return { type: 'keyword', keyword }; // These are violations
          }
          if (keyword === 'placeholder' && /\bplaceholder[-:]/.test(value)) {
            continue; // Skip CSS placeholder utilities
          }
          return { type: 'keyword', keyword };
        }
      }

      // Check for forbidden model patterns
      for (const pattern of forbiddenModelPatterns) {
        if (pattern.test(value)) {
          return { type: 'pattern', pattern: pattern.source };
        }
      }

      return false;
    }

    // Get appropriate message based on violation type
    function getMessageId(violation, value) {
      if (violation.keyword) {
        if (['test', 'mock', 'fake', 'dummy'].includes(violation.keyword)) {
          return 'forbiddenTestLogic';
        }
        if (['placeholder', 'demo', 'sample', 'example'].includes(violation.keyword)) {
          return 'forbiddenPlaceholder';
        }
      }
      return 'forbiddenModel';
    }

    return {
      // Check string literals
      Literal(node) {
        if (typeof node.value === 'string') {
          const violation = containsForbiddenContent(node.value);
          if (violation) {
            const messageId = getMessageId(violation, node.value);
            context.report({
              node,
              messageId,
              data: { value: node.value },
            });
          }
        }
      },

      // Check template literals
      TemplateLiteral(node) {
        for (const quasi of node.quasis) {
          if (quasi.value && quasi.value.raw) {
            const violation = containsForbiddenContent(quasi.value.raw);
            if (violation) {
              const messageId = getMessageId(violation, quasi.value.raw);
              context.report({
                node: quasi,
                messageId,
                data: { value: quasi.value.raw },
              });
            }
          }
        }
      },

      // Check property names
      Property(node) {
        if (node.key && node.key.type === 'Literal' && typeof node.key.value === 'string') {
          const violation = containsForbiddenContent(node.key.value);
          if (violation) {
            const messageId = getMessageId(violation, node.key.value);
            context.report({
              node: node.key,
              messageId,
              data: { value: node.key.value },
            });
          }
        }
        if (node.key && node.key.type === 'Identifier') {
          const violation = containsForbiddenContent(node.key.name);
          if (violation) {
            const messageId = getMessageId(violation, node.key.name);
            context.report({
              node: node.key,
              messageId,
              data: { value: node.key.name },
            });
          }
        }
      },

      // Check variable names
      VariableDeclarator(node) {
        if (node.id && node.id.type === 'Identifier') {
          const violation = containsForbiddenContent(node.id.name);
          if (violation) {
            const messageId = getMessageId(violation, node.id.name);
            context.report({
              node: node.id,
              messageId,
              data: { value: node.id.name },
            });
          }
        }
      },

      // Check function names
      FunctionDeclaration(node) {
        if (node.id && node.id.name) {
          const violation = containsForbiddenContent(node.id.name);
          if (violation) {
            const messageId = getMessageId(violation, node.id.name);
            context.report({
              node: node.id,
              messageId,
              data: { value: node.id.name },
            });
          }
        }
      },

      // Check class names
      ClassDeclaration(node) {
        if (node.id && node.id.name) {
          const violation = containsForbiddenContent(node.id.name);
          if (violation) {
            const messageId = getMessageId(violation, node.id.name);
            context.report({
              node: node.id,
              messageId,
              data: { value: node.id.name },
            });
          }
        }
      },

      // Check method names
      MethodDefinition(node) {
        if (node.key && node.key.type === 'Identifier') {
          const violation = containsForbiddenContent(node.key.name);
          if (violation) {
            const messageId = getMessageId(violation, node.key.name);
            context.report({
              node: node.key,
              messageId,
              data: { value: node.key.name },
            });
          }
        }
      },

      // Check import/export names
      ImportSpecifier(node) {
        if (node.imported && node.imported.name) {
          const violation = containsForbiddenContent(node.imported.name);
          if (violation) {
            const messageId = getMessageId(violation, node.imported.name);
            context.report({
              node: node.imported,
              messageId,
              data: { value: node.imported.name },
            });
          }
        }
      },

      // Check comments for forbidden content
      Program(node) {
        const sourceCode = context.getSourceCode();
        const comments = sourceCode.getAllComments();

        for (const comment of comments) {
          const violation = containsForbiddenContent(comment.value);
          if (violation) {
            const messageId = getMessageId(violation, comment.value.trim());
            context.report({
              node: comment,
              messageId,
              data: { value: comment.value.trim() },
            });
          }
        }
      },
    };
  },
};
