#!/usr/bin/env node

/**
 * Standalone script to check for forbidden models and test logic
 * Enforces User Guidelines by scanning all source files
 */

const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Import our custom rule logic
const noForbiddenModelsRule = require('../eslint-rules/no-forbidden-models');

// Forbidden keywords that should never appear in production code
const forbiddenKeywords = [
  'test',
  'mock',
  'placeholder',
  'demo',
  'fake',
  'dummy',
  'scaffold',
  'sample',
  'example',
  'temp',
  'temporary',
  'debug',
  'dev-only',
  'todo-remove',
  'fixme',
  'hack',
  'workaround'
];

// Specific forbidden model patterns
const forbiddenModelPatterns = [
  /test[-_]?model/i,
  /mock[-_]?model/i,
  /placeholder[-_]?model/i,
  /demo[-_]?model/i,
  /fake[-_]?model/i,
  /dummy[-_]?model/i,
  /sample[-_]?model/i,
  /example[-_]?model/i,
  /gpt[-_]?3\.5[-_]?turbo[-_]?test/i,
  /claude[-_]?test/i,
  /anthropic[-_]?test/i,
  /openai[-_]?test/i,
];

// Files to scan
const includePatterns = [
  'components/**/*.{ts,tsx,js,jsx}',
  'app/**/*.{ts,tsx,js,jsx}',
  'lib/**/*.{ts,tsx,js,jsx}',
  'hooks/**/*.{ts,tsx,js,jsx}',
  'electron/**/*.{ts,js}',
  'scripts/**/*.{ts,js}',
  '*.{ts,tsx,js,jsx}',
];

// Files to exclude
const excludeFilePatterns = [
  'node_modules/**',
  '.next/**',
  'out/**',
  'dist-electron/**',
  '*.min.js',
  'public/**',
  '.git/**',
  'eslint-rules/**', // Allow in ESLint rules themselves
  'scripts/checkForbiddenContent.js', // Allow in the checker itself
  'scripts/validateModelMetadata.js', // Allow in validation scripts
  'scripts/validateModelMetadata.ts', // Allow in validation scripts
  'snapshots/**', // Allow in exported snapshots
];

// Patterns to exclude from checking (legitimate uses)
const excludeContentPatterns = [
  // CSS classes and Tailwind utilities
  /placeholder[-:]text/i,
  /placeholder[-:]muted/i,
  /placeholder[-:]foreground/i,
  /placeholder[-:]opacity/i,
  /placeholder[-:]color/i,

  // File paths and extensions
  /\.test\./i,
  /__tests__/i,
  /test[-_]?dir/i,

  // Documentation and comments
  /example[-_]?usage/i,
  /code[-_]?example/i,
  /usage[-_]?example/i,

  // Technical terms
  /temperature/i,
  /attempt/i,
  /retry[-_]?attempt/i,
  /latest/i,
  /update[-_]?stat/i,
  /execute[-_]?step/i,
  /get[-_]?system[-_]?prompt/i,

  // Legitimate debugging
  /debug[-_]?mode/i,
  /supports[-_]?debug/i,
  /advanced[-_]?debug/i,
  /simple[-_]?debug/i,
];

// Check if a string contains forbidden content
function containsForbiddenContent(value, filePath, lineNumber) {
  if (typeof value !== 'string') return null;

  // Skip if it matches exclude patterns
  for (const excludePattern of excludeContentPatterns) {
    if (excludePattern.test(value)) {
      return null;
    }
  }

  const violations = [];

  // Check for forbidden keywords (but only as whole words or in specific contexts)
  for (const keyword of forbiddenKeywords) {
    // Create word boundary regex for more precise matching
    const wordBoundaryRegex = new RegExp(`\\b${keyword}\\b`, 'i');
    if (wordBoundaryRegex.test(value)) {
      // Additional context checks for common false positives
      if (keyword === 'test' && /\b(test[-_]?dir|test[-_]?file|test[-_]?coverage|testing[-_]?framework)\b/i.test(value)) {
        continue; // Skip legitimate test-related terms
      }
      if (keyword === 'mock' && /\bmock[-_]?(data|response|api)\b/i.test(value)) {
        // These are violations
        violations.push({
          type: 'keyword',
          keyword,
          value,
          filePath,
          lineNumber,
          severity: getKeywordSeverity(keyword),
        });
        continue;
      }
      if (keyword === 'placeholder' && /\bplaceholder[-:]/.test(value)) {
        continue; // Skip CSS placeholder utilities
      }
      violations.push({
        type: 'keyword',
        keyword,
        value,
        filePath,
        lineNumber,
        severity: getKeywordSeverity(keyword),
      });
    }
  }

  // Check for forbidden model patterns
  for (const pattern of forbiddenModelPatterns) {
    if (pattern.test(value)) {
      violations.push({
        type: 'pattern',
        pattern: pattern.source,
        value,
        filePath,
        lineNumber,
        severity: 'error',
      });
    }
  }

  return violations.length > 0 ? violations : null;
}

// Get severity level for keywords
function getKeywordSeverity(keyword) {
  const errorKeywords = ['test', 'mock', 'fake', 'dummy', 'placeholder'];
  const warningKeywords = ['demo', 'sample', 'example', 'temp', 'temporary'];

  if (errorKeywords.includes(keyword)) return 'error';
  if (warningKeywords.includes(keyword)) return 'warning';
  return 'info';
}

// Scan a single file for violations
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const violations = [];

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // Check for string literals (simple regex approach)
      const stringMatches = line.match(/(["'`])((?:(?!\1)[^\\]|\\.)*)(\1)/g);
      if (stringMatches) {
        stringMatches.forEach(match => {
          const cleanValue = match.slice(1, -1); // Remove quotes
          const lineViolations = containsForbiddenContent(cleanValue, filePath, lineNumber);
          if (lineViolations) {
            violations.push(...lineViolations);
          }
        });
      }

      // Check for variable names, function names, etc.
      const identifierMatches = line.match(/\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g);
      if (identifierMatches) {
        identifierMatches.forEach(identifier => {
          const lineViolations = containsForbiddenContent(identifier, filePath, lineNumber);
          if (lineViolations) {
            violations.push(...lineViolations);
          }
        });
      }

      // Check comments
      const commentMatch = line.match(/\/\/\s*(.+)$|\/\*\s*(.+?)\s*\*\//);
      if (commentMatch) {
        const commentText = commentMatch[1] || commentMatch[2];
        if (commentText) {
          const lineViolations = containsForbiddenContent(commentText, filePath, lineNumber);
          if (lineViolations) {
            violations.push(...lineViolations);
          }
        }
      }
    });

    return violations;
  } catch (error) {
    console.error(`Error scanning file ${filePath}:`, error.message);
    return [];
  }
}

// Main execution
async function main() {
  console.log('🔍 Scanning for forbidden models and test logic...\n');

  let allViolations = [];
  let filesScanned = 0;

  // Get all files to scan
  for (const pattern of includePatterns) {
    try {
      const files = await glob(pattern, {
        ignore: excludeFilePatterns,
        cwd: process.cwd(),
      });

      for (const file of files) {
        const violations = scanFile(file);
        if (violations.length > 0) {
          allViolations.push(...violations);
        }
        filesScanned++;
      }
    } catch (error) {
      console.error(`Error processing pattern ${pattern}:`, error.message);
    }
  }

  // Report results
  console.log(`📊 Scanned ${filesScanned} files\n`);

  if (allViolations.length === 0) {
    console.log('✅ No forbidden content found!');
    console.log('🎉 All files comply with User Guidelines');
    process.exit(0);
  }

  // Group violations by severity
  const errors = allViolations.filter(v => v.severity === 'error');
  const warnings = allViolations.filter(v => v.severity === 'warning');
  const infos = allViolations.filter(v => v.severity === 'info');

  // Report violations
  if (errors.length > 0) {
    console.log(`❌ ${errors.length} ERROR(S) found:`);
    errors.forEach(violation => {
      console.log(`  ${violation.filePath}:${violation.lineNumber} - "${violation.value}"`);
      console.log(`    Reason: Contains forbidden ${violation.type} "${violation.keyword || violation.pattern}"`);
    });
    console.log();
  }

  if (warnings.length > 0) {
    console.log(`⚠️  ${warnings.length} WARNING(S) found:`);
    warnings.forEach(violation => {
      console.log(`  ${violation.filePath}:${violation.lineNumber} - "${violation.value}"`);
      console.log(`    Reason: Contains suspicious ${violation.type} "${violation.keyword || violation.pattern}"`);
    });
    console.log();
  }

  if (infos.length > 0) {
    console.log(`ℹ️  ${infos.length} INFO(S) found:`);
    infos.forEach(violation => {
      console.log(`  ${violation.filePath}:${violation.lineNumber} - "${violation.value}"`);
      console.log(`    Reason: Contains questionable ${violation.type} "${violation.keyword || violation.pattern}"`);
    });
    console.log();
  }

  console.log('🔧 Please fix all violations before committing.');
  console.log('💡 Use production-safe model IDs and real implementations only.');

  // Exit with error code if there are errors
  process.exit(errors.length > 0 ? 1 : 0);
}

// Handle CLI execution
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = {
  containsForbiddenContent,
  scanFile,
  forbiddenKeywords,
  forbiddenModelPatterns,
};
