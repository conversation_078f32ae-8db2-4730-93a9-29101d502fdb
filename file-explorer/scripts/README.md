# Scripts Directory

This directory contains automation scripts for the project.

## 📋 Available Scripts

### `validateModelMetadata.js` / `validateModelMetadata.ts`
**Purpose**: Validates all model metadata files for compliance with User Guidelines

**Usage**:
```bash
# JavaScript version (recommended for CI)
npm run validate:models

# TypeScript version (requires ts-node)
npm run validate:models:ts
```

**What it checks**:
- ❌ Forbidden keywords (test, mock, placeholder, etc.)
- ❌ Forbidden model ID patterns
- ❌ Placeholder pricing values (0.0, 999, etc.)
- ⚠️ Missing required fields (id, label, description)
- ⚠️ Proper TypeScript structure and exports

**Exit codes**:
- `0` - Validation passed (warnings allowed)
- `1` - Validation failed (violations found)

**Example output**:
```
🔍 Starting Model Metadata Validation...

📁 Found 10 metadata files to validate:
   - components/agents/anthropic-models.ts
   - components/agents/openai-models.ts
   ...

📊 Validation Report
==================================================
📁 Files scanned: 10
❌ Total violations: 0
⚠️  Total warnings: 3

✅ VALIDATION PASSED
```

## 🔧 Integration

### Package.json Scripts
The validation is integrated into the build process:
- `precommit` - Runs before git commits
- `prebuild` - Runs before builds
- `validate:all` - Runs validation + linting

### CI/CD
- GitHub Actions workflow in `.github/workflows/validate-metadata.yml`
- Runs on push/PR to main/develop branches
- Blocks merges if validation fails

### Git Hooks
- Pre-commit hook in `.husky/pre-commit`
- Prevents commits with violations
- Allows commits with warnings only

## 📝 Adding New Scripts

When adding new scripts to this directory:

1. **Make them executable**: `chmod +x script-name.js`
2. **Add to package.json**: Include in the `scripts` section
3. **Document here**: Update this README
4. **Follow naming convention**: Use kebab-case for filenames
5. **Include error handling**: Proper exit codes for CI integration
