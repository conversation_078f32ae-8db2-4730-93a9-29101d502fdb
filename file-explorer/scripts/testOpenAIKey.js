#!/usr/bin/env node

/**
 * ✅ Step 3: External API key validation script
 * Test OpenAI API key outside the application to verify connectivity
 */

const https = require('https');

function testOpenAIKey(apiKey) {
  if (!apiKey) {
    console.error('❌ No API key provided');
    console.log('Usage: node scripts/testOpenAIKey.js <your-openai-api-key>');
    process.exit(1);
  }

  console.log('🔍 Testing OpenAI API key...');
  console.log(`Key: ${apiKey.substring(0, 8)}...`);

  const options = {
    hostname: 'api.openai.com',
    port: 443,
    path: '/v1/models',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }
  };

  const req = https.request(options, (res) => {
    let data = '';

    console.log(`📡 Response Status: ${res.statusCode}`);
    console.log(`📡 Response Headers:`, res.headers);

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        
        if (res.statusCode === 200) {
          console.log('✅ API key is valid!');
          console.log(`📊 Total models returned: ${response.data?.length || 0}`);
          
          if (response.data && response.data.length > 0) {
            console.log('🎯 Sample models:');
            response.data.slice(0, 10).forEach((model, index) => {
              console.log(`  ${index + 1}. ${model.id} (${model.owned_by})`);
            });
            
            if (response.data.length > 10) {
              console.log(`  ... and ${response.data.length - 10} more models`);
            }

            // Check for GPT models specifically
            const gptModels = response.data.filter(m => m.id.startsWith('gpt-'));
            console.log(`🤖 GPT models found: ${gptModels.length}`);
            
            if (gptModels.length > 0) {
              console.log('🎯 GPT models:');
              gptModels.forEach((model, index) => {
                console.log(`  ${index + 1}. ${model.id}`);
              });
            }
          }
        } else {
          console.error('❌ API key validation failed');
          console.error('Response:', response);
        }
      } catch (parseError) {
        console.error('❌ Failed to parse response:', parseError);
        console.error('Raw response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Network error:', error);
  });

  req.setTimeout(10000, () => {
    console.error('❌ Request timeout');
    req.destroy();
  });

  req.end();
}

// Get API key from command line argument
const apiKey = process.argv[2];
testOpenAIKey(apiKey);
