#!/usr/bin/env node

/**
 * ✅ Alert Threshold Test Script
 * Tests real-time cost alert system with configurable thresholds
 * Validates that alerts trigger when usage crosses threshold percentages
 */

const fs = require('fs');
const path = require('path');

console.log('🚨 Testing Alert Threshold System\n');

// Test 1: Verify alert system integration
console.log('📋 Test 1: Alert System Integration');

const requiredFiles = [
  'lib/alert-manager.ts',
  'components/budget/use-threshold-alerts.tsx',
  'components/budget/alert-display.tsx',
  'lib/notification-service.ts'
];

let integrationScore = 0;
requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} exists`);
    integrationScore++;
  } else {
    console.log(`❌ ${file} missing`);
  }
});

console.log(`📊 Integration Score: ${integrationScore}/${requiredFiles.length}`);

// Test 2: Verify alert manager functionality
console.log('\n📋 Test 2: Alert Manager Functionality');

const alertManagerPath = path.join(__dirname, '../lib/alert-manager.ts');
const alertManagerContent = fs.readFileSync(alertManagerPath, 'utf8');

const alertFeatures = [
  'checkThresholds',
  'forceCheckThresholds',
  'ThresholdAlert',
  'createThresholdAlert',
  'createBudgetAlert',
  'triggerAlert',
  'onAlert',
  'acknowledgeAlert'
];

let alertScore = 0;
alertFeatures.forEach(feature => {
  if (alertManagerContent.includes(feature)) {
    console.log(`✅ ${feature} implemented`);
    alertScore++;
  } else {
    console.log(`❌ ${feature} missing`);
  }
});

console.log(`📊 Alert Manager Score: ${alertScore}/${alertFeatures.length}`);

// Test 3: Verify UI integration
console.log('\n📋 Test 3: UI Integration');

const mainAppPath = path.join(__dirname, '../app/page.tsx');
const agentSystemPath = path.join(__dirname, '../app/agent-system/page.tsx');
const costTabPath = path.join(__dirname, '../components/settings/isolated-cost-tab.tsx');

const uiIntegrationChecks = [
  { name: 'Main App useAlertNotifications', file: mainAppPath, pattern: 'useAlertNotifications' },
  { name: 'Main App AlertBanner', file: mainAppPath, pattern: 'AlertBanner' },
  { name: 'Main App periodic checking', file: mainAppPath, pattern: 'checkThresholds' },
  { name: 'Agent System useAlertNotifications', file: agentSystemPath, pattern: 'useAlertNotifications' },
  { name: 'Agent System AlertBanner', file: agentSystemPath, pattern: 'AlertBanner' },
  { name: 'Cost Tab useAlertNotifications', file: costTabPath, pattern: 'useAlertNotifications' }
];

let uiScore = 0;
uiIntegrationChecks.forEach(check => {
  if (fs.existsSync(check.file)) {
    const content = fs.readFileSync(check.file, 'utf8');
    if (content.includes(check.pattern)) {
      console.log(`✅ ${check.name}`);
      uiScore++;
    } else {
      console.log(`❌ ${check.name}`);
    }
  } else {
    console.log(`❌ ${check.name} - file missing`);
  }
});

console.log(`📊 UI Integration Score: ${uiScore}/${uiIntegrationChecks.length}`);

// Test 4: Verify budget enforcer integration
console.log('\n📋 Test 4: Budget Enforcer Integration');

const budgetEnforcerPath = path.join(__dirname, '../lib/budget-enforcer.ts');
const budgetEnforcerContent = fs.readFileSync(budgetEnforcerPath, 'utf8');

const enforcerIntegrationChecks = [
  'alertManager.checkThresholds',
  'recordCost',
  'Check thresholds after recording cost',
  'Threshold alert triggered'
];

let enforcerScore = 0;
enforcerIntegrationChecks.forEach(check => {
  if (budgetEnforcerContent.includes(check)) {
    console.log(`✅ ${check}`);
    enforcerScore++;
  } else {
    console.log(`❌ ${check}`);
  }
});

console.log(`📊 Enforcer Integration Score: ${enforcerScore}/${enforcerIntegrationChecks.length}`);

// Test 5: Verify threshold logic
console.log('\n📋 Test 5: Threshold Logic Validation');

const thresholdLogicChecks = [
  'isNearThreshold',
  'isOverBudget',
  'utilizationPercentage',
  'alertThreshold',
  'budgetLimit',
  'currentMonthlyCost'
];

let logicScore = 0;
thresholdLogicChecks.forEach(check => {
  if (alertManagerContent.includes(check)) {
    console.log(`✅ ${check} logic present`);
    logicScore++;
  } else {
    console.log(`❌ ${check} logic missing`);
  }
});

console.log(`📊 Threshold Logic Score: ${logicScore}/${thresholdLogicChecks.length}`);

// Overall Assessment
console.log('\n🎯 Overall Assessment');

const totalScore = integrationScore + alertScore + uiScore + enforcerScore + logicScore;
const maxScore = requiredFiles.length + alertFeatures.length + uiIntegrationChecks.length + enforcerIntegrationChecks.length + thresholdLogicChecks.length;

console.log(`📊 Total Score: ${totalScore}/${maxScore} (${((totalScore/maxScore)*100).toFixed(1)}%)`);

if (totalScore >= maxScore * 0.9) {
  console.log('🎉 Alert Threshold System: EXCELLENT');
} else if (totalScore >= maxScore * 0.8) {
  console.log('✅ Alert Threshold System: GOOD');
} else if (totalScore >= maxScore * 0.7) {
  console.log('⚠️ Alert Threshold System: NEEDS IMPROVEMENT');
} else {
  console.log('❌ Alert Threshold System: INCOMPLETE');
}

// Test Instructions
console.log('\n📝 Manual Testing Instructions:');
console.log('1. Set budget limit to $10.00 in Settings → Cost');
console.log('2. Set alert threshold to 80% in Cost Settings');
console.log('3. Enable "Track Usage" in Cost Settings');
console.log('4. Submit tasks to agents until cost reaches ~$8.00 (80% of $10.00)');
console.log('5. Verify threshold alert appears as toast notification');
console.log('6. Continue until cost exceeds $10.00 to trigger budget exceeded alert');

console.log('\n✅ Expected Alert Behavior:');
console.log('📊 Threshold Alert (80%):');
console.log('   - Toast notification: "⚠️ You\'ve exceeded your alert threshold: $8.00 (80.0% of $10.00 budget)"');
console.log('   - Alert banner appears in main UI');
console.log('   - Alert only triggers once per month');
console.log('   - Yellow warning icon and styling');

console.log('\n📊 Budget Exceeded Alert (100%+):');
console.log('   - Toast notification: "🚨 Budget exceeded: $10.50 (105.0% of $10.00 budget)"');
console.log('   - Alert banner shows red destructive styling');
console.log('   - Tasks are rejected with budget exceeded error');
console.log('   - Alert only triggers once per month');

console.log('\n🔍 Verification Steps:');
console.log('1. Check Settings → Cost tab for AlertDisplay component');
console.log('2. Verify toast notifications appear with correct messages');
console.log('3. Confirm alert banner shows in main UI and Agent System');
console.log('4. Test alert acknowledgment and clearing functionality');
console.log('5. Verify alerts reset properly at month boundaries');

console.log('\n🛠️ Troubleshooting:');
console.log('- If alerts don\'t trigger:');
console.log('  • Ensure "Track Usage" is enabled');
console.log('  • Check alert threshold is set (default 80%)');
console.log('  • Verify cost tracking is recording actual usage');
console.log('  • Look for JavaScript errors in browser console');

console.log('\n- If alerts trigger multiple times:');
console.log('  • Check alert state persistence in localStorage');
console.log('  • Verify month-based alert reset logic');
console.log('  • Test alert acknowledgment functionality');

process.exit(totalScore >= maxScore * 0.8 ? 0 : 1);
