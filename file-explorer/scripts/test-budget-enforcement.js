#!/usr/bin/env node

/**
 * ✅ Budget Enforcement Test Script
 * Tests budget limit enforcement with real cost tracking
 * Validates that tasks are rejected when budget limits are exceeded
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Budget Enforcement Implementation\n');

// Test 1: Verify budget enforcement is integrated in agent manager
console.log('📋 Test 1: Agent Manager Integration');

const agentManagerPath = path.join(__dirname, '../components/agents/agent-manager-complete.ts');
const agentManagerContent = fs.readFileSync(agentManagerPath, 'utf8');

const requiredImports = [
  'budgetEnforcer',
  'BudgetExceededError',
  'isBudgetExceededError'
];

const requiredMethods = [
  'checkBudgetBeforeExecution',
  'TaskRejectedReason',
  'notifyBudgetExceededFailure'
];

let integrationScore = 0;
const totalChecks = requiredImports.length + requiredMethods.length;

console.log('🔍 Checking required imports...');
requiredImports.forEach(importName => {
  if (agentManagerContent.includes(importName)) {
    console.log(`✅ ${importName} imported`);
    integrationScore++;
  } else {
    console.log(`❌ ${importName} missing`);
  }
});

console.log('\n🔍 Checking required methods...');
requiredMethods.forEach(methodName => {
  if (agentManagerContent.includes(methodName)) {
    console.log(`✅ ${methodName} implemented`);
    integrationScore++;
  } else {
    console.log(`❌ ${methodName} missing`);
  }
});

console.log(`\n📊 Integration Score: ${integrationScore}/${totalChecks}`);

// Test 2: Verify budget enforcement flow
console.log('\n📋 Test 2: Budget Enforcement Flow');

const budgetCheckPattern = /checkBudgetBeforeExecution.*await.*executeTask/s;
const errorHandlingPattern = /TaskRejectedReason\.BudgetExceeded/;
const kanbanUpdatePattern = /moveCardToColumn.*budget/i;

const flowChecks = [
  { name: 'Budget check before execution', pattern: budgetCheckPattern },
  { name: 'Budget exceeded error handling', pattern: errorHandlingPattern },
  { name: 'Kanban card update on budget exceeded', pattern: kanbanUpdatePattern }
];

let flowScore = 0;
flowChecks.forEach(check => {
  if (check.pattern.test(agentManagerContent)) {
    console.log(`✅ ${check.name}`);
    flowScore++;
  } else {
    console.log(`❌ ${check.name}`);
  }
});

console.log(`\n📊 Flow Score: ${flowScore}/${flowChecks.length}`);

// Test 3: Verify budget enforcer functionality
console.log('\n📋 Test 3: Budget Enforcer Functionality');

const budgetEnforcerPath = path.join(__dirname, '../lib/budget-enforcer.ts');
const budgetEnforcerContent = fs.readFileSync(budgetEnforcerPath, 'utf8');

const enforcerFeatures = [
  'checkBudget',
  'enforceBudget',
  'recordCost',
  'isBudgetEnforcementEnabled',
  'BudgetCheckResult'
];

let enforcerScore = 0;
enforcerFeatures.forEach(feature => {
  if (budgetEnforcerContent.includes(feature)) {
    console.log(`✅ ${feature} implemented`);
    enforcerScore++;
  } else {
    console.log(`❌ ${feature} missing`);
  }
});

console.log(`\n📊 Enforcer Score: ${enforcerScore}/${enforcerFeatures.length}`);

// Test 4: Verify LLM request service integration
console.log('\n📋 Test 4: LLM Request Service Integration');

const llmServicePath = path.join(__dirname, '../components/agents/llm-request-service.ts');
const llmServiceContent = fs.readFileSync(llmServicePath, 'utf8');

const llmIntegrationChecks = [
  'budgetEnforcer.enforceBudget',
  'budgetEnforcer.recordCost',
  'isBudgetExceededError',
  'Budget enforcement - check before making the request'
];

let llmScore = 0;
llmIntegrationChecks.forEach(check => {
  if (llmServiceContent.includes(check)) {
    console.log(`✅ ${check}`);
    llmScore++;
  } else {
    console.log(`❌ ${check}`);
  }
});

console.log(`\n📊 LLM Integration Score: ${llmScore}/${llmIntegrationChecks.length}`);

// Test 5: Verify UI components
console.log('\n📋 Test 5: UI Components');

const budgetStatusPath = path.join(__dirname, '../components/budget/budget-status.tsx');
const budgetErrorPath = path.join(__dirname, '../components/budget/budget-error-handler.tsx');

const uiComponents = [
  { name: 'BudgetStatus component', path: budgetStatusPath },
  { name: 'BudgetErrorHandler component', path: budgetErrorPath }
];

let uiScore = 0;
uiComponents.forEach(component => {
  if (fs.existsSync(component.path)) {
    const content = fs.readFileSync(component.path, 'utf8');
    if (content.includes('budgetEnforcer') || content.includes('BudgetExceededError')) {
      console.log(`✅ ${component.name} with budget integration`);
      uiScore++;
    } else {
      console.log(`⚠️ ${component.name} exists but no budget integration`);
    }
  } else {
    console.log(`❌ ${component.name} missing`);
  }
});

console.log(`\n📊 UI Score: ${uiScore}/${uiComponents.length}`);

// Overall Assessment
console.log('\n🎯 Overall Assessment');

const totalScore = integrationScore + flowScore + enforcerScore + llmScore + uiScore;
const maxScore = totalChecks + flowChecks.length + enforcerFeatures.length + llmIntegrationChecks.length + uiComponents.length;

console.log(`📊 Total Score: ${totalScore}/${maxScore} (${((totalScore/maxScore)*100).toFixed(1)}%)`);

if (totalScore >= maxScore * 0.9) {
  console.log('🎉 Budget Enforcement Implementation: EXCELLENT');
} else if (totalScore >= maxScore * 0.8) {
  console.log('✅ Budget Enforcement Implementation: GOOD');
} else if (totalScore >= maxScore * 0.7) {
  console.log('⚠️ Budget Enforcement Implementation: NEEDS IMPROVEMENT');
} else {
  console.log('❌ Budget Enforcement Implementation: INCOMPLETE');
}

// Test Instructions
console.log('\n📝 Manual Testing Instructions:');
console.log('1. Set a low budget limit (e.g., $1.00) in Settings → Cost');
console.log('2. Enable "Track Usage" in Cost Settings');
console.log('3. Submit a task to an agent that would exceed the budget');
console.log('4. Verify the task is rejected with budget exceeded message');
console.log('5. Check that the Kanban card (if any) is moved to backlog');
console.log('6. Verify budget status shows "Over Budget" in Cost tab');

console.log('\n🔍 Expected Log Messages:');
console.log('- "🚫 Task rejected due to budget limit: ..."');
console.log('- "💸 Budget exceeded: ..."');
console.log('- "💸 Kanban card moved to backlog due to budget limit exceeded"');

process.exit(totalScore >= maxScore * 0.8 ? 0 : 1);
