#!/usr/bin/env node

/**
 * Test script for StressTestRunner functionality
 * This script validates that the stress testing system works correctly
 */

const fs = require('fs');
const path = require('path');

// Test the StressTestRunner structure and interfaces
function testStressTestRunner() {
  console.log('🧪 Testing StressTestRunner structure...');
  
  try {
    // Read the StressTestRunner file
    const stressTestRunnerPath = path.join(__dirname, '../systems/stress/StressTestRunner.ts');
    const content = fs.readFileSync(stressTestRunnerPath, 'utf8');
    
    // Check for required interfaces
    const requiredInterfaces = [
      'StressTestConfig',
      'StressTestResult',
      'TaskExecutionResult',
      'StressTestError'
    ];
    
    const requiredMethods = [
      'runTest',
      'validateConfig',
      'generateTestTasks',
      'executeStressTest',
      'calculateFinalMetrics',
      'getStatus',
      'stopTest'
    ];
    
    const requiredFeatures = [
      'real task execution',
      'concurrency limits',
      'metrics collection',
      'error handling',
      'timeout support'
    ];
    
    // Test interface definitions
    let interfacesPassed = 0;
    requiredInterfaces.forEach(interfaceName => {
      if (content.includes(`interface ${interfaceName}`)) {
        console.log(`✅ Interface ${interfaceName}: FOUND`);
        interfacesPassed++;
      } else {
        console.log(`❌ Interface ${interfaceName}: MISSING`);
      }
    });
    
    // Test method implementations
    let methodsPassed = 0;
    requiredMethods.forEach(methodName => {
      if (content.includes(`${methodName}(`)) {
        console.log(`✅ Method ${methodName}: FOUND`);
        methodsPassed++;
      } else {
        console.log(`❌ Method ${methodName}: MISSING`);
      }
    });
    
    // Test feature implementations
    let featuresPassed = 0;
    const featureChecks = [
      { name: 'real task execution', check: content.includes('agentManager.submitTask') },
      { name: 'concurrency limits', check: content.includes('maxConcurrentTasks') },
      { name: 'metrics collection', check: content.includes('metrics:') },
      { name: 'error handling', check: content.includes('try {') && content.includes('catch') },
      { name: 'timeout support', check: content.includes('withTimeout') }
    ];
    
    featureChecks.forEach(feature => {
      if (feature.check) {
        console.log(`✅ Feature ${feature.name}: IMPLEMENTED`);
        featuresPassed++;
      } else {
        console.log(`❌ Feature ${feature.name}: MISSING`);
      }
    });
    
    // Test task variations
    const taskVariationChecks = [
      { name: 'simple tasks', check: content.includes('getSimpleTaskVariations') },
      { name: 'complex tasks', check: content.includes('getComplexTaskVariations') },
      { name: 'max token tests', check: content.includes('max-tokens') },
      { name: 'malformed prompts', check: content.includes('malformed') },
      { name: 'long responses', check: content.includes('long-response') }
    ];
    
    let variationsPassed = 0;
    taskVariationChecks.forEach(variation => {
      if (variation.check) {
        console.log(`✅ Task variation ${variation.name}: IMPLEMENTED`);
        variationsPassed++;
      } else {
        console.log(`❌ Task variation ${variation.name}: MISSING`);
      }
    });
    
    // Summary
    console.log('\n📊 StressTestRunner Test Results:');
    console.log(`   Interfaces: ${interfacesPassed}/${requiredInterfaces.length}`);
    console.log(`   Methods: ${methodsPassed}/${requiredMethods.length}`);
    console.log(`   Features: ${featuresPassed}/${featureChecks.length}`);
    console.log(`   Task Variations: ${variationsPassed}/${taskVariationChecks.length}`);
    
    const totalPassed = interfacesPassed + methodsPassed + featuresPassed + variationsPassed;
    const totalRequired = requiredInterfaces.length + requiredMethods.length + featureChecks.length + taskVariationChecks.length;
    
    console.log(`   Overall: ${totalPassed}/${totalRequired} (${((totalPassed/totalRequired)*100).toFixed(1)}%)`);
    
    return totalPassed === totalRequired;
    
  } catch (error) {
    console.error('❌ StressTestRunner test failed:', error.message);
    return false;
  }
}

// Test the analytics service integration
function testAnalyticsIntegration() {
  console.log('\n🧪 Testing Analytics Service integration...');
  
  try {
    // Read the analytics service file
    const analyticsServicePath = path.join(__dirname, '../services/analytics-service.ts');
    const content = fs.readFileSync(analyticsServicePath, 'utf8');
    
    const requiredMethods = [
      'reportStressTestResults',
      'getStressTestResults',
      'getStressTestAnalytics',
      'clearStressTestResults'
    ];
    
    const requiredFeatures = [
      'StressTestResult import',
      'stress test storage',
      'metrics aggregation',
      'trend analysis'
    ];
    
    // Test method implementations
    let methodsPassed = 0;
    requiredMethods.forEach(methodName => {
      if (content.includes(`${methodName}(`)) {
        console.log(`✅ Analytics method ${methodName}: FOUND`);
        methodsPassed++;
      } else {
        console.log(`❌ Analytics method ${methodName}: MISSING`);
      }
    });
    
    // Test feature implementations
    let featuresPassed = 0;
    const featureChecks = [
      { name: 'StressTestResult import', check: content.includes('StressTestResult') },
      { name: 'stress test storage', check: content.includes('stressTestResults') },
      { name: 'metrics aggregation', check: content.includes('averageSuccessRate') },
      { name: 'trend analysis', check: content.includes('recentTrend') }
    ];
    
    featureChecks.forEach(feature => {
      if (feature.check) {
        console.log(`✅ Analytics feature ${feature.name}: IMPLEMENTED`);
        featuresPassed++;
      } else {
        console.log(`❌ Analytics feature ${feature.name}: MISSING`);
      }
    });
    
    console.log('\n📊 Analytics Integration Test Results:');
    console.log(`   Methods: ${methodsPassed}/${requiredMethods.length}`);
    console.log(`   Features: ${featuresPassed}/${featureChecks.length}`);
    
    return methodsPassed === requiredMethods.length && featuresPassed === featureChecks.length;
    
  } catch (error) {
    console.error('❌ Analytics integration test failed:', error.message);
    return false;
  }
}

// Test configuration validation
function testConfigValidation() {
  console.log('\n🧪 Testing configuration validation...');
  
  try {
    // Test valid configuration structure
    const validConfig = {
      agents: ['agent1', 'agent2'],
      taskType: 'simple',
      duration: 30,
      concurrency: 3,
      maxTasks: 50
    };
    
    const invalidConfigs = [
      { agents: [], taskType: 'simple', duration: 30, concurrency: 3 }, // empty agents
      { agents: ['agent1'], taskType: 'simple', duration: 0, concurrency: 3 }, // zero duration
      { agents: ['agent1'], taskType: 'simple', duration: 30, concurrency: 0 }, // zero concurrency
      { agents: ['agent1'], taskType: 'invalid', duration: 30, concurrency: 3 } // invalid task type
    ];
    
    console.log('✅ Valid config structure: DEFINED');
    console.log('✅ Invalid config examples: DEFINED');
    console.log('✅ Configuration validation: READY FOR TESTING');
    
    return true;
    
  } catch (error) {
    console.error('❌ Configuration validation test failed:', error.message);
    return false;
  }
}

// Main test runner
function runTests() {
  console.log('🚀 Starting StressTestRunner Validation Tests\n');
  
  const results = [
    testStressTestRunner(),
    testAnalyticsIntegration(),
    testConfigValidation()
  ];
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`\n📊 Final Test Results: ${passed}/${total} test suites passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! StressTestRunner is ready for implementation.');
    console.log('\n📋 Next Steps:');
    console.log('   1. Integrate StressTestRunner with UI components');
    console.log('   2. Add stress test controls to Settings or Metrics tab');
    console.log('   3. Test with real agent system');
    console.log('   4. Validate concurrency limits and safety measures');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed. Please review the implementation.');
    process.exit(1);
  }
}

// Run the tests
runTests();
