#!/usr/bin/env node

/**
 * CLI tool to export model registry as versioned JSON snapshots
 * Enables historical tracking, Git diffing, and external service publishing
 */

const fs = require('fs');
const path = require('path');

// Model metadata (same as listModels.js for consistency)
const OPENAI_MODEL_METADATA = {
  'gpt-4': {
    id: 'gpt-4',
    label: 'GPT-4',
    description: 'Most capable GPT-4 model with superior reasoning and complex task handling',
    contextSize: 8192,
    pricing: { input: 0.03, output: 0.06 },
    tags: ['advanced-reasoning', 'general-purpose', 'creative-writing', 'complex-analysis']
  },
  'gpt-4-turbo': {
    id: 'gpt-4-turbo',
    label: 'GPT-4 Turbo',
    description: 'Faster and more efficient version of GPT-4 with extended context',
    contextSize: 128000,
    pricing: { input: 0.01, output: 0.03 },
    tags: ['fast', 'high-context', 'advanced-reasoning', 'general-purpose']
  },
  'gpt-4o': {
    id: 'gpt-4o',
    label: 'GPT-4o',
    description: 'Multimodal flagship model with vision, audio, and text capabilities',
    contextSize: 128000,
    pricing: { input: 0.005, output: 0.015 },
    tags: ['multimodal', 'vision', 'fast', 'advanced-reasoning', 'latest']
  },
  'gpt-4o-mini': {
    id: 'gpt-4o-mini',
    label: 'GPT-4o Mini',
    description: 'Smaller, faster, and more affordable version of GPT-4o',
    contextSize: 128000,
    pricing: { input: 0.00015, output: 0.0006 },
    tags: ['fast', 'affordable', 'multimodal', 'general-purpose']
  },
  'gpt-3.5-turbo': {
    id: 'gpt-3.5-turbo',
    label: 'GPT-3.5 Turbo',
    description: 'Fast and efficient model for most conversational tasks',
    contextSize: 16385,
    pricing: { input: 0.0015, output: 0.002 },
    tags: ['fast', 'affordable', 'conversational', 'general-purpose']
  }
};

const ANTHROPIC_MODELS = [
  {
    id: 'claude-3-5-sonnet-20241022',
    label: 'Claude 3.5 Sonnet',
    description: 'Most intelligent Claude model with enhanced reasoning and coding capabilities',
    contextLength: 200000,
    costPer1kTokens: { input: 0.003, output: 0.015 },
    capabilities: ['advanced-reasoning', 'code', 'creative-writing', 'complex-analysis', 'latest']
  },
  {
    id: 'claude-3-opus-20240229',
    label: 'Claude 3 Opus',
    description: 'Most powerful Claude model with exceptional reasoning and analysis capabilities',
    contextLength: 200000,
    costPer1kTokens: { input: 0.015, output: 0.075 },
    capabilities: ['advanced-reasoning', 'complex-analysis', 'creative-writing', 'code']
  },
  {
    id: 'claude-3-sonnet-20240229',
    label: 'Claude 3 Sonnet',
    description: 'Balanced performance and speed for most use cases',
    contextLength: 200000,
    costPer1kTokens: { input: 0.003, output: 0.015 },
    capabilities: ['general-purpose', 'code', 'complex-analysis', 'creative-writing']
  },
  {
    id: 'claude-3-haiku-20240307',
    label: 'Claude 3 Haiku',
    description: 'Fastest Claude model for quick responses',
    contextLength: 200000,
    costPer1kTokens: { input: 0.00025, output: 0.00125 },
    capabilities: ['fast', 'affordable', 'conversational']
  }
];

const OPENROUTER_MODEL_METADATA = {
  'mixtral': {
    id: 'mistralai/mixtral-8x7b-instruct',
    label: 'Mixtral 8x7B Instruct',
    description: 'High-quality sparse mixture of experts model with strong reasoning capabilities',
    contextSize: 32768,
    pricing: { input: 0.00024, output: 0.00024 },
    tags: ['open-weight', 'advanced-reasoning', 'fast', 'mixture-of-experts'],
    provider: 'Mistral AI'
  },
  'llama-3.1-70b': {
    id: 'meta-llama/llama-3.1-70b-instruct',
    label: 'Llama 3.1 70B Instruct',
    description: 'Meta\'s flagship open-source model with excellent reasoning capabilities',
    contextSize: 131072,
    pricing: { input: 0.00088, output: 0.00088 },
    tags: ['open-weight', 'advanced-reasoning', 'long-context'],
    provider: 'Meta'
  },
  'claude-3-sonnet': {
    id: 'anthropic/claude-3-sonnet',
    label: 'Claude 3 Sonnet',
    description: 'Anthropic\'s balanced model via OpenRouter',
    contextSize: 200000,
    pricing: { input: 0.003, output: 0.015 },
    tags: ['advanced-reasoning', 'general-purpose', 'creative-writing'],
    provider: 'Anthropic'
  }
};

const DEEPSEEK_MODEL_METADATA = {
  'deepseek-chat': {
    id: 'deepseek-chat',
    label: 'DeepSeek Chat',
    description: 'General-purpose conversational AI model with strong reasoning capabilities',
    contextSize: 32768,
    pricing: { input: 0.0014, output: 0.0028 },
    tags: ['advanced-reasoning', 'conversational', 'general-purpose', 'affordable']
  },
  'deepseek-coder': {
    id: 'deepseek-coder',
    label: 'DeepSeek Coder',
    description: 'Specialized coding model with excellent programming capabilities',
    contextSize: 32768,
    pricing: { input: 0.0014, output: 0.0028 },
    tags: ['code', 'programming', 'debugging', 'developer-friendly']
  }
};

const FIREWORKS_MODEL_METADATA = {
  'llama-3.1-70b': {
    id: 'accounts/fireworks/models/llama-v3p1-70b-instruct',
    label: 'Llama 3.1 70B Instruct',
    description: 'Meta\'s flagship open-source model optimized for fast inference on Fireworks',
    contextSize: 131072,
    pricing: { input: 0.0009, output: 0.0009 },
    tags: ['open-weight', 'advanced-reasoning', 'fast', 'long-context']
  },
  'mixtral-8x7b': {
    id: 'accounts/fireworks/models/mixtral-8x7b-instruct',
    label: 'Mixtral 8x7B Instruct',
    description: 'Mistral\'s mixture of experts model optimized for Fireworks infrastructure',
    contextSize: 32768,
    pricing: { input: 0.0009, output: 0.0009 },
    tags: ['open-weight', 'mixture-of-experts', 'fast', 'affordable']
  }
};

const GOOGLE_MODEL_METADATA = {
  'gemini-pro': {
    id: 'gemini-pro',
    label: 'Gemini Pro',
    description: 'Google\'s flagship multimodal AI model for complex reasoning tasks',
    contextSize: 32768,
    pricing: { input: 0.000125, output: 0.000375 },
    tags: ['multimodal', 'advanced-reasoning', 'vision', 'fast']
  },
  'gemini-1.5-pro': {
    id: 'gemini-1.5-pro',
    label: 'Gemini 1.5 Pro',
    description: 'Enhanced version with extended context window and improved capabilities',
    contextSize: 1000000,
    pricing: { input: 0.007, output: 0.021 },
    tags: ['multimodal', 'long-context', 'advanced-reasoning', 'vision', 'code']
  },
  'gemini-1.5-flash': {
    id: 'gemini-1.5-flash',
    label: 'Gemini 1.5 Flash',
    description: 'Faster, more efficient version optimized for speed and cost',
    contextSize: 1000000,
    pricing: { input: 0.000075, output: 0.0003 },
    tags: ['multimodal', 'fast', 'affordable', 'long-context']
  }
};

/**
 * Convert models to unified format for export
 */
function getAllModels() {
  const allModels = [];

  // OpenAI models
  Object.values(OPENAI_MODEL_METADATA).forEach(model => {
    allModels.push({
      provider: 'openai',
      providerName: 'OpenAI',
      modelId: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      releaseDate: model.releaseDate
    });
  });

  // Anthropic models
  ANTHROPIC_MODELS.forEach(model => {
    allModels.push({
      provider: 'anthropic',
      providerName: 'Anthropic',
      modelId: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextLength,
      pricing: model.costPer1kTokens,
      tags: model.capabilities,
      releaseDate: model.releaseDate
    });
  });

  // OpenRouter models
  Object.values(OPENROUTER_MODEL_METADATA).forEach(model => {
    allModels.push({
      provider: 'openrouter',
      providerName: 'OpenRouter',
      modelId: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      originalProvider: model.provider
    });
  });

  // DeepSeek models
  Object.values(DEEPSEEK_MODEL_METADATA).forEach(model => {
    allModels.push({
      provider: 'deepseek',
      providerName: 'DeepSeek',
      modelId: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      releaseDate: model.releaseDate
    });
  });

  // Fireworks models
  Object.values(FIREWORKS_MODEL_METADATA).forEach(model => {
    allModels.push({
      provider: 'fireworks',
      providerName: 'Fireworks AI',
      modelId: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      releaseDate: model.releaseDate
    });
  });

  // Google models
  Object.values(GOOGLE_MODEL_METADATA).forEach(model => {
    allModels.push({
      provider: 'google',
      providerName: 'Google AI',
      modelId: model.id,
      label: model.label,
      description: model.description,
      contextSize: model.contextSize,
      pricing: model.pricing,
      tags: model.tags,
      releaseDate: model.releaseDate
    });
  });

  return allModels;
}

/**
 * Generate provider statistics
 */
function getProviderStats(models) {
  const stats = {};
  
  models.forEach(model => {
    if (!stats[model.provider]) {
      stats[model.provider] = {
        name: model.providerName,
        count: 0,
        models: []
      };
    }
    stats[model.provider].count++;
    stats[model.provider].models.push(model.modelId);
  });
  
  return stats;
}

/**
 * Main export function
 */
async function main() {
  try {
    console.log('🔄 Generating model registry snapshot...');
    
    // Get all models
    const models = getAllModels();
    
    // Sort models by provider, then by model ID for consistent ordering
    const sortedModels = models.sort((a, b) => {
      if (a.provider !== b.provider) {
        return a.provider.localeCompare(b.provider);
      }
      return a.modelId.localeCompare(b.modelId);
    });
    
    // Generate timestamp
    const timestamp = new Date().toISOString();
    const fileTimestamp = timestamp.replace(/[:.]/g, '-');
    
    // Create export data
    const exportData = {
      metadata: {
        generatedAt: timestamp,
        version: '1.0.0',
        description: 'Model registry snapshot with verified metadata from official sources',
        totalModels: sortedModels.length,
        totalProviders: new Set(sortedModels.map(m => m.provider)).size
      },
      providerStats: getProviderStats(sortedModels),
      models: sortedModels
    };
    
    // Ensure snapshots directory exists
    const outDir = path.resolve(__dirname, '../snapshots');
    if (!fs.existsSync(outDir)) {
      fs.mkdirSync(outDir, { recursive: true });
    }
    
    // Generate filename
    const fileName = `model-registry-${fileTimestamp}.json`;
    const filePath = path.join(outDir, fileName);
    
    // Write snapshot file
    fs.writeFileSync(filePath, JSON.stringify(exportData, null, 2));
    
    // Generate latest snapshot (for easy access)
    const latestPath = path.join(outDir, 'model-registry-latest.json');
    fs.writeFileSync(latestPath, JSON.stringify(exportData, null, 2));
    
    console.log(`✅ Exported snapshot to: snapshots/${fileName}`);
    console.log(`📋 Summary: ${exportData.metadata.totalModels} models across ${exportData.metadata.totalProviders} providers`);
    console.log(`🔗 Latest snapshot: snapshots/model-registry-latest.json`);
    
    // Display provider breakdown
    console.log('\n📊 Provider Breakdown:');
    Object.entries(exportData.providerStats).forEach(([provider, stats]) => {
      console.log(`  ${stats.name}: ${stats.count} models`);
    });
    
  } catch (error) {
    console.error('❌ Failed to export model metadata snapshot:', error);
    process.exit(1);
  }
}

// Handle CLI execution
if (require.main === module) {
  main();
}
