#!/usr/bin/env npx ts-node

/**
 * CLI tool to list all available models with metadata
 * Provides a comprehensive overview of all models across all providers
 */

import chalk from 'chalk';
import { unifiedModelService, UnifiedModelMetadata } from '../components/agents/unified-model-service.js';

// Command line argument parsing
const args = process.argv.slice(2);
const isJsonMode = args.includes('--json') || args.includes('-j');
const showHelp = args.includes('--help') || args.includes('-h');
const filterProvider = args.find(arg => arg.startsWith('--provider='))?.split('=')[1];
const showStats = args.includes('--stats');

/**
 * Format pricing information for display
 */
function formatPrice(pricing?: { input?: number; output?: number }): string {
  if (!pricing || (!pricing.input && !pricing.output)) {
    return 'Unavailable';
  }

  const inputPrice = pricing.input ? `$${pricing.input.toFixed(6)}` : 'N/A';
  const outputPrice = pricing.output ? `$${pricing.output.toFixed(6)}` : 'N/A';

  return `Input: ${inputPrice} | Output: ${outputPrice}`;
}

/**
 * Format context size for display
 */
function formatContextSize(contextSize?: number): string {
  if (!contextSize) return 'Unknown';

  if (contextSize >= 1000000) {
    return `${(contextSize / 1000000).toFixed(1)}M tokens`;
  } else if (contextSize >= 1000) {
    return `${(contextSize / 1000).toFixed(0)}K tokens`;
  } else {
    return `${contextSize} tokens`;
  }
}

/**
 * Format tags for display
 */
function formatTags(tags?: string[]): string {
  if (!tags || tags.length === 0) return 'None';
  return tags.join(', ');
}

/**
 * Get provider color for consistent styling
 */
function getProviderColor(provider: string): (text: string) => string {
  const colors: Record<string, (text: string) => string> = {
    'openai': chalk.green,
    'anthropic': chalk.blue,
    'openrouter': chalk.magenta,
    'google': chalk.yellow,
    'deepseek': chalk.cyan,
    'fireworks': chalk.red,
    'azure': chalk.blueBright
  };

  return colors[provider.toLowerCase()] || chalk.white;
}

/**
 * Display help information
 */
function showHelpText(): void {
  console.log(chalk.bold('\n🧠 Model Registry CLI Tool\n'));
  console.log('Usage: npm run list:models [options]\n');
  console.log('Options:');
  console.log('  --json, -j           Output in JSON format');
  console.log('  --provider=<name>    Filter by provider (openai, anthropic, openrouter, etc.)');
  console.log('  --stats              Show provider statistics only');
  console.log('  --help, -h           Show this help message\n');
  console.log('Examples:');
  console.log('  npm run list:models');
  console.log('  npm run list:models --json');
  console.log('  npm run list:models --provider=openai');
  console.log('  npm run list:models --stats\n');
}

/**
 * Display provider statistics
 */
function displayStats(): void {
  const stats = unifiedModelService.getProviderStats();
  const totalModels = Object.values(stats).reduce((sum, stat) => sum + stat.count, 0);

  console.log(chalk.bold('\n📊 Model Registry Statistics\n'));
  console.log(`Total Models: ${chalk.bold(totalModels.toString())}\n`);

  Object.entries(stats).forEach(([provider, stat]) => {
    const color = getProviderColor(provider);
    const providerName = provider.toUpperCase();
    const statusIcon = stat.hasMetadata ? '✅' : '❌';

    console.log(`${statusIcon} ${color(providerName.padEnd(12))} ${stat.count.toString().padStart(3)} models`);
  });

  console.log();
}

/**
 * Main function to display models
 */
function main(): void {
  try {
    // Show help if requested
    if (showHelp) {
      showHelpText();
      return;
    }

    // Show stats if requested
    if (showStats) {
      displayStats();
      return;
    }

    // Get models (filtered by provider if specified)
    let modelsByProvider = unifiedModelService.getModelsByProvider();

    if (filterProvider) {
      const provider = filterProvider.toLowerCase();
      if (!(provider in modelsByProvider)) {
        console.error(chalk.red(`❌ Unknown provider: ${filterProvider}`));
        console.error(chalk.gray('Available providers: openai, anthropic, openrouter, google, deepseek, fireworks, azure'));
        process.exit(1);
      }

      modelsByProvider = {
        [provider]: modelsByProvider[provider as keyof typeof modelsByProvider]
      } as any;
    }

    // JSON output mode
    if (isJsonMode) {
      const jsonOutput = {
        timestamp: new Date().toISOString(),
        totalProviders: Object.keys(modelsByProvider).length,
        totalModels: Object.values(modelsByProvider).reduce((sum, models) => sum + models.length, 0),
        providers: modelsByProvider
      };

      console.log(JSON.stringify(jsonOutput, null, 2));
      return;
    }

    // Human-readable output
    console.log(chalk.bold('\n🧠 Model Registry Overview\n'));

    // Calculate totals
    const totalProviders = Object.keys(modelsByProvider).filter(provider =>
      modelsByProvider[provider as keyof typeof modelsByProvider].length > 0
    ).length;
    const totalModels = Object.values(modelsByProvider).reduce((sum, models) => sum + models.length, 0);

    console.log(chalk.gray(`${totalProviders} providers • ${totalModels} models with verified metadata\n`));

    // Display models by provider
    Object.entries(modelsByProvider).forEach(([provider, models]) => {
      if (models.length === 0) return;

      const color = getProviderColor(provider);
      const providerName = models[0]?.providerName || provider.toUpperCase();

      console.log(color(`📦 ${providerName.toUpperCase()} (${models.length} models)`));

      models.forEach((model: UnifiedModelMetadata) => {
        const contextInfo = formatContextSize(model.contextSize);
        const pricingInfo = formatPrice(model.pricing);
        const tagsInfo = formatTags(model.tags);

        console.log(`${chalk.green('  •')} ${chalk.bold(model.label)} ${chalk.gray(`(${model.id})`)}`);
        console.log(`    ${chalk.gray('Context:')} ${contextInfo}`);
        console.log(`    ${chalk.gray('Pricing:')} ${pricingInfo}`);

        if (model.tags && model.tags.length > 0) {
          console.log(`    ${chalk.gray('Tags:')} ${tagsInfo}`);
        }

        if (model.originalProvider) {
          console.log(`    ${chalk.gray('Via:')} ${model.originalProvider}`);
        }

        if (model.description) {
          console.log(`    ${chalk.gray(model.description)}`);
        }

        console.log();
      });
    });

    console.log(chalk.gray('💡 Use --json for machine-readable output or --help for more options'));

  } catch (error) {
    console.error(chalk.red('❌ Failed to list models:'), error);
    process.exit(1);
  }
}

// Handle CLI execution
if (require.main === module) {
  main();
}
