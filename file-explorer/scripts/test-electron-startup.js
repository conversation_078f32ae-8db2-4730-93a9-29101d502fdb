#!/usr/bin/env node

/**
 * Test Electron Startup Script
 * 
 * This script tests if Electron can start properly and helps debug startup issues.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Electron Startup...\n');

// Check if required files exist
const requiredFiles = [
  'dist-electron/main.js',
  'dist-electron/preload.js',
  'package.json'
];

console.log('📁 Checking required files:');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Missing required files. Run:');
  console.log('  npm run compile:electron');
  console.log('  npm run copy:preload');
  process.exit(1);
}

// Check if Electron is available
console.log('\n🔍 Checking Electron availability:');

try {
  const electronPath = require.resolve('electron');
  console.log(`  ✅ Electron found at: ${electronPath}`);
} catch (error) {
  console.log('  ❌ Electron not found. Run: npm install');
  process.exit(1);
}

// Test Electron startup
console.log('\n🚀 Testing Electron startup (will exit after 3 seconds):');

const electronProcess = spawn('npx', ['electron', '.', '--dev'], {
  stdio: 'pipe',
  cwd: process.cwd()
});

let output = '';
let errorOutput = '';

electronProcess.stdout.on('data', (data) => {
  output += data.toString();
  console.log(`  📤 STDOUT: ${data.toString().trim()}`);
});

electronProcess.stderr.on('data', (data) => {
  errorOutput += data.toString();
  console.log(`  📥 STDERR: ${data.toString().trim()}`);
});

electronProcess.on('error', (error) => {
  console.log(`  ❌ Process Error: ${error.message}`);
});

electronProcess.on('close', (code) => {
  console.log(`\n📊 Process exited with code: ${code}`);
  
  if (code === 0) {
    console.log('✅ Electron started successfully!');
  } else {
    console.log('❌ Electron failed to start.');
    
    if (errorOutput.includes('EADDRINUSE')) {
      console.log('💡 Port conflict detected. Kill existing processes:');
      console.log('   killall node');
      console.log('   killall electron');
    }
    
    if (errorOutput.includes('Cannot find module')) {
      console.log('💡 Missing dependencies. Run:');
      console.log('   npm install');
    }
    

  }
  
  console.log('\n📋 Full Output:');
  console.log('STDOUT:', output || '(empty)');
  console.log('STDERR:', errorOutput || '(empty)');
});

// Kill the process after 3 seconds for testing
setTimeout(() => {
  console.log('\n⏰ Killing test process after 3 seconds...');
  electronProcess.kill('SIGTERM');
  
  setTimeout(() => {
    electronProcess.kill('SIGKILL');
  }, 1000);
}, 3000);

// Handle script termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  electronProcess.kill('SIGTERM');
  process.exit(0);
});

console.log('⏳ Waiting for Electron to start...');
console.log('   (Process will be killed after 3 seconds for testing)');
console.log('   Press Ctrl+C to stop early');
