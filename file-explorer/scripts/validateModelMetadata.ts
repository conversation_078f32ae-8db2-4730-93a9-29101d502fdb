#!/usr/bin/env ts-node
// scripts/validateModelMetadata.ts
// CI-Safe Auto-Validation Script for Model Metadata & Compliance Enforcement

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { ALLOWED_CAPABILITY_TAGS, validateCapabilityTags, suggestValidTags } from '../components/agents/capability-tags';

// Configuration
const FORBIDDEN_KEYWORDS = [
  'test', 'mock', 'placeholder', 'demo', 'example', 'fake', 'scaffold', 'dummy',
  'sample', 'template', 'boilerplate', 'stub', 'temporary', 'temp'
];

const FORBIDDEN_MODEL_PATTERNS = [
  /test-model-\d+/i,
  /mock-\w+/i,
  /placeholder-\w+/i,
  /demo-\w+/i,
  /example-\w+/i,
  /fake-\w+/i,
  /dummy-\w+/i
];

const FORBIDDEN_PRICING_VALUES = [0.0, 0.00, 0.000];

interface ValidationResult {
  file: string;
  violations: string[];
  warnings: string[];
}

interface ModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input?: number;
    output?: number;
  };
  tags?: string[];
  provider?: string;
}

class MetadataValidator {
  private results: ValidationResult[] = [];
  private totalFiles = 0;
  private totalViolations = 0;
  private totalWarnings = 0;

  /**
   * Main validation entry point
   */
  public async validate(): Promise<boolean> {
    console.log('🔍 Starting Model Metadata Validation...\n');

    const baseDir = path.resolve(__dirname, '..');
    const metadataFiles = this.findMetadataFiles(baseDir);

    if (metadataFiles.length === 0) {
      console.log('⚠️  No metadata files found to validate');
      return true;
    }

    console.log(`📁 Found ${metadataFiles.length} metadata files to validate:`);
    metadataFiles.forEach(file => console.log(`   - ${path.relative(baseDir, file)}`));
    console.log('');

    for (const file of metadataFiles) {
      await this.validateFile(file);
    }

    return this.generateReport();
  }

  /**
   * Find all model metadata files
   */
  private findMetadataFiles(baseDir: string): string[] {
    const files: string[] = [];
    const agentsDir = path.join(baseDir, 'components', 'agents');

    if (!fs.existsSync(agentsDir)) {
      console.warn(`⚠️  Agents directory not found: ${agentsDir}`);
      return files;
    }

    const metadataPatterns = [
      '*-models.ts',
      'model-*.ts',
      'metadata-*.ts'
    ];

    const agentFiles = fs.readdirSync(agentsDir);

    for (const file of agentFiles) {
      if (file.endsWith('-models.ts') || file.includes('model') || file.includes('metadata')) {
        const fullPath = path.join(agentsDir, file);
        if (fs.statSync(fullPath).isFile()) {
          files.push(fullPath);
        }
      }
    }

    return files;
  }

  /**
   * Validate a single metadata file
   */
  private async validateFile(filePath: string): Promise<void> {
    this.totalFiles++;
    const result: ValidationResult = {
      file: path.relative(path.resolve(__dirname, '..'), filePath),
      violations: [],
      warnings: []
    };

    try {
      const content = fs.readFileSync(filePath, 'utf8');

      // Check for forbidden keywords
      this.checkForbiddenKeywords(content, result);

      // Check for forbidden model patterns
      this.checkForbiddenModelPatterns(content, result);

      // Check for placeholder pricing
      this.checkPlaceholderPricing(content, result);

      // Check for missing required fields
      this.checkRequiredFields(content, result);

      // Check for suspicious pricing values
      this.checkSuspiciousPricing(content, result);

      // Check for proper metadata structure
      this.checkMetadataStructure(content, result);

      // Check capability tags
      this.checkCapabilityTags(content, result);

    } catch (error) {
      result.violations.push(`❌ Failed to read file: ${error}`);
    }

    if (result.violations.length > 0 || result.warnings.length > 0) {
      this.results.push(result);
    }

    this.totalViolations += result.violations.length;
    this.totalWarnings += result.warnings.length;
  }

  /**
   * Check for forbidden keywords in content
   */
  private checkForbiddenKeywords(content: string, result: ValidationResult): void {
    const lowerContent = content.toLowerCase();

    for (const keyword of FORBIDDEN_KEYWORDS) {
      if (lowerContent.includes(keyword)) {
        // Check if it's in a comment or safe context
        const lines = content.split('\n');
        const matchingLines = lines
          .map((line, index) => ({ line: line.toLowerCase(), number: index + 1, original: line }))
          .filter(({ line }) => line.includes(keyword));

        for (const { line, number, original } of matchingLines) {
          // Skip if it's in a comment explaining what NOT to do
          if (line.includes('//') && (line.includes('forbidden') || line.includes('not') || line.includes('avoid'))) {
            continue;
          }

          // Skip if it's in a string explaining validation
          if (line.includes('forbidden_keywords') || line.includes('validation')) {
            continue;
          }

          // Skip legitimate uses of keywords
          if (this.isLegitimateKeywordUse(keyword, line, original)) {
            continue;
          }

          result.violations.push(`❌ Forbidden keyword "${keyword}" found at line ${number}: ${original.trim()}`);
        }
      }
    }
  }

  /**
   * Check if keyword use is legitimate (not a violation)
   */
  private isLegitimateKeywordUse(keyword: string, lowerLine: string, originalLine: string): boolean {
    switch (keyword) {
      case 'test':
        // Allow "latest", "fastest", "contest", etc.
        return lowerLine.includes('latest') ||
               lowerLine.includes('fastest') ||
               lowerLine.includes('contest') ||
               lowerLine.includes('greatest') ||
               originalLine.includes('Latest') ||
               originalLine.includes('Fastest');

      case 'placeholder':
        // Allow placeholder as a prop name or parameter
        return lowerLine.includes('placeholder?:') ||
               lowerLine.includes('placeholder =') ||
               lowerLine.includes('placeholder,') ||
               lowerLine.includes('placeholder}') ||
               lowerLine.includes('{placeholder}') ||
               originalLine.includes('placeholder="') ||
               originalLine.includes('placeholder={');

      case 'example':
        // Allow "example" in comments or documentation
        return lowerLine.includes('//') ||
               lowerLine.includes('/*') ||
               lowerLine.includes('*') ||
               lowerLine.includes('description:') ||
               originalLine.includes('(e.g.,');

      case 'demo':
        // Allow "demo" in legitimate contexts
        return lowerLine.includes('//') ||
               lowerLine.includes('/*') ||
               lowerLine.includes('description:');

      default:
        return false;
    }
  }

  /**
   * Check for forbidden model ID patterns
   */
  private checkForbiddenModelPatterns(content: string, result: ValidationResult): void {
    for (const pattern of FORBIDDEN_MODEL_PATTERNS) {
      const matches = content.match(pattern);
      if (matches) {
        result.violations.push(`❌ Forbidden model pattern found: ${matches[0]}`);
      }
    }
  }

  /**
   * Check for placeholder pricing values
   */
  private checkPlaceholderPricing(content: string, result: ValidationResult): void {
    // Check for explicit zero pricing
    const zeroPatterns = [
      /input:\s*0\.0+/g,
      /output:\s*0\.0+/g,
      /input:\s*0[^.]/, // input: 0 (without decimal)
      /output:\s*0[^.]/ // output: 0 (without decimal)
    ];

    for (const pattern of zeroPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        result.violations.push(`❌ Placeholder pricing detected: ${matches[0]}`);
      }
    }

    // Check for obviously fake pricing
    const fakePricingPatterns = [
      /input:\s*999/g,
      /output:\s*999/g,
      /input:\s*0\.123456/g,
      /output:\s*0\.123456/g
    ];

    for (const pattern of fakePricingPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        result.violations.push(`❌ Suspicious pricing pattern detected: ${matches[0]}`);
      }
    }
  }

  /**
   * Check for required fields in metadata
   */
  private checkRequiredFields(content: string, result: ValidationResult): void {
    // Check if metadata objects have required fields
    const metadataBlocks = content.match(/{\s*id:\s*['"][^'"]+['"][^}]+}/g);

    if (metadataBlocks) {
      for (const block of metadataBlocks) {
        if (!block.includes('label:')) {
          result.violations.push(`❌ Missing required field 'label' in metadata block`);
        }

        if (!block.includes('description:') && !block.includes('// No description')) {
          result.warnings.push(`⚠️  Missing 'description' field in metadata block`);
        }
      }
    }
  }

  /**
   * Check for suspicious pricing values
   */
  private checkSuspiciousPricing(content: string, result: ValidationResult): void {
    // Check for pricing that seems too high or too low
    const pricingMatches = content.match(/(?:input|output):\s*([\d.]+)/g);

    if (pricingMatches) {
      for (const match of pricingMatches) {
        const value = parseFloat(match.split(':')[1].trim());

        if (value > 1.0) {
          result.warnings.push(`⚠️  Unusually high pricing detected: ${match} (>${1.0})`);
        }

        if (value > 0 && value < 0.00001) {
          result.warnings.push(`⚠️  Unusually low pricing detected: ${match} (<0.00001)`);
        }
      }
    }
  }

  /**
   * Check metadata structure and exports
   */
  private checkMetadataStructure(content: string, result: ValidationResult): void {
    // Check for proper TypeScript interface
    if (!content.includes('interface') && !content.includes('type')) {
      result.warnings.push(`⚠️  No TypeScript interface/type definitions found`);
    }

    // Check for proper exports
    if (!content.includes('export')) {
      result.violations.push(`❌ No exports found - metadata not accessible`);
    }

    // Check for metadata constant
    if (!content.includes('_METADATA') && !content.includes('_MODELS')) {
      result.warnings.push(`⚠️  No metadata constant found (expected *_METADATA or *_MODELS)`);
    }
  }

  /**
   * Check capability tags against whitelist
   */
  private checkCapabilityTags(content: string, result: ValidationResult): void {
    // Extract all tags from the content
    const allTags = this.extractCapabilityTags(content);

    if (allTags.length === 0) {
      return; // No tags found, skip validation
    }

    // Validate tags using the imported function
    const validation = validateCapabilityTags(allTags);

    // Report violations
    if (!validation.valid) {
      result.violations.push(`❌ Invalid capability tags found: ${validation.invalidTags.join(', ')}`);

      for (const invalidTag of validation.invalidTags) {
        const suggestions = suggestValidTags(invalidTag);
        if (suggestions.length > 0) {
          result.violations.push(`💡 Suggestions for "${invalidTag}": ${suggestions.join(', ')}`);
        }
      }
    }

    // Report statistics
    if (validation.validTags.length > 0) {
      result.warnings.push(`📊 Found ${validation.validTags.length} valid capability tags: ${validation.validTags.join(', ')}`);
    }
  }

  /**
   * Extract capability tags from content
   */
  private extractCapabilityTags(content: string): string[] {
    const tags: string[] = [];

    // Match tags arrays: tags: ['tag1', 'tag2', 'tag3']
    const tagMatches = content.match(/tags:\s*\[([^\]]+)\]/g);

    if (tagMatches) {
      for (const match of tagMatches) {
        // Extract individual tags from the array
        const tagArray = match.match(/['"`]([^'"`]+)['"`]/g);
        if (tagArray) {
          const extractedTags = tagArray.map(tag => tag.replace(/['"`]/g, ''));
          tags.push(...extractedTags);
        }
      }
    }

    // Also match capabilities arrays: capabilities: ['cap1', 'cap2']
    const capabilityMatches = content.match(/capabilities:\s*\[([^\]]+)\]/g);

    if (capabilityMatches) {
      for (const match of capabilityMatches) {
        const capArray = match.match(/['"`]([^'"`]+)['"`]/g);
        if (capArray) {
          const extractedCaps = capArray.map(cap => cap.replace(/['"`]/g, ''));
          tags.push(...extractedCaps);
        }
      }
    }

    return tags;
  }

  /**
   * Generate validation report
   */
  private generateReport(): boolean {
    console.log('📊 Validation Report');
    console.log('='.repeat(50));
    console.log(`📁 Files scanned: ${this.totalFiles}`);
    console.log(`❌ Total violations: ${this.totalViolations}`);
    console.log(`⚠️  Total warnings: ${this.totalWarnings}`);
    console.log('');

    if (this.results.length === 0) {
      console.log('✅ All metadata files passed validation!');
      console.log('🎉 No compliance violations found.');
      return true;
    }

    console.log('🚫 Issues found in the following files:');
    console.log('');

    for (const result of this.results) {
      console.log(`📄 ${result.file}`);

      if (result.violations.length > 0) {
        console.log('  Violations:');
        result.violations.forEach(violation => console.log(`    ${violation}`));
      }

      if (result.warnings.length > 0) {
        console.log('  Warnings:');
        result.warnings.forEach(warning => console.log(`    ${warning}`));
      }

      console.log('');
    }

    if (this.totalViolations > 0) {
      console.log('💥 VALIDATION FAILED');
      console.log('🔧 Please fix all violations before proceeding.');
      return false;
    } else {
      console.log('✅ VALIDATION PASSED');
      console.log('⚠️  Please review warnings when possible.');
      return true;
    }
  }
}

// Main execution
async function main() {
  const validator = new MetadataValidator();
  const success = await validator.validate();

  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Validation script failed:', error);
    process.exit(1);
  });
}

export { MetadataValidator };
