#!/usr/bin/env tsx
// scripts/discover-unverified-models.ts
// 🧩 Subtask 1: Discover All Unverified Models
// Mode: 2️⃣ Junior

import { getAllProviders, getProviderConfig, LLMProvider } from '../components/agents/llm-provider-registry';
import { hasOpenAIModelMetadata } from '../components/agents/openai-models';
import { ANTHROPIC_MODELS } from '../components/agents/anthropic-models';

interface UnverifiedModelDiscovery {
  unverified_models: string[];
  provider_breakdown: Record<string, {
    total_discovered: number;
    verified: number;
    unverified: string[];
  }>;
  summary: {
    total_providers: number;
    total_models_discovered: number;
    total_verified: number;
    total_unverified: number;
  };
}

/**
 * Simulate dynamic model discovery for providers that support it
 * This represents what would be fetched from APIs in a real environment
 */
const SIMULATED_DYNAMIC_MODELS = {
  openai: [
    // Current verified models
    'gpt-4', 'gpt-4-turbo', 'gpt-4-turbo-preview', 'gpt-4o', 'gpt-4o-mini',
    'gpt-4o-mini-audio-preview-2024-12-17', 'gpt-4o-audio-preview', 'gpt-4o-realtime-preview',
    'gpt-3.5-turbo',
    // Potentially unverified models that might appear in API
    'o1-preview', 'o1-mini', 'o3-mini', 'gpt-4.1', 'gpt-4.1-mini', 'gpt-4.1-nano',
    'gpt-4o-2024-05-13', 'gpt-4o-2024-08-06', 'gpt-4-0125-preview', 'gpt-4-1106-preview',
    'gpt-4-vision-preview', 'gpt-4-turbo-2024-04-09', 'gpt-3.5-turbo-0125',
    'gpt-3.5-turbo-1106', 'gpt-3.5-turbo-instruct', 'dall-e-3', 'dall-e-2',
    'whisper-1', 'tts-1', 'tts-1-hd', 'text-embedding-3-small', 'text-embedding-3-large',
    'text-embedding-ada-002'
  ],
  openrouter: [
    // Static models from registry
    'mistralai/mixtral-8x7b-instruct', 'meta-llama/llama-3.1-70b-instruct',
    'anthropic/claude-3-sonnet', 'openai/gpt-4', 'google/gemini-pro',
    // Potentially unverified models
    'anthropic/claude-3.5-sonnet', 'anthropic/claude-3-opus', 'anthropic/claude-3-haiku',
    'meta-llama/llama-3.1-405b-instruct', 'meta-llama/llama-3.1-8b-instruct',
    'mistralai/mistral-7b-instruct', 'mistralai/mistral-large', 'qwen/qwen-2-72b-instruct',
    'deepseek/deepseek-chat', 'deepseek/deepseek-coder'
  ],
  google: [
    // Static models from registry
    'gemini-pro', 'gemini-1.5-pro', 'gemini-1.5-flash',
    // Potentially unverified models
    'gemini-1.5-pro-latest', 'gemini-1.5-flash-latest', 'gemini-1.0-pro',
    'gemini-1.0-pro-vision', 'gemini-1.0-pro-001', 'text-bison-001', 'chat-bison-001'
  ],
  deepseek: [
    // Static models from registry
    'deepseek-chat', 'deepseek-coder',
    // Potentially unverified models
    'deepseek-chat-v2', 'deepseek-coder-v2', 'deepseek-math', 'deepseek-reasoning'
  ],
  fireworks: [
    // Static models from registry
    'accounts/fireworks/models/llama-v3p1-70b-instruct',
    'accounts/fireworks/models/mixtral-8x7b-instruct',
    'accounts/fireworks/models/qwen2-72b-instruct',
    // Potentially unverified models
    'accounts/fireworks/models/llama-v3p1-405b-instruct',
    'accounts/fireworks/models/llama-v3p1-8b-instruct',
    'accounts/fireworks/models/mistral-7b-instruct'
  ]
};

/**
 * Check if a model is verified for a specific provider
 */
function isModelVerified(provider: string, modelId: string): boolean {
  switch (provider) {
    case 'openai':
      return hasOpenAIModelMetadata(modelId);
    case 'anthropic':
      return ANTHROPIC_MODELS.some(model => model.id === modelId);
    case 'openrouter':
    case 'deepseek':
    case 'fireworks':
    case 'google':
    case 'azure':
      // For now, assume static models from registry are verified
      // This will be enhanced in later subtasks
      const config = getProviderConfig(provider as LLMProvider);
      return modelId in config.modelMap;
    default:
      return false;
  }
}

/**
 * Discover unverified models across all providers
 */
function discoverUnverifiedModels(): UnverifiedModelDiscovery {
  const providers = getAllProviders();
  const allUnverifiedModels: string[] = [];
  const providerBreakdown: Record<string, any> = {};

  let totalModelsDiscovered = 0;
  let totalVerified = 0;

  for (const provider of providers) {
    const config = getProviderConfig(provider);
    let discoveredModels: string[] = [];

    // Get models based on provider capabilities
    if (config.supportsModelFetching && SIMULATED_DYNAMIC_MODELS[provider as keyof typeof SIMULATED_DYNAMIC_MODELS]) {
      // Use simulated dynamic models for providers that support API fetching
      discoveredModels = SIMULATED_DYNAMIC_MODELS[provider as keyof typeof SIMULATED_DYNAMIC_MODELS];
    } else {
      // Use static models from registry for providers without dynamic fetching
      discoveredModels = Object.keys(config.modelMap);
    }

    // Categorize models as verified or unverified
    const verifiedModels: string[] = [];
    const unverifiedModels: string[] = [];

    for (const modelId of discoveredModels) {
      if (isModelVerified(provider, modelId)) {
        verifiedModels.push(modelId);
      } else {
        unverifiedModels.push(modelId);
        allUnverifiedModels.push(`${provider}:${modelId}`);
      }
    }

    providerBreakdown[provider] = {
      total_discovered: discoveredModels.length,
      verified: verifiedModels.length,
      unverified: unverifiedModels
    };

    totalModelsDiscovered += discoveredModels.length;
    totalVerified += verifiedModels.length;
  }

  return {
    unverified_models: allUnverifiedModels,
    provider_breakdown: providerBreakdown,
    summary: {
      total_providers: providers.length,
      total_models_discovered: totalModelsDiscovered,
      total_verified: totalVerified,
      total_unverified: allUnverifiedModels.length
    }
  };
}

/**
 * Main execution
 */
async function main() {
  console.log('🔍 Discovering Unverified Models...\n');

  const discovery = discoverUnverifiedModels();

  // Display summary
  console.log('📊 Discovery Summary:');
  console.log(`   Total Providers: ${discovery.summary.total_providers}`);
  console.log(`   Total Models Discovered: ${discovery.summary.total_models_discovered}`);
  console.log(`   Total Verified: ${discovery.summary.total_verified}`);
  console.log(`   Total Unverified: ${discovery.summary.total_unverified}\n`);

  // Display provider breakdown
  console.log('📋 Provider Breakdown:');
  Object.entries(discovery.provider_breakdown).forEach(([provider, data]) => {
    console.log(`   ${provider.toUpperCase()}:`);
    console.log(`     Discovered: ${data.total_discovered}`);
    console.log(`     Verified: ${data.verified}`);
    console.log(`     Unverified: ${data.unverified.length}`);
    if (data.unverified.length > 0) {
      console.log(`     Unverified Models: ${data.unverified.slice(0, 3).join(', ')}${data.unverified.length > 3 ? '...' : ''}`);
    }
    console.log('');
  });

  // Output completion format as required
  console.log('✅ Subtask 1 Completion:');
  console.log(JSON.stringify({
    unverified_models: discovery.unverified_models
  }, null, 2));
}

if (require.main === module) {
  main().catch(console.error);
}

export { discoverUnverifiedModels, isModelVerified };
