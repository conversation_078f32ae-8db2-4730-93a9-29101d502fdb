#!/usr/bin/env node

/**
 * CLI tool to list all available models with metadata
 * Provides a comprehensive overview of all models across all providers
 */

const chalk = require('chalk');

// Since we can't directly require TypeScript files, we'll define the metadata inline
// This is a simplified version that includes the essential model data

const OPENAI_MODEL_METADATA = {
  'gpt-4': {
    id: 'gpt-4',
    label: 'GPT-4',
    description: 'Most capable GPT-4 model with superior reasoning and complex task handling',
    contextSize: 8192,
    pricing: { input: 0.03, output: 0.06 },
    tags: ['advanced-reasoning', 'general-purpose', 'creative-writing', 'complex-analysis']
  },
  'gpt-4-turbo': {
    id: 'gpt-4-turbo',
    label: 'GPT-4 Turbo',
    description: 'Faster and more efficient version of GPT-4 with extended context',
    contextSize: 128000,
    pricing: { input: 0.01, output: 0.03 },
    tags: ['fast', 'high-context', 'advanced-reasoning', 'general-purpose']
  },
  'gpt-4o': {
    id: 'gpt-4o',
    label: 'GPT-4o',
    description: 'Multimodal flagship model with vision, audio, and text capabilities',
    contextSize: 128000,
    pricing: { input: 0.005, output: 0.015 },
    tags: ['multimodal', 'vision', 'fast', 'advanced-reasoning', 'latest']
  },
  'gpt-4o-mini': {
    id: 'gpt-4o-mini',
    label: 'GPT-4o Mini',
    description: 'Smaller, faster, and more affordable version of GPT-4o',
    contextSize: 128000,
    pricing: { input: 0.00015, output: 0.0006 },
    tags: ['fast', 'affordable', 'multimodal', 'general-purpose']
  },
  'gpt-3.5-turbo': {
    id: 'gpt-3.5-turbo',
    label: 'GPT-3.5 Turbo',
    description: 'Fast and efficient model for most conversational tasks',
    contextSize: 16385,
    pricing: { input: 0.0015, output: 0.002 },
    tags: ['fast', 'affordable', 'conversational', 'general-purpose']
  }
};

const ANTHROPIC_MODELS = [
  {
    id: 'claude-3-5-sonnet-20241022',
    label: 'Claude 3.5 Sonnet',
    description: 'Most intelligent Claude model with enhanced reasoning and coding capabilities',
    contextLength: 200000,
    costPer1kTokens: { input: 0.003, output: 0.015 },
    capabilities: ['advanced-reasoning', 'code', 'creative-writing', 'complex-analysis', 'latest']
  },
  {
    id: 'claude-3-opus-20240229',
    label: 'Claude 3 Opus',
    description: 'Most powerful Claude model with exceptional reasoning and analysis capabilities',
    contextLength: 200000,
    costPer1kTokens: { input: 0.015, output: 0.075 },
    capabilities: ['advanced-reasoning', 'complex-analysis', 'creative-writing', 'code']
  },
  {
    id: 'claude-3-sonnet-20240229',
    label: 'Claude 3 Sonnet',
    description: 'Balanced performance and speed for most use cases',
    contextLength: 200000,
    costPer1kTokens: { input: 0.003, output: 0.015 },
    capabilities: ['general-purpose', 'code', 'complex-analysis', 'creative-writing']
  },
  {
    id: 'claude-3-haiku-20240307',
    label: 'Claude 3 Haiku',
    description: 'Fastest Claude model for quick responses',
    contextLength: 200000,
    costPer1kTokens: { input: 0.00025, output: 0.00125 },
    capabilities: ['fast', 'affordable', 'conversational']
  }
];

const OPENROUTER_MODEL_METADATA = {
  'mixtral': {
    id: 'mistralai/mixtral-8x7b-instruct',
    label: 'Mixtral 8x7B Instruct',
    description: 'High-quality sparse mixture of experts model with strong reasoning capabilities',
    contextSize: 32768,
    pricing: { input: 0.00024, output: 0.00024 },
    tags: ['open-weight', 'advanced-reasoning', 'fast', 'mixture-of-experts'],
    provider: 'Mistral AI'
  },
  'llama-3.1-70b': {
    id: 'meta-llama/llama-3.1-70b-instruct',
    label: 'Llama 3.1 70B Instruct',
    description: 'Meta\'s flagship open-source model with excellent reasoning capabilities',
    contextSize: 131072,
    pricing: { input: 0.00088, output: 0.00088 },
    tags: ['open-weight', 'advanced-reasoning', 'long-context'],
    provider: 'Meta'
  },
  'claude-3-sonnet': {
    id: 'anthropic/claude-3-sonnet',
    label: 'Claude 3 Sonnet',
    description: 'Anthropic\'s balanced model via OpenRouter',
    contextSize: 200000,
    pricing: { input: 0.003, output: 0.015 },
    tags: ['advanced-reasoning', 'general-purpose', 'creative-writing'],
    provider: 'Anthropic'
  }
};

const DEEPSEEK_MODEL_METADATA = {
  'deepseek-chat': {
    id: 'deepseek-chat',
    label: 'DeepSeek Chat',
    description: 'General-purpose conversational AI model with strong reasoning capabilities',
    contextSize: 32768,
    pricing: { input: 0.0014, output: 0.0028 },
    tags: ['advanced-reasoning', 'conversational', 'general-purpose', 'affordable']
  },
  'deepseek-coder': {
    id: 'deepseek-coder',
    label: 'DeepSeek Coder',
    description: 'Specialized coding model with excellent programming capabilities',
    contextSize: 32768,
    pricing: { input: 0.0014, output: 0.0028 },
    tags: ['code', 'programming', 'debugging', 'developer-friendly']
  }
};

const FIREWORKS_MODEL_METADATA = {
  'llama-3.1-70b': {
    id: 'accounts/fireworks/models/llama-v3p1-70b-instruct',
    label: 'Llama 3.1 70B Instruct',
    description: 'Meta\'s flagship open-source model optimized for fast inference on Fireworks',
    contextSize: 131072,
    pricing: { input: 0.0009, output: 0.0009 },
    tags: ['open-weight', 'advanced-reasoning', 'fast', 'long-context']
  },
  'mixtral-8x7b': {
    id: 'accounts/fireworks/models/mixtral-8x7b-instruct',
    label: 'Mixtral 8x7B Instruct',
    description: 'Mistral\'s mixture of experts model optimized for Fireworks infrastructure',
    contextSize: 32768,
    pricing: { input: 0.0009, output: 0.0009 },
    tags: ['open-weight', 'mixture-of-experts', 'fast', 'affordable']
  }
};

const GOOGLE_MODEL_METADATA = {
  'gemini-pro': {
    id: 'gemini-pro',
    label: 'Gemini Pro',
    description: 'Google\'s flagship multimodal AI model for complex reasoning tasks',
    contextSize: 32768,
    pricing: { input: 0.000125, output: 0.000375 },
    tags: ['multimodal', 'advanced-reasoning', 'vision', 'fast']
  },
  'gemini-1.5-pro': {
    id: 'gemini-1.5-pro',
    label: 'Gemini 1.5 Pro',
    description: 'Enhanced version with extended context window and improved capabilities',
    contextSize: 1000000,
    pricing: { input: 0.007, output: 0.021 },
    tags: ['multimodal', 'long-context', 'advanced-reasoning', 'vision', 'code']
  },
  'gemini-1.5-flash': {
    id: 'gemini-1.5-flash',
    label: 'Gemini 1.5 Flash',
    description: 'Faster, more efficient version optimized for speed and cost',
    contextSize: 1000000,
    pricing: { input: 0.000075, output: 0.0003 },
    tags: ['multimodal', 'fast', 'affordable', 'long-context']
  }
};

// Command line argument parsing
const args = process.argv.slice(2);
const isJsonMode = args.includes('--json') || args.includes('-j');
const showHelp = args.includes('--help') || args.includes('-h');
const filterProvider = args.find(arg => arg.startsWith('--provider='))?.split('=')[1];
const showStats = args.includes('--stats');

/**
 * Format pricing information for display
 */
function formatPrice(pricing) {
  if (!pricing || (!pricing.input && !pricing.output)) {
    return 'Unavailable';
  }

  const inputPrice = pricing.input ? `$${pricing.input.toFixed(6)}` : 'N/A';
  const outputPrice = pricing.output ? `$${pricing.output.toFixed(6)}` : 'N/A';

  return `Input: ${inputPrice} | Output: ${outputPrice}`;
}

/**
 * Format context size for display
 */
function formatContextSize(contextSize) {
  if (!contextSize) return 'Unknown';

  if (contextSize >= 1000000) {
    return `${(contextSize / 1000000).toFixed(1)}M tokens`;
  } else if (contextSize >= 1000) {
    return `${(contextSize / 1000).toFixed(0)}K tokens`;
  } else {
    return `${contextSize} tokens`;
  }
}

/**
 * Format tags for display
 */
function formatTags(tags) {
  if (!tags || tags.length === 0) return 'None';
  return tags.join(', ');
}

/**
 * Get provider color for consistent styling
 */
function getProviderColor(provider) {
  const colors = {
    'openai': chalk.green,
    'anthropic': chalk.blue,
    'openrouter': chalk.magenta,
    'google': chalk.yellow,
    'deepseek': chalk.cyan,
    'fireworks': chalk.red,
    'azure': chalk.blueBright
  };

  return colors[provider.toLowerCase()] || chalk.white;
}

/**
 * Convert models to unified format
 */
function getAllModels() {
  const allModels = [];

  // OpenAI models
  if (OPENAI_MODEL_METADATA) {
    Object.values(OPENAI_MODEL_METADATA).forEach(model => {
      allModels.push({
        id: model.id,
        label: model.label,
        description: model.description,
        contextSize: model.contextSize,
        pricing: model.pricing,
        tags: model.tags,
        provider: 'openai',
        providerName: 'OpenAI',
        releaseDate: model.releaseDate
      });
    });
  }

  // Anthropic models
  if (ANTHROPIC_MODELS) {
    ANTHROPIC_MODELS.forEach(model => {
      allModels.push({
        id: model.id,
        label: model.label,
        description: model.description,
        contextSize: model.contextLength,
        pricing: model.costPer1kTokens,
        tags: model.capabilities,
        provider: 'anthropic',
        providerName: 'Anthropic',
        releaseDate: model.releaseDate
      });
    });
  }

  // OpenRouter models
  if (OPENROUTER_MODEL_METADATA) {
    Object.values(OPENROUTER_MODEL_METADATA).forEach(model => {
      allModels.push({
        id: model.id,
        label: model.label,
        description: model.description,
        contextSize: model.contextSize,
        pricing: model.pricing,
        tags: model.tags,
        provider: 'openrouter',
        providerName: 'OpenRouter',
        originalProvider: model.provider
      });
    });
  }

  // DeepSeek models
  if (DEEPSEEK_MODEL_METADATA) {
    Object.values(DEEPSEEK_MODEL_METADATA).forEach(model => {
      allModels.push({
        id: model.id,
        label: model.label,
        description: model.description,
        contextSize: model.contextSize,
        pricing: model.pricing,
        tags: model.tags,
        provider: 'deepseek',
        providerName: 'DeepSeek',
        releaseDate: model.releaseDate
      });
    });
  }

  // Fireworks models
  if (FIREWORKS_MODEL_METADATA) {
    Object.values(FIREWORKS_MODEL_METADATA).forEach(model => {
      allModels.push({
        id: model.id,
        label: model.label,
        description: model.description,
        contextSize: model.contextSize,
        pricing: model.pricing,
        tags: model.tags,
        provider: 'fireworks',
        providerName: 'Fireworks AI',
        releaseDate: model.releaseDate
      });
    });
  }

  // Google models
  if (GOOGLE_MODEL_METADATA) {
    Object.values(GOOGLE_MODEL_METADATA).forEach(model => {
      allModels.push({
        id: model.id,
        label: model.label,
        description: model.description,
        contextSize: model.contextSize,
        pricing: model.pricing,
        tags: model.tags,
        provider: 'google',
        providerName: 'Google AI',
        releaseDate: model.releaseDate
      });
    });
  }

  return allModels;
}

/**
 * Group models by provider
 */
function getModelsByProvider() {
  const allModels = getAllModels();
  const grouped = {
    openai: [],
    anthropic: [],
    openrouter: [],
    azure: [],
    google: [],
    deepseek: [],
    fireworks: []
  };

  allModels.forEach(model => {
    if (grouped[model.provider]) {
      grouped[model.provider].push(model);
    }
  });

  return grouped;
}

/**
 * Display help information
 */
function showHelpText() {
  console.log(chalk.bold('\n🧠 Model Registry CLI Tool\n'));
  console.log('Usage: npm run list:models [options]\n');
  console.log('Options:');
  console.log('  --json, -j           Output in JSON format');
  console.log('  --provider=<name>    Filter by provider (openai, anthropic, openrouter, etc.)');
  console.log('  --stats              Show provider statistics only');
  console.log('  --help, -h           Show this help message\n');
  console.log('Examples:');
  console.log('  npm run list:models');
  console.log('  npm run list:models --json');
  console.log('  npm run list:models --provider=openai');
  console.log('  npm run list:models --stats\n');
}

/**
 * Display provider statistics
 */
function displayStats() {
  const modelsByProvider = getModelsByProvider();
  const totalModels = Object.values(modelsByProvider).reduce((sum, models) => sum + models.length, 0);

  console.log(chalk.bold('\n📊 Model Registry Statistics\n'));
  console.log(`Total Models: ${chalk.bold(totalModels.toString())}\n`);

  Object.entries(modelsByProvider).forEach(([provider, models]) => {
    const color = getProviderColor(provider);
    const providerName = provider.toUpperCase();
    const statusIcon = models.length > 0 ? '✅' : '❌';

    console.log(`${statusIcon} ${color(providerName.padEnd(12))} ${models.length.toString().padStart(3)} models`);
  });

  console.log();
}

/**
 * Main function to display models
 */
function main() {
  try {
    // Show help if requested
    if (showHelp) {
      showHelpText();
      return;
    }

    // Show stats if requested
    if (showStats) {
      displayStats();
      return;
    }

    // Get models (filtered by provider if specified)
    let modelsByProvider = getModelsByProvider();

    if (filterProvider) {
      const provider = filterProvider.toLowerCase();
      if (!(provider in modelsByProvider)) {
        console.error(chalk.red(`❌ Unknown provider: ${filterProvider}`));
        console.error(chalk.gray('Available providers: openai, anthropic, openrouter, google, deepseek, fireworks, azure'));
        process.exit(1);
      }

      modelsByProvider = {
        [provider]: modelsByProvider[provider]
      };
    }

    // JSON output mode
    if (isJsonMode) {
      const jsonOutput = {
        timestamp: new Date().toISOString(),
        totalProviders: Object.keys(modelsByProvider).length,
        totalModels: Object.values(modelsByProvider).reduce((sum, models) => sum + models.length, 0),
        providers: modelsByProvider
      };

      console.log(JSON.stringify(jsonOutput, null, 2));
      return;
    }

    // Human-readable output
    console.log(chalk.bold('\n🧠 Model Registry Overview\n'));

    // Calculate totals
    const totalProviders = Object.keys(modelsByProvider).filter(provider =>
      modelsByProvider[provider].length > 0
    ).length;
    const totalModels = Object.values(modelsByProvider).reduce((sum, models) => sum + models.length, 0);

    console.log(chalk.gray(`${totalProviders} providers • ${totalModels} models with verified metadata\n`));

    // Display models by provider
    Object.entries(modelsByProvider).forEach(([provider, models]) => {
      if (models.length === 0) return;

      const color = getProviderColor(provider);
      const providerName = models[0]?.providerName || provider.toUpperCase();

      console.log(color(`📦 ${providerName.toUpperCase()} (${models.length} models)`));

      models.forEach((model) => {
        const contextInfo = formatContextSize(model.contextSize);
        const pricingInfo = formatPrice(model.pricing);
        const tagsInfo = formatTags(model.tags);

        console.log(`${chalk.green('  •')} ${chalk.bold(model.label)} ${chalk.gray(`(${model.id})`)}`);
        console.log(`    ${chalk.gray('Context:')} ${contextInfo}`);
        console.log(`    ${chalk.gray('Pricing:')} ${pricingInfo}`);

        if (model.tags && model.tags.length > 0) {
          console.log(`    ${chalk.gray('Tags:')} ${tagsInfo}`);
        }

        if (model.originalProvider) {
          console.log(`    ${chalk.gray('Via:')} ${model.originalProvider}`);
        }

        if (model.description) {
          console.log(`    ${chalk.gray(model.description)}`);
        }

        console.log();
      });
    });

    console.log(chalk.gray('💡 Use --json for machine-readable output or --help for more options'));

  } catch (error) {
    console.error(chalk.red('❌ Failed to list models:'), error);
    process.exit(1);
  }
}

// Handle CLI execution
if (require.main === module) {
  main();
}
