#!/usr/bin/env node

/**
 * ✅ Status Indicators Test Script
 * Validates that all System settings have proper status mappings
 * and verifies the integration evidence
 */

const fs = require('fs');
const path = require('path');

// Read the status map file
const statusMapPath = path.join(__dirname, '../components/settings/system-status-map.ts');
const statusMapContent = fs.readFileSync(statusMapPath, 'utf8');

// Read the settings manager file to get SystemSettings interface
const settingsManagerPath = path.join(__dirname, '../components/settings/settings-manager.ts');
const settingsManagerContent = fs.readFileSync(settingsManagerPath, 'utf8');

console.log('🔍 Testing Status Indicators Implementation\n');

// Extract SystemSettings interface keys
const systemSettingsMatch = settingsManagerContent.match(/export interface SystemSettings \{([\s\S]*?)\}/);
if (!systemSettingsMatch) {
  console.error('❌ Could not find SystemSettings interface');
  process.exit(1);
}

const systemSettingsContent = systemSettingsMatch[1];
const settingKeys = [];
const lines = systemSettingsContent.split('\n');
for (const line of lines) {
  const match = line.trim().match(/^(\w+):/);
  if (match) {
    settingKeys.push(match[1]);
  }
}

console.log('📋 Found SystemSettings keys:', settingKeys);

// Extract status mappings
const statusMapMatch = statusMapContent.match(/SYSTEM_SETTINGS_STATUS_MAP: Record<keyof SystemSettings, SettingStatusInfo> = \{([\s\S]*?)\};/);
if (!statusMapMatch) {
  console.error('❌ Could not find SYSTEM_SETTINGS_STATUS_MAP');
  process.exit(1);
}

const statusMapContentInner = statusMapMatch[1];
const mappedKeys = [];
const statusLines = statusMapContentInner.split('\n');
for (const line of statusLines) {
  const match = line.trim().match(/^(\w+):/);
  if (match) {
    mappedKeys.push(match[1]);
  }
}

console.log('🗺️  Found status mappings for:', mappedKeys);

// Validate completeness
const missingKeys = settingKeys.filter(key => !mappedKeys.includes(key));
const extraKeys = mappedKeys.filter(key => !settingKeys.includes(key));

console.log('\n✅ Validation Results:');
if (missingKeys.length === 0 && extraKeys.length === 0) {
  console.log('✅ All SystemSettings have status mappings');
  console.log('✅ No extra mappings found');
  console.log('✅ Status mapping is complete and accurate');
} else {
  if (missingKeys.length > 0) {
    console.log('❌ Missing status mappings for:', missingKeys);
  }
  if (extraKeys.length > 0) {
    console.log('⚠️  Extra status mappings found:', extraKeys);
  }
}

// Extract and validate status types
const statusTypes = ['active', 'cosmetic', 'broken'];
const statusCounts = { active: 0, cosmetic: 0, broken: 0 };

for (const key of mappedKeys) {
  const keyRegex = new RegExp(`${key}:\\s*\\{[\\s\\S]*?status:\\s*'(\\w+)'`, 'g');
  const match = keyRegex.exec(statusMapContentInner);
  if (match) {
    const status = match[1];
    if (statusTypes.includes(status)) {
      statusCounts[status]++;
    } else {
      console.log(`⚠️  Invalid status type '${status}' for ${key}`);
    }
  }
}

console.log('\n📊 Status Distribution:');
console.log(`✅ Active: ${statusCounts.active}`);
console.log(`⚠️  Cosmetic: ${statusCounts.cosmetic}`);
console.log(`❌ Broken: ${statusCounts.broken}`);

// Validate integration evidence
console.log('\n🔍 Integration Evidence Check:');
const integrationKeywords = [
  'AutoSaveEngine', 'ThemeBridge', 'ConcurrencyManager', 
  'useTimeout', 'telemetry', 'debug', 'useSystemSettings'
];

let evidenceFound = 0;
for (const keyword of integrationKeywords) {
  if (statusMapContent.includes(keyword)) {
    evidenceFound++;
    console.log(`✅ Found evidence: ${keyword}`);
  }
}

console.log(`\n📈 Integration Evidence Score: ${evidenceFound}/${integrationKeywords.length}`);

if (evidenceFound >= integrationKeywords.length * 0.8) {
  console.log('✅ Strong integration evidence found');
} else {
  console.log('⚠️  Weak integration evidence - consider adding more details');
}

console.log('\n🎯 Test Summary:');
console.log(`✅ Settings mapped: ${mappedKeys.length}/${settingKeys.length}`);
console.log(`✅ Active settings: ${statusCounts.active}`);
console.log(`⚠️  Cosmetic settings: ${statusCounts.cosmetic}`);
console.log(`❌ Broken settings: ${statusCounts.broken}`);
console.log(`🔍 Integration evidence: ${evidenceFound}/${integrationKeywords.length}`);

if (missingKeys.length === 0 && statusCounts.active >= settingKeys.length * 0.8) {
  console.log('\n🎉 Status Indicators Implementation: PASSED');
  process.exit(0);
} else {
  console.log('\n⚠️  Status Indicators Implementation: NEEDS REVIEW');
  process.exit(1);
}
