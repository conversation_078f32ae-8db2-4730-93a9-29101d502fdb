{
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "outDir": "./dist-electron",
    "rootDir": "./electron",
    "resolveJsonModule": true,
    "isolatedModules": false, // Main process might have multiple files
    "noEmit": false, // We need to emit JS files for Electron main
    "moduleResolution": "node",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"] // If electron main needs to import from app structure
    }
  },
  "include": ["electron/**/*.ts"],
  "exclude": ["node_modules", "app", "components", "lib", "public", "styles", "out", ".next"]
}